// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		A1000000000000000001 /* MotionFitProApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000001 /* MotionFitProApp.swift */; };
		A1000000000000000002 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000002 /* ContentView.swift */; };
		A1000000000000000003 /* AppCoordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000003 /* AppCoordinator.swift */; };
		A1000000000000000005 /* ARSessionManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000005 /* ARSessionManager.swift */; };
		A1000000000000000006 /* MLProcessingManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000006 /* MLProcessingManager.swift */; };
		A1000000000000000007 /* AudioManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000007 /* AudioManager.swift */; };
		A1000000000000000011 /* MotionFitProError.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000011 /* MotionFitProError.swift */; };
		A1000000000000000012 /* Logger.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000012 /* Logger.swift */; };
		A1000000000000000013 /* AppConstants.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000013 /* AppConstants.swift */; };
		A1000000000000000014 /* View+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000014 /* View+Extensions.swift */; };
		A1000000000000000015 /* Color+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000015 /* Color+Extensions.swift */; };
		A1000000000000000016 /* CoreDataManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000016 /* CoreDataManager.swift */; };
		A1000000000000000017 /* CloudKitManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000017 /* CloudKitManager.swift */; };
		A1000000000000000020 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A2000000000000000020 /* Assets.xcassets */; };
		A1000000000000000021 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A2000000000000000021 /* Preview Assets.xcassets */; };
		A1000000000000000030 /* UserProfile.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000030 /* UserProfile.swift */; };
		A1000000000000000031 /* WorkoutSession.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000031 /* WorkoutSession.swift */; };
		A1000000000000000032 /* ExercisePerformance.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000032 /* ExercisePerformance.swift */; };
		A1000000000000000033 /* SetPerformance.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000033 /* SetPerformance.swift */; };
		A1000000000000000034 /* RepPerformance.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000034 /* RepPerformance.swift */; };
		A1000000000000000035 /* DataController.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000035 /* DataController.swift */; };
		A1000000000000000040 /* UserProfileRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000040 /* UserProfileRepository.swift */; };
		A1000000000000000041 /* WorkoutSessionRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000041 /* WorkoutSessionRepository.swift */; };
		A1000000000000000042 /* ExercisePerformanceRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000042 /* ExercisePerformanceRepository.swift */; };
		A1000000000000000043 /* SetPerformanceRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000043 /* SetPerformanceRepository.swift */; };
		A1000000000000000044 /* RepPerformanceRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000044 /* RepPerformanceRepository.swift */; };
		A1000000000000000050 /* SampleDataGenerator.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000050 /* SampleDataGenerator.swift */; };
		A1000000000000000060 /* MotionFitProTests/DataLayerTests/UserProfileRepositoryTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000060 /* MotionFitProTests/DataLayerTests/UserProfileRepositoryTests.swift */; };
		A1000000000000000061 /* MotionFitProTests/DataLayerTests/WorkoutSessionRepositoryTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000061 /* MotionFitProTests/DataLayerTests/WorkoutSessionRepositoryTests.swift */; };
		A1000000000000000062 /* MotionFitProTests/DataLayerTests/DataControllerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000062 /* MotionFitProTests/DataLayerTests/DataControllerTests.swift */; };
		A1000000000000000063 /* MotionFitProTests/DataLayerTests/SampleDataGeneratorTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000063 /* MotionFitProTests/DataLayerTests/SampleDataGeneratorTests.swift */; };
		A1000000000000000064 /* MotionFitProTests/DataLayerTests/SwiftDataModelsTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000064 /* MotionFitProTests/DataLayerTests/SwiftDataModelsTests.swift */; };
		A1000000000000000065 /* MotionFitPro/Data/Models/SwiftData/ExerciseType.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000065 /* MotionFitPro/Data/Models/SwiftData/ExerciseType.swift */; };
		A1000000000000000066 /* MotionFitPro/AR/Models/BodyPoseData.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000066 /* MotionFitPro/AR/Models/BodyPoseData.swift */; };
		A1000000000000000067 /* MotionFitPro/Data/Models/FormFeedbackModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000067 /* MotionFitPro/Data/Models/FormFeedbackModel.swift */; };
		A1000000000000000068 /* MotionFitPro/TestSquatView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000068 /* MotionFitPro/TestSquatView.swift */; };
		A1000000000000000069 /* MotionFitPro/Workout/WorkoutSessionManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2000000000000000069 /* MotionFitPro/Workout/WorkoutSessionManager.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		A3000000000000000001 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A9000000000000000001 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A0000000000000000000;
			remoteInfo = MotionFitPro;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		57715C7F2E26AD80007FA426 /* MotionFitPro.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = MotionFitPro.entitlements; sourceTree = "<group>"; };
		A0000000000000000001 /* MotionFitPro.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = MotionFitPro.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A0000000000000000002 /* MotionFitProTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MotionFitProTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		A2000000000000000001 /* MotionFitProApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MotionFitProApp.swift; sourceTree = "<group>"; };
		A2000000000000000002 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		A2000000000000000003 /* AppCoordinator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppCoordinator.swift; sourceTree = "<group>"; };
		A2000000000000000005 /* ARSessionManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ARSessionManager.swift; sourceTree = "<group>"; };
		A2000000000000000006 /* MLProcessingManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MLProcessingManager.swift; sourceTree = "<group>"; };
		A2000000000000000007 /* AudioManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AudioManager.swift; sourceTree = "<group>"; };
		A2000000000000000011 /* MotionFitProError.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MotionFitProError.swift; sourceTree = "<group>"; };
		A2000000000000000012 /* Logger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Logger.swift; sourceTree = "<group>"; };
		A2000000000000000013 /* AppConstants.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppConstants.swift; sourceTree = "<group>"; };
		A2000000000000000014 /* View+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "View+Extensions.swift"; sourceTree = "<group>"; };
		A2000000000000000015 /* Color+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Color+Extensions.swift"; sourceTree = "<group>"; };
		A2000000000000000016 /* CoreDataManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CoreDataManager.swift; sourceTree = "<group>"; };
		A2000000000000000017 /* CloudKitManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CloudKitManager.swift; sourceTree = "<group>"; };
		A2000000000000000020 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A2000000000000000021 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		A2000000000000000022 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A2000000000000000030 /* UserProfile.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserProfile.swift; sourceTree = "<group>"; };
		A2000000000000000031 /* WorkoutSession.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WorkoutSession.swift; sourceTree = "<group>"; };
		A2000000000000000032 /* ExercisePerformance.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExercisePerformance.swift; sourceTree = "<group>"; };
		A2000000000000000033 /* SetPerformance.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SetPerformance.swift; sourceTree = "<group>"; };
		A2000000000000000034 /* RepPerformance.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RepPerformance.swift; sourceTree = "<group>"; };
		A2000000000000000035 /* DataController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataController.swift; sourceTree = "<group>"; };
		A2000000000000000040 /* UserProfileRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserProfileRepository.swift; sourceTree = "<group>"; };
		A2000000000000000041 /* WorkoutSessionRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WorkoutSessionRepository.swift; sourceTree = "<group>"; };
		A2000000000000000042 /* ExercisePerformanceRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExercisePerformanceRepository.swift; sourceTree = "<group>"; };
		A2000000000000000043 /* SetPerformanceRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SetPerformanceRepository.swift; sourceTree = "<group>"; };
		A2000000000000000044 /* RepPerformanceRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RepPerformanceRepository.swift; sourceTree = "<group>"; };
		A2000000000000000050 /* SampleDataGenerator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SampleDataGenerator.swift; sourceTree = "<group>"; };
		A2000000000000000060 /* MotionFitProTests/DataLayerTests/UserProfileRepositoryTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MotionFitProTests/DataLayerTests/UserProfileRepositoryTests.swift; sourceTree = "<group>"; };
		A2000000000000000061 /* MotionFitProTests/DataLayerTests/WorkoutSessionRepositoryTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MotionFitProTests/DataLayerTests/WorkoutSessionRepositoryTests.swift; sourceTree = "<group>"; };
		A2000000000000000062 /* MotionFitProTests/DataLayerTests/DataControllerTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MotionFitProTests/DataLayerTests/DataControllerTests.swift; sourceTree = "<group>"; };
		A2000000000000000063 /* MotionFitProTests/DataLayerTests/SampleDataGeneratorTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MotionFitProTests/DataLayerTests/SampleDataGeneratorTests.swift; sourceTree = "<group>"; };
		A2000000000000000064 /* MotionFitProTests/DataLayerTests/SwiftDataModelsTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MotionFitProTests/DataLayerTests/SwiftDataModelsTests.swift; sourceTree = "<group>"; };
		A2000000000000000065 /* MotionFitPro/Data/Models/SwiftData/ExerciseType.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MotionFitPro/Data/Models/SwiftData/ExerciseType.swift; sourceTree = "<group>"; };
		A2000000000000000066 /* MotionFitPro/AR/Models/BodyPoseData.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MotionFitPro/AR/Models/BodyPoseData.swift; sourceTree = "<group>"; };
		A2000000000000000067 /* MotionFitPro/Data/Models/FormFeedbackModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MotionFitPro/Data/Models/FormFeedbackModel.swift; sourceTree = "<group>"; };
		A2000000000000000068 /* MotionFitPro/TestSquatView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MotionFitPro/TestSquatView.swift; sourceTree = "<group>"; };
		A2000000000000000069 /* MotionFitPro/Workout/WorkoutSessionManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MotionFitPro/Workout/WorkoutSessionManager.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A4000000000000000001 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A4000000000000000002 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		57715C7E2E261ED7007FA426 /* Recovered References */ = {
			isa = PBXGroup;
			children = (
				A2000000000000000065 /* MotionFitPro/Data/Models/SwiftData/ExerciseType.swift */,
				A2000000000000000066 /* MotionFitPro/AR/Models/BodyPoseData.swift */,
				A2000000000000000067 /* MotionFitPro/Data/Models/FormFeedbackModel.swift */,
				A2000000000000000068 /* MotionFitPro/TestSquatView.swift */,
				A2000000000000000069 /* MotionFitPro/Workout/WorkoutSessionManager.swift */,
			);
			name = "Recovered References";
			sourceTree = "<group>";
		};
		A5000000000000000001 = {
			isa = PBXGroup;
			children = (
				A5000000000000000002 /* MotionFitPro */,
				A5000000000000000003 /* MotionFitProTests */,
				A5000000000000000004 /* Products */,
				57715C7E2E261ED7007FA426 /* Recovered References */,
			);
			sourceTree = "<group>";
		};
		A5000000000000000002 /* MotionFitPro */ = {
			isa = PBXGroup;
			children = (
				57715C7F2E26AD80007FA426 /* MotionFitPro.entitlements */,
				A5000000000000000010 /* App */,
				A5000000000000000031 /* AR */,
				A5000000000000000033 /* Audio */,
				A5000000000000000015 /* Core */,
				A5000000000000000012 /* Data */,
				A5000000000000000013 /* Domain */,
				A5000000000000000032 /* ML */,
				A5000000000000000014 /* Presentation */,
				A5000000000000000034 /* Preview Content */,
				A5000000000000000035 /* Resources */,
				A2000000000000000022 /* Info.plist */,
			);
			path = MotionFitPro;
			sourceTree = "<group>";
		};
		A5000000000000000003 /* MotionFitProTests */ = {
			isa = PBXGroup;
			children = (
				A5000000000000000030 /* DataLayerTests */,
			);
			path = MotionFitProTests;
			sourceTree = "<group>";
		};
		A5000000000000000004 /* Products */ = {
			isa = PBXGroup;
			children = (
				A0000000000000000001 /* MotionFitPro.app */,
				A0000000000000000002 /* MotionFitProTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A5000000000000000010 /* App */ = {
			isa = PBXGroup;
			children = (
				A2000000000000000001 /* MotionFitProApp.swift */,
				A2000000000000000002 /* ContentView.swift */,
			);
			path = App;
			sourceTree = "<group>";
		};
		A5000000000000000011 /* Coordinators */ = {
			isa = PBXGroup;
			children = (
				A2000000000000000003 /* AppCoordinator.swift */,
			);
			path = Coordinators;
			sourceTree = "<group>";
		};
		A5000000000000000012 /* Data */ = {
			isa = PBXGroup;
			children = (
				A5000000000000000019 /* Models */,
				A5000000000000000020 /* Repositories */,
				A5000000000000000021 /* SwiftData */,
				A5000000000000000022 /* SampleData */,
				A5000000000000000024 /* CloudKit */,
				A5000000000000000025 /* CoreData */,
			);
			path = Data;
			sourceTree = "<group>";
		};
		A5000000000000000013 /* Domain */ = {
			isa = PBXGroup;
			children = (
			);
			path = Domain;
			sourceTree = "<group>";
		};
		A5000000000000000014 /* Presentation */ = {
			isa = PBXGroup;
			children = (
				A5000000000000000011 /* Coordinators */,
			);
			path = Presentation;
			sourceTree = "<group>";
		};
		A5000000000000000015 /* Core */ = {
			isa = PBXGroup;
			children = (
				A5000000000000000016 /* Constants */,
				A5000000000000000017 /* Extensions */,
				A5000000000000000018 /* Utilities */,
			);
			path = Core;
			sourceTree = "<group>";
		};
		A5000000000000000016 /* Constants */ = {
			isa = PBXGroup;
			children = (
				A2000000000000000013 /* AppConstants.swift */,
			);
			path = Constants;
			sourceTree = "<group>";
		};
		A5000000000000000017 /* Extensions */ = {
			isa = PBXGroup;
			children = (
				A2000000000000000014 /* View+Extensions.swift */,
				A2000000000000000015 /* Color+Extensions.swift */,
			);
			path = Extensions;
			sourceTree = "<group>";
		};
		A5000000000000000018 /* Utilities */ = {
			isa = PBXGroup;
			children = (
				A2000000000000000011 /* MotionFitProError.swift */,
				A2000000000000000012 /* Logger.swift */,
			);
			path = Utilities;
			sourceTree = "<group>";
		};
		A5000000000000000019 /* Models */ = {
			isa = PBXGroup;
			children = (
				A5000000000000000023 /* SwiftData */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		A5000000000000000020 /* Repositories */ = {
			isa = PBXGroup;
			children = (
				A2000000000000000040 /* UserProfileRepository.swift */,
				A2000000000000000041 /* WorkoutSessionRepository.swift */,
				A2000000000000000042 /* ExercisePerformanceRepository.swift */,
				A2000000000000000043 /* SetPerformanceRepository.swift */,
				A2000000000000000044 /* RepPerformanceRepository.swift */,
			);
			path = Repositories;
			sourceTree = "<group>";
		};
		A5000000000000000021 /* SwiftData */ = {
			isa = PBXGroup;
			children = (
				A2000000000000000035 /* DataController.swift */,
			);
			path = SwiftData;
			sourceTree = "<group>";
		};
		A5000000000000000022 /* SampleData */ = {
			isa = PBXGroup;
			children = (
				A2000000000000000050 /* SampleDataGenerator.swift */,
			);
			path = SampleData;
			sourceTree = "<group>";
		};
		A5000000000000000023 /* SwiftData */ = {
			isa = PBXGroup;
			children = (
				A2000000000000000030 /* UserProfile.swift */,
				A2000000000000000031 /* WorkoutSession.swift */,
				A2000000000000000032 /* ExercisePerformance.swift */,
				A2000000000000000033 /* SetPerformance.swift */,
				A2000000000000000034 /* RepPerformance.swift */,
			);
			path = SwiftData;
			sourceTree = "<group>";
		};
		A5000000000000000024 /* CloudKit */ = {
			isa = PBXGroup;
			children = (
				A2000000000000000017 /* CloudKitManager.swift */,
			);
			path = CloudKit;
			sourceTree = "<group>";
		};
		A5000000000000000025 /* CoreData */ = {
			isa = PBXGroup;
			children = (
				A2000000000000000016 /* CoreDataManager.swift */,
			);
			path = CoreData;
			sourceTree = "<group>";
		};
		A5000000000000000030 /* DataLayerTests */ = {
			isa = PBXGroup;
			children = (
				A2000000000000000060 /* MotionFitProTests/DataLayerTests/UserProfileRepositoryTests.swift */,
				A2000000000000000061 /* MotionFitProTests/DataLayerTests/WorkoutSessionRepositoryTests.swift */,
				A2000000000000000062 /* MotionFitProTests/DataLayerTests/DataControllerTests.swift */,
				A2000000000000000063 /* MotionFitProTests/DataLayerTests/SampleDataGeneratorTests.swift */,
				A2000000000000000064 /* MotionFitProTests/DataLayerTests/SwiftDataModelsTests.swift */,
			);
			path = DataLayerTests;
			sourceTree = "<group>";
		};
		A5000000000000000031 /* AR */ = {
			isa = PBXGroup;
			children = (
				A2000000000000000005 /* ARSessionManager.swift */,
			);
			path = AR;
			sourceTree = "<group>";
		};
		A5000000000000000032 /* ML */ = {
			isa = PBXGroup;
			children = (
				A2000000000000000006 /* MLProcessingManager.swift */,
			);
			path = ML;
			sourceTree = "<group>";
		};
		A5000000000000000033 /* Audio */ = {
			isa = PBXGroup;
			children = (
				A2000000000000000007 /* AudioManager.swift */,
			);
			path = Audio;
			sourceTree = "<group>";
		};
		A5000000000000000034 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				A2000000000000000021 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		A5000000000000000035 /* Resources */ = {
			isa = PBXGroup;
			children = (
				A2000000000000000020 /* Assets.xcassets */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A0000000000000000000 /* MotionFitPro */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A8000000000000000001 /* Build configuration list for PBXNativeTarget "MotionFitPro" */;
			buildPhases = (
				A6000000000000000001 /* Sources */,
				A4000000000000000001 /* Frameworks */,
				A7000000000000000001 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = MotionFitPro;
			productName = MotionFitPro;
			productReference = A0000000000000000001 /* MotionFitPro.app */;
			productType = "com.apple.product-type.application";
		};
		A0000000000000000100 /* MotionFitProTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A8000000000000000002 /* Build configuration list for PBXNativeTarget "MotionFitProTests" */;
			buildPhases = (
				A6000000000000000002 /* Sources */,
				A4000000000000000002 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				A3000000000000000002 /* PBXTargetDependency */,
			);
			name = MotionFitProTests;
			productName = MotionFitProTests;
			productReference = A0000000000000000002 /* MotionFitProTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A9000000000000000001 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1520;
				LastUpgradeCheck = 1520;
				TargetAttributes = {
					A0000000000000000000 = {
						CreatedOnToolsVersion = 15.2;
					};
					A0000000000000000100 = {
						CreatedOnToolsVersion = 15.2;
						TestTargetID = A0000000000000000000;
					};
				};
			};
			buildConfigurationList = A8000000000000000003 /* Build configuration list for PBXProject "MotionFitPro" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = A5000000000000000001;
			productRefGroup = A5000000000000000004 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A0000000000000000000 /* MotionFitPro */,
				A0000000000000000100 /* MotionFitProTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A7000000000000000001 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1000000000000000021 /* Preview Assets.xcassets in Resources */,
				A1000000000000000020 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A6000000000000000001 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1000000000000000001 /* MotionFitProApp.swift in Sources */,
				A1000000000000000002 /* ContentView.swift in Sources */,
				A1000000000000000003 /* AppCoordinator.swift in Sources */,
				A1000000000000000005 /* ARSessionManager.swift in Sources */,
				A1000000000000000006 /* MLProcessingManager.swift in Sources */,
				A1000000000000000007 /* AudioManager.swift in Sources */,
				A1000000000000000011 /* MotionFitProError.swift in Sources */,
				A1000000000000000012 /* Logger.swift in Sources */,
				A1000000000000000013 /* AppConstants.swift in Sources */,
				A1000000000000000014 /* View+Extensions.swift in Sources */,
				A1000000000000000015 /* Color+Extensions.swift in Sources */,
				A1000000000000000016 /* CoreDataManager.swift in Sources */,
				A1000000000000000017 /* CloudKitManager.swift in Sources */,
				A1000000000000000030 /* UserProfile.swift in Sources */,
				A1000000000000000031 /* WorkoutSession.swift in Sources */,
				A1000000000000000032 /* ExercisePerformance.swift in Sources */,
				A1000000000000000033 /* SetPerformance.swift in Sources */,
				A1000000000000000034 /* RepPerformance.swift in Sources */,
				A1000000000000000035 /* DataController.swift in Sources */,
				A1000000000000000040 /* UserProfileRepository.swift in Sources */,
				A1000000000000000041 /* WorkoutSessionRepository.swift in Sources */,
				A1000000000000000042 /* ExercisePerformanceRepository.swift in Sources */,
				A1000000000000000043 /* SetPerformanceRepository.swift in Sources */,
				A1000000000000000044 /* RepPerformanceRepository.swift in Sources */,
				A1000000000000000050 /* SampleDataGenerator.swift in Sources */,
				A1000000000000000065 /* MotionFitPro/Data/Models/SwiftData/ExerciseType.swift in Sources */,
				A1000000000000000066 /* MotionFitPro/AR/Models/BodyPoseData.swift in Sources */,
				A1000000000000000067 /* MotionFitPro/Data/Models/FormFeedbackModel.swift in Sources */,
				A1000000000000000068 /* MotionFitPro/TestSquatView.swift in Sources */,
				A1000000000000000069 /* MotionFitPro/Workout/WorkoutSessionManager.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A6000000000000000002 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1000000000000000060 /* MotionFitProTests/DataLayerTests/UserProfileRepositoryTests.swift in Sources */,
				A1000000000000000061 /* MotionFitProTests/DataLayerTests/WorkoutSessionRepositoryTests.swift in Sources */,
				A1000000000000000062 /* MotionFitProTests/DataLayerTests/DataControllerTests.swift in Sources */,
				A1000000000000000063 /* MotionFitProTests/DataLayerTests/SampleDataGeneratorTests.swift in Sources */,
				A1000000000000000064 /* MotionFitProTests/DataLayerTests/SwiftDataModelsTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		A3000000000000000002 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A0000000000000000000 /* MotionFitPro */;
			targetProxy = A3000000000000000001 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		A8000000000000000004 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 6.0;
			};
			name = Debug;
		};
		A8000000000000000005 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_VERSION = 6.0;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		A8000000000000000006 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = MotionFitPro/MotionFitPro.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"MotionFitPro/Preview Content\"";
				DEVELOPMENT_TEAM = 6386ADS4F7;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = MotionFitPro/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "MotionFitPro uses the camera for real-time exercise form analysis and motion tracking to provide personalized workout feedback.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.vs.motionfitpro;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = NO;
			};
			name = Debug;
		};
		A8000000000000000007 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = MotionFitPro/MotionFitPro.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"MotionFitPro/Preview Content\"";
				DEVELOPMENT_TEAM = 6386ADS4F7;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = MotionFitPro/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "MotionFitPro uses the camera for real-time exercise form analysis and motion tracking to provide personalized workout feedback.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.vs.motionfitpro;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = NO;
			};
			name = Release;
		};
		A8000000000000000008 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.vs.motionfitpro.MotionFitProTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MotionFitPro.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MotionFitPro";
			};
			name = Debug;
		};
		A8000000000000000009 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.vs.motionfitpro.MotionFitProTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MotionFitPro.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MotionFitPro";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A8000000000000000001 /* Build configuration list for PBXNativeTarget "MotionFitPro" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A8000000000000000006 /* Debug */,
				A8000000000000000007 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A8000000000000000002 /* Build configuration list for PBXNativeTarget "MotionFitProTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A8000000000000000008 /* Debug */,
				A8000000000000000009 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A8000000000000000003 /* Build configuration list for PBXProject "MotionFitPro" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A8000000000000000004 /* Debug */,
				A8000000000000000005 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = A9000000000000000001 /* Project object */;
}
