Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -project MotionFitPro.xcodeproj -scheme MotionFitPro -configuration Debug build

--- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:macOS, arch:arm64, variant:Designed for [iPad,iPhone], id:00008132-0010782E22A1801C, name:My Mac }
{ platform:iOS, id:dvtdevice-DVTiPhonePlaceholder-iphoneos:placeholder, name:Any iOS Device }
{ platform:iOS Simulator, id:dvtdevice-DVTiOSDeviceSimulatorPlaceholder-iphonesimulator:placeholder, name:Any iOS Simulator Device }
{ platform:visionOS Simulator, arch:arm64, variant:Designed for [iPad,iPhone], id:2F8F003C-7C61-40F2-AAD7-472AF8BD0725, OS:2.5, name:Apple Vision Pro }
{ platform:iOS Simulator, arch:arm64, id:4B5F3C64-7497-4046-93E4-353E6D1F076C, OS:18.5, name:iPad (A16) }
{ platform:iOS Simulator, arch:x86_64, id:4B5F3C64-7497-4046-93E4-353E6D1F076C, OS:18.5, name:iPad (A16) }
{ platform:iOS Simulator, arch:arm64, id:3F75FE68-1EE3-4AF6-A6FE-DBB4BEF68209, OS:18.5, name:iPad Air 11-inch (M3) }
{ platform:iOS Simulator, arch:x86_64, id:3F75FE68-1EE3-4AF6-A6FE-DBB4BEF68209, OS:18.5, name:iPad Air 11-inch (M3) }
{ platform:iOS Simulator, arch:arm64, id:CA65C7E4-D01D-4155-9627-AC3A2A6691C7, OS:18.5, name:iPad Air 13-inch (M3) }
{ platform:iOS Simulator, arch:x86_64, id:CA65C7E4-D01D-4155-9627-AC3A2A6691C7, OS:18.5, name:iPad Air 13-inch (M3) }
{ platform:iOS Simulator, arch:arm64, id:A07CF027-2300-4705-B6EA-54B58BAB369D, OS:18.5, name:iPad Pro 11-inch (M4) }
{ platform:iOS Simulator, arch:x86_64, id:A07CF027-2300-4705-B6EA-54B58BAB369D, OS:18.5, name:iPad Pro 11-inch (M4) }
{ platform:iOS Simulator, arch:arm64, id:5D6F95E1-F306-483B-B9DE-3A3BAB760476, OS:18.5, name:iPad Pro 13-inch (M4) }
{ platform:iOS Simulator, arch:x86_64, id:5D6F95E1-F306-483B-B9DE-3A3BAB760476, OS:18.5, name:iPad Pro 13-inch (M4) }
{ platform:iOS Simulator, arch:arm64, id:7EF0D206-35A3-4DE4-B225-C57B2B822A8A, OS:18.5, name:iPad mini (A17 Pro) }
{ platform:iOS Simulator, arch:x86_64, id:7EF0D206-35A3-4DE4-B225-C57B2B822A8A, OS:18.5, name:iPad mini (A17 Pro) }
{ platform:iOS Simulator, arch:arm64, id:0CDEECB3-3A1D-4F1F-82DD-9195D52E04AF, OS:18.5, name:iPhone 16 }
{ platform:iOS Simulator, arch:x86_64, id:0CDEECB3-3A1D-4F1F-82DD-9195D52E04AF, OS:18.5, name:iPhone 16 }
{ platform:iOS Simulator, arch:arm64, id:CCCD9A00-7050-4E9B-8BAF-845627FEA0AD, OS:18.5, name:iPhone 16 Plus }
{ platform:iOS Simulator, arch:x86_64, id:CCCD9A00-7050-4E9B-8BAF-845627FEA0AD, OS:18.5, name:iPhone 16 Plus }
{ platform:iOS Simulator, arch:arm64, id:75A9C3D6-F7B9-40DC-8520-21045EAC3D1E, OS:18.5, name:iPhone 16 Pro }
{ platform:iOS Simulator, arch:x86_64, id:75A9C3D6-F7B9-40DC-8520-21045EAC3D1E, OS:18.5, name:iPhone 16 Pro }
{ platform:iOS Simulator, arch:arm64, id:9B4E16D2-5E01-497F-A2C8-A17C612D53D2, OS:18.5, name:iPhone 16 Pro Max }
{ platform:iOS Simulator, arch:x86_64, id:9B4E16D2-5E01-497F-A2C8-A17C612D53D2, OS:18.5, name:iPhone 16 Pro Max }
{ platform:iOS Simulator, arch:arm64, id:4D2E2A55-E698-4F4B-A227-C50414ABB6CC, OS:18.5, name:iPhone 16e }
{ platform:iOS Simulator, arch:x86_64, id:4D2E2A55-E698-4F4B-A227-C50414ABB6CC, OS:18.5, name:iPhone 16e }
ComputePackagePrebuildTargetDependencyGraph

Prepare packages

CreateBuildRequest

SendProjectDescription

CreateBuildOperation

ComputeTargetDependencyGraph
note: Building targets in dependency order
note: Target dependency graph (1 target)
    Target 'MotionFitPro' in project 'MotionFitPro' (no dependencies)

GatherProvisioningInputs

CreateBuildDescription

ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro.xcodeproj
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk -o /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache

SwiftDriver MotionFitPro normal arm64 com.apple.xcode.tools.swift.compiler (in target 'MotionFitPro' from project 'MotionFitPro')
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1
    builtin-SwiftDriver -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc -module-name MotionFitPro -Onone -enforce-exclusivity\=checked @/Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/Objects-normal/arm64/MotionFitPro.SwiftFileList -DDEBUG -enable-experimental-feature DebugDescriptionMacro -sdk /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk -target arm64-apple-ios17.0 -g -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -Xfrontend -serialize-debugging-options -profile-coverage-mapping -profile-generate -enable-testing -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Index.noindex/DataStore -swift-version 6 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Products/Debug-iphoneos -F /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Products/Debug-iphoneos -emit-localized-strings -emit-localized-strings-path /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/Objects-normal/arm64 -c -j10 -enable-batch-mode -incremental -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache -output-file-map /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/Objects-normal/arm64/MotionFitPro-OutputFileMap.json -use-frontend-parseable-output -save-temps -no-color-diagnostics -serialize-diagnostics -emit-dependencies -emit-module -emit-module-path /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/Objects-normal/arm64/MotionFitPro.swiftmodule -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/swift-overrides.hmap -emit-const-values -Xfrontend -const-gather-protocols-file -Xfrontend /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/Objects-normal/arm64/MotionFitPro_const_extract_protocols.json -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/MotionFitPro-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/MotionFitPro-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/MotionFitPro-all-target-headers.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/MotionFitPro-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Products/Debug-iphoneos/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/DerivedSources -Xcc -DDEBUG\=1 -emit-objc-header -emit-objc-header-path /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/Objects-normal/arm64/MotionFitPro-Swift.h -working-directory /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1 -experimental-emit-module-separately -disable-cmo

SwiftCompile normal arm64 Compiling\ MotionFitProApp.swift,\ ContentView.swift,\ AppCoordinator.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/App/MotionFitProApp.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/App/ContentView.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Presentation/Coordinators/AppCoordinator.swift (in target 'MotionFitPro' from project 'MotionFitPro')

SwiftCompile normal arm64 /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/App/MotionFitProApp.swift (in target 'MotionFitPro' from project 'MotionFitPro')
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1
    

SwiftCompile normal arm64 /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/App/ContentView.swift (in target 'MotionFitPro' from project 'MotionFitPro')
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1
    

SwiftCompile normal arm64 /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Presentation/Coordinators/AppCoordinator.swift (in target 'MotionFitPro' from project 'MotionFitPro')
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1
    

SwiftCompile normal arm64 Compiling\ DataController.swift,\ UserProfileRepository.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/SwiftData/DataController.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Repositories/UserProfileRepository.swift (in target 'MotionFitPro' from project 'MotionFitPro')
SwiftCompile normal arm64 /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/SwiftData/DataController.swift (in target 'MotionFitPro' from project 'MotionFitPro')
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1
    

SwiftCompile normal arm64 /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Repositories/UserProfileRepository.swift (in target 'MotionFitPro' from project 'MotionFitPro')
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1
    

SwiftEmitModule normal arm64 Emitting\ module\ for\ MotionFitPro (in target 'MotionFitPro' from project 'MotionFitPro')

EmitSwiftModule normal arm64 (in target 'MotionFitPro' from project 'MotionFitPro')
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1
    

SwiftCompile normal arm64 Compiling\ WorkoutSessionRepository.swift,\ ExercisePerformanceRepository.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Repositories/WorkoutSessionRepository.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Repositories/ExercisePerformanceRepository.swift (in target 'MotionFitPro' from project 'MotionFitPro')
Failed frontend command:
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swift-frontend -frontend -c /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/App/MotionFitProApp.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/App/ContentView.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Presentation/Coordinators/AppCoordinator.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/AR/ARSessionManager.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/ML/MLProcessingManager.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Audio/AudioManager.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Core/Utilities/MotionFitProError.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Core/Utilities/Logger.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Core/Constants/AppConstants.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Core/Extensions/View+Extensions.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Core/Extensions/Color+Extensions.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/CoreData/CoreDataManager.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/CloudKit/CloudKitManager.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Models/SwiftData/UserProfile.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Models/SwiftData/WorkoutSession.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Models/SwiftData/ExercisePerformance.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Models/SwiftData/SetPerformance.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Models/SwiftData/RepPerformance.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/SwiftData/DataController.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Repositories/UserProfileRepository.swift -primary-file /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Repositories/WorkoutSessionRepository.swift -primary-file /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Repositories/ExercisePerformanceRepository.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Repositories/SetPerformanceRepository.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Repositories/RepPerformanceRepository.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/SampleData/SampleDataGenerator.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/DerivedSources/GeneratedAssetSymbols.swift -emit-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/Objects-normal/arm64/WorkoutSessionRepository.d -emit-const-values-path /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/Objects-normal/arm64/WorkoutSessionRepository.swiftconstvalues -emit-reference-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/Objects-normal/arm64/WorkoutSessionRepository.swiftdeps -serialize-diagnostics-path /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/Objects-normal/arm64/WorkoutSessionRepository.dia -emit-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/Objects-normal/arm64/ExercisePerformanceRepository.d -emit-const-values-path /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/Objects-normal/arm64/ExercisePerformanceRepository.swiftconstvalues -emit-reference-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/Objects-normal/arm64/ExercisePerformanceRepository.swiftdeps -serialize-diagnostics-path /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/Objects-normal/arm64/ExercisePerformanceRepository.dia -emit-localized-strings -emit-localized-strings-path /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/Objects-normal/arm64 -target arm64-apple-ios17.0 -Xllvm -aarch64-use-tbi -enable-objc-interop -stack-check -sdk /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk -I /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Products/Debug-iphoneos -F /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Products/Debug-iphoneos -no-color-diagnostics -enable-testing -g -debug-info-format\=dwarf -dwarf-version\=4 -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -profile-generate -profile-coverage-mapping -swift-version 6 -enforce-exclusivity\=checked -Onone -D DEBUG -serialize-debugging-options -const-gather-protocols-file /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/Objects-normal/arm64/MotionFitPro_const_extract_protocols.json -enable-experimental-feature DebugDescriptionMacro -empty-abi-descriptor -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -working-directory -Xcc /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1 -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift -enable-anonymous-context-mangled-names -file-compilation-dir /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1 -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.5-22F76-7fa4eea80a99bbfdc046826b63ec4baf.sdkstatcache -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/swift-overrides.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/MotionFitPro-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/MotionFitPro-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/MotionFitPro-all-target-headers.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/MotionFitPro-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Products/Debug-iphoneos/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/DerivedSources -Xcc -DDEBUG\=1 -module-name MotionFitPro -frontend-parseable-output -disable-clang-spi -target-sdk-version 18.5 -target-sdk-name iphoneos18.5 -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -in-process-plugin-server-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/libSwiftInProcPluginServer.dylib -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/lib/swift/host/plugins -o /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/Objects-normal/arm64/WorkoutSessionRepository.o -o /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/Objects-normal/arm64/ExercisePerformanceRepository.o -index-unit-output-path /MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/Objects-normal/arm64/WorkoutSessionRepository.o -index-unit-output-path /MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/Objects-normal/arm64/ExercisePerformanceRepository.o -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Index.noindex/DataStore -index-system-modules

SwiftCompile normal arm64 /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Repositories/WorkoutSessionRepository.swift (in target 'MotionFitPro' from project 'MotionFitPro')
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1
    
/Users/<USER>/Projects/Xcode Projects/App idea 1/MotionFitPro/Data/Models/SwiftData/WorkoutSession.swift:23:51: error: cannot find 'Achievement' in scope
    @Relationship(deleteRule: .cascade, inverse: \Achievement.workoutSession)
                                                  ^~~~~~~~~~~
/Users/<USER>/Projects/Xcode Projects/App idea 1/MotionFitPro/Data/Models/SwiftData/WorkoutSession.swift:23:51: error: invalid component of Swift key path
    @Relationship(deleteRule: .cascade, inverse: \Achievement.workoutSession)
                                                  ^
/Users/<USER>/Projects/Xcode Projects/App idea 1/MotionFitPro/Data/Models/SwiftData/WorkoutSession.swift:24:24: error: cannot find type 'Achievement' in scope
    var achievements: [Achievement] = []
                       ^~~~~~~~~~~

SwiftCompile normal arm64 /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Repositories/ExercisePerformanceRepository.swift (in target 'MotionFitPro' from project 'MotionFitPro')
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1
    

SwiftCompile normal arm64 Compiling\ ExercisePerformance.swift,\ SetPerformance.swift,\ RepPerformance.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Models/SwiftData/ExercisePerformance.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Models/SwiftData/SetPerformance.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Models/SwiftData/RepPerformance.swift (in target 'MotionFitPro' from project 'MotionFitPro')

SwiftCompile normal arm64 /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Models/SwiftData/ExercisePerformance.swift (in target 'MotionFitPro' from project 'MotionFitPro')
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1
    

SwiftCompile normal arm64 /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Models/SwiftData/SetPerformance.swift (in target 'MotionFitPro' from project 'MotionFitPro')
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1
    

SwiftCompile normal arm64 /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Models/SwiftData/RepPerformance.swift (in target 'MotionFitPro' from project 'MotionFitPro')
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1
    

SwiftCompile normal arm64 Compiling\ View+Extensions.swift,\ Color+Extensions.swift,\ CoreDataManager.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Core/Extensions/View+Extensions.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Core/Extensions/Color+Extensions.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/CoreData/CoreDataManager.swift (in target 'MotionFitPro' from project 'MotionFitPro')

SwiftCompile normal arm64 /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Core/Extensions/View+Extensions.swift (in target 'MotionFitPro' from project 'MotionFitPro')
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1
    

SwiftCompile normal arm64 /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Core/Extensions/Color+Extensions.swift (in target 'MotionFitPro' from project 'MotionFitPro')
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1
    

SwiftCompile normal arm64 /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/CoreData/CoreDataManager.swift (in target 'MotionFitPro' from project 'MotionFitPro')
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1
    

SwiftCompile normal arm64 Compiling\ MotionFitProError.swift,\ Logger.swift,\ AppConstants.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Core/Utilities/MotionFitProError.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Core/Utilities/Logger.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Core/Constants/AppConstants.swift (in target 'MotionFitPro' from project 'MotionFitPro')

SwiftCompile normal arm64 /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Core/Utilities/MotionFitProError.swift (in target 'MotionFitPro' from project 'MotionFitPro')
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1
    

SwiftCompile normal arm64 /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Core/Utilities/Logger.swift (in target 'MotionFitPro' from project 'MotionFitPro')
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1
    

SwiftCompile normal arm64 /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Core/Constants/AppConstants.swift (in target 'MotionFitPro' from project 'MotionFitPro')
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1
    

SwiftCompile normal arm64 Compiling\ SampleDataGenerator.swift,\ GeneratedAssetSymbols.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/SampleData/SampleDataGenerator.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/DerivedSources/GeneratedAssetSymbols.swift (in target 'MotionFitPro' from project 'MotionFitPro')

SwiftCompile normal arm64 /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/SampleData/SampleDataGenerator.swift (in target 'MotionFitPro' from project 'MotionFitPro')
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1
    

SwiftCompile normal arm64 /Users/<USER>/Library/Developer/Xcode/DerivedData/MotionFitPro-hkxcrxtogwgkebfmerphdednwoji/Build/Intermediates.noindex/MotionFitPro.build/Debug-iphoneos/MotionFitPro.build/DerivedSources/GeneratedAssetSymbols.swift (in target 'MotionFitPro' from project 'MotionFitPro')
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1
    

SwiftCompile normal arm64 Compiling\ SetPerformanceRepository.swift,\ RepPerformanceRepository.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Repositories/SetPerformanceRepository.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Repositories/RepPerformanceRepository.swift (in target 'MotionFitPro' from project 'MotionFitPro')

SwiftCompile normal arm64 /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Repositories/SetPerformanceRepository.swift (in target 'MotionFitPro' from project 'MotionFitPro')
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1
    

SwiftCompile normal arm64 /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Repositories/RepPerformanceRepository.swift (in target 'MotionFitPro' from project 'MotionFitPro')
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1
    

SwiftCompile normal arm64 Compiling\ CloudKitManager.swift,\ UserProfile.swift,\ WorkoutSession.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/CloudKit/CloudKitManager.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Models/SwiftData/UserProfile.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Models/SwiftData/WorkoutSession.swift (in target 'MotionFitPro' from project 'MotionFitPro')

SwiftCompile normal arm64 /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/CloudKit/CloudKitManager.swift (in target 'MotionFitPro' from project 'MotionFitPro')
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1
    

SwiftCompile normal arm64 /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Models/SwiftData/UserProfile.swift (in target 'MotionFitPro' from project 'MotionFitPro')
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1
    

SwiftCompile normal arm64 /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Models/SwiftData/WorkoutSession.swift (in target 'MotionFitPro' from project 'MotionFitPro')
    cd /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1
    

SwiftCompile normal arm64 Compiling\ ARSessionManager.swift,\ MLProcessingManager.swift,\ AudioManager.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/AR/ARSessionManager.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/ML/MLProcessingManager.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Audio/AudioManager.swift (in target 'MotionFitPro' from project 'MotionFitPro')

SwiftDriverJobDiscovery normal arm64 Compiling MotionFitProError.swift, Logger.swift, AppConstants.swift (in target 'MotionFitPro' from project 'MotionFitPro')

** BUILD FAILED **


The following build commands failed:
	SwiftCompile normal arm64 Compiling\ WorkoutSessionRepository.swift,\ ExercisePerformanceRepository.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Repositories/WorkoutSessionRepository.swift /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Repositories/ExercisePerformanceRepository.swift (in target 'MotionFitPro' from project 'MotionFitPro')
	SwiftCompile normal arm64 /Users/<USER>/Projects/Xcode\ Projects/App\ idea\ 1/MotionFitPro/Data/Repositories/WorkoutSessionRepository.swift (in target 'MotionFitPro' from project 'MotionFitPro')
	Building project MotionFitPro with scheme MotionFitPro and configuration Debug
(3 failures)
