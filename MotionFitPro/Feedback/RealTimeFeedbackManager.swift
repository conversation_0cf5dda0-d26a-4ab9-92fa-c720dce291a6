import Foundation
import Combine
import SwiftUI

/// Manages real-time feedback during workouts including form analysis, coaching cues, and performance metrics
@MainActor
class RealTimeFeedbackManager: ObservableObject {
    
    // MARK: - Published Properties
    @Published var currentFeedback: [FormFeedback] = []
    @Published var isAnalyzing: Bool = false
    @Published var formScore: Float = 0.0
    @Published var repQuality: RepQuality = .unknown
    @Published var coachingCue: String = ""
    @Published var showingFormAlert: Bool = false
    @Published var performanceMetrics: RealTimeMetrics = RealTimeMetrics()
    
    // MARK: - Private Properties
    private let mlProcessingManager = MLProcessingManager.shared
    private let audioManager = AudioManager.shared
    private let hapticManager = HapticManager.shared
    private let logger = Logger.shared
    
    private var cancellables = Set<AnyCancellable>()
    private var feedbackHistory: [FormFeedback] = []
    private var lastFeedbackTime: Date = Date()
    private var currentExercise: ExerciseType?
    private var workoutSettings: WorkoutSettings = WorkoutSettings()
    
    // Feedback timing controls
    private let minFeedbackInterval: TimeInterval = 2.0 // Minimum time between feedback
    private let maxFeedbackHistory = 50
    
    // Form thresholds
    private let criticalFormThreshold: Float = 0.5
    private let goodFormThreshold: Float = 0.8
    private let excellentFormThreshold: Float = 0.9
    
    // MARK: - Initialization
    init() {
        setupSubscriptions()
    }
    
    // MARK: - Public Interface
    
    /// Start real-time feedback for a specific exercise
    func startFeedback(for exercise: ExerciseType, settings: WorkoutSettings) {
        currentExercise = exercise
        workoutSettings = settings
        isAnalyzing = true
        
        // Clear previous feedback
        currentFeedback.removeAll()
        feedbackHistory.removeAll()
        formScore = 0.0
        repQuality = .unknown
        
        // Configure audio and haptic feedback
        configureAudioFeedback()
        configureHapticFeedback()
        
        logger.info("Started real-time feedback for \(exercise)", category: .feedback)
    }
    
    /// Stop real-time feedback
    func stopFeedback() {
        isAnalyzing = false
        currentExercise = nil
        currentFeedback.removeAll()
        coachingCue = ""
        showingFormAlert = false
        
        logger.info("Stopped real-time feedback", category: .feedback)
    }
    
    /// Process new pose data and generate feedback
    func processPoseData(_ poseData: BodyPoseData) {
        guard isAnalyzing, let exercise = currentExercise else { return }
        
        // Update performance metrics
        updatePerformanceMetrics(poseData)
        
        // Analyze form if enabled
        if workoutSettings.realTimeFeedback {
            analyzeForm(poseData, for: exercise)
        }
        
        // Generate coaching cues
        if workoutSettings.voiceCoachingEnabled {
            generateCoachingCues(poseData, for: exercise)
        }
    }
    
    /// Manually trigger feedback for a specific issue
    func triggerFeedback(_ feedback: FormFeedback) {
        addFeedback(feedback)
        deliverFeedback(feedback)
    }
    
    /// Clear current feedback
    func clearFeedback() {
        currentFeedback.removeAll()
        coachingCue = ""
        showingFormAlert = false
    }
    
    // MARK: - Private Methods
    
    private func setupSubscriptions() {
        // Subscribe to ML processing results
        mlProcessingManager.$formScore
            .receive(on: DispatchQueue.main)
            .sink { [weak self] score in
                self?.handleFormScore(score)
            }
            .store(in: &cancellables)
        
        mlProcessingManager.$lastAnalysis
            .receive(on: DispatchQueue.main)
            .compactMap { $0 }
            .sink { [weak self] analysis in
                self?.handleExerciseAnalysis(analysis)
            }
            .store(in: &cancellables)
        
        mlProcessingManager.$repCount
            .receive(on: DispatchQueue.main)
            .sink { [weak self] repCount in
                self?.handleRepCompletion(repCount)
            }
            .store(in: &cancellables)
    }
    
    private func configureAudioFeedback() {
        audioManager.setCoachingPersonality(workoutSettings.coachingPersonality)
        audioManager.setSpeechEnabled(workoutSettings.voiceCoachingEnabled)
        audioManager.setSoundEffectsEnabled(workoutSettings.audioFeedback)
    }
    
    private func configureHapticFeedback() {
        hapticManager.setHapticsEnabled(workoutSettings.hapticFeedback)
    }
    
    private func updatePerformanceMetrics(_ poseData: BodyPoseData) {
        performanceMetrics.lastUpdateTime = Date()
        performanceMetrics.trackingQuality = poseData.trackingQuality
        performanceMetrics.confidence = poseData.confidence
        performanceMetrics.frameRate = calculateFrameRate()
        
        // Update stability metrics
        if let centerOfMass = poseData.centerOfMass {
            performanceMetrics.stability = calculateStability(centerOfMass)
        }
    }
    
    private func analyzeForm(_ poseData: BodyPoseData, for exercise: ExerciseType) {
        // Check if enough time has passed since last feedback
        guard Date().timeIntervalSince(lastFeedbackTime) >= minFeedbackInterval else { return }
        
        // Analyze form based on exercise type
        let formAnalysis = performFormAnalysis(poseData, for: exercise)
        formScore = formAnalysis.overallScore
        
        // Generate feedback based on analysis
        let feedback = generateFormFeedback(formAnalysis, exercise: exercise)
        
        if !feedback.isEmpty {
            for item in feedback {
                addFeedback(item)
                deliverFeedback(item)
            }
            lastFeedbackTime = Date()
        }
        
        // Check for critical form issues
        if formScore < criticalFormThreshold && workoutSettings.pauseOnFormError {
            triggerFormAlert()
        }
    }
    
    private func performFormAnalysis(_ poseData: BodyPoseData, for exercise: ExerciseType) -> FormAnalysis {
        // This would integrate with the FormAnalyzer
        switch exercise {
        case .squat:
            return analyzeSquatForm(poseData)
        case .pushUp:
            return analyzePushUpForm(poseData)
        case .plank:
            return analyzePlankForm(poseData)
        default:
            return FormAnalysis(scores: [:], overallScore: 0.5, criticalIssues: [], recommendations: [])
        }
    }
    
    private func analyzeSquatForm(_ poseData: BodyPoseData) -> FormAnalysis {
        var scores: [FormCriteria: Float] = [:]
        var issues: [FormIssue] = []
        var recommendations: [String] = []
        
        // Check knee alignment
        if let leftKnee = poseData.joints[.leftLowerLeg],
           let rightKnee = poseData.joints[.rightLowerLeg],
           let leftAnkle = poseData.joints[.leftFoot],
           let rightAnkle = poseData.joints[.rightFoot] {
            
            let kneeAlignment = checkKneeAlignment(leftKnee: leftKnee, rightKnee: rightKnee, leftAnkle: leftAnkle, rightAnkle: rightAnkle)
            scores[.jointAlignment] = kneeAlignment
            
            if kneeAlignment < 0.7 {
                issues.append(FormIssue(criteria: .jointAlignment, severity: .medium, description: "Knees caving inward", jointInvolved: .leftLowerLeg))
                recommendations.append("Keep knees aligned with toes")
            }
        }
        
        // Check squat depth
        if let hips = poseData.joints[.root],
           let knees = poseData.joints[.leftLowerLeg] {
            
            let depth = checkSquatDepth(hips: hips, knees: knees)
            scores[.rangeOfMotion] = depth
            
            if depth < 0.6 {
                recommendations.append("Squat deeper - aim for thighs parallel to ground")
            }
        }
        
        let overallScore = scores.values.reduce(0, +) / Float(scores.count)
        
        return FormAnalysis(
            scores: scores,
            overallScore: overallScore,
            criticalIssues: issues,
            recommendations: recommendations
        )
    }
    
    private func analyzePushUpForm(_ poseData: BodyPoseData) -> FormAnalysis {
        var scores: [FormCriteria: Float] = [:]
        var issues: [FormIssue] = []
        var recommendations: [String] = []
        
        // Check body alignment
        if let head = poseData.joints[.head],
           let shoulders = poseData.joints[.spine6],
           let hips = poseData.joints[.root] {
            
            let alignment = checkBodyLineAlignment(head: head, shoulders: shoulders, hips: hips)
            scores[.jointAlignment] = alignment
            
            if alignment < 0.7 {
                issues.append(FormIssue(criteria: .jointAlignment, severity: .medium, description: "Body not in straight line", jointInvolved: .spine6))
                recommendations.append("Keep body in straight line from head to heels")
            }
        }
        
        let overallScore = scores.values.reduce(0, +) / Float(scores.count)
        
        return FormAnalysis(
            scores: scores,
            overallScore: overallScore,
            criticalIssues: issues,
            recommendations: recommendations
        )
    }
    
    private func analyzePlankForm(_ poseData: BodyPoseData) -> FormAnalysis {
        // Similar to push-up but focus on maintaining position
        return analyzePushUpForm(poseData)
    }
    
    private func generateFormFeedback(_ analysis: FormAnalysis, exercise: ExerciseType) -> [FormFeedback] {
        var feedback: [FormFeedback] = []
        
        // Generate feedback for critical issues
        for issue in analysis.criticalIssues {
            let feedbackItem = FormFeedback(
                type: .correction,
                message: issue.description,
                exerciseType: exercise,
                severity: issue.severity,
                correctionSuggestion: getCorrection(for: issue, exercise: exercise)
            )
            feedback.append(feedbackItem)
        }
        
        // Generate positive feedback for good form
        if analysis.overallScore > excellentFormThreshold {
            let encouragement = FormFeedback(
                type: .encouragement,
                message: "Excellent form!",
                exerciseType: exercise,
                severity: .low
            )
            feedback.append(encouragement)
        }
        
        return feedback
    }
    
    private func generateCoachingCues(_ poseData: BodyPoseData, for exercise: ExerciseType) {
        // Generate contextual coaching cues based on current form
        let cues = getCoachingCues(for: exercise, formScore: formScore)
        
        if !cues.isEmpty && coachingCue != cues.first {
            coachingCue = cues.randomElement() ?? ""
            
            // Deliver audio cue if enabled
            if workoutSettings.voiceCoachingEnabled && !coachingCue.isEmpty {
                audioManager.speak(coachingCue)
            }
        }
    }
    
    private func handleFormScore(_ score: Float) {
        formScore = score
        
        // Update rep quality
        if score > excellentFormThreshold {
            repQuality = .excellent
        } else if score > goodFormThreshold {
            repQuality = .good
        } else if score > criticalFormThreshold {
            repQuality = .acceptable
        } else {
            repQuality = .poor
        }
    }
    
    private func handleExerciseAnalysis(_ analysis: ExerciseAnalysis) {
        // Process the detailed analysis from ML
        let feedback = analysis.feedback
        
        for item in feedback {
            addFeedback(item)
            
            // Deliver immediate feedback for critical issues
            if item.severity == .high {
                deliverFeedback(item)
            }
        }
    }
    
    private func handleRepCompletion(_ repCount: Int) {
        // Provide feedback on rep completion
        let qualityFeedback = getRepQualityFeedback(repQuality)
        
        if !qualityFeedback.isEmpty {
            audioManager.speak(qualityFeedback)
        }
        
        // Haptic feedback
        hapticManager.trigger(.repCompleted)
    }
    
    private func addFeedback(_ feedback: FormFeedback) {
        currentFeedback.append(feedback)
        feedbackHistory.append(feedback)
        
        // Limit history size
        if feedbackHistory.count > maxFeedbackHistory {
            feedbackHistory.removeFirst()
        }
        
        // Remove old feedback from current list
        if currentFeedback.count > 3 {
            currentFeedback.removeFirst()
        }
    }
    
    private func deliverFeedback(_ feedback: FormFeedback) {
        // Audio feedback
        if workoutSettings.audioFeedback {
            audioManager.speak(feedback.message)
        }
        
        // Haptic feedback
        if workoutSettings.hapticFeedback {
            switch feedback.severity {
            case .high:
                hapticManager.trigger(.error)
            case .medium:
                hapticManager.trigger(.warning)
            case .low:
                hapticManager.trigger(.success)
            }
        }
        
        logger.debug("Delivered feedback: \(feedback.message)", category: .feedback)
    }
    
    private func triggerFormAlert() {
        showingFormAlert = true
        audioManager.speak("Check your form")
        hapticManager.trigger(.formCorrection)
    }
    
    // MARK: - Helper Methods
    
    private func calculateFrameRate() -> Double {
        // Implementation would calculate actual frame rate
        return 30.0
    }
    
    private func calculateStability(_ centerOfMass: simd_float3) -> Float {
        // Implementation would calculate stability based on center of mass movement
        return 0.8
    }
    
    private func checkKneeAlignment(leftKnee: Joint3D, rightKnee: Joint3D, leftAnkle: Joint3D, rightAnkle: Joint3D) -> Float {
        // Implementation would check knee-ankle alignment
        return 0.8
    }
    
    private func checkSquatDepth(hips: Joint3D, knees: Joint3D) -> Float {
        // Implementation would check if hips are below knee level
        let hipHeight = hips.position.y
        let kneeHeight = knees.position.y
        return hipHeight <= kneeHeight ? 1.0 : 0.6
    }
    
    private func checkBodyLineAlignment(head: Joint3D, shoulders: Joint3D, hips: Joint3D) -> Float {
        // Implementation would check if body forms a straight line
        return 0.8
    }
    
    private func getCorrection(for issue: FormIssue, exercise: ExerciseType) -> String? {
        switch (issue.criteria, exercise) {
        case (.jointAlignment, .squat):
            return "Point toes slightly outward and track knees over them"
        case (.jointAlignment, .pushUp):
            return "Engage core and maintain straight body line"
        case (.rangeOfMotion, .squat):
            return "Sit back into the squat as if sitting in a chair"
        default:
            return nil
        }
    }
    
    private func getCoachingCues(for exercise: ExerciseType, formScore: Float) -> [String] {
        switch exercise {
        case .squat:
            if formScore < 0.7 {
                return ["Keep your chest up", "Drive through your heels", "Control the descent"]
            } else {
                return ["Great depth!", "Perfect form", "Keep it up!"]
            }
        case .pushUp:
            if formScore < 0.7 {
                return ["Keep your core tight", "Lower with control", "Push through your palms"]
            } else {
                return ["Excellent range!", "Perfect alignment", "Strong push!"]
            }
        default:
            return ["Keep going!", "You've got this!", "Stay focused!"]
        }
    }
    
    private func getRepQualityFeedback(_ quality: RepQuality) -> String {
        switch quality {
        case .excellent:
            return "Perfect rep!"
        case .good:
            return "Good form!"
        case .acceptable:
            return "Keep working on form"
        case .poor:
            return "Focus on technique"
        case .unknown:
            return ""
        }
    }
}

// MARK: - Supporting Types

struct RealTimeMetrics {
    var lastUpdateTime: Date = Date()
    var trackingQuality: TrackingQuality = .initializing
    var confidence: Float = 0.0
    var frameRate: Double = 0.0
    var stability: Float = 0.0
}

enum RepQuality {
    case excellent
    case good
    case acceptable
    case poor
    case unknown
    
    var color: Color {
        switch self {
        case .excellent: return .green
        case .good: return .blue
        case .acceptable: return .yellow
        case .poor: return .red
        case .unknown: return .gray
        }
    }
    
    var description: String {
        switch self {
        case .excellent: return "Excellent"
        case .good: return "Good"
        case .acceptable: return "Acceptable"
        case .poor: return "Poor"
        case .unknown: return "Unknown"
        }
    }
}

// MARK: - Logger Category Extension
extension Logger.Category {
    static let feedback = Logger.Category(rawValue: "feedback")
}
