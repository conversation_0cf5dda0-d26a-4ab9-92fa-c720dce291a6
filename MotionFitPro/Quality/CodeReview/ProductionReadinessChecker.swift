import Foundation

// MARK: - Production Readiness Checker

/// Comprehensive production readiness validation system
final class ProductionReadinessChecker {
    
    // MARK: - Properties
    
    private let logger = Logger()
    
    // MARK: - Main Validation
    
    func performFullProductionCheck() -> ProductionReadinessReport {
        logger.info("Starting production readiness check", category: .quality)
        
        let report = ProductionReadinessReport(
            configurationCheck: validateConfiguration(),
            securityCheck: validateSecurity(),
            performanceCheck: validatePerformance(),
            privacyCheck: validatePrivacy(),
            accessibilityCheck: validateAccessibility(),
            localizationCheck: validateLocalization(),
            testingCheck: validateTesting(),
            appStoreCheck: validateAppStoreCompliance(),
            codeQualityCheck: validateCodeQuality(),
            dependencyCheck: validateDependencies()
        )
        
        logger.info("Production readiness check completed", category: .quality)
        return report
    }
    
    // MARK: - Configuration Validation
    
    private func validateConfiguration() -> ValidationResult {
        var issues: [ValidationIssue] = []
        var warnings: [ValidationWarning] = []
        
        // Check production configuration
        let configIssues = ProductionConfiguration.validateConfiguration()
        issues.append(contentsOf: configIssues.map { 
            ValidationIssue(type: .configuration, description: $0, severity: .critical)
        })
        
        // Check environment settings
        if ProductionConfiguration.environment != .production {
            issues.append(ValidationIssue(
                type: .configuration,
                description: "Environment not set to production",
                severity: .critical
            ))
        }
        
        // Check debug settings
        if ProductionConfiguration.isDebugMode {
            issues.append(ValidationIssue(
                type: .configuration,
                description: "Debug mode enabled in production build",
                severity: .critical
            ))
        }
        
        // Check logging level
        if ProductionConfiguration.logLevel == .debug {
            warnings.append(ValidationWarning(
                type: .configuration,
                description: "Debug logging enabled - may impact performance"
            ))
        }
        
        return ValidationResult(
            category: .configuration,
            passed: issues.isEmpty,
            issues: issues,
            warnings: warnings
        )
    }
    
    // MARK: - Security Validation
    
    private func validateSecurity() -> ValidationResult {
        var issues: [ValidationIssue] = []
        var warnings: [ValidationWarning] = []
        
        // Check encryption
        if !ProductionConfiguration.securityConfiguration.encryptionAlgorithm.contains("AES-256") {
            issues.append(ValidationIssue(
                type: .security,
                description: "Weak encryption algorithm",
                severity: .high
            ))
        }
        
        // Check certificate pinning
        if !ProductionConfiguration.securityConfiguration.certificatePinningEnabled {
            warnings.append(ValidationWarning(
                type: .security,
                description: "Certificate pinning disabled"
            ))
        }
        
        // Check biometric authentication
        if !ProductionConfiguration.securityConfiguration.biometricAuthenticationEnabled {
            warnings.append(ValidationWarning(
                type: .security,
                description: "Biometric authentication disabled"
            ))
        }
        
        // Check jailbreak detection
        if !ProductionConfiguration.securityConfiguration.jailbreakDetectionEnabled {
            warnings.append(ValidationWarning(
                type: .security,
                description: "Jailbreak detection disabled"
            ))
        }
        
        return ValidationResult(
            category: .security,
            passed: issues.isEmpty,
            issues: issues,
            warnings: warnings
        )
    }
    
    // MARK: - Performance Validation
    
    private func validatePerformance() -> ValidationResult {
        var issues: [ValidationIssue] = []
        var warnings: [ValidationWarning] = []
        
        // Check frame rate targets
        let targetFPS = ProductionConfiguration.performanceConfiguration.targetFrameRate
        if targetFPS < 30.0 {
            warnings.append(ValidationWarning(
                type: .performance,
                description: "Target frame rate below 30 FPS"
            ))
        }
        
        // Check memory thresholds
        let memoryThreshold = ProductionConfiguration.performanceConfiguration.memoryWarningThreshold
        if memoryThreshold > 300.0 {
            warnings.append(ValidationWarning(
                type: .performance,
                description: "Memory warning threshold may be too high"
            ))
        }
        
        // Check thermal throttling
        if !ProductionConfiguration.performanceConfiguration.thermalThrottlingEnabled {
            warnings.append(ValidationWarning(
                type: .performance,
                description: "Thermal throttling disabled"
            ))
        }
        
        return ValidationResult(
            category: .performance,
            passed: issues.isEmpty,
            issues: issues,
            warnings: warnings
        )
    }
    
    // MARK: - Privacy Validation
    
    private func validatePrivacy() -> ValidationResult {
        var issues: [ValidationIssue] = []
        var warnings: [ValidationWarning] = []
        
        // Check data collection consent
        if ProductionConfiguration.privacyConfiguration.dataCollectionEnabled && 
           ProductionConfiguration.privacyConfiguration.analyticsEnabled {
            warnings.append(ValidationWarning(
                type: .privacy,
                description: "Analytics enabled by default - should require user consent"
            ))
        }
        
        // Check data retention
        let retentionDays = ProductionConfiguration.privacyConfiguration.dataRetentionDays
        if retentionDays > 365 {
            warnings.append(ValidationWarning(
                type: .privacy,
                description: "Data retention period exceeds 1 year"
            ))
        }
        
        // Check encryption
        if !ProductionConfiguration.privacyConfiguration.encryptionEnabled {
            issues.append(ValidationIssue(
                type: .privacy,
                description: "Data encryption disabled",
                severity: .critical
            ))
        }
        
        return ValidationResult(
            category: .privacy,
            passed: issues.isEmpty,
            issues: issues,
            warnings: warnings
        )
    }
    
    // MARK: - Accessibility Validation
    
    private func validateAccessibility() -> ValidationResult {
        var issues: [ValidationIssue] = []
        var warnings: [ValidationWarning] = []
        
        let accessibilityConfig = ProductionConfiguration.accessibilityConfiguration
        
        // Check VoiceOver support
        if !accessibilityConfig.voiceOverSupport {
            issues.append(ValidationIssue(
                type: .accessibility,
                description: "VoiceOver support disabled",
                severity: .medium
            ))
        }
        
        // Check Dynamic Type support
        if !accessibilityConfig.dynamicTypeSupport {
            warnings.append(ValidationWarning(
                type: .accessibility,
                description: "Dynamic Type support disabled"
            ))
        }
        
        // Check reduced motion support
        if !accessibilityConfig.reducedMotionSupport {
            warnings.append(ValidationWarning(
                type: .accessibility,
                description: "Reduced motion support disabled"
            ))
        }
        
        return ValidationResult(
            category: .accessibility,
            passed: issues.isEmpty,
            issues: issues,
            warnings: warnings
        )
    }
    
    // MARK: - Localization Validation
    
    private func validateLocalization() -> ValidationResult {
        var issues: [ValidationIssue] = []
        var warnings: [ValidationWarning] = []
        
        let localizationConfig = ProductionConfiguration.localizationConfiguration
        
        // Check supported languages
        if localizationConfig.supportedLanguages.count < 2 {
            warnings.append(ValidationWarning(
                type: .localization,
                description: "Limited language support"
            ))
        }
        
        // Check default language
        if localizationConfig.defaultLanguage != "en" {
            warnings.append(ValidationWarning(
                type: .localization,
                description: "Default language is not English"
            ))
        }
        
        return ValidationResult(
            category: .localization,
            passed: issues.isEmpty,
            issues: issues,
            warnings: warnings
        )
    }
    
    // MARK: - Testing Validation
    
    private func validateTesting() -> ValidationResult {
        var issues: [ValidationIssue] = []
        var warnings: [ValidationWarning] = []
        
        // Check if test code is included in production
        if Bundle.main.path(forResource: "TestBase", ofType: "swift") != nil {
            issues.append(ValidationIssue(
                type: .testing,
                description: "Test code included in production build",
                severity: .high
            ))
        }
        
        // Check crash reporting
        if !ProductionConfiguration.qaConfiguration.crashReportingEnabled {
            warnings.append(ValidationWarning(
                type: .testing,
                description: "Crash reporting disabled"
            ))
        }
        
        return ValidationResult(
            category: .testing,
            passed: issues.isEmpty,
            issues: issues,
            warnings: warnings
        )
    }
    
    // MARK: - App Store Compliance Validation
    
    private func validateAppStoreCompliance() -> ValidationResult {
        var issues: [ValidationIssue] = []
        var warnings: [ValidationWarning] = []
        
        // Check bundle identifier
        let bundleId = Bundle.main.bundleIdentifier ?? ""
        if !bundleId.contains("com.motionfitpro") {
            issues.append(ValidationIssue(
                type: .appStore,
                description: "Invalid bundle identifier",
                severity: .critical
            ))
        }
        
        // Check version format
        let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? ""
        if !version.matches(pattern: #"^\d+\.\d+\.\d+$"#) {
            issues.append(ValidationIssue(
                type: .appStore,
                description: "Invalid version format",
                severity: .medium
            ))
        }
        
        return ValidationResult(
            category: .appStore,
            passed: issues.isEmpty,
            issues: issues,
            warnings: warnings
        )
    }
    
    // MARK: - Code Quality Validation
    
    private func validateCodeQuality() -> ValidationResult {
        var issues: [ValidationIssue] = []
        var warnings: [ValidationWarning] = []
        
        // Check for TODO/FIXME comments in production
        // This would typically be done by a static analysis tool
        
        // Check for debug print statements
        // This would be caught by static analysis
        
        // Check for force unwrapping
        // This would be caught by static analysis
        
        return ValidationResult(
            category: .codeQuality,
            passed: issues.isEmpty,
            issues: issues,
            warnings: warnings
        )
    }
    
    // MARK: - Dependency Validation
    
    private func validateDependencies() -> ValidationResult {
        var issues: [ValidationIssue] = []
        var warnings: [ValidationWarning] = []
        
        // Check for development dependencies in production
        // This would check Package.swift or similar
        
        // Check for outdated dependencies
        // This would check for security vulnerabilities
        
        return ValidationResult(
            category: .dependencies,
            passed: issues.isEmpty,
            issues: issues,
            warnings: warnings
        )
    }
}

// MARK: - Supporting Types

struct ProductionReadinessReport {
    let configurationCheck: ValidationResult
    let securityCheck: ValidationResult
    let performanceCheck: ValidationResult
    let privacyCheck: ValidationResult
    let accessibilityCheck: ValidationResult
    let localizationCheck: ValidationResult
    let testingCheck: ValidationResult
    let appStoreCheck: ValidationResult
    let codeQualityCheck: ValidationResult
    let dependencyCheck: ValidationResult
    
    var overallPassed: Bool {
        return [
            configurationCheck, securityCheck, performanceCheck,
            privacyCheck, accessibilityCheck, localizationCheck,
            testingCheck, appStoreCheck, codeQualityCheck, dependencyCheck
        ].allSatisfy { $0.passed }
    }
    
    var criticalIssues: [ValidationIssue] {
        return allIssues.filter { $0.severity == .critical }
    }
    
    var allIssues: [ValidationIssue] {
        return [
            configurationCheck, securityCheck, performanceCheck,
            privacyCheck, accessibilityCheck, localizationCheck,
            testingCheck, appStoreCheck, codeQualityCheck, dependencyCheck
        ].flatMap { $0.issues }
    }
    
    var allWarnings: [ValidationWarning] {
        return [
            configurationCheck, securityCheck, performanceCheck,
            privacyCheck, accessibilityCheck, localizationCheck,
            testingCheck, appStoreCheck, codeQualityCheck, dependencyCheck
        ].flatMap { $0.warnings }
    }
}

struct ValidationResult {
    let category: ValidationCategory
    let passed: Bool
    let issues: [ValidationIssue]
    let warnings: [ValidationWarning]
}

struct ValidationIssue {
    let type: ValidationType
    let description: String
    let severity: IssueSeverity
}

struct ValidationWarning {
    let type: ValidationType
    let description: String
}

enum ValidationCategory: String, CaseIterable {
    case configuration = "Configuration"
    case security = "Security"
    case performance = "Performance"
    case privacy = "Privacy"
    case accessibility = "Accessibility"
    case localization = "Localization"
    case testing = "Testing"
    case appStore = "App Store"
    case codeQuality = "Code Quality"
    case dependencies = "Dependencies"
}

enum ValidationType: String {
    case configuration = "Configuration"
    case security = "Security"
    case performance = "Performance"
    case privacy = "Privacy"
    case accessibility = "Accessibility"
    case localization = "Localization"
    case testing = "Testing"
    case appStore = "App Store"
    case codeQuality = "Code Quality"
    case dependencies = "Dependencies"
}

enum IssueSeverity: String, CaseIterable {
    case low = "Low"
    case medium = "Medium"
    case high = "High"
    case critical = "Critical"
    
    var priority: Int {
        switch self {
        case .low: return 1
        case .medium: return 2
        case .high: return 3
        case .critical: return 4
        }
    }
}

// MARK: - String Extension for Pattern Matching

extension String {
    func matches(pattern: String) -> Bool {
        return range(of: pattern, options: .regularExpression) != nil
    }
}
