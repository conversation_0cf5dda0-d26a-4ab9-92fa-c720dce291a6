import SwiftUI
import ARKit

/// Enhanced squat tracking view with session management and animations
struct TestSquatView: View {
    @StateObject private var workoutManager = WorkoutSessionManager()
    @StateObject private var arManager = ARSessionManager.shared
    
    // Session state
    @State private var sessionStartTime: Date?
    @State private var showingSessionSummary = false
    @State private var sessionSummary: SessionSummary?
    
    // Animation states
    @State private var repAnimationScale: CGFloat = 1.0
    @State private var showRepBurst = false
    @State private var lastRepCount = 0
    
    // Timer
    @State private var timer: Timer?
    @State private var elapsedTime: TimeInterval = 0
    
    var body: some View {
        NavigationView {
            ZStack {
                // AR Camera View
                ARCameraView()
                    .ignoresSafeArea()
                
                // Overlay UI
                VStack {
                    // Top: Rep Counter
                    HStack {
                        VStack(alignment: .leading) {
                            Text("SQUAT TRACKER")
                                .font(.headline)
                                .foregroundColor(.white)
                            
                            Text("Reps: \(workoutManager.repCount)")
                                .font(.largeTitle)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .scaleEffect(repAnimationScale)
                                .animation(.spring(response: 0.3, dampingFraction: 0.6), value: repAnimationScale)
                        }
                        
                        Spacer()
                        
                        // Status indicator with timer
                        VStack(alignment: .trailing) {
                            Circle()
                                .fill(arManager.isBodyDetected ? .green : .red)
                                .frame(width: 20, height: 20)
                            
                            Text(arManager.isBodyDetected ? "Tracking" : "Not Tracking")
                                .font(.caption)
                                .foregroundColor(.white)
                            
                            if workoutManager.sessionState == .active {
                                Text("Time: \(formatTime(elapsedTime))")
                                    .font(.caption)
                                    .foregroundColor(.white)
                                    .padding(.top, 4)
                            }
                        }
                    }
                    .padding()
                    .background(.ultraThinMaterial)
                    .cornerRadius(12)
                    .padding()
                    
                    Spacer()
                    
                    // Center: Enhanced form feedback
                    if workoutManager.sessionState == .active {
                        VStack(spacing: 12) {
                            // Form score with color indicator
                            HStack {
                                Circle()
                                    .fill(formScoreColor)
                                    .frame(width: 16, height: 16)
                                    .animation(.easeInOut(duration: 0.3), value: formScoreColor)
                                
                                Text("Form Score")
                                    .font(.headline)
                                    .foregroundColor(.white)
                                
                                Spacer()
                                
                                Text("\(Int(workoutManager.currentFormScore * 100))%")
                                    .font(.headline)
                                    .fontWeight(.bold)
                                    .foregroundColor(formScoreColor)
                            }
                            
                            // Feedback text with animation
                            Text(formFeedbackText)
                                .font(.subheadline)
                                .foregroundColor(.white)
                                .multilineTextAlignment(.center)
                                .animation(.easeInOut(duration: 0.3), value: formFeedbackText)
                        }
                        .padding()
                        .background(.ultraThinMaterial)
                        .cornerRadius(12)
                        .padding(.horizontal)
                    }
                    
                    Spacer()
                    
                    // Bottom: Enhanced Controls
                    VStack(spacing: 12) {
                        if workoutManager.sessionState == .notStarted {
                            Button(action: startWorkout) {
                                Text("Start Squat Session")
                                    .font(.headline)
                                    .foregroundColor(.white)
                                    .padding()
                                    .frame(maxWidth: .infinity)
                                    .background(.green)
                                    .cornerRadius(12)
                            }
                        } else {
                            HStack(spacing: 12) {
                                if workoutManager.sessionState == .active {
                                    Button(action: pauseWorkout) {
                                        Text("Pause")
                                            .font(.headline)
                                            .foregroundColor(.white)
                                            .padding()
                                            .frame(maxWidth: .infinity)
                                            .background(.orange)
                                            .cornerRadius(12)
                                    }
                                } else if workoutManager.sessionState == .paused {
                                    Button(action: resumeWorkout) {
                                        Text("Resume")
                                            .font(.headline)
                                            .foregroundColor(.white)
                                            .padding()
                                            .frame(maxWidth: .infinity)
                                            .background(.blue)
                                            .cornerRadius(12)
                                    }
                                }
                                
                                Button(action: stopWorkout) {
                                    Text("Stop Session")
                                        .font(.headline)
                                        .foregroundColor(.white)
                                        .padding()
                                        .frame(maxWidth: .infinity)
                                        .background(.red)
                                        .cornerRadius(12)
                                }
                            }
                        }
                    }
                    .padding()
                }
            }
            .navigationBarHidden(true)
        }
        .onAppear {
            setupTest()
        }
        .onChange(of: workoutManager.repCount) { _, newValue in
            if newValue > lastRepCount {
                animateRepCompletion()
                lastRepCount = newValue
            }
        }
        .onChange(of: workoutManager.sessionState) { _, newState in
            handleSessionStateChange(newState)
        }
        .sheet(isPresented: $showingSessionSummary) {
            if let summary = sessionSummary {
                SessionSummaryView(summary: summary) {
                    showingSessionSummary = false
                    resetSession()
                }
            }
        }
        .overlay(
            // Rep completion burst animation
            Group {
                if showRepBurst {
                    RepBurstView()
                        .transition(.scale.combined(with: .opacity))
                        .animation(.spring(response: 0.5, dampingFraction: 0.7), value: showRepBurst)
                }
            }
        )
    }
    
    private var formScoreColor: Color {
        let score = workoutManager.currentFormScore
        if score > 0.8 { return .green }
        if score > 0.6 { return .yellow }
        if score > 0.4 { return .orange }
        return .red
    }
    
    private var formFeedbackText: String {
        let score = workoutManager.currentFormScore
        if workoutManager.sessionState != .active {
            return "Press start to begin tracking"
        }
        
        if score > 0.8 {
            return "Excellent form! Keep it up!"
        } else if score > 0.6 {
            return "Good form, watch your depth"
        } else if score > 0.4 {
            return "Focus on proper squat form"
        } else {
            return "Go deeper, keep knees aligned"
        }
    }
    
    private func setupTest() {
        print("Setting up squat tracking test...")
        lastRepCount = workoutManager.repCount
    }
    
    private func startWorkout() {
        Task {
            do {
                try await workoutManager.startSession(exercises: [.squat])
                sessionStartTime = Date()
                startTimer()
                print("Squat tracking session started")
            } catch {
                print("Failed to start workout: \(error)")
            }
        }
    }
    
    private func pauseWorkout() {
        workoutManager.pauseSession()
        stopTimer()
    }
    
    private func resumeWorkout() {
        workoutManager.resumeSession()
        startTimer()
    }
    
    private func stopWorkout() {
        Task {
            await workoutManager.stopSession()
            stopTimer()
            generateSessionSummary()
            print("Squat tracking session stopped")
        }
    }
    
    // MARK: - Timer Management
    
    private func startTimer() {
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            Task { @MainActor in
                elapsedTime += 1.0
            }
        }
    }
    
    private func stopTimer() {
        timer?.invalidate()
        timer = nil
    }
    
    private func formatTime(_ time: TimeInterval) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    // MARK: - Animation Helpers
    
    private func animateRepCompletion() {
        // Scale animation for rep count
        withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
            repAnimationScale = 1.3
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                repAnimationScale = 1.0
            }
        }
        
        // Show burst animation
        withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
            showRepBurst = true
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            withAnimation(.easeOut(duration: 0.3)) {
                showRepBurst = false
            }
        }
    }
    
    private func handleSessionStateChange(_ newState: WorkoutSessionState) {
        switch newState {
        case .active:
            if timer == nil {
                startTimer()
            }
        case .paused:
            stopTimer()
        case .completed:
            stopTimer()
            generateSessionSummary()
        default:
            break
        }
    }
    
    private func generateSessionSummary() {
        guard let startTime = sessionStartTime else { return }
        
        let summary = SessionSummary(
            totalReps: workoutManager.repCount,
            duration: elapsedTime,
            averageFormScore: workoutManager.currentFormScore,
            startTime: startTime,
            endTime: Date(),
            exerciseType: .squat
        )
        
        sessionSummary = summary
        showingSessionSummary = true
    }
    
    private func resetSession() {
        sessionStartTime = nil
        elapsedTime = 0
        lastRepCount = 0
        sessionSummary = nil
        repAnimationScale = 1.0
        showRepBurst = false
    }
}

// Simple AR Camera View
struct ARCameraView: UIViewRepresentable {
    func makeUIView(context: Context) -> ARSCNView {
        let arView = ARSCNView()
        arView.automaticallyUpdatesLighting = true
        arView.antialiasingMode = .multisampling4X
        
        // Connect to ARSessionManager for pose tracking
        arView.session = ARSessionManager.shared.session
        
        // Add a fallback background for simulator
        #if targetEnvironment(simulator)
        arView.backgroundColor = .black
        #endif
        
        return arView
    }
    
    func updateUIView(_ uiView: ARSCNView, context: Context) {
        // Updates handled by ARSessionManager
    }
}

// MARK: - Session Summary Data Structure

struct SessionSummary {
    let totalReps: Int
    let duration: TimeInterval
    let averageFormScore: Float
    let startTime: Date
    let endTime: Date
    let exerciseType: ExerciseType
    
    var formattedDuration: String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    var formGrade: String {
        switch averageFormScore {
        case 0.9...1.0: return "A+"
        case 0.8..<0.9: return "A"
        case 0.7..<0.8: return "B+"
        case 0.6..<0.7: return "B"
        case 0.5..<0.6: return "C"
        default: return "D"
        }
    }
    
    var formGradeColor: Color {
        switch averageFormScore {
        case 0.8...1.0: return .green
        case 0.6..<0.8: return .yellow
        case 0.4..<0.6: return .orange
        default: return .red
        }
    }
}

// MARK: - Session Summary View

struct SessionSummaryView: View {
    let summary: SessionSummary
    let onDismiss: () -> Void
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // Header
                VStack(spacing: 8) {
                    Text("🎉 Workout Complete!")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text(summary.exerciseType.displayName)
                        .font(.title2)
                        .foregroundColor(.secondary)
                }
                .padding(.top, 20)
                
                // Stats Cards
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                    StatCard(title: "Total Reps", value: "\(summary.totalReps)", color: .blue)
                    StatCard(title: "Duration", value: summary.formattedDuration, color: .green)
                    StatCard(title: "Form Grade", value: summary.formGrade, color: summary.formGradeColor)
                    StatCard(title: "Form Score", value: "\(Int(summary.averageFormScore * 100))%", color: summary.formGradeColor)
                }
                .padding(.horizontal)
                
                // Encouragement Message
                VStack(spacing: 8) {
                    Text(getEncouragementMessage())
                        .font(.headline)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)
                    
                    Text("Keep up the great work!")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
                .padding(.horizontal)
                
                Spacer()
                
                // Done Button
                Button(action: onDismiss) {
                    Text("Done")
                        .font(.headline)
                        .foregroundColor(.white)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(.blue)
                        .cornerRadius(12)
                }
                .padding(.horizontal)
                .padding(.bottom, 20)
            }
            .navigationBarHidden(true)
        }
    }
    
    private func getEncouragementMessage() -> String {
        let reps = summary.totalReps
        let formScore = summary.averageFormScore
        
        if reps >= 20 && formScore >= 0.8 {
            return "Outstanding performance! Perfect form and great endurance!"
        } else if reps >= 15 && formScore >= 0.7 {
            return "Excellent work! You're building great strength and technique!"
        } else if reps >= 10 && formScore >= 0.6 {
            return "Good job! Focus on form for even better results!"
        } else if reps >= 5 {
            return "Nice start! Keep practicing to improve your form!"
        } else {
            return "Great first step! Every journey begins with a single rep!"
        }
    }
}

// MARK: - Stat Card View

struct StatCard: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .textCase(.uppercase)
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(color.opacity(0.1))
        .cornerRadius(12)
    }
}

// MARK: - Rep Burst Animation View

struct RepBurstView: View {
    @State private var animate = false
    
    var body: some View {
        ZStack {
            // Burst particles
            ForEach(0..<8, id: \.self) { index in
                Circle()
                    .fill(Color.yellow)
                    .frame(width: 8, height: 8)
                    .offset(
                        x: animate ? cos(Double(index) * .pi / 4) * 60 : 0,
                        y: animate ? sin(Double(index) * .pi / 4) * 60 : 0
                    )
                    .opacity(animate ? 0 : 1)
                    .animation(.easeOut(duration: 0.8), value: animate)
            }
            
            // Center burst
            Circle()
                .fill(Color.green)
                .frame(width: 20, height: 20)
                .scaleEffect(animate ? 2.0 : 1.0)
                .opacity(animate ? 0 : 1)
                .animation(.easeOut(duration: 0.6), value: animate)
        }
        .onAppear {
            animate = true
        }
    }
}

#Preview {
    TestSquatView()
        .environmentObject(ARSessionManager.shared)
        .environmentObject(MLProcessingManager.shared)
}