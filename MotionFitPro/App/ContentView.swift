import SwiftUI

struct ContentView: View {
    @State private var isLoading = true
    
    var body: some View {
        Group {
            if isLoading {
                PremiumLaunchView()
                    .onAppear {
                        // Simulate a brief launch process
                        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                            withAnimation(.easeInOut(duration: 0.8)) {
                                isLoading = false
                            }
                        }
                    }
            } else {
                WorkoutProgramSelectionView()
            }
        }
        .animation(.easeInOut, value: isLoading)
    }
}

struct PremiumLaunchView: View {
    @State private var animateElements = false
    @State private var animateParticles = false
    
    var body: some View {
        ZStack {
            // Premium Dark Background
            LinearGradient(
                colors: [
                    Color(red: 0.1, green: 0.1, blue: 0.2),
                    Color(red: 0.05, green: 0.05, blue: 0.15),
                    Color.black
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            // Animated particles
            GeometryReader { geometry in
                ForEach(0..<30, id: \.self) { i in
                    Circle()
                        .fill(.white.opacity(0.05))
                        .frame(width: CGFloat.random(in: 10...30))
                        .position(
                            x: CGFloat.random(in: 0...geometry.size.width),
                            y: CGFloat.random(in: 0...geometry.size.height)
                        )
                        .scaleEffect(animateParticles ? 1.2 : 0.8)
                        .animation(
                            .easeInOut(duration: Double.random(in: 2...4))
                            .repeatForever(autoreverses: true),
                            value: animateParticles
                        )
                }
            }
            
            VStack(spacing: 30) {
                // App Icon with glow effect
                ZStack {
                    Circle()
                        .fill(.blue.opacity(0.3))
                        .frame(width: 140, height: 140)
                        .blur(radius: 20)
                        .scaleEffect(animateElements ? 1.2 : 0.8)
                    
                    Circle()
                        .fill(.blue.opacity(0.6))
                        .frame(width: 120, height: 120)
                        .blur(radius: 10)
                    
                    Image(systemName: "figure.strengthtraining.traditional")
                        .font(.system(size: 50, weight: .bold))
                        .foregroundColor(.white)
                        .scaleEffect(animateElements ? 1.0 : 0.8)
                }
                .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: animateElements)
                
                VStack(spacing: 16) {
                    Text("MotionFitPro")
                        .font(.system(size: 36, weight: .bold, design: .rounded))
                        .foregroundColor(.white)
                        .opacity(animateElements ? 1 : 0)
                        .offset(y: animateElements ? 0 : 20)
                    
                    Text("AI-Powered Motion Tracking")
                        .font(.title3)
                        .fontWeight(.medium)
                        .foregroundColor(.white.opacity(0.8))
                        .opacity(animateElements ? 1 : 0)
                        .offset(y: animateElements ? 0 : 20)
                    
                    Text("Premium Fitness Experience")
                        .font(.subheadline)
                        .foregroundColor(.blue.opacity(0.9))
                        .opacity(animateElements ? 1 : 0)
                        .offset(y: animateElements ? 0 : 20)
                }
                .animation(.easeOut(duration: 1.2).delay(0.5), value: animateElements)
                
                // Premium loading indicator
                VStack(spacing: 12) {
                    HStack(spacing: 8) {
                        ForEach(0..<3, id: \.self) { index in
                            Circle()
                                .fill(.blue)
                                .frame(width: 8, height: 8)
                                .scaleEffect(animateElements ? 1.2 : 0.8)
                                .animation(
                                    .easeInOut(duration: 0.6)
                                    .repeatForever(autoreverses: true)
                                    .delay(Double(index) * 0.2),
                                    value: animateElements
                                )
                        }
                    }
                    
                    Text("Initializing AI Systems...")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                        .opacity(animateElements ? 1 : 0)
                }
                .padding(.top, 40)
                .animation(.easeOut(duration: 1.0).delay(1.0), value: animateElements)
            }
        }
        .onAppear {
            animateElements = true
            animateParticles = true
        }
    }
}

struct LaunchView: View {
    var body: some View {
        ZStack {
            LinearGradient(
                gradient: Gradient(colors: [.blue, .purple]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: 20) {
                Image(systemName: "figure.run")
                    .font(.system(size: 80))
                    .foregroundColor(.white)
                
                Text("MotionFitPro")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Text("AI-Powered Motion Tracking")
                    .font(.headline)
                    .foregroundColor(.white.opacity(0.8))
                
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    .scaleEffect(1.2)
                    .padding(.top, 40)
            }
        }
    }
}

struct MainNavigationView: View {
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            HomeView()
                .tabItem {
                    Image(systemName: "house.fill")
                    Text("Home")
                }
                .tag(0)
            
            WorkoutSelectionView()
                .tabItem {
                    Image(systemName: "figure.run")
                    Text("Workouts")
                }
                .tag(1)
            
            ProgressViewTab()
                .tabItem {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                    Text("Progress")
                }
                .tag(2)
            
            ProfileView()
                .tabItem {
                    Image(systemName: "person.fill")
                    Text("Profile")
                }
                .tag(3)
        }
        .tint(.blue)
    }
}

struct HomeView: View {
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    Text("Welcome to MotionFitPro")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text("AI-Powered Motion Tracking Workouts")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Button("Start Quick Workout") {
                        // Placeholder action
                        print("Starting workout...")
                    }
                    .frame(height: 50)
                    .frame(maxWidth: .infinity)
                    .background(.blue)
                    .foregroundColor(.white)
                    .cornerRadius(12)
                    .padding(.horizontal)
                }
                .padding()
            }
            .navigationTitle("Home")
        }
    }
}

struct WorkoutSelectionView: View {
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Workout Selection")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Text("Select your workout type")
                    .foregroundColor(.secondary)
                
                Button("Start Squat Tracking") {
                    // For now, this just shows the TestSquatView which is already shown
                    print("Squat tracking selected")
                }
                .frame(height: 60)
                .frame(maxWidth: .infinity)
                .background(.blue)
                .foregroundColor(.white)
                .cornerRadius(12)
                .padding()
                
                Spacer()
            }
            .padding()
            .navigationTitle("Workouts")
        }
    }
}

// Renamed to avoid conflict with SwiftUI.ProgressView
struct ProgressViewTab: View {
    var body: some View {
        NavigationView {
            VStack {
                Text("Progress Tracking")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                Text("Coming Soon")
                    .foregroundColor(.secondary)
            }
            .navigationTitle("Progress")
        }
    }
}

struct ProfileView: View {
    var body: some View {
        NavigationView {
            VStack {
                Text("User Profile")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                Text("Coming Soon")
                    .foregroundColor(.secondary)
            }
            .navigationTitle("Profile")
        }
    }
}

private let itemFormatter: DateFormatter = {
    let formatter = DateFormatter()
    formatter.dateStyle = .short
    formatter.timeStyle = .medium
    return formatter
}()

#Preview {
    ContentView()
        .environmentObject(ARSessionManager.shared)
        .environmentObject(MLProcessingManager.shared)
        .environmentObject(AudioManager.shared)
}