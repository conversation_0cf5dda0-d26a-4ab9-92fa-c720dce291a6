import Foundation
import SwiftData
import Combine

@MainActor
class DataController: ObservableObject {
    static let shared = DataController()

    // MARK: - Published Properties
    @Published var isSyncing: Bool = false
    @Published var lastSyncDate: Date?
    @Published var syncError: Error?

    // MARK: - Properties
    let container: ModelContainer
    // private let cloudKitManager = CloudKitManager.shared // Disabled for now
    private let logger = Logger.shared
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization
    init() {
        let schema = Schema([
            UserProfile.self,
            WorkoutSession.self,
            ExercisePerformance.self,
            SetPerformance.self,
            RepPerformance.self
        ])

        // Disable CloudKit for now - use local-only storage
        let configuration = ModelConfiguration(
            schema: schema,
            isStoredInMemoryOnly: false,
            cloudKitDatabase: .none
        )

        do {
            container = try ModelContainer(for: schema, configurations: [configuration])
            // CloudKit sync disabled for now
            // setupCloudKitSync()
        } catch {
            logger.error("Failed to create SwiftData container: \(error)", category: .data)
            
            // Fallback to in-memory container to prevent app crash
            let fallbackConfig = ModelConfiguration(
                schema: schema,
                isStoredInMemoryOnly: true,
                cloudKitDatabase: .none
            )
            
            do {
                container = try ModelContainer(for: schema, configurations: [fallbackConfig])
                logger.warning("Using in-memory fallback container", category: .data)
                syncError = error
            } catch {
                // Last resort: create minimal container without CloudKit
                let minimalConfig = ModelConfiguration(isStoredInMemoryOnly: true)
                do {
                    container = try ModelContainer(for: schema, configurations: [minimalConfig])
                    logger.error("Using minimal in-memory container as last resort", category: .data)
                } catch {
                    // If all else fails, create an empty container
                    logger.error("Failed to create any container, app may not function properly: \(error)", category: .data)
                    container = try! ModelContainer(for: schema) // This is the ultimate fallback
                }
            }
        }
    }

    var context: ModelContext {
        return container.mainContext
    }

    // MARK: - Basic CRUD Operations

    func save() {
        do {
            try context.save()
            logger.debug("SwiftData context saved successfully", category: .data)
        } catch {
            logger.error("Failed to save SwiftData context: \(error)", category: .data)
            syncError = error
        }
    }

    func insert<T: PersistentModel>(_ model: T) {
        context.insert(model)
        save()
    }

    func delete<T: PersistentModel>(_ model: T) {
        context.delete(model)
        save()
    }

    func fetch<T: PersistentModel>(_ type: T.Type) throws -> [T] {
        let descriptor = FetchDescriptor<T>()
        return try context.fetch(descriptor)
    }

    func fetchFirst<T: PersistentModel>(_ type: T.Type) throws -> T? {
        var descriptor = FetchDescriptor<T>()
        descriptor.fetchLimit = 1
        return try context.fetch(descriptor).first
    }

    func count<T: PersistentModel>(_ type: T.Type) throws -> Int {
        let descriptor = FetchDescriptor<T>()
        return try context.fetchCount(descriptor)
    }

    // MARK: - Workout Session Operations

    func saveWorkoutSession(_ session: WorkoutSession) async {
        insert(session)

        // CloudKit sync disabled for now
        // if cloudKitManager.accountStatus == .available {
        //     do {
        //         try await cloudKitManager.saveWorkoutSession(session)
        //         logger.info("Workout session synced to CloudKit", category: .data)
        //     } catch {
        //         logger.error("Failed to sync workout session to CloudKit: \(error)", category: .data)
        //         syncError = error
        //     }
        // }
    }

    func fetchWorkoutSessions() async throws -> [WorkoutSession] {
        // First try to get from local storage
        let localSessions = try fetch(WorkoutSession.self)

        // CloudKit sync disabled for now
        // if cloudKitManager.accountStatus == .available {
        //     do {
        //         let cloudSessions = try await cloudKitManager.fetchWorkoutSessions()
        //         logger.info("Fetched \(cloudSessions.count) sessions from CloudKit", category: .data)
        //         return mergeWorkoutSessions(local: localSessions, cloud: cloudSessions)
        //     } catch {
        //         logger.warning("Failed to fetch from CloudKit, using local data: \(error)", category: .data)
        //         return localSessions
        //     }
        // }

        return localSessions
    }

    // MARK: - User Profile Operations

    func saveUserProfile(_ profile: UserProfile) async {
        insert(profile)

        // CloudKit sync disabled for now
        // if cloudKitManager.accountStatus == .available {
        //     do {
        //         try await cloudKitManager.saveUserProfile(profile)
        //         logger.info("User profile synced to CloudKit", category: .data)
        //     } catch {
        //         logger.error("Failed to sync user profile to CloudKit: \(error)", category: .data)
        //         syncError = error
        //     }
        // }
    }

    func fetchUserProfile() async throws -> UserProfile? {
        // First try local storage
        let localProfile = try fetchFirst(UserProfile.self)

        // CloudKit sync disabled for now
        // if cloudKitManager.accountStatus == .available {
        //     do {
        //         if let cloudProfile = try await cloudKitManager.fetchUserProfile() {
        //             if let local = localProfile {
        //                 return cloudProfile.lastModified > local.lastModified ? cloudProfile : local
        //             } else {
        //                 return cloudProfile
        //             }
        //         }
        //     } catch {
        //         logger.warning("Failed to fetch user profile from CloudKit: \(error)", category: .data)
        //     }
        // }

        return localProfile
    }

    // MARK: - CloudKit Sync

    private func setupCloudKitSync() {
        // CloudKit sync disabled for now
        // cloudKitManager.$isSyncing
        //     .receive(on: DispatchQueue.main)
        //     .assign(to: &$isSyncing)
        // 
        // cloudKitManager.$lastSyncDate
        //     .receive(on: DispatchQueue.main)
        //     .assign(to: &$lastSyncDate)
        // 
        // cloudKitManager.$syncError
        //     .receive(on: DispatchQueue.main)
        //     .assign(to: &$syncError)
    }

    func syncWithCloudKit() async {
        // CloudKit sync disabled for now
        logger.info("CloudKit sync is disabled - using local storage only", category: .data)
        // guard cloudKitManager.accountStatus == .available else {
        //     logger.warning("CloudKit not available for sync", category: .data)
        //     return
        // }
        // 
        // isSyncing = true
        // defer { isSyncing = false }
        // 
        // do {
        //     try await cloudKitManager.syncAllData()
        //     lastSyncDate = Date()
        //     logger.info("CloudKit sync completed successfully", category: .data)
        // } catch {
        //     logger.error("CloudKit sync failed: \(error)", category: .data)
        //     syncError = error
        // }
    }

    // MARK: - Data Merging

    private func mergeWorkoutSessions(local: [WorkoutSession], cloud: [WorkoutSession]) -> [WorkoutSession] {
        var merged: [UUID: WorkoutSession] = [:]

        // Add local sessions
        for session in local {
            merged[session.id] = session
        }

        // Add or update with cloud sessions
        for session in cloud {
            if let existing = merged[session.id] {
                // Use the most recent version
                if let cloudEndTime = session.endTime, let localEndTime = existing.endTime {
                    merged[session.id] = cloudEndTime > localEndTime ? session : existing
                } else {
                    merged[session.id] = session.endTime != nil ? session : existing
                }
            } else {
                merged[session.id] = session
            }
        }

        return Array(merged.values).sorted { $0.startTime > $1.startTime }
    }
}

// Note: Logger.Category.data is already defined in Core/Utilities/Logger.swift