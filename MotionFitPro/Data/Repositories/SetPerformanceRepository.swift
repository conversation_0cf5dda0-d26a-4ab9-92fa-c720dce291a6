import Foundation
import SwiftData

@MainActor
class SetPerformanceRepository: ObservableObject {
    private let dataController: DataController
    
    init(dataController: DataController = DataController.shared) {
        self.dataController = dataController
    }
    
    func createSetPerformance(_ setPerformance: SetPerformance) throws {
        dataController.context.insert(setPerformance)
        dataController.save()
    }
    
    func fetchSetPerformance(id: UUID) throws -> SetPerformance? {
        let descriptor = FetchDescriptor<SetPerformance>(
            predicate: #Predicate { $0.id == id }
        )
        return try dataController.context.fetch(descriptor).first
    }
    
    func fetchSetPerformancesForExercise(exercisePerformanceID: UUID) throws -> [SetPerformance] {
        let descriptor = FetchDescriptor<SetPerformance>(
            predicate: #Predicate { $0.exercisePerformanceID == exercisePerformanceID },
            sortBy: [SortDescriptor(\.setNumber)]
        )
        return try dataController.context.fetch(descriptor)
    }
    
    func updateSetPerformance(_ setPerformance: SetPerformance) {
        setPerformance.lastModified = Date()
        dataController.save()
    }
    
    func deleteSetPerformance(_ setPerformance: SetPerformance) {
        dataController.context.delete(setPerformance)
        dataController.save()
    }
}