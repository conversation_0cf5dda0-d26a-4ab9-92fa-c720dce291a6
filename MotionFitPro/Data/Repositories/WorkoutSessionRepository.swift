import Foundation
import SwiftData

@MainActor
class WorkoutSessionRepository: ObservableObject {
    private let dataController: DataController
    
    init(dataController: DataController = DataController.shared) {
        self.dataController = dataController
    }
    
    func createWorkoutSession(_ workoutSession: WorkoutSession) throws {
        dataController.context.insert(workoutSession)
        dataController.save()
    }
    
    func fetchWorkoutSession(id: UUID) throws -> WorkoutSession? {
        let descriptor = FetchDescriptor<WorkoutSession>(
            predicate: #Predicate { $0.id == id }
        )
        return try dataController.context.fetch(descriptor).first
    }
    
    func fetchWorkoutSessionsForUser(userID: UUID) throws -> [WorkoutSession] {
        let descriptor = FetchDescriptor<WorkoutSession>(
            predicate: #Predicate { $0.userID == userID },
            sortBy: [SortDescriptor(\.startTime, order: .reverse)]
        )
        return try dataController.context.fetch(descriptor)
    }
    
    func fetchRecentWorkoutSessions(limit: Int = 10) throws -> [WorkoutSession] {
        var descriptor = FetchDescriptor<WorkoutSession>(
            sortBy: [SortDescriptor(\.startTime, order: .reverse)]
        )
        descriptor.fetchLimit = limit
        return try dataController.context.fetch(descriptor)
    }
    
    func updateWorkoutSession(_ workoutSession: WorkoutSession) {
        workoutSession.lastModified = Date()
        dataController.save()
    }
    
    func deleteWorkoutSession(_ workoutSession: WorkoutSession) {
        dataController.context.delete(workoutSession)
        dataController.save()
    }
}