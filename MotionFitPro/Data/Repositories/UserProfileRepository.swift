import Foundation
import SwiftData

@MainActor
class UserProfileRepository: ObservableObject {
    private let dataController: DataController
    
    init(dataController: DataController = DataController.shared) {
        self.dataController = dataController
    }
    
    func createUserProfile(_ userProfile: UserProfile) throws {
        dataController.context.insert(userProfile)
        dataController.save()
    }
    
    func fetchUserProfile(id: UUID) throws -> UserProfile? {
        let descriptor = FetchDescriptor<UserProfile>(
            predicate: #Predicate { $0.id == id }
        )
        return try dataController.context.fetch(descriptor).first
    }
    
    func fetchAllUserProfiles() throws -> [UserProfile] {
        let descriptor = FetchDescriptor<UserProfile>(
            sortBy: [SortDescriptor(\.name)]
        )
        return try dataController.context.fetch(descriptor)
    }
    
    func updateUserProfile(_ userProfile: UserProfile) {
        userProfile.lastModified = Date()
        dataController.save()
    }
    
    func deleteUserProfile(_ userProfile: UserProfile) {
        dataController.context.delete(userProfile)
        dataController.save()
    }
}