import Foundation
import SwiftData
import Combine

// MARK: - Repository Implementations

/// Concrete implementation of user profile repository
@MainActor
final class UserProfileRepository: UserProfileRepositoryProtocol, ObservableObject {
    private let modelContext: ModelContext
    private let logger = Logger()
    
    init(modelContext: ModelContext? = nil) {
        // Use provided context or create default one
        if let context = modelContext {
            self.modelContext = context
        } else {
            // Create default context - this should be injected in production
            let container = try! ModelContainer(for: UserProfile.self)
            self.modelContext = ModelContext(container)
        }
    }
    
    func getCurrentProfile() async throws -> UserProfile? {
        let descriptor = FetchDescriptor<UserProfile>(
            sortBy: [SortDescriptor(\.lastModified, order: .reverse)]
        )
        
        do {
            let profiles = try modelContext.fetch(descriptor)
            return profiles.first
        } catch {
            logger.error("Failed to fetch current profile: \(error)", category: .data)
            throw RepositoryError.fetchFailed(error)
        }
    }
    
    func saveProfile(_ profile: UserProfile) async throws {
        do {
            modelContext.insert(profile)
            try modelContext.save()
            logger.info("Profile saved successfully", category: .data)
        } catch {
            logger.error("Failed to save profile: \(error)", category: .data)
            throw RepositoryError.saveFailed(error)
        }
    }
    
    func updateProfile(_ profile: UserProfile) async throws {
        do {
            profile.updateLastModified()
            try modelContext.save()
            logger.info("Profile updated successfully", category: .data)
        } catch {
            logger.error("Failed to update profile: \(error)", category: .data)
            throw RepositoryError.updateFailed(error)
        }
    }
    
    func deleteProfile(_ profileId: UUID) async throws {
        let descriptor = FetchDescriptor<UserProfile>(
            predicate: #Predicate { $0.id == profileId }
        )
        
        do {
            let profiles = try modelContext.fetch(descriptor)
            guard let profile = profiles.first else {
                throw RepositoryError.notFound
            }
            
            modelContext.delete(profile)
            try modelContext.save()
            logger.info("Profile deleted successfully", category: .data)
        } catch {
            logger.error("Failed to delete profile: \(error)", category: .data)
            throw RepositoryError.deleteFailed(error)
        }
    }
    
    func getAllProfiles() async throws -> [UserProfile] {
        let descriptor = FetchDescriptor<UserProfile>(
            sortBy: [SortDescriptor(\.lastModified, order: .reverse)]
        )
        
        do {
            return try modelContext.fetch(descriptor)
        } catch {
            logger.error("Failed to fetch all profiles: \(error)", category: .data)
            throw RepositoryError.fetchFailed(error)
        }
    }
}

/// Concrete implementation of workout repository
@MainActor
final class WorkoutRepository: WorkoutRepositoryProtocol, ObservableObject {
    private let modelContext: ModelContext
    private let logger = Logger()
    
    init(modelContext: ModelContext? = nil) {
        if let context = modelContext {
            self.modelContext = context
        } else {
            let container = try! ModelContainer(for: WorkoutSession.self)
            self.modelContext = ModelContext(container)
        }
    }
    
    func getWorkouts(for userId: UUID) async throws -> [WorkoutSession] {
        let descriptor = FetchDescriptor<WorkoutSession>(
            predicate: #Predicate { $0.userId == userId },
            sortBy: [SortDescriptor(\.startTime, order: .reverse)]
        )
        
        do {
            return try modelContext.fetch(descriptor)
        } catch {
            logger.error("Failed to fetch workouts for user \(userId): \(error)", category: .data)
            throw RepositoryError.fetchFailed(error)
        }
    }
    
    func saveWorkout(_ workout: WorkoutSession) async throws {
        do {
            modelContext.insert(workout)
            try modelContext.save()
            logger.info("Workout saved successfully", category: .data)
        } catch {
            logger.error("Failed to save workout: \(error)", category: .data)
            throw RepositoryError.saveFailed(error)
        }
    }
    
    func updateWorkout(_ workout: WorkoutSession) async throws {
        do {
            try modelContext.save()
            logger.info("Workout updated successfully", category: .data)
        } catch {
            logger.error("Failed to update workout: \(error)", category: .data)
            throw RepositoryError.updateFailed(error)
        }
    }
    
    func deleteWorkout(_ workoutId: UUID) async throws {
        let descriptor = FetchDescriptor<WorkoutSession>(
            predicate: #Predicate { $0.id == workoutId }
        )
        
        do {
            let workouts = try modelContext.fetch(descriptor)
            guard let workout = workouts.first else {
                throw RepositoryError.notFound
            }
            
            modelContext.delete(workout)
            try modelContext.save()
            logger.info("Workout deleted successfully", category: .data)
        } catch {
            logger.error("Failed to delete workout: \(error)", category: .data)
            throw RepositoryError.deleteFailed(error)
        }
    }
    
    func getWorkoutHistory(for userId: UUID, limit: Int) async throws -> [WorkoutSession] {
        let descriptor = FetchDescriptor<WorkoutSession>(
            predicate: #Predicate { $0.userId == userId },
            sortBy: [SortDescriptor(\.startTime, order: .reverse)]
        )
        descriptor.fetchLimit = limit
        
        do {
            return try modelContext.fetch(descriptor)
        } catch {
            logger.error("Failed to fetch workout history: \(error)", category: .data)
            throw RepositoryError.fetchFailed(error)
        }
    }
}

/// Concrete implementation of exercise repository
@MainActor
final class ExerciseRepository: ExerciseRepositoryProtocol, ObservableObject {
    private let logger = Logger()
    
    func getAllExercises() async throws -> [ExerciseType] {
        // Return all available exercise types
        return ExerciseType.allCases.filter { $0 != .unknown }
    }
    
    func getExercise(by type: ExerciseType) async throws -> ExerciseType? {
        guard type != .unknown else { return nil }
        return type
    }
    
    func getExercisesForMuscleGroup(_ muscleGroup: MuscleGroup) async throws -> [ExerciseType] {
        return ExerciseType.allCases.filter { exerciseType in
            exerciseType.targetMuscles.contains { muscle in
                muscle.lowercased().contains(muscleGroup.displayName.lowercased())
            }
        }
    }
    
    func getExercisesForDifficulty(_ difficulty: DifficultyLevel) async throws -> [ExerciseType] {
        return ExerciseType.allCases.filter { exerciseType in
            exerciseType.difficultyLevel == difficulty.numericValue
        }
    }
}

// MARK: - Repository Errors

enum RepositoryError: LocalizedError {
    case fetchFailed(Error)
    case saveFailed(Error)
    case updateFailed(Error)
    case deleteFailed(Error)
    case notFound
    case invalidData
    case networkError(Error)
    case unauthorized
    
    var errorDescription: String? {
        switch self {
        case .fetchFailed(let error):
            return "Failed to fetch data: \(error.localizedDescription)"
        case .saveFailed(let error):
            return "Failed to save data: \(error.localizedDescription)"
        case .updateFailed(let error):
            return "Failed to update data: \(error.localizedDescription)"
        case .deleteFailed(let error):
            return "Failed to delete data: \(error.localizedDescription)"
        case .notFound:
            return "Requested data not found"
        case .invalidData:
            return "Invalid data provided"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        case .unauthorized:
            return "Unauthorized access"
        }
    }
}

// MARK: - Repository Factory

final class RepositoryFactory {
    static let shared = RepositoryFactory()
    
    private init() {}
    
    func createUserProfileRepository(with context: ModelContext? = nil) -> UserProfileRepositoryProtocol {
        return UserProfileRepository(modelContext: context)
    }
    
    func createWorkoutRepository(with context: ModelContext? = nil) -> WorkoutRepositoryProtocol {
        return WorkoutRepository(modelContext: context)
    }
    
    func createExerciseRepository() -> ExerciseRepositoryProtocol {
        return ExerciseRepository()
    }
}
