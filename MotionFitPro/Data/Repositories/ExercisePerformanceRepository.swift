import Foundation
import SwiftData

@MainActor
class ExercisePerformanceRepository: ObservableObject {
    private let dataController: DataController
    
    init(dataController: DataController = DataController.shared) {
        self.dataController = dataController
    }
    
    func createExercisePerformance(_ exercisePerformance: ExercisePerformance) throws {
        dataController.context.insert(exercisePerformance)
        dataController.save()
    }
    
    func fetchExercisePerformance(id: UUID) throws -> ExercisePerformance? {
        let descriptor = FetchDescriptor<ExercisePerformance>(
            predicate: #Predicate { $0.id == id }
        )
        return try dataController.context.fetch(descriptor).first
    }
    
    func fetchExercisePerformancesForWorkout(workoutSessionID: UUID) throws -> [ExercisePerformance] {
        let descriptor = FetchDescriptor<ExercisePerformance>(
            predicate: #Predicate { $0.workoutSessionID == workoutSessionID },
            sortBy: [SortDescriptor(\.startTime)]
        )
        return try dataController.context.fetch(descriptor)
    }
    
    func updateExercisePerformance(_ exercisePerformance: ExercisePerformance) {
        exercisePerformance.lastModified = Date()
        dataController.save()
    }
    
    func deleteExercisePerformance(_ exercisePerformance: ExercisePerformance) {
        dataController.context.delete(exercisePerformance)
        dataController.save()
    }
}