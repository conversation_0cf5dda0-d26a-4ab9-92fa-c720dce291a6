import Foundation
import SwiftData

@MainActor
class RepPerformanceRepository: ObservableObject {
    private let dataController: DataController
    
    init(dataController: DataController = DataController.shared) {
        self.dataController = dataController
    }
    
    func createRepPerformance(_ repPerformance: RepPerformance) throws {
        dataController.context.insert(repPerformance)
        dataController.save()
    }
    
    func fetchRepPerformance(id: UUID) throws -> RepPerformance? {
        let descriptor = FetchDescriptor<RepPerformance>(
            predicate: #Predicate { $0.id == id }
        )
        return try dataController.context.fetch(descriptor).first
    }
    
    func fetchRepPerformancesForSet(setPerformanceID: UUID) throws -> [RepPerformance] {
        let descriptor = FetchDescriptor<RepPerformance>(
            predicate: #Predicate { $0.setPerformanceID == setPerformanceID },
            sortBy: [SortDescriptor(\.repNumber)]
        )
        return try dataController.context.fetch(descriptor)
    }
    
    func updateRepPerformance(_ repPerformance: RepPerformance) {
        dataController.save()
    }
    
    func deleteRepPerformance(_ repPerformance: RepPerformance) {
        dataController.context.delete(repPerformance)
        dataController.save()
    }
}