import Foundation

// Represents the category of an exercise.
enum ExerciseCategory: String, Codable, CaseIterable {
    case strength = "Strength"
    case cardio = "Cardio"
    case flexibility = "Flexibility"
    case balance = "Balance"
}

// Represents the difficulty level of an exercise.
enum ExerciseDifficulty: String, Codable, CaseIterable {
    case beginner = "Beginner"
    case intermediate = "Intermediate"
    case advanced = "Advanced"
}

// Represents the equipment used for an exercise.
enum Equipment: String, Codable, CaseIterable {
    case none = "None"
    case dumbbells = "Dumbbells"
    case barbell = "Barbell"
    case kettlebell = "Kettlebell"
    case resistanceBand = "Resistance Band"
}
