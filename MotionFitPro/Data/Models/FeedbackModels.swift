import Foundation

// MARK: - Unified Feedback System

/// Unified feedback type for all feedback scenarios
enum FeedbackType: String, CaseIterable, Codable, Sendable {
    case encouragement = "encouragement"
    case correction = "correction"
    case warning = "warning"
    case achievement = "achievement"
    case instruction = "instruction"
    case motivation = "motivation"
    case celebration = "celebration"
    case safety = "safety"
    case rest = "rest"
    case information = "information"
    
    var displayName: String {
        switch self {
        case .encouragement: return "Encouragement"
        case .correction: return "Form Correction"
        case .warning: return "Warning"
        case .achievement: return "Achievement"
        case .instruction: return "Instruction"
        case .motivation: return "Motivation"
        case .celebration: return "Celebration"
        case .safety: return "Safety Alert"
        case .rest: return "Rest Guidance"
        case .information: return "Information"
        }
    }
    
    var priority: FeedbackPriority {
        switch self {
        case .safety: return .immediate
        case .warning, .correction: return .high
        case .encouragement, .motivation, .achievement: return .normal
        case .rest, .information, .instruction, .celebration: return .low
        }
    }

    var icon: String {
        switch self {
        case .encouragement: return "hand.thumbsup.fill"
        case .correction: return "exclamationmark.triangle.fill"
        case .warning: return "exclamationmark.octagon.fill"
        case .achievement: return "star.fill"
        case .instruction: return "info.circle.fill"
        case .motivation: return "flame.fill"
        case .celebration: return "party.popper.fill"
        case .safety: return "shield.fill"
        case .rest: return "pause.circle.fill"
        case .information: return "info.circle"
        }
    }

    var color: String {
        switch self {
        case .encouragement: return "green"
        case .correction: return "orange"
        case .warning: return "red"
        case .achievement: return "yellow"
        case .instruction: return "blue"
        case .motivation: return "purple"
        case .celebration: return "pink"
        case .safety: return "red"
        case .rest: return "gray"
        case .information: return "blue"
        }
    }
}

/// Feedback severity levels
enum FeedbackSeverity: String, CaseIterable, Codable, Sendable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case critical = "critical"
    
    var displayName: String {
        switch self {
        case .low: return "Minor"
        case .medium: return "Moderate"
        case .high: return "Important"
        case .critical: return "Critical"
        }
    }
    
    var priority: Int {
        switch self {
        case .low: return 1
        case .medium: return 2
        case .high: return 3
        case .critical: return 4
        }
    }
}

/// Feedback priority levels
enum FeedbackPriority: String, Codable, Sendable {
    case immediate // Safety warnings, stop commands
    case high // Form corrections, important guidance
    case normal // Encouragement, tips
    case low // Background information
    
    var displayName: String {
        switch self {
        case .immediate: return "Immediate"
        case .high: return "High"
        case .normal: return "Normal"
        case .low: return "Low"
        }
    }
}

/// Delivery methods for feedback
enum DeliveryMethod: String, CaseIterable, Codable, Sendable {
    case speech = "speech"
    case haptic = "haptic"
    case visual = "visual"
    case audio = "audio"
    
    var displayName: String {
        switch self {
        case .speech: return "Speech"
        case .haptic: return "Haptic"
        case .visual: return "Visual"
        case .audio: return "Audio"
        }
    }
}

// MARK: - Unified Form Feedback Model

/// Comprehensive form feedback model
struct FormFeedback: Identifiable, Codable, Sendable {
    let id: UUID
    let type: FeedbackType
    let message: String
    let title: String?
    let timestamp: Date
    let exerciseType: ExerciseType?
    let severity: FeedbackSeverity
    let priority: FeedbackPriority
    let jointInvolved: JointName?
    let correctionSuggestion: String?
    let videoTimestamp: TimeInterval?
    let duration: TimeInterval
    let autoDismiss: Bool
    let deliveryMethods: Set<DeliveryMethod>
    
    init(
        id: UUID = UUID(),
        type: FeedbackType,
        message: String,
        title: String? = nil,
        timestamp: Date = Date(),
        exerciseType: ExerciseType? = nil,
        severity: FeedbackSeverity = .medium,
        priority: FeedbackPriority? = nil,
        jointInvolved: JointName? = nil,
        correctionSuggestion: String? = nil,
        videoTimestamp: TimeInterval? = nil,
        duration: TimeInterval = 3.0,
        autoDismiss: Bool = true,
        deliveryMethods: Set<DeliveryMethod> = [.visual]
    ) {
        self.id = id
        self.type = type
        self.message = message
        self.title = title
        self.timestamp = timestamp
        self.exerciseType = exerciseType
        self.severity = severity
        self.priority = priority ?? type.priority
        self.jointInvolved = jointInvolved
        self.correctionSuggestion = correctionSuggestion
        self.videoTimestamp = videoTimestamp
        self.duration = duration
        self.autoDismiss = autoDismiss
        self.deliveryMethods = deliveryMethods
    }
}

// MARK: - Coaching Feedback Model

/// Coaching-specific feedback model
struct CoachingFeedback: Identifiable, Codable, Sendable {
    let id: UUID
    let message: String
    let type: FeedbackType
    let priority: FeedbackPriority
    let personality: CoachingPersonality
    let deliveryMethod: Set<DeliveryMethod>
    let timestamp: Date
    
    init(
        id: UUID = UUID(),
        message: String,
        type: FeedbackType,
        priority: FeedbackPriority,
        personality: CoachingPersonality,
        deliveryMethod: Set<DeliveryMethod>,
        timestamp: Date = Date()
    ) {
        self.id = id
        self.message = message
        self.type = type
        self.priority = priority
        self.personality = personality
        self.deliveryMethod = deliveryMethod
        self.timestamp = timestamp
    }
}

// MARK: - Workout Feedback Model

/// Workout-specific feedback model
struct WorkoutFeedback: Identifiable, Codable, Sendable {
    let id: UUID
    let type: WorkoutFeedbackType
    let message: String
    let priority: SpeechPriority
    let shouldInterrupt: Bool
    let timestamp: Date

    enum WorkoutFeedbackType: String, CaseIterable, Codable, Sendable {
        case repCompleted = "repCompleted"
        case setCompleted = "setCompleted"
        case formCorrection = "formCorrection"
        case encouragement = "encouragement"
        case countdown = "countdown"
        case workoutComplete = "workoutComplete"
        
        var displayName: String {
            switch self {
            case .repCompleted: return "Rep Completed"
            case .setCompleted: return "Set Completed"
            case .formCorrection: return "Form Correction"
            case .encouragement: return "Encouragement"
            case .countdown: return "Countdown"
            case .workoutComplete: return "Workout Complete"
            }
        }
    }
    
    init(
        id: UUID = UUID(),
        type: WorkoutFeedbackType,
        message: String,
        priority: SpeechPriority = .normal,
        shouldInterrupt: Bool = false,
        timestamp: Date = Date()
    ) {
        self.id = id
        self.type = type
        self.message = message
        self.priority = priority
        self.shouldInterrupt = shouldInterrupt
        self.timestamp = timestamp
    }
}

// MARK: - Speech Priority

enum SpeechPriority: Int, CaseIterable, Codable, Sendable {
    case low = 1
    case normal = 2
    case high = 3
    case urgent = 4
    
    var displayName: String {
        switch self {
        case .low: return "Low"
        case .normal: return "Normal"
        case .high: return "High"
        case .urgent: return "Urgent"
        }
    }
}

// MARK: - Convenience Extensions

extension FormFeedback {
    static func success(_ title: String, message: String) -> FormFeedback {
        FormFeedback(
            type: .achievement,
            message: message,
            title: title,
            severity: .low,
            duration: 2.0,
            deliveryMethods: [.visual, .haptic]
        )
    }
    
    static func warning(_ title: String, message: String) -> FormFeedback {
        FormFeedback(
            type: .warning,
            message: message,
            title: title,
            severity: .medium,
            duration: 4.0,
            deliveryMethods: [.visual, .haptic, .speech]
        )
    }
    
    static func error(_ title: String, message: String) -> FormFeedback {
        FormFeedback(
            type: .safety,
            message: message,
            title: title,
            severity: .critical,
            duration: 5.0,
            autoDismiss: false,
            deliveryMethods: [.visual, .haptic, .speech, .audio]
        )
    }
    
    static func coaching(_ title: String, message: String) -> FormFeedback {
        FormFeedback(
            type: .correction,
            message: message,
            title: title,
            severity: .medium,
            duration: 6.0,
            deliveryMethods: [.visual, .speech]
        )
    }
}
