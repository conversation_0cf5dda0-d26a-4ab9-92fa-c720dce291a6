import Foundation
import SwiftData

@Model
class SetPerformance {
    @Attribute(.unique) var id: UUID
    var exercisePerformanceID: UUID
    var setNumber: Int
    var startTime: Date
    var endTime: Date?
    var duration: TimeInterval
    var targetReps: Int
    var completedReps: Int
    var averageFormScore: Double
    var restDuration: TimeInterval
    var status: SetStatus
    var createdDate: Date
    var lastModified: Date
    
    // Relationships
    @Relationship(deleteRule: .cascade, inverse: \RepPerformance.setPerformance)
    var repPerformances: [RepPerformance] = []

    var exercisePerformance: ExercisePerformance?

    init(exercisePerformanceID: UUID, setNumber: Int, targetReps: Int) {
        self.id = UUID()
        self.exercisePerformanceID = exercisePerformanceID
        self.setNumber = setNumber
        self.startTime = Date()
        self.duration = 0
        self.targetReps = targetReps
        self.completedReps = 0
        self.averageFormScore = 0.0
        self.restDuration = 0
        self.status = .inProgress
        self.createdDate = Date()
        self.lastModified = Date()
    }
}

enum SetStatus: String, CaseIterable, Codable {
    case inProgress = "inProgress"
    case completed = "completed"
    case failed = "failed"
}