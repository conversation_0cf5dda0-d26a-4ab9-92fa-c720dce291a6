import Foundation

/// An enumeration of all exercises that the app can recognize and track.
enum ExerciseType: String, CaseIterable, Codable, Sendable {
    case squat = "squat"
    case pushUp = "pushUp"
    case lunge = "lunge"
    case jumpingJack = "jumpingJack"
    case plank = "plank"
    case burpee = "burpee"
    case mountainClimber = "mountainClimber"
    case unknown = "unknown"

    /// A user-friendly display name for the exercise.
    var displayName: String {
        switch self {
        case .squat: return "Squat"
        case .pushUp: return "Push-Up"
        case .lunge: return "Lunge"
        case .jumpingJack: return "Jumping Jack"
        case .plank: return "Plank"
        case .burpee: return "Burpee"
        case .mountainClimber: return "Mountain Climber"
        case .unknown: return "Unknown Exercise"
        }
    }

    /// A brief description of how to perform the exercise.
    var description: String {
        switch self {
        case .squat:
            return "Stand with feet shoulder-width apart, lower your body by bending your knees and hips."
        case .pushUp:
            return "Start in a plank position, lower your body until your chest nearly touches the floor."
        case .lunge:
            return "Step forward with one leg, lowering your hips until both knees are bent at about 90 degrees."
        case .jumpingJack:
            return "Jump to a position with legs spread wide and hands overhead, then return to starting position."
        case .plank:
            return "Hold a push-up position with your body in a straight line from head to heels."
        case .burpee:
            return "Drop into squat, jump back to plank, do push-up, jump feet back, then jump up with arms overhead."
        case .mountainClimber:
            return "Start in plank position, alternate bringing knees to chest in a running motion."
        case .unknown:
            return "Exercise type not recognized."
        }
    }

    /// The primary muscle groups targeted by this exercise.
    var targetMuscles: [String] {
        switch self {
        case .squat:
            return ["Quadriceps", "Glutes", "Hamstrings", "Calves"]
        case .pushUp:
            return ["Chest", "Shoulders", "Triceps", "Core"]
        case .lunge:
            return ["Quadriceps", "Glutes", "Hamstrings", "Calves"]
        case .jumpingJack:
            return ["Cardiovascular", "Legs", "Arms"]
        case .plank:
            return ["Core", "Shoulders", "Back"]
        case .burpee:
            return ["Full Body", "Cardiovascular", "Core"]
        case .mountainClimber:
            return ["Core", "Shoulders", "Cardiovascular"]
        case .unknown:
            return []
        }
    }

    /// Estimated calories burned per minute for an average person.
    var caloriesPerMinute: Double {
        switch self {
        case .squat: return 8.0
        case .pushUp: return 7.0
        case .lunge: return 6.0
        case .jumpingJack: return 10.0
        case .plank: return 5.0
        case .burpee: return 15.0
        case .mountainClimber: return 12.0
        case .unknown: return 0.0
        }
    }

    /// Difficulty level of the exercise (1-5 scale).
    var difficultyLevel: Int {
        switch self {
        case .squat: return 2
        case .pushUp: return 3
        case .lunge: return 3
        case .jumpingJack: return 1
        case .plank: return 2
        case .burpee: return 5
        case .mountainClimber: return 4
        case .unknown: return 0
        }
    }
}

