import Foundation
import SwiftData

@Model
class Achievement {
    @Attribute(.unique) var id: UUID
    var userID: UUID
    var name: String
    var title: String
    var description: String
    var category: AchievementCategory
    var type: AchievementType
    var iconName: String
    var badgeColor: String
    var isUnlocked: Bool
    var unlockedDate: Date?
    var progress: Double // 0.0 to 1.0
    var targetValue: Double
    var currentValue: Double
    var rarity: AchievementRarity
    var points: Int
    var createdDate: Date
    var lastModified: Date
    
    // Relationships
    var workoutSession: WorkoutSession?
    
    init(
        userID: UUID,
        name: String,
        title: String? = nil,
        description: String,
        category: AchievementCategory,
        type: AchievementType = .milestone,
        iconName: String = "star.fill",
        badgeColor: String = "blue",
        targetValue: Double = 1.0,
        rarity: AchievementRarity = .common,
        points: Int = 10
    ) {
        self.id = UUID()
        self.userID = userID
        self.name = name
        self.title = title ?? name
        self.description = description
        self.category = category
        self.type = type
        self.iconName = iconName
        self.badgeColor = badgeColor
        self.isUnlocked = false
        self.unlockedDate = nil
        self.progress = 0.0
        self.targetValue = targetValue
        self.currentValue = 0.0
        self.rarity = rarity
        self.points = points
        self.createdDate = Date()
        self.lastModified = Date()
    }
    
    // MARK: - Progress Management
    
    func updateProgress(_ newValue: Double) {
        currentValue = min(newValue, targetValue)
        progress = targetValue > 0 ? currentValue / targetValue : 0.0
        
        if progress >= 1.0 && !isUnlocked {
            unlock()
        }
        
        lastModified = Date()
    }
    
    func unlock() {
        guard !isUnlocked else { return }
        
        isUnlocked = true
        unlockedDate = Date()
        progress = 1.0
        currentValue = targetValue
        lastModified = Date()
    }
    
    func reset() {
        isUnlocked = false
        unlockedDate = nil
        progress = 0.0
        currentValue = 0.0
        lastModified = Date()
    }
    
    // MARK: - Computed Properties
    
    var progressPercentage: Int {
        return Int(progress * 100)
    }
    
    var remainingValue: Double {
        return max(0, targetValue - currentValue)
    }
    
    var isCompleted: Bool {
        return isUnlocked && progress >= 1.0
    }
    
    var displayColor: String {
        return isUnlocked ? badgeColor : "gray"
    }
}

// MARK: - Achievement Category

enum AchievementCategory: String, CaseIterable, Codable, Sendable {
    case consistency = "consistency"
    case strength = "strength"
    case endurance = "endurance"
    case form = "form"
    case milestone = "milestone"
    case social = "social"
    case special = "special"
    case technique = "technique"
    case dedication = "dedication"
    case improvement = "improvement"
    
    var displayName: String {
        switch self {
        case .consistency: return "Consistency"
        case .strength: return "Strength"
        case .endurance: return "Endurance"
        case .form: return "Perfect Form"
        case .milestone: return "Milestone"
        case .social: return "Social"
        case .special: return "Special"
        case .technique: return "Technique"
        case .dedication: return "Dedication"
        case .improvement: return "Improvement"
        }
    }
    
    var icon: String {
        switch self {
        case .consistency: return "calendar.badge.checkmark"
        case .strength: return "dumbbell.fill"
        case .endurance: return "heart.fill"
        case .form: return "target"
        case .milestone: return "flag.fill"
        case .social: return "person.2.fill"
        case .special: return "star.fill"
        case .technique: return "gear"
        case .dedication: return "flame.fill"
        case .improvement: return "chart.line.uptrend.xyaxis"
        }
    }
}

// MARK: - Achievement Type

enum AchievementType: String, CaseIterable, Codable, Sendable {
    case milestone = "milestone"
    case repCount = "rep_count"
    case formQuality = "form_quality"
    case consistency = "consistency"
    case duration = "duration"
    case streak = "streak"
    case improvement = "improvement"
    case exploration = "exploration"
    case mastery = "mastery"
    case challenge = "challenge"
    
    var displayName: String {
        switch self {
        case .milestone: return "Milestone"
        case .repCount: return "Rep Count"
        case .formQuality: return "Form Quality"
        case .consistency: return "Consistency"
        case .duration: return "Duration"
        case .streak: return "Streak"
        case .improvement: return "Improvement"
        case .exploration: return "Exploration"
        case .mastery: return "Mastery"
        case .challenge: return "Challenge"
        }
    }
}

// MARK: - Achievement Rarity

enum AchievementRarity: String, CaseIterable, Codable, Sendable {
    case common = "common"
    case uncommon = "uncommon"
    case rare = "rare"
    case epic = "epic"
    case legendary = "legendary"
    
    var displayName: String {
        switch self {
        case .common: return "Common"
        case .uncommon: return "Uncommon"
        case .rare: return "Rare"
        case .epic: return "Epic"
        case .legendary: return "Legendary"
        }
    }
    
    var color: String {
        switch self {
        case .common: return "gray"
        case .uncommon: return "green"
        case .rare: return "blue"
        case .epic: return "purple"
        case .legendary: return "orange"
        }
    }
    
    var pointsMultiplier: Double {
        switch self {
        case .common: return 1.0
        case .uncommon: return 1.5
        case .rare: return 2.0
        case .epic: return 3.0
        case .legendary: return 5.0
        }
    }
}

// MARK: - Achievement Factory

struct AchievementFactory {
    
    static func createFirstWorkoutAchievement(for userID: UUID) -> Achievement {
        return Achievement(
            userID: userID,
            name: "First Steps",
            description: "Complete your first workout session",
            category: .milestone,
            type: .milestone,
            iconName: "figure.walk",
            badgeColor: "green",
            targetValue: 1.0,
            rarity: .common,
            points: 10
        )
    }
    
    static func createPerfectFormAchievement(for userID: UUID) -> Achievement {
        return Achievement(
            userID: userID,
            name: "Perfect Form",
            description: "Maintain 95%+ form score for an entire workout",
            category: .form,
            type: .formQuality,
            iconName: "target",
            badgeColor: "blue",
            targetValue: 0.95,
            rarity: .rare,
            points: 50
        )
    }
    
    static func createConsistencyAchievement(for userID: UUID) -> Achievement {
        return Achievement(
            userID: userID,
            name: "Week Warrior",
            description: "Complete 5 workouts in a single week",
            category: .consistency,
            type: .consistency,
            iconName: "calendar.badge.checkmark",
            badgeColor: "orange",
            targetValue: 5.0,
            rarity: .uncommon,
            points: 25
        )
    }
    
    static func createCenturyClubAchievement(for userID: UUID) -> Achievement {
        return Achievement(
            userID: userID,
            name: "Century Club",
            description: "Complete 100 reps in a single session",
            category: .strength,
            type: .repCount,
            iconName: "100.square.fill",
            badgeColor: "red",
            targetValue: 100.0,
            rarity: .epic,
            points: 100
        )
    }
    
    static func createEnduranceLegendAchievement(for userID: UUID) -> Achievement {
        return Achievement(
            userID: userID,
            name: "Endurance Legend",
            description: "Complete 10 hours of total workout time",
            category: .endurance,
            type: .duration,
            iconName: "heart.fill",
            badgeColor: "purple",
            targetValue: 36000.0, // 10 hours in seconds
            rarity: .legendary,
            points: 200
        )
    }
}
