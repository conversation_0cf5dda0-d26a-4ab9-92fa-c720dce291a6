import Foundation
import SwiftData

@Model
class WorkoutSession {
    @Attribute(.unique) var id: UUID
    var userID: UUID
    var startTime: Date
    var endTime: Date?
    var totalDuration: TimeInterval
    var totalReps: Int
    var averageFormScore: Double
    var caloriesBurned: Double
    var workoutType: String
    var status: WorkoutStatus
    var notes: String?
    var createdDate: Date
    var lastModified: Date
    
    @Relationship(deleteRule: .cascade, inverse: \ExercisePerformance.workoutSession)
    var exercisePerformances: [ExercisePerformance] = []

    // Achievement system will be implemented in future version
    // @Relationship(deleteRule: .cascade, inverse: \Achievement.workoutSession)
    // var achievements: [Achievement] = []

    init(userID: UUID, workoutType: String) {
        self.id = UUID()
        self.userID = userID
        self.startTime = Date()
        self.totalDuration = 0
        self.totalReps = 0
        self.averageFormScore = 0.0
        self.caloriesBurned = 0.0
        self.workoutType = workoutType
        self.status = .inProgress
        self.createdDate = Date()
        self.lastModified = Date()
    }

    // MARK: - Convenience Methods

    func addRepetition(exercise: ExerciseType, formScore: Float) {
        let performance = ExercisePerformance(
            exerciseType: exercise,
            reps: 1,
            sets: 1,
            formScore: Double(formScore),
            workoutSession: self
        )
        exercisePerformances.append(performance)
        totalReps += 1
        updateAverageFormScore()
        lastModified = Date()
    }

    private func updateAverageFormScore() {
        guard !exercisePerformances.isEmpty else { return }
        let totalScore = exercisePerformances.reduce(into: 0.0) { result, performance in
            result += performance.averageFormScore
        }
        averageFormScore = totalScore / Double(exercisePerformances.count)
    }

    func complete() {
        endTime = Date()
        totalDuration = endTime?.timeIntervalSince(startTime) ?? 0
        status = .completed
        lastModified = Date()
    }
}

enum WorkoutStatus: String, CaseIterable, Codable {
    case inProgress = "inProgress"
    case completed = "completed"
    case paused = "paused"
    case cancelled = "cancelled"
}