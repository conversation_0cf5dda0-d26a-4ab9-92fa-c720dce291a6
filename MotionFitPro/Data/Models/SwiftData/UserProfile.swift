import Foundation
import SwiftData

// Import the unified CoachingPersonality definition

@Model
class UserProfile {
    @Attribute(.unique) var id: UUID
    var name: String
    var email: String?
    var age: Int?
    var height: Double? // in meters
    var weight: Double? // in kg
    var fitnessLevel: String
    var preferredCoachingStyle: String
    var fitnessGoals: [String]
    var preferredWorkoutDuration: String?
    var targetMuscleGroups: [String]
    var availableEquipment: [String]
    var createdDate: Date
    var lastModified: Date

    init(
        name: String,
        email: String? = nil,
        age: Int? = nil,
        height: Double? = nil,
        weight: Double? = nil,
        fitnessLevel: String = "beginner",
        preferredCoachingStyle: String = "encouraging",
        fitnessGoals: [String] = [],
        preferredWorkoutDuration: String? = nil,
        targetMuscleGroups: [String] = [],
        availableEquipment: [String] = []
    ) {
        self.id = UUID()
        self.name = name
        self.email = email
        self.age = age
        self.height = height
        self.weight = weight
        self.fitnessLevel = fitnessLevel
        self.preferredCoachingStyle = preferredCoachingStyle
        self.fitnessGoals = fitnessGoals
        self.preferredWorkoutDuration = preferredWorkoutDuration
        self.targetMuscleGroups = targetMuscleGroups
        self.availableEquipment = availableEquipment
        self.createdDate = Date()
        self.lastModified = Date()
    }

    // Helper methods
    func updateLastModified() {
        lastModified = Date()
    }
}

enum FitnessLevel: String, CaseIterable, Codable {
    case beginner = "beginner"
    case intermediate = "intermediate"
    case advanced = "advanced"
    case expert = "expert"

    var displayName: String {
        switch self {
        case .beginner: return "Beginner"
        case .intermediate: return "Intermediate"
        case .advanced: return "Advanced"
        case .expert: return "Expert"
        }
    }
}

enum FitnessGoal: String, CaseIterable, Codable {
    case weightLoss = "Weight Loss"
    case muscleGain = "Muscle Gain"
    case endurance = "Endurance"
    case flexibility = "Flexibility"
    case strength = "Strength"
    case generalFitness = "General Fitness"
    case rehabilitation = "Rehabilitation"
    case buildMuscle = "Build Muscle"
    case loseWeight = "Lose Weight"
    case improveStrength = "Improve Strength"

    var displayName: String {
        return rawValue
    }
}

enum WorkoutDuration: String, CaseIterable, Codable {
    case short = "short"      // 15-30 minutes
    case medium = "medium"    // 30-60 minutes
    case long = "long"        // 60+ minutes

    var displayName: String {
        switch self {
        case .short: return "Short (15-30 min)"
        case .medium: return "Medium (30-60 min)"
        case .long: return "Long (60+ min)"
        }
    }

    var timeRange: ClosedRange<TimeInterval> {
        switch self {
        case .short: return 900...1800    // 15-30 minutes
        case .medium: return 1800...3600  // 30-60 minutes
        case .long: return 3600...7200    // 60-120 minutes
        }
    }
}

enum MuscleGroup: String, CaseIterable, Codable {
    case chest = "chest"
    case back = "back"
    case shoulders = "shoulders"
    case biceps = "biceps"
    case triceps = "triceps"
    case forearms = "forearms"
    case core = "core"
    case quadriceps = "quadriceps"
    case hamstrings = "hamstrings"
    case glutes = "glutes"
    case calves = "calves"
    case fullBody = "fullBody"

    var displayName: String {
        switch self {
        case .chest: return "Chest"
        case .back: return "Back"
        case .shoulders: return "Shoulders"
        case .biceps: return "Biceps"
        case .triceps: return "Triceps"
        case .forearms: return "Forearms"
        case .core: return "Core"
        case .quadriceps: return "Quadriceps"
        case .hamstrings: return "Hamstrings"
        case .glutes: return "Glutes"
        case .calves: return "Calves"
        case .fullBody: return "Full Body"
        }
    }
}

enum Equipment: String, CaseIterable, Codable {
    case none = "none"
    case dumbbells = "dumbbells"
    case barbell = "barbell"
    case kettlebell = "kettlebell"
    case resistanceBand = "resistance_band"
    case pullupBar = "pullup_bar"
    case bench = "bench"
    case mat = "mat"
    case ball = "ball"
    case cable = "cable"

    var displayName: String {
        switch self {
        case .none: return "No Equipment"
        case .dumbbells: return "Dumbbells"
        case .barbell: return "Barbell"
        case .kettlebell: return "Kettlebell"
        case .resistanceBand: return "Resistance Band"
        case .pullupBar: return "Pull-up Bar"
        case .bench: return "Bench"
        case .mat: return "Exercise Mat"
        case .ball: return "Exercise Ball"
        case .cable: return "Cable Machine"
        }
    }
}