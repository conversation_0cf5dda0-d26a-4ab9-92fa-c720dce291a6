import Foundation
import SwiftUI

// MARK: - Form Analysis Types

/// Criteria for evaluating exercise form
enum FormCriteria: String, CaseIterable, Sendable {
    case jointAlignment = "joint_alignment"
    case rangeOfMotion = "range_of_motion"
    case tempo = "tempo"
    case stability = "stability"
    case posture = "posture"
    case breathing = "breathing"
    case coordination = "coordination"
    case balance = "balance"
    
    var displayName: String {
        switch self {
        case .jointAlignment: return "Joint Alignment"
        case .rangeOfMotion: return "Range of Motion"
        case .tempo: return "Tempo"
        case .stability: return "Stability"
        case .posture: return "Posture"
        case .breathing: return "Breathing"
        case .coordination: return "Coordination"
        case .balance: return "Balance"
        }
    }
    
    var description: String {
        switch self {
        case .jointAlignment: return "Proper alignment of joints during movement"
        case .rangeOfMotion: return "Full range of motion through the exercise"
        case .tempo: return "Controlled speed and timing of movement"
        case .stability: return "Core stability and body control"
        case .posture: return "Proper spine and body positioning"
        case .breathing: return "Coordinated breathing pattern"
        case .coordination: return "Smooth, coordinated movement"
        case .balance: return "Maintaining balance throughout exercise"
        }
    }
    
    var importance: FormImportance {
        switch self {
        case .jointAlignment, .posture: return .critical
        case .stability, .rangeOfMotion: return .high
        case .tempo, .coordination: return .medium
        case .breathing, .balance: return .low
        }
    }
}

/// Importance level of form criteria
enum FormImportance: String, CaseIterable {
    case critical = "critical"
    case high = "high"
    case medium = "medium"
    case low = "low"
    
    var displayName: String {
        switch self {
        case .critical: return "Critical"
        case .high: return "High"
        case .medium: return "Medium"
        case .low: return "Low"
        }
    }
    
    var color: Color {
        switch self {
        case .critical: return .red
        case .high: return .orange
        case .medium: return .yellow
        case .low: return .blue
        }
    }
}

/// Specific form issue detected during exercise
struct FormIssue: Identifiable, Sendable {
    let id = UUID()
    let criteria: FormCriteria
    let severity: FeedbackSeverity
    let description: String
    let jointInvolved: JointName?
    let timestamp: Date
    let confidence: Float
    let correctionSuggestion: String?
    
    init(criteria: FormCriteria, severity: FeedbackSeverity, description: String, jointInvolved: JointName? = nil, correctionSuggestion: String? = nil) {
        self.criteria = criteria
        self.severity = severity
        self.description = description
        self.jointInvolved = jointInvolved
        self.timestamp = Date()
        self.confidence = 0.8
        self.correctionSuggestion = correctionSuggestion
    }
}

/// Severity levels for feedback and issues
enum FeedbackSeverity: String, CaseIterable, Sendable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case critical = "critical"
    
    var displayName: String {
        switch self {
        case .low: return "Minor"
        case .medium: return "Moderate"
        case .high: return "Important"
        case .critical: return "Critical"
        }
    }
    
    var color: Color {
        switch self {
        case .low: return .blue
        case .medium: return .yellow
        case .high: return .orange
        case .critical: return .red
        }
    }
    
    var priority: Int {
        switch self {
        case .low: return 1
        case .medium: return 2
        case .high: return 3
        case .critical: return 4
        }
    }
    
    var shouldInterrupt: Bool {
        switch self {
        case .critical, .high: return true
        case .medium, .low: return false
        }
    }
}

// MARK: - Safety Types

/// Safety risk levels for exercises
enum SafetyRisk: String, CaseIterable, Sendable {
    case none = "none"
    case low = "low"
    case medium = "medium"
    case high = "high"
    case critical = "critical"
    
    var displayName: String {
        switch self {
        case .none: return "Safe"
        case .low: return "Low Risk"
        case .medium: return "Medium Risk"
        case .high: return "High Risk"
        case .critical: return "Critical Risk"
        }
    }
    
    var color: Color {
        switch self {
        case .none: return .green
        case .low: return .yellow
        case .medium: return .orange
        case .high: return .red
        case .critical: return .purple
        }
    }
    
    var shouldStopExercise: Bool {
        switch self {
        case .critical, .high: return true
        case .medium, .low, .none: return false
        }
    }
}

// MARK: - Movement Types

/// Phases of exercise movement
enum MovementPhase: String, CaseIterable, Sendable {
    case start = "start"
    case eccentric = "eccentric"
    case bottom = "bottom"
    case concentric = "concentric"
    case top = "top"
    case middle = "middle"
    case transition = "transition"
    case hold = "hold"
    
    var displayName: String {
        switch self {
        case .start: return "Starting Position"
        case .eccentric: return "Lowering Phase"
        case .bottom: return "Bottom Position"
        case .concentric: return "Lifting Phase"
        case .top: return "Top Position"
        case .middle: return "Mid Movement"
        case .transition: return "Transition"
        case .hold: return "Hold Position"
        }
    }
    
    var description: String {
        switch self {
        case .start: return "Initial position before movement begins"
        case .eccentric: return "Muscle lengthening phase"
        case .bottom: return "Lowest point of the movement"
        case .concentric: return "Muscle shortening phase"
        case .top: return "Highest point of the movement"
        case .middle: return "Middle of the movement range"
        case .transition: return "Transitioning between phases"
        case .hold: return "Holding position (isometric)"
        }
    }
}

/// Direction of movement
enum MovementDirection: String, CaseIterable, Sendable {
    case up = "up"
    case down = "down"
    case forward = "forward"
    case backward = "backward"
    case left = "left"
    case right = "right"
    case rotation = "rotation"
    case stationary = "stationary"
    
    var displayName: String {
        switch self {
        case .up: return "Upward"
        case .down: return "Downward"
        case .forward: return "Forward"
        case .backward: return "Backward"
        case .left: return "Left"
        case .right: return "Right"
        case .rotation: return "Rotational"
        case .stationary: return "Stationary"
        }
    }
}

// MARK: - Coaching Types

/// Context for providing feedback
enum FeedbackContext: String, CaseIterable, Sendable {
    case excellentForm = "excellent_form"
    case goodForm = "good_form"
    case formCorrection = "form_correction"
    case safety = "safety"
    case encouragement = "encouragement"
    case repCompletion = "rep_completion"
    case setCompletion = "set_completion"
    case workoutCompletion = "workout_completion"
    case motivation = "motivation"
    case instruction = "instruction"
    
    var displayName: String {
        switch self {
        case .excellentForm: return "Excellent Form"
        case .goodForm: return "Good Form"
        case .formCorrection: return "Form Correction"
        case .safety: return "Safety Alert"
        case .encouragement: return "Encouragement"
        case .repCompletion: return "Rep Completed"
        case .setCompletion: return "Set Completed"
        case .workoutCompletion: return "Workout Completed"
        case .motivation: return "Motivation"
        case .instruction: return "Instruction"
        }
    }
    
    static func repCompletion(repNumber: Int, setNumber: Int, formScore: Float) -> FeedbackContext {
        return .repCompletion
    }
}

/// Speech priority levels
enum SpeechPriority: String, CaseIterable, Sendable {
    case low = "low"
    case normal = "normal"
    case high = "high"
    case urgent = "urgent"
    
    var displayName: String {
        switch self {
        case .low: return "Low Priority"
        case .normal: return "Normal Priority"
        case .high: return "High Priority"
        case .urgent: return "Urgent"
        }
    }
    
    var shouldInterrupt: Bool {
        switch self {
        case .urgent, .high: return true
        case .normal, .low: return false
        }
    }
}

// MARK: - Analytics Types

/// User progress tracking
struct UserProgress: Sendable {
    let userId: UUID
    let totalWorkouts: Int
    let totalExercises: Int
    let averageFormScore: Float
    let improvementRate: Float
    let lastWorkoutDate: Date?
    let streakDays: Int
    let achievements: [Achievement]
    
    init(userId: UUID) {
        self.userId = userId
        self.totalWorkouts = 0
        self.totalExercises = 0
        self.averageFormScore = 0.0
        self.improvementRate = 0.0
        self.lastWorkoutDate = nil
        self.streakDays = 0
        self.achievements = []
    }
}

/// Analytics event for tracking
struct AnalyticsEvent: Sendable {
    let id = UUID()
    let name: String
    let parameters: [String: Any]
    let timestamp: Date
    let userId: UUID?
    
    init(name: String, parameters: [String: Any] = [:], userId: UUID? = nil) {
        self.name = name
        self.parameters = parameters
        self.timestamp = Date()
        self.userId = userId
    }
}

/// Workout insights generated from analytics
struct WorkoutInsight: Identifiable, Sendable {
    let id = UUID()
    let type: InsightType
    let title: String
    let description: String
    let actionable: Bool
    let priority: InsightPriority
    let timestamp: Date
    let data: [String: Any]?
    
    init(type: InsightType, title: String, description: String, actionable: Bool, priority: InsightPriority, data: [String: Any]? = nil) {
        self.type = type
        self.title = title
        self.description = description
        self.actionable = actionable
        self.priority = priority
        self.timestamp = Date()
        self.data = data
    }
}

/// Types of insights
enum InsightType: String, CaseIterable, Sendable {
    case improvement = "improvement"
    case recommendation = "recommendation"
    case warning = "warning"
    case achievement = "achievement"
    case trend = "trend"
    
    var displayName: String {
        switch self {
        case .improvement: return "Improvement"
        case .recommendation: return "Recommendation"
        case .warning: return "Warning"
        case .achievement: return "Achievement"
        case .trend: return "Trend"
        }
    }
    
    var icon: String {
        switch self {
        case .improvement: return "arrow.up.circle"
        case .recommendation: return "lightbulb"
        case .warning: return "exclamationmark.triangle"
        case .achievement: return "star.circle"
        case .trend: return "chart.line.uptrend.xyaxis"
        }
    }
}

/// Priority levels for insights
enum InsightPriority: String, CaseIterable, Sendable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    
    var displayName: String {
        switch self {
        case .low: return "Low"
        case .medium: return "Medium"
        case .high: return "High"
        }
    }
    
    var color: Color {
        switch self {
        case .low: return .blue
        case .medium: return .orange
        case .high: return .red
        }
    }
}

// MARK: - Permission Types

/// Types of permissions the app requires
enum PermissionType: String, CaseIterable, Sendable {
    case camera = "camera"
    case microphone = "microphone"
    case motion = "motion"
    case healthKit = "health_kit"
    case notifications = "notifications"
    case location = "location"
    
    var displayName: String {
        switch self {
        case .camera: return "Camera"
        case .microphone: return "Microphone"
        case .motion: return "Motion & Fitness"
        case .healthKit: return "Health Data"
        case .notifications: return "Notifications"
        case .location: return "Location"
        }
    }
    
    var description: String {
        switch self {
        case .camera: return "Required for AR body tracking"
        case .microphone: return "For voice feedback and commands"
        case .motion: return "To track your movement and activity"
        case .healthKit: return "To sync workout data with Health app"
        case .notifications: return "For workout reminders and achievements"
        case .location: return "For outdoor workout tracking"
        }
    }
    
    var isRequired: Bool {
        switch self {
        case .camera, .motion: return true
        case .microphone, .healthKit, .notifications, .location: return false
        }
    }
}

/// Permission status
enum PermissionStatus: String, CaseIterable, Sendable {
    case notDetermined = "not_determined"
    case denied = "denied"
    case authorized = "authorized"
    case restricted = "restricted"
    
    var displayName: String {
        switch self {
        case .notDetermined: return "Not Determined"
        case .denied: return "Denied"
        case .authorized: return "Authorized"
        case .restricted: return "Restricted"
        }
    }
    
    var isGranted: Bool {
        return self == .authorized
    }
}
