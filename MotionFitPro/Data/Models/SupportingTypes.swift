import Foundation

// MARK: - Supporting Types for the Application

/// Skill level for users
enum SkillLevel: String, CaseIterable, Codable, Sendable {
    case beginner = "beginner"
    case intermediate = "intermediate"
    case advanced = "advanced"
    case expert = "expert"
    
    var displayName: String {
        switch self {
        case .beginner: return "Beginner"
        case .intermediate: return "Intermediate"
        case .advanced: return "Advanced"
        case .expert: return "Expert"
        }
    }
    
    var description: String {
        switch self {
        case .beginner: return "New to fitness or this exercise"
        case .intermediate: return "Some experience with fitness"
        case .advanced: return "Experienced with regular training"
        case .expert: return "Highly experienced athlete or trainer"
        }
    }
    
    var feedbackFrequency: Double {
        switch self {
        case .beginner: return 1.0      // Most frequent feedback
        case .intermediate: return 0.7   // Moderate feedback
        case .advanced: return 0.4       // Less frequent feedback
        case .expert: return 0.2         // Minimal feedback
        }
    }
}

/// Difficulty levels for exercises and workouts
enum DifficultyLevel: String, CaseIterable, Codable, Sendable {
    case veryEasy = "veryEasy"
    case easy = "easy"
    case moderate = "moderate"
    case hard = "hard"
    case veryHard = "veryHard"
    case extreme = "extreme"
    
    var displayName: String {
        switch self {
        case .veryEasy: return "Very Easy"
        case .easy: return "Easy"
        case .moderate: return "Moderate"
        case .hard: return "Hard"
        case .veryHard: return "Very Hard"
        case .extreme: return "Extreme"
        }
    }
    
    var numericValue: Int {
        switch self {
        case .veryEasy: return 1
        case .easy: return 2
        case .moderate: return 3
        case .hard: return 4
        case .veryHard: return 5
        case .extreme: return 6
        }
    }
    
    var color: String {
        switch self {
        case .veryEasy: return "green"
        case .easy: return "mint"
        case .moderate: return "yellow"
        case .hard: return "orange"
        case .veryHard: return "red"
        case .extreme: return "purple"
        }
    }
}

/// Workout categories
enum WorkoutCategory: String, CaseIterable, Codable, Sendable {
    case strength = "strength"
    case cardio = "cardio"
    case flexibility = "flexibility"
    case balance = "balance"
    case endurance = "endurance"
    case hiit = "hiit"
    case yoga = "yoga"
    case pilates = "pilates"
    case functional = "functional"
    case rehabilitation = "rehabilitation"
    
    var displayName: String {
        switch self {
        case .strength: return "Strength Training"
        case .cardio: return "Cardiovascular"
        case .flexibility: return "Flexibility"
        case .balance: return "Balance"
        case .endurance: return "Endurance"
        case .hiit: return "HIIT"
        case .yoga: return "Yoga"
        case .pilates: return "Pilates"
        case .functional: return "Functional"
        case .rehabilitation: return "Rehabilitation"
        }
    }
    
    var description: String {
        switch self {
        case .strength: return "Build muscle strength and power"
        case .cardio: return "Improve cardiovascular health"
        case .flexibility: return "Increase range of motion and flexibility"
        case .balance: return "Enhance stability and coordination"
        case .endurance: return "Build muscular and cardiovascular endurance"
        case .hiit: return "High-intensity interval training"
        case .yoga: return "Mind-body practice combining poses and breathing"
        case .pilates: return "Core-focused low-impact exercise"
        case .functional: return "Real-world movement patterns"
        case .rehabilitation: return "Recovery and injury prevention"
        }
    }
    
    var icon: String {
        switch self {
        case .strength: return "dumbbell.fill"
        case .cardio: return "heart.fill"
        case .flexibility: return "figure.flexibility"
        case .balance: return "figure.mind.and.body"
        case .endurance: return "timer"
        case .hiit: return "bolt.fill"
        case .yoga: return "leaf.fill"
        case .pilates: return "figure.core.training"
        case .functional: return "figure.walk"
        case .rehabilitation: return "cross.fill"
        }
    }
}

/// Haptic feedback types
enum HapticType: String, CaseIterable, Codable, Sendable {
    case selection = "selection"
    case success = "success"
    case warning = "warning"
    case error = "error"
    case light = "light"
    case medium = "medium"
    case heavy = "heavy"
    
    var displayName: String {
        switch self {
        case .selection: return "Selection"
        case .success: return "Success"
        case .warning: return "Warning"
        case .error: return "Error"
        case .light: return "Light"
        case .medium: return "Medium"
        case .heavy: return "Heavy"
        }
    }
}

/// Energy levels for workout intensity
enum EnergyLevel: String, CaseIterable, Codable, Sendable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case maximum = "maximum"
    
    var displayName: String {
        switch self {
        case .low: return "Low"
        case .medium: return "Medium"
        case .high: return "High"
        case .maximum: return "Maximum"
        }
    }
    
    var intensityPercentage: Double {
        switch self {
        case .low: return 0.3
        case .medium: return 0.6
        case .high: return 0.8
        case .maximum: return 1.0
        }
    }
}

/// Difficulty adjustment recommendations
enum DifficultyAdjustment: String, CaseIterable, Codable, Sendable {
    case decrease = "decrease"
    case maintain = "maintain"
    case increase = "increase"
    
    var displayName: String {
        switch self {
        case .decrease: return "Decrease"
        case .maintain: return "Maintain"
        case .increase: return "Increase"
        }
    }
    
    var description: String {
        switch self {
        case .decrease: return "Reduce difficulty for better form"
        case .maintain: return "Current difficulty is appropriate"
        case .increase: return "Ready for increased challenge"
        }
    }
}

/// Tracking quality levels
enum TrackingQuality: String, CaseIterable, Codable, Sendable {
    case poor = "poor"
    case fair = "fair"
    case good = "good"
    case excellent = "excellent"
    
    var displayName: String {
        switch self {
        case .poor: return "Poor"
        case .fair: return "Fair"
        case .good: return "Good"
        case .excellent: return "Excellent"
        }
    }
    
    var confidenceThreshold: Float {
        switch self {
        case .poor: return 0.3
        case .fair: return 0.5
        case .good: return 0.7
        case .excellent: return 0.9
        }
    }
    
    var color: String {
        switch self {
        case .poor: return "red"
        case .fair: return "orange"
        case .good: return "yellow"
        case .excellent: return "green"
        }
    }
}

/// Sound effect types
enum SoundEffect: String, CaseIterable, Codable, Sendable {
    case repComplete = "repComplete"
    case setComplete = "setComplete"
    case workoutStart = "workoutStart"
    case workoutComplete = "workoutComplete"
    case achievement = "achievement"
    case countdown = "countdown"
    case error = "error"
    case success = "success"
    case warning = "warning"
    case notification = "notification"

    var filename: String {
        switch self {
        case .repComplete: return "rep_complete"
        case .setComplete: return "set_complete"
        case .workoutStart: return "workout_start"
        case .workoutComplete: return "workout_complete"
        case .achievement: return "achievement"
        case .countdown: return "countdown"
        case .error: return "error"
        case .success: return "success"
        case .warning: return "warning"
        case .notification: return "notification"
        }
    }
    
    var displayName: String {
        switch self {
        case .repComplete: return "Rep Complete"
        case .setComplete: return "Set Complete"
        case .workoutStart: return "Workout Start"
        case .workoutComplete: return "Workout Complete"
        case .achievement: return "Achievement"
        case .countdown: return "Countdown"
        case .error: return "Error"
        case .success: return "Success"
        case .warning: return "Warning"
        case .notification: return "Notification"
        }
    }
}
