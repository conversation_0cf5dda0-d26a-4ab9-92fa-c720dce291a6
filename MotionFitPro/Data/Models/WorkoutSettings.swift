import Foundation

/// Comprehensive workout settings and preferences
struct WorkoutSettings: Codable, Sendable {
    var duration: WorkoutDuration
    var difficulty: ExerciseDifficulty
    var restBetweenSets: TimeInterval
    var restBetweenExercises: TimeInterval
    var coachingPersonality: CoachingPersonality
    var audioFeedback: Bool
    var hapticFeedback: Bool
    var formStrictness: FormStrictness
    var autoProgressSets: Bool
    var countdownEnabled: Bool
    var musicEnabled: Bool
    var voiceCoachingEnabled: Bool
    var realTimeFeedback: Bool
    var pauseOnFormError: Bool
    var targetHeartRateZone: HeartRateZone?
    var preferredExerciseTypes: [ExerciseCategory]
    var avoidedEquipment: [Equipment]
    var accessibilityMode: AccessibilityMode
    var cameraPosition: CameraPosition
    var trackingMode: TrackingMode
    
    init(
        duration: WorkoutDuration = .medium,
        difficulty: ExerciseDifficulty = .beginner,
        restBetweenSets: TimeInterval = 60,
        restBetweenExercises: TimeInterval = 120,
        coachingPersonality: CoachingPersonality = .encouraging,
        audioFeedback: Bool = true,
        hapticFeedback: Bool = true,
        formStrictness: FormStrictness = .moderate,
        autoProgressSets: Bool = false,
        countdownEnabled: Bool = true,
        musicEnabled: Bool = true,
        voiceCoachingEnabled: Bool = true,
        realTimeFeedback: Bool = true,
        pauseOnFormError: Bool = false,
        targetHeartRateZone: HeartRateZone? = nil,
        preferredExerciseTypes: [ExerciseCategory] = [],
        avoidedEquipment: [Equipment] = [],
        accessibilityMode: AccessibilityMode = .standard,
        cameraPosition: CameraPosition = .front,
        trackingMode: TrackingMode = .full
    ) {
        self.duration = duration
        self.difficulty = difficulty
        self.restBetweenSets = restBetweenSets
        self.restBetweenExercises = restBetweenExercises
        self.coachingPersonality = coachingPersonality
        self.audioFeedback = audioFeedback
        self.hapticFeedback = hapticFeedback
        self.formStrictness = formStrictness
        self.autoProgressSets = autoProgressSets
        self.countdownEnabled = countdownEnabled
        self.musicEnabled = musicEnabled
        self.voiceCoachingEnabled = voiceCoachingEnabled
        self.realTimeFeedback = realTimeFeedback
        self.pauseOnFormError = pauseOnFormError
        self.targetHeartRateZone = targetHeartRateZone
        self.preferredExerciseTypes = preferredExerciseTypes
        self.avoidedEquipment = avoidedEquipment
        self.accessibilityMode = accessibilityMode
        self.cameraPosition = cameraPosition
        self.trackingMode = trackingMode
    }
}

// MARK: - Workout Duration
enum WorkoutDuration: String, CaseIterable, Codable, Sendable {
    case quick = "quick"        // 5-15 minutes
    case short = "short"        // 15-30 minutes
    case medium = "medium"      // 30-45 minutes
    case long = "long"          // 45-60 minutes
    case extended = "extended"  // 60+ minutes
    case custom = "custom"      // User-defined
    
    var displayName: String {
        switch self {
        case .quick: return "Quick (5-15 min)"
        case .short: return "Short (15-30 min)"
        case .medium: return "Medium (30-45 min)"
        case .long: return "Long (45-60 min)"
        case .extended: return "Extended (60+ min)"
        case .custom: return "Custom"
        }
    }
    
    var timeRange: ClosedRange<TimeInterval> {
        switch self {
        case .quick: return 300...900      // 5-15 minutes
        case .short: return 900...1800     // 15-30 minutes
        case .medium: return 1800...2700   // 30-45 minutes
        case .long: return 2700...3600     // 45-60 minutes
        case .extended: return 3600...7200 // 60-120 minutes
        case .custom: return 0...7200      // 0-120 minutes
        }
    }
    
    var recommendedExerciseCount: Int {
        switch self {
        case .quick: return 3
        case .short: return 5
        case .medium: return 8
        case .long: return 12
        case .extended: return 15
        case .custom: return 8
        }
    }
}

// MARK: - Form Strictness
enum FormStrictness: String, CaseIterable, Codable, Sendable {
    case relaxed = "relaxed"
    case moderate = "moderate"
    case strict = "strict"
    case expert = "expert"
    
    var displayName: String {
        switch self {
        case .relaxed: return "Relaxed"
        case .moderate: return "Moderate"
        case .strict: return "Strict"
        case .expert: return "Expert"
        }
    }
    
    var description: String {
        switch self {
        case .relaxed: return "Focus on movement, less strict form requirements"
        case .moderate: return "Balanced approach to form and movement"
        case .strict: return "High standards for proper form"
        case .expert: return "Professional-level form requirements"
        }
    }
    
    var formThreshold: Float {
        switch self {
        case .relaxed: return 0.6
        case .moderate: return 0.75
        case .strict: return 0.85
        case .expert: return 0.95
        }
    }
    
    var correctionFrequency: Float {
        switch self {
        case .relaxed: return 0.3
        case .moderate: return 0.5
        case .strict: return 0.8
        case .expert: return 1.0
        }
    }
}

// MARK: - Heart Rate Zones
enum HeartRateZone: String, CaseIterable, Codable, Sendable {
    case recovery = "recovery"      // 50-60% max HR
    case aerobic = "aerobic"        // 60-70% max HR
    case anaerobic = "anaerobic"    // 70-80% max HR
    case threshold = "threshold"    // 80-90% max HR
    case maximum = "maximum"        // 90-100% max HR
    
    var displayName: String {
        switch self {
        case .recovery: return "Recovery (50-60%)"
        case .aerobic: return "Aerobic (60-70%)"
        case .anaerobic: return "Anaerobic (70-80%)"
        case .threshold: return "Threshold (80-90%)"
        case .maximum: return "Maximum (90-100%)"
        }
    }
    
    var intensityRange: ClosedRange<Float> {
        switch self {
        case .recovery: return 0.5...0.6
        case .aerobic: return 0.6...0.7
        case .anaerobic: return 0.7...0.8
        case .threshold: return 0.8...0.9
        case .maximum: return 0.9...1.0
        }
    }
    
    var color: String {
        switch self {
        case .recovery: return "blue"
        case .aerobic: return "green"
        case .anaerobic: return "yellow"
        case .threshold: return "orange"
        case .maximum: return "red"
        }
    }
}

// MARK: - Accessibility Mode
enum AccessibilityMode: String, CaseIterable, Codable, Sendable {
    case standard = "standard"
    case voiceOver = "voice_over"
    case largeText = "large_text"
    case highContrast = "high_contrast"
    case reducedMotion = "reduced_motion"
    case assistive = "assistive"
    
    var displayName: String {
        switch self {
        case .standard: return "Standard"
        case .voiceOver: return "VoiceOver"
        case .largeText: return "Large Text"
        case .highContrast: return "High Contrast"
        case .reducedMotion: return "Reduced Motion"
        case .assistive: return "Assistive Mode"
        }
    }
    
    var description: String {
        switch self {
        case .standard: return "Standard accessibility features"
        case .voiceOver: return "Enhanced VoiceOver support"
        case .largeText: return "Larger text and UI elements"
        case .highContrast: return "High contrast colors and borders"
        case .reducedMotion: return "Minimal animations and transitions"
        case .assistive: return "Full assistive technology support"
        }
    }
}

// MARK: - Camera Position
enum CameraPosition: String, CaseIterable, Codable, Sendable {
    case front = "front"
    case back = "back"
    case auto = "auto"
    
    var displayName: String {
        switch self {
        case .front: return "Front Camera"
        case .back: return "Back Camera"
        case .auto: return "Auto Select"
        }
    }
}

// MARK: - Tracking Mode
enum TrackingMode: String, CaseIterable, Codable, Sendable {
    case full = "full"              // Full body tracking
    case upper = "upper"            // Upper body only
    case lower = "lower"            // Lower body only
    case core = "core"              // Core/torso focus
    case performance = "performance" // High performance mode
    case battery = "battery"        // Battery saving mode
    
    var displayName: String {
        switch self {
        case .full: return "Full Body"
        case .upper: return "Upper Body"
        case .lower: return "Lower Body"
        case .core: return "Core Focus"
        case .performance: return "High Performance"
        case .battery: return "Battery Saver"
        }
    }
    
    var description: String {
        switch self {
        case .full: return "Track entire body for comprehensive analysis"
        case .upper: return "Focus on upper body movements"
        case .lower: return "Focus on lower body movements"
        case .core: return "Emphasize core and torso tracking"
        case .performance: return "Maximum accuracy and frame rate"
        case .battery: return "Reduced tracking for longer battery life"
        }
    }
    
    var frameRate: Int {
        switch self {
        case .full: return 30
        case .upper: return 30
        case .lower: return 30
        case .core: return 30
        case .performance: return 60
        case .battery: return 15
        }
    }
}

// MARK: - Settings Validation
extension WorkoutSettings {
    /// Validate that settings are reasonable
    var isValid: Bool {
        return restBetweenSets >= 0 &&
               restBetweenSets <= 300 && // Max 5 minutes
               restBetweenExercises >= 0 &&
               restBetweenExercises <= 600 // Max 10 minutes
    }
    
    /// Get recommended settings based on user fitness level
    static func recommended(for fitnessLevel: FitnessLevel) -> WorkoutSettings {
        switch fitnessLevel {
        case .beginner:
            return WorkoutSettings(
                duration: .short,
                difficulty: .beginner,
                restBetweenSets: 90,
                restBetweenExercises: 180,
                formStrictness: .moderate,
                realTimeFeedback: true,
                pauseOnFormError: true
            )
        case .intermediate:
            return WorkoutSettings(
                duration: .medium,
                difficulty: .intermediate,
                restBetweenSets: 60,
                restBetweenExercises: 120,
                formStrictness: .strict,
                autoProgressSets: true
            )
        case .advanced:
            return WorkoutSettings(
                duration: .long,
                difficulty: .advanced,
                restBetweenSets: 45,
                restBetweenExercises: 90,
                formStrictness: .expert,
                trackingMode: .performance
            )
        }
    }
}

// MARK: - Fitness Level
enum FitnessLevel: String, CaseIterable, Codable, Sendable {
    case beginner = "beginner"
    case intermediate = "intermediate"
    case advanced = "advanced"
    
    var displayName: String {
        switch self {
        case .beginner: return "Beginner"
        case .intermediate: return "Intermediate"
        case .advanced: return "Advanced"
        }
    }
}
