import Foundation
import SwiftUI

// MARK: - Coaching Personality

/// Unified coaching personality types for consistent user experience
enum CoachingPersonality: String, CaseIterable, Sendable {
    case encouraging = "encouraging"
    case motivational = "motivational"
    case supportive = "supportive"
    case professional = "professional"
    case friendly = "friendly"
    case strict = "strict"
    case gentle = "gentle"
    case energetic = "energetic"
    case calm = "calm"
    case humorous = "humorous"
    
    var displayName: String {
        switch self {
        case .encouraging: return "Encouraging"
        case .motivational: return "Motivational"
        case .supportive: return "Supportive"
        case .professional: return "Professional"
        case .friendly: return "Friendly"
        case .strict: return "Strict"
        case .gentle: return "Gentle"
        case .energetic: return "Energetic"
        case .calm: return "Calm"
        case .humorous: return "Humorous"
        }
    }
    
    var description: String {
        switch self {
        case .encouraging: return "Positive reinforcement and gentle guidance"
        case .motivational: return "High-energy motivation and challenge"
        case .supportive: return "Understanding and patient assistance"
        case .professional: return "Clear, direct, and technical feedback"
        case .friendly: return "Warm, conversational, and approachable"
        case .strict: return "Firm discipline and high standards"
        case .gentle: return "Soft-spoken and nurturing approach"
        case .energetic: return "High-energy and enthusiastic coaching"
        case .calm: return "Peaceful and mindful guidance"
        case .humorous: return "Light-hearted with appropriate humor"
        }
    }
    
    var characteristics: [String] {
        switch self {
        case .encouraging:
            return ["Positive", "Uplifting", "Confidence-building", "Patient"]
        case .motivational:
            return ["Inspiring", "Challenging", "Goal-oriented", "Dynamic"]
        case .supportive:
            return ["Understanding", "Empathetic", "Reassuring", "Adaptive"]
        case .professional:
            return ["Technical", "Precise", "Objective", "Informative"]
        case .friendly:
            return ["Warm", "Conversational", "Approachable", "Personal"]
        case .strict:
            return ["Disciplined", "Demanding", "Structured", "Results-focused"]
        case .gentle:
            return ["Soft-spoken", "Nurturing", "Mindful", "Peaceful"]
        case .energetic:
            return ["High-energy", "Enthusiastic", "Vibrant", "Exciting"]
        case .calm:
            return ["Peaceful", "Centered", "Balanced", "Mindful"]
        case .humorous:
            return ["Light-hearted", "Fun", "Entertaining", "Engaging"]
        }
    }
    
    var color: Color {
        switch self {
        case .encouraging: return .green
        case .motivational: return .red
        case .supportive: return .blue
        case .professional: return .gray
        case .friendly: return .orange
        case .strict: return .purple
        case .gentle: return .pink
        case .energetic: return .yellow
        case .calm: return .mint
        case .humorous: return .cyan
        }
    }
    
    var icon: String {
        switch self {
        case .encouraging: return "hand.thumbsup.fill"
        case .motivational: return "flame.fill"
        case .supportive: return "heart.fill"
        case .professional: return "briefcase.fill"
        case .friendly: return "person.2.fill"
        case .strict: return "exclamationmark.triangle.fill"
        case .gentle: return "leaf.fill"
        case .energetic: return "bolt.fill"
        case .calm: return "moon.fill"
        case .humorous: return "face.smiling.fill"
        }
    }
    
    // MARK: - Coaching Behavior
    
    var feedbackStyle: FeedbackStyle {
        switch self {
        case .encouraging, .supportive, .friendly, .gentle:
            return .positive
        case .motivational, .energetic:
            return .challenging
        case .professional:
            return .technical
        case .strict:
            return .direct
        case .calm:
            return .mindful
        case .humorous:
            return .playful
        }
    }
    
    var correctionApproach: CorrectionApproach {
        switch self {
        case .encouraging, .supportive, .gentle:
            return .gentle
        case .motivational, .energetic:
            return .motivating
        case .professional:
            return .technical
        case .friendly, .humorous:
            return .conversational
        case .strict:
            return .firm
        case .calm:
            return .mindful
        }
    }
    
    var celebrationStyle: CelebrationStyle {
        switch self {
        case .encouraging, .supportive:
            return .warm
        case .motivational, .energetic:
            return .enthusiastic
        case .professional:
            return .acknowledgment
        case .friendly, .humorous:
            return .fun
        case .strict:
            return .approval
        case .gentle, .calm:
            return .peaceful
        }
    }
    
    // MARK: - Message Generation
    
    func generateMessage(for context: FeedbackContext, customization: MessageCustomization? = nil) -> String {
        let baseMessages = getBaseMessages(for: context)
        let selectedMessage = baseMessages.randomElement() ?? "Keep going!"
        
        return customization?.apply(to: selectedMessage, personality: self) ?? selectedMessage
    }
    
    private func getBaseMessages(for context: FeedbackContext) -> [String] {
        switch (self, context) {
        case (.encouraging, .excellentForm):
            return ["Excellent form! You're doing great!", "Perfect technique! Keep it up!", "Outstanding! Your form is spot on!"]
        case (.encouraging, .formCorrection):
            return ["Let's adjust that slightly", "Small tweak needed here", "Almost there, just a minor adjustment"]
        case (.motivational, .excellentForm):
            return ["Crushing it! Incredible form!", "You're unstoppable! Perfect technique!", "Beast mode activated! Flawless execution!"]
        case (.motivational, .formCorrection):
            return ["Push through and fix that form!", "You've got this! Make that adjustment!", "Power through with better technique!"]
        case (.professional, .excellentForm):
            return ["Form parameters optimal", "Technique execution: excellent", "Biomechanical alignment: correct"]
        case (.professional, .formCorrection):
            return ["Adjust joint alignment", "Optimize movement pattern", "Correct biomechanical positioning"]
        case (.friendly, .excellentForm):
            return ["Looking good, friend!", "Nice work! Your form is great!", "Hey, that's some solid technique!"]
        case (.friendly, .formCorrection):
            return ["Let's tweak that a bit", "How about we adjust this?", "Mind if we fix that form?"]
        case (.strict, .excellentForm):
            return ["Acceptable form. Continue.", "Standards met. Proceed.", "Form approved. Next rep."]
        case (.strict, .formCorrection):
            return ["Incorrect. Fix immediately.", "Unacceptable form. Correct now.", "Standards not met. Adjust."]
        case (.gentle, .excellentForm):
            return ["Beautiful movement", "Lovely form, well done", "Graceful and controlled"]
        case (.gentle, .formCorrection):
            return ["Let's gently adjust that", "Softly correct this movement", "Mindfully improve your form"]
        case (.energetic, .excellentForm):
            return ["AMAZING! You're on fire!", "WOW! Incredible form!", "YES! That's how it's done!"]
        case (.energetic, .formCorrection):
            return ["Let's pump up that form!", "Energy up! Fix that technique!", "Power through with better form!"]
        case (.calm, .excellentForm):
            return ["Peaceful and controlled", "Centered and balanced", "Mindful execution"]
        case (.calm, .formCorrection):
            return ["Breathe and adjust", "Find your center, then correct", "Mindfully improve"]
        case (.humorous, .excellentForm):
            return ["Form so good, it's illegal in 12 states!", "Your technique is comedy gold - perfectly executed!", "That form is funnier than my jokes - because it actually works!"]
        case (.humorous, .formCorrection):
            return ["Let's debug that movement", "Time for a form update!", "Your technique needs a patch!"]
        default:
            return ["Keep going!", "You're doing well!", "Nice work!"]
        }
    }
}

// MARK: - Supporting Types

enum FeedbackStyle: String, CaseIterable {
    case positive = "positive"
    case challenging = "challenging"
    case technical = "technical"
    case direct = "direct"
    case mindful = "mindful"
    case playful = "playful"
}

enum CorrectionApproach: String, CaseIterable {
    case gentle = "gentle"
    case motivating = "motivating"
    case technical = "technical"
    case conversational = "conversational"
    case firm = "firm"
    case mindful = "mindful"
}

enum CelebrationStyle: String, CaseIterable {
    case warm = "warm"
    case enthusiastic = "enthusiastic"
    case acknowledgment = "acknowledgment"
    case fun = "fun"
    case approval = "approval"
    case peaceful = "peaceful"
}

// MARK: - Message Customization

struct MessageCustomization {
    let includeUserName: Bool
    let includeMetrics: Bool
    let includeEmoji: Bool
    let verbosity: Verbosity
    
    enum Verbosity: String, CaseIterable {
        case brief = "brief"
        case normal = "normal"
        case detailed = "detailed"
    }
    
    func apply(to message: String, personality: CoachingPersonality) -> String {
        var customizedMessage = message
        
        // Add emoji based on personality and setting
        if includeEmoji {
            customizedMessage = addEmoji(to: customizedMessage, personality: personality)
        }
        
        // Adjust verbosity
        customizedMessage = adjustVerbosity(customizedMessage, verbosity: verbosity)
        
        return customizedMessage
    }
    
    private func addEmoji(to message: String, personality: CoachingPersonality) -> String {
        let emojis: [String]
        
        switch personality {
        case .encouraging: emojis = ["👍", "💪", "⭐", "🌟"]
        case .motivational: emojis = ["🔥", "💥", "⚡", "🚀"]
        case .supportive: emojis = ["❤️", "🤗", "💙", "🌈"]
        case .professional: emojis = ["✅", "📊", "🎯", "📈"]
        case .friendly: emojis = ["😊", "👋", "🙂", "😄"]
        case .strict: emojis = ["⚠️", "❗", "🎯", "📏"]
        case .gentle: emojis = ["🌸", "🕊️", "🌿", "☁️"]
        case .energetic: emojis = ["⚡", "🎉", "🌟", "💫"]
        case .calm: emojis = ["🧘", "🌙", "🌊", "🍃"]
        case .humorous: emojis = ["😄", "🤣", "😂", "🎭"]
        }
        
        let selectedEmoji = emojis.randomElement() ?? ""
        return "\(selectedEmoji) \(message)"
    }
    
    private func adjustVerbosity(_ message: String, verbosity: Verbosity) -> String {
        switch verbosity {
        case .brief:
            // Shorten message
            let words = message.split(separator: " ")
            return words.prefix(5).joined(separator: " ")
        case .normal:
            return message
        case .detailed:
            // Could add more context, but for now return as-is
            return message
        }
    }
}

// MARK: - Coaching Preferences

struct CoachingPreferences: Codable, Sendable {
    let personality: CoachingPersonality
    let customization: MessageCustomization
    let feedbackFrequency: FeedbackFrequency
    let correctionSensitivity: CorrectionSensitivity
    
    enum FeedbackFrequency: String, CaseIterable, Codable {
        case minimal = "minimal"
        case normal = "normal"
        case frequent = "frequent"
        case constant = "constant"
    }
    
    enum CorrectionSensitivity: String, CaseIterable, Codable {
        case low = "low"
        case medium = "medium"
        case high = "high"
    }
    
    static let `default` = CoachingPreferences(
        personality: .encouraging,
        customization: MessageCustomization(
            includeUserName: false,
            includeMetrics: false,
            includeEmoji: true,
            verbosity: .normal
        ),
        feedbackFrequency: .normal,
        correctionSensitivity: .medium
    )
}
