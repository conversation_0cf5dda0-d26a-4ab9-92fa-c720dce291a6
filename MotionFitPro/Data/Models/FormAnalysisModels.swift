import Foundation
import simd

// MARK: - Form Analysis Models

/// Comprehensive form analysis result
struct FormAnalysis: Sendable, Codable {
    let overallScore: Float
    let scores: [FormCriteria: Float]
    let issues: [FormIssue]
    let recommendations: [String]
    let timestamp: Date
    let exerciseType: ExerciseType?
    let confidence: Float
    
    init(
        overallScore: Float,
        scores: [FormCriteria: Float] = [:],
        issues: [FormIssue] = [],
        recommendations: [String] = [],
        timestamp: Date = Date(),
        exerciseType: ExerciseType? = nil,
        confidence: Float = 1.0
    ) {
        self.overallScore = overallScore
        self.scores = scores
        self.issues = issues
        self.recommendations = recommendations
        self.timestamp = timestamp
        self.exerciseType = exerciseType
        self.confidence = confidence
    }
    
    var qualityGrade: String {
        switch overallScore {
        case 0.9...1.0: return "A"
        case 0.8..<0.9: return "B"
        case 0.7..<0.8: return "C"
        case 0.6..<0.7: return "D"
        default: return "F"
        }
    }
}

/// Form criteria for evaluation
enum FormCriteria: String, CaseIterable, Codable, Sendable {
    case jointAlignment = "jointAlignment"
    case rangeOfMotion = "rangeOfMotion"
    case tempo = "tempo"
    case stability = "stability"
    case symmetry = "symmetry"
    case posture = "posture"
    case breathing = "breathing"
    case control = "control"
    
    var displayName: String {
        switch self {
        case .jointAlignment: return "Joint Alignment"
        case .rangeOfMotion: return "Range of Motion"
        case .tempo: return "Tempo"
        case .stability: return "Stability"
        case .symmetry: return "Symmetry"
        case .posture: return "Posture"
        case .breathing: return "Breathing"
        case .control: return "Control"
        }
    }
    
    var description: String {
        switch self {
        case .jointAlignment: return "Proper alignment of joints during movement"
        case .rangeOfMotion: return "Full range of motion through the exercise"
        case .tempo: return "Appropriate speed and rhythm of movement"
        case .stability: return "Core stability and balance throughout exercise"
        case .symmetry: return "Equal movement on both sides of the body"
        case .posture: return "Maintaining proper posture and spine alignment"
        case .breathing: return "Proper breathing pattern during exercise"
        case .control: return "Controlled movement without momentum"
        }
    }
}

/// Form issue detected during analysis
struct FormIssue: Identifiable, Codable, Sendable {
    let id: UUID
    let type: FormIssueType
    let criteria: FormCriteria
    let severity: FeedbackSeverity
    let description: String
    let jointInvolved: JointName?
    let timestamp: Date
    let confidence: Float
    let correctionSuggestion: String?
    
    init(
        id: UUID = UUID(),
        type: FormIssueType = .technique,
        criteria: FormCriteria,
        severity: FeedbackSeverity,
        description: String,
        jointInvolved: JointName? = nil,
        timestamp: Date = Date(),
        confidence: Float = 1.0,
        correctionSuggestion: String? = nil
    ) {
        self.id = id
        self.type = type
        self.criteria = criteria
        self.severity = severity
        self.description = description
        self.jointInvolved = jointInvolved
        self.timestamp = timestamp
        self.confidence = confidence
        self.correctionSuggestion = correctionSuggestion
    }
}

/// Types of form issues
enum FormIssueType: String, CaseIterable, Codable, Sendable {
    case technique = "technique"
    case safety = "safety"
    case efficiency = "efficiency"
    case posture = "posture"
    
    var displayName: String {
        switch self {
        case .technique: return "Technique"
        case .safety: return "Safety"
        case .efficiency: return "Efficiency"
        case .posture: return "Posture"
        }
    }
    
    var priority: FeedbackPriority {
        switch self {
        case .safety: return .immediate
        case .technique: return .high
        case .posture: return .normal
        case .efficiency: return .low
        }
    }
}

/// Safety risk levels
enum SafetyRisk: String, CaseIterable, Codable, Sendable {
    case none = "none"
    case low = "low"
    case medium = "medium"
    case high = "high"
    case critical = "critical"
    
    var displayName: String {
        switch self {
        case .none: return "No Risk"
        case .low: return "Low Risk"
        case .medium: return "Medium Risk"
        case .high: return "High Risk"
        case .critical: return "Critical Risk"
        }
    }
    
    var color: String {
        switch self {
        case .none: return "green"
        case .low: return "yellow"
        case .medium: return "orange"
        case .high: return "red"
        case .critical: return "purple"
        }
    }
    
    var priority: FeedbackPriority {
        switch self {
        case .none: return .low
        case .low: return .normal
        case .medium: return .high
        case .high, .critical: return .immediate
        }
    }
}

/// Exercise analysis result
struct ExerciseAnalysis: Identifiable, Codable, Sendable {
    let id: UUID
    let exercise: ExerciseType
    let timestamp: Date
    let formScore: Double
    let repPhase: MovementPhase
    let poseData: BodyPoseData
    let detectedIssues: [FormIssue]
    let safetyRisk: SafetyRisk
    let confidence: Float
    let formAnalysis: FormAnalysis?
    
    init(
        id: UUID = UUID(),
        exercise: ExerciseType,
        timestamp: Date = Date(),
        formScore: Double = 0.8,
        repPhase: MovementPhase = .middle,
        poseData: BodyPoseData,
        detectedIssues: [FormIssue] = [],
        safetyRisk: SafetyRisk = .none,
        confidence: Float = 0.8,
        formAnalysis: FormAnalysis? = nil
    ) {
        self.id = id
        self.exercise = exercise
        self.timestamp = timestamp
        self.formScore = formScore
        self.repPhase = repPhase
        self.poseData = poseData
        self.detectedIssues = detectedIssues
        self.safetyRisk = safetyRisk
        self.confidence = confidence
        self.formAnalysis = formAnalysis
    }
}

/// Movement phases during exercise
enum MovementPhase: String, CaseIterable, Codable, Sendable {
    case preparation = "preparation"
    case start = "start"
    case eccentric = "eccentric"
    case bottom = "bottom"
    case concentric = "concentric"
    case top = "top"
    case middle = "middle"
    case end = "end"
    case completion = "completion"
    case rest = "rest"
    
    var displayName: String {
        switch self {
        case .preparation: return "Preparation"
        case .start: return "Start"
        case .eccentric: return "Lowering"
        case .bottom: return "Bottom"
        case .concentric: return "Lifting"
        case .top: return "Top"
        case .middle: return "Middle"
        case .end: return "End"
        case .completion: return "Completion"
        case .rest: return "Rest"
        }
    }
}

/// Exercise state tracking
enum ExerciseState: String, CaseIterable, Codable, Sendable {
    case notStarted = "notStarted"
    case inProgress = "inProgress"
    case paused = "paused"
    case completed = "completed"
    case failed = "failed"
    
    var displayName: String {
        switch self {
        case .notStarted: return "Not Started"
        case .inProgress: return "In Progress"
        case .paused: return "Paused"
        case .completed: return "Completed"
        case .failed: return "Failed"
        }
    }
}

/// Rep quality assessment
enum RepQuality: String, CaseIterable, Codable, Sendable {
    case excellent = "excellent"
    case good = "good"
    case acceptable = "acceptable"
    case poor = "poor"
    case unknown = "unknown"
    
    var displayName: String {
        switch self {
        case .excellent: return "Excellent"
        case .good: return "Good"
        case .acceptable: return "Acceptable"
        case .poor: return "Poor"
        case .unknown: return "Unknown"
        }
    }
    
    var color: String {
        switch self {
        case .excellent: return "green"
        case .good: return "blue"
        case .acceptable: return "yellow"
        case .poor: return "red"
        case .unknown: return "gray"
        }
    }
    
    var scoreRange: ClosedRange<Float> {
        switch self {
        case .excellent: return 0.9...1.0
        case .good: return 0.8..<0.9
        case .acceptable: return 0.7..<0.8
        case .poor: return 0.0..<0.7
        case .unknown: return 0.0...0.0
        }
    }
}
