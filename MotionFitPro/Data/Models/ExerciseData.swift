import Foundation

/// Comprehensive data model for exercises in the MotionFitPro app
struct ExerciseData: Identifiable, Codable, Hashable, Sendable {
    let id: UUID
    let name: String
    let type: ExerciseType
    let category: ExerciseCategory
    let difficulty: ExerciseDifficulty
    let equipment: Equipment
    let duration: TimeInterval
    let description: String
    let instructions: [String]
    let muscleGroups: [MuscleGroup]
    let caloriesBurned: Double
    let videoPreviewURL: URL?
    let thumbnailName: String
    let biomechanicalPoints: [BiomechanicalPoint]
    let commonMistakes: [String]
    let modifications: [ExerciseModification]
    let progressionLevel: Int
    let restTime: TimeInterval
    let targetReps: Int
    let targetSets: Int
    
    init(
        id: UUID = UUID(),
        name: String,
        type: ExerciseType,
        category: ExerciseCategory,
        difficulty: ExerciseDifficulty,
        equipment: Equipment,
        duration: TimeInterval,
        description: String,
        instructions: [String],
        muscleGroups: [MuscleGroup],
        caloriesBurned: Double,
        videoPreviewURL: URL? = nil,
        thumbnailName: String,
        biomechanicalPoints: [BiomechanicalPoint] = [],
        commonMistakes: [String] = [],
        modifications: [ExerciseModification] = [],
        progressionLevel: Int = 1,
        restTime: TimeInterval = 60,
        targetReps: Int = 12,
        targetSets: Int = 3
    ) {
        self.id = id
        self.name = name
        self.type = type
        self.category = category
        self.difficulty = difficulty
        self.equipment = equipment
        self.duration = duration
        self.description = description
        self.instructions = instructions
        self.muscleGroups = muscleGroups
        self.caloriesBurned = caloriesBurned
        self.videoPreviewURL = videoPreviewURL
        self.thumbnailName = thumbnailName
        self.biomechanicalPoints = biomechanicalPoints
        self.commonMistakes = commonMistakes
        self.modifications = modifications
        self.progressionLevel = progressionLevel
        self.restTime = restTime
        self.targetReps = targetReps
        self.targetSets = targetSets
    }
}

// MARK: - Exercise Categories
enum ExerciseCategory: String, CaseIterable, Codable, Sendable {
    case strength = "strength"
    case cardio = "cardio"
    case flexibility = "flexibility"
    case balance = "balance"
    case plyometric = "plyometric"
    case core = "core"
    case functional = "functional"
    
    var displayName: String {
        switch self {
        case .strength: return "Strength"
        case .cardio: return "Cardio"
        case .flexibility: return "Flexibility"
        case .balance: return "Balance"
        case .plyometric: return "Plyometric"
        case .core: return "Core"
        case .functional: return "Functional"
        }
    }
    
    var icon: String {
        switch self {
        case .strength: return "dumbbell.fill"
        case .cardio: return "heart.fill"
        case .flexibility: return "figure.flexibility"
        case .balance: return "figure.mind.and.body"
        case .plyometric: return "figure.jumprope"
        case .core: return "figure.core.training"
        case .functional: return "figure.functional.training"
        }
    }
}

// MARK: - Exercise Difficulty
enum ExerciseDifficulty: String, CaseIterable, Codable, Sendable {
    case beginner = "beginner"
    case intermediate = "intermediate"
    case advanced = "advanced"
    case expert = "expert"
    
    var displayName: String {
        switch self {
        case .beginner: return "Beginner"
        case .intermediate: return "Intermediate"
        case .advanced: return "Advanced"
        case .expert: return "Expert"
        }
    }
    
    var color: String {
        switch self {
        case .beginner: return "green"
        case .intermediate: return "yellow"
        case .advanced: return "orange"
        case .expert: return "red"
        }
    }
    
    var multiplier: Double {
        switch self {
        case .beginner: return 0.8
        case .intermediate: return 1.0
        case .advanced: return 1.3
        case .expert: return 1.6
        }
    }
}

// MARK: - Equipment Types
enum Equipment: String, CaseIterable, Codable, Sendable {
    case none = "none"
    case dumbbells = "dumbbells"
    case barbell = "barbell"
    case kettlebell = "kettlebell"
    case resistanceBand = "resistance_band"
    case pullupBar = "pullup_bar"
    case bench = "bench"
    case mat = "mat"
    case ball = "ball"
    case cable = "cable"
    
    var displayName: String {
        switch self {
        case .none: return "No Equipment"
        case .dumbbells: return "Dumbbells"
        case .barbell: return "Barbell"
        case .kettlebell: return "Kettlebell"
        case .resistanceBand: return "Resistance Band"
        case .pullupBar: return "Pull-up Bar"
        case .bench: return "Bench"
        case .mat: return "Exercise Mat"
        case .ball: return "Exercise Ball"
        case .cable: return "Cable Machine"
        }
    }
    
    var icon: String {
        switch self {
        case .none: return "hand.raised.fill"
        case .dumbbells: return "dumbbell.fill"
        case .barbell: return "barbell"
        case .kettlebell: return "kettlebell"
        case .resistanceBand: return "band"
        case .pullupBar: return "pullup.bar"
        case .bench: return "bench"
        case .mat: return "mat"
        case .ball: return "ball"
        case .cable: return "cable"
        }
    }
}

// MARK: - Muscle Groups
enum MuscleGroup: String, CaseIterable, Codable, Sendable {
    case chest = "chest"
    case back = "back"
    case shoulders = "shoulders"
    case biceps = "biceps"
    case triceps = "triceps"
    case forearms = "forearms"
    case core = "core"
    case quadriceps = "quadriceps"
    case hamstrings = "hamstrings"
    case glutes = "glutes"
    case calves = "calves"
    case fullBody = "full_body"
    
    var displayName: String {
        switch self {
        case .chest: return "Chest"
        case .back: return "Back"
        case .shoulders: return "Shoulders"
        case .biceps: return "Biceps"
        case .triceps: return "Triceps"
        case .forearms: return "Forearms"
        case .core: return "Core"
        case .quadriceps: return "Quadriceps"
        case .hamstrings: return "Hamstrings"
        case .glutes: return "Glutes"
        case .calves: return "Calves"
        case .fullBody: return "Full Body"
        }
    }
}

// MARK: - Biomechanical Points
struct BiomechanicalPoint: Codable, Hashable, Sendable {
    let jointName: String
    let targetAngle: Double
    let tolerance: Double
    let phase: ExercisePhase
    
    enum ExercisePhase: String, Codable, Sendable {
        case start = "start"
        case middle = "middle"
        case end = "end"
        case transition = "transition"
    }
}

// MARK: - Exercise Modifications
struct ExerciseModification: Codable, Hashable, Sendable {
    let name: String
    let description: String
    let difficultyChange: Int // -2 to +2
    let equipmentRequired: Equipment?
}
