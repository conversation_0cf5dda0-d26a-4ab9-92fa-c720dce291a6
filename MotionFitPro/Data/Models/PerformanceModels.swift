import Foundation

// MARK: - Workout Performance
struct WorkoutPerformance: Codable, Sendable {
    let id: UUID
    let workoutSessionId: UUID
    let startTime: Date
    let endTime: Date?
    let totalDuration: TimeInterval
    let exercisesCompleted: Int
    let totalExercises: Int
    let averageFormScore: Double
    let caloriesBurned: Double
    let averageHeartRate: Int?
    let maxHeartRate: Int?
    let totalReps: Int
    let totalSets: Int
    let overallRating: Int // 1-5 stars
    let notes: String?
    let achievements: [Achievement]
    let personalRecords: [PersonalRecord]
    
    init(
        id: UUID = UUID(),
        workoutSessionId: UUID,
        startTime: Date,
        endTime: Date? = nil,
        totalDuration: TimeInterval = 0,
        exercisesCompleted: Int = 0,
        totalExercises: Int,
        averageFormScore: Double = 0.0,
        caloriesBurned: Double = 0.0,
        averageHeartRate: Int? = nil,
        maxHeartRate: Int? = nil,
        totalReps: Int = 0,
        totalSets: Int = 0,
        overallRating: Int = 0,
        notes: String? = nil,
        achievements: [Achievement] = [],
        personalRecords: [PersonalRecord] = []
    ) {
        self.id = id
        self.workoutSessionId = workoutSessionId
        self.startTime = startTime
        self.endTime = endTime
        self.totalDuration = totalDuration
        self.exercisesCompleted = exercisesCompleted
        self.totalExercises = totalExercises
        self.averageFormScore = averageFormScore
        self.caloriesBurned = caloriesBurned
        self.averageHeartRate = averageHeartRate
        self.maxHeartRate = maxHeartRate
        self.totalReps = totalReps
        self.totalSets = totalSets
        self.overallRating = overallRating
        self.notes = notes
        self.achievements = achievements
        self.personalRecords = personalRecords
    }
    
    var completionPercentage: Double {
        guard totalExercises > 0 else { return 0.0 }
        return Double(exercisesCompleted) / Double(totalExercises) * 100.0
    }
    
    var isCompleted: Bool {
        return exercisesCompleted >= totalExercises
    }
    
    var formGrade: FormGrade {
        switch averageFormScore {
        case 0.9...1.0: return .excellent
        case 0.8..<0.9: return .good
        case 0.7..<0.8: return .fair
        case 0.6..<0.7: return .poor
        default: return .needsWork
        }
    }
}

// FormFeedback is now defined in Data/Models/FeedbackModels.swift

// FeedbackType and FeedbackSeverity are now defined in Data/Models/FeedbackModels.swift

// MARK: - Achievement
struct Achievement: Identifiable, Codable, Sendable {
    let id: UUID
    let name: String
    let description: String
    let category: AchievementCategory
    let iconName: String
    let unlockedDate: Date?
    let progress: Double // 0.0 to 1.0
    let target: Double
    let current: Double
    let isUnlocked: Bool
    let rarity: AchievementRarity
    let points: Int
    
    init(
        id: UUID = UUID(),
        name: String,
        description: String,
        category: AchievementCategory,
        iconName: String,
        unlockedDate: Date? = nil,
        progress: Double = 0.0,
        target: Double,
        current: Double = 0.0,
        isUnlocked: Bool = false,
        rarity: AchievementRarity = .common,
        points: Int = 10
    ) {
        self.id = id
        self.name = name
        self.description = description
        self.category = category
        self.iconName = iconName
        self.unlockedDate = unlockedDate
        self.progress = progress
        self.target = target
        self.current = current
        self.isUnlocked = isUnlocked
        self.rarity = rarity
        self.points = points
    }
}

enum AchievementCategory: String, CaseIterable, Codable, Sendable {
    case consistency = "consistency"
    case strength = "strength"
    case endurance = "endurance"
    case form = "form"
    case milestone = "milestone"
    case social = "social"
    case special = "special"
    
    var displayName: String {
        switch self {
        case .consistency: return "Consistency"
        case .strength: return "Strength"
        case .endurance: return "Endurance"
        case .form: return "Perfect Form"
        case .milestone: return "Milestone"
        case .social: return "Social"
        case .special: return "Special"
        }
    }
}

enum AchievementRarity: String, CaseIterable, Codable, Sendable {
    case common = "common"
    case uncommon = "uncommon"
    case rare = "rare"
    case epic = "epic"
    case legendary = "legendary"
    
    var displayName: String {
        switch self {
        case .common: return "Common"
        case .uncommon: return "Uncommon"
        case .rare: return "Rare"
        case .epic: return "Epic"
        case .legendary: return "Legendary"
        }
    }
    
    var color: String {
        switch self {
        case .common: return "gray"
        case .uncommon: return "green"
        case .rare: return "blue"
        case .epic: return "purple"
        case .legendary: return "orange"
        }
    }
}

// MARK: - Personal Record
struct PersonalRecord: Identifiable, Codable, Sendable {
    let id: UUID
    let exerciseType: ExerciseType
    let recordType: RecordType
    let value: Double
    let unit: String
    let achievedDate: Date
    let previousRecord: Double?
    let improvement: Double?
    let workoutSessionId: UUID
    let notes: String?
    
    init(
        id: UUID = UUID(),
        exerciseType: ExerciseType,
        recordType: RecordType,
        value: Double,
        unit: String,
        achievedDate: Date = Date(),
        previousRecord: Double? = nil,
        improvement: Double? = nil,
        workoutSessionId: UUID,
        notes: String? = nil
    ) {
        self.id = id
        self.exerciseType = exerciseType
        self.recordType = recordType
        self.value = value
        self.unit = unit
        self.achievedDate = achievedDate
        self.previousRecord = previousRecord
        self.improvement = improvement
        self.workoutSessionId = workoutSessionId
        self.notes = notes
    }
}

enum RecordType: String, CaseIterable, Codable, Sendable {
    case maxReps = "max_reps"
    case maxWeight = "max_weight"
    case longestHold = "longest_hold"
    case bestForm = "best_form"
    case fastestTime = "fastest_time"
    case mostSets = "most_sets"
    case totalVolume = "total_volume"
    
    var displayName: String {
        switch self {
        case .maxReps: return "Max Reps"
        case .maxWeight: return "Max Weight"
        case .longestHold: return "Longest Hold"
        case .bestForm: return "Best Form Score"
        case .fastestTime: return "Fastest Time"
        case .mostSets: return "Most Sets"
        case .totalVolume: return "Total Volume"
        }
    }
    
    var icon: String {
        switch self {
        case .maxReps: return "number.circle.fill"
        case .maxWeight: return "scalemass.fill"
        case .longestHold: return "timer.circle.fill"
        case .bestForm: return "star.circle.fill"
        case .fastestTime: return "stopwatch.fill"
        case .mostSets: return "list.number"
        case .totalVolume: return "chart.bar.fill"
        }
    }
}

// MARK: - Form Grade
enum FormGrade: String, CaseIterable, Codable, Sendable {
    case excellent = "excellent"
    case good = "good"
    case fair = "fair"
    case poor = "poor"
    case needsWork = "needs_work"
    
    var displayName: String {
        switch self {
        case .excellent: return "Excellent"
        case .good: return "Good"
        case .fair: return "Fair"
        case .poor: return "Poor"
        case .needsWork: return "Needs Work"
        }
    }
    
    var color: String {
        switch self {
        case .excellent: return "green"
        case .good: return "blue"
        case .fair: return "yellow"
        case .poor: return "orange"
        case .needsWork: return "red"
        }
    }
    
    var letter: String {
        switch self {
        case .excellent: return "A+"
        case .good: return "A"
        case .fair: return "B"
        case .poor: return "C"
        case .needsWork: return "D"
        }
    }
}
