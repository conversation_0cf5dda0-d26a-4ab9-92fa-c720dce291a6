import Foundation
import CoreData

class CoreDataManager: ObservableObject {
    nonisolated(unsafe) static let shared = CoreDataManager()
    
    private init() {}
    
    lazy var persistentContainer: NSPersistentContainer = {
        let container = NSPersistentContainer(name: "MotionFitPro")
        container.loadPersistentStores { _, error in
            if let error = error {
                Logger.shared.error("Core Data failed to load: \(error.localizedDescription)", category: .data)
                
                // Try to delete the corrupted store and recreate it
                if let storeURL = container.persistentStoreCoordinator.persistentStores.first?.url {
                    do {
                        try container.persistentStoreCoordinator.destroyPersistentStore(at: storeURL, ofType: NSSQLiteStoreType, options: nil)
                        try container.persistentStoreCoordinator.addPersistentStore(ofType: NSSQLiteStoreType, configurationName: nil, at: storeURL, options: nil)
                        Logger.shared.info("Core Data store recreated successfully", category: .data)
                    } catch {
                        Logger.shared.error("Failed to recreate Core Data store: \(error)", category: .data)
                        // Continue with in-memory store as fallback
                    }
                }
            }
        }
        container.viewContext.automaticallyMergesChangesFromParent = true
        return container
    }()
    
    var context: NSManagedObjectContext {
        return persistentContainer.viewContext
    }
    
    func save() {
        if context.hasChanges {
            do {
                try context.save()
            } catch {
                print("Failed to save Core Data context: \(error)")
            }
        }
    }
}