import Foundation
import SwiftData

@MainActor
class SampleDataGenerator {

    /// Generates comprehensive sample data for development and testing
    static func generateSampleData(modelContext: ModelContext) async {
        Logger.shared.info("Generating comprehensive sample data", category: .data)

        // Check if data already exists
        let descriptor = FetchDescriptor<UserProfile>()
        let existingUsers = try? modelContext.fetch(descriptor)

        if let users = existingUsers, !users.isEmpty {
            Logger.shared.info("Sample data already exists, skipping generation", category: .data)
            return
        }

        // Generate sample user profile
        let sampleUser = createSampleUser()
        modelContext.insert(sampleUser)

        // Generate multiple workout sessions
        let workoutSessions = createSampleWorkoutSessions(for: sampleUser.id)
        for session in workoutSessions {
            modelContext.insert(session)

            // Generate exercise performances for each session
            let exercises = createSampleExercisePerformances(for: session.id)
            for exercise in exercises {
                modelContext.insert(exercise)

                // Generate set performances
                let sets = createSampleSetPerformances(for: exercise.id, targetSets: exercise.targetSets)
                for set in sets {
                    modelContext.insert(set)

                    // Generate rep performances
                    let reps = createSampleRepPerformances(for: set.id, targetReps: set.targetReps)
                    for rep in reps {
                        modelContext.insert(rep)
                    }
                }
            }
        }

        // Save all data
        try? modelContext.save()
        Logger.shared.info("Sample data generation complete", category: .data)
    }

    /// Generates minimal sample data for quick testing
    static func generateMinimalSampleData(modelContext: ModelContext) async {
        Logger.shared.info("Generating minimal sample data", category: .data)

        // Check if data already exists
        let descriptor = FetchDescriptor<UserProfile>()
        let existingUsers = try? modelContext.fetch(descriptor)

        if let users = existingUsers, !users.isEmpty {
            Logger.shared.info("Sample data already exists, skipping generation", category: .data)
            return
        }

        // Generate single user
        let user = createSampleUser()
        modelContext.insert(user)

        // Generate single workout session
        let workout = createQuickWorkoutSession(for: user.id)
        modelContext.insert(workout)

        // Generate basic exercises
        let exercises = createBasicExercises(for: workout.id)
        for exercise in exercises {
            modelContext.insert(exercise)
        }

        // Save data
        try? modelContext.save()
        Logger.shared.info("Minimal sample data generation complete", category: .data)
    }

    // MARK: - Private Helper Methods

    private static func createSampleUser() -> UserProfile {
        return UserProfile(
            name: "Alex Johnson",
            age: 28,
            height: 1.75, // 175 cm
            weight: 70.0, // 70 kg
            fitnessLevel: "intermediate",
            preferredCoachingStyle: "encouraging"
        )
    }

    private static func createSampleWorkoutSessions(for userID: UUID) -> [WorkoutSession] {
        var sessions: [WorkoutSession] = []

        // Create 10 workout sessions over the past month
        for i in 0..<10 {
            let daysAgo = Double(i * 3) // Every 3 days
            let startTime = Calendar.current.date(byAdding: .day, value: -Int(daysAgo), to: Date()) ?? Date()

            let session = WorkoutSession(userID: userID, workoutType: getRandomWorkoutType())
            session.startTime = startTime
            session.endTime = startTime.addingTimeInterval(Double.random(in: 1800...3600)) // 30-60 minutes
            session.totalDuration = session.endTime?.timeIntervalSince(session.startTime) ?? 0
            session.totalReps = Int.random(in: 50...150)
            session.averageFormScore = Double.random(in: 70...95)
            session.caloriesBurned = Double.random(in: 200...500)
            session.status = .completed

            sessions.append(session)
        }

        return sessions
    }

    private static func createQuickWorkoutSession(for userID: UUID) -> WorkoutSession {
        let session = WorkoutSession(userID: userID, workoutType: "Quick Strength Training")
        session.endTime = session.startTime.addingTimeInterval(1800) // 30 minutes
        session.totalDuration = 1800
        session.totalReps = 45
        session.averageFormScore = 85.0
        session.caloriesBurned = 250.0
        session.status = .completed
        return session
    }

    private static func createSampleExercisePerformances(for workoutSessionID: UUID) -> [ExercisePerformance] {
        let exerciseTypes: [ExerciseType] = [.squat, .pushUp, .plank]
        var performances: [ExercisePerformance] = []

        for (index, exerciseType) in exerciseTypes.enumerated() {
            let performance = ExercisePerformance(
                workoutSessionID: workoutSessionID,
                exerciseType: exerciseType,
                targetSets: 3
            )
            performance.startTime = Date().addingTimeInterval(Double(index * 300)) // 5 minutes apart
            performance.endTime = performance.startTime.addingTimeInterval(Double.random(in: 180...300))
            performance.duration = performance.endTime?.timeIntervalSince(performance.startTime) ?? 0
            performance.totalReps = Int.random(in: 10...20)
            performance.completedSets = 3
            performance.averageFormScore = Double.random(in: 75...95)
            performance.bestFormScore = Double.random(in: 85...100)
            performance.worstFormScore = Double.random(in: 60...80)
            performance.caloriesBurned = Double.random(in: 50...100)
            performance.status = .completed

            performances.append(performance)
        }

        return performances
    }

    private static func createBasicExercises(for workoutSessionID: UUID) -> [ExercisePerformance] {
        let basicExercises: [ExerciseType] = [.squat, .pushUp, .plank]
        var exercises: [ExercisePerformance] = []

        for exerciseType in basicExercises {
            let exercise = ExercisePerformance(
                workoutSessionID: workoutSessionID,
                exerciseType: exerciseType,
                targetSets: 3
            )
            exercise.completedSets = 3
            exercise.totalReps = 15
            exercise.averageFormScore = 80.0
            exercise.status = .completed

            exercises.append(exercise)
        }

        return exercises
    }

    private static func createSampleSetPerformances(for exercisePerformanceID: UUID, targetSets: Int) -> [SetPerformance] {
        var sets: [SetPerformance] = []

        for setNumber in 1...targetSets {
            let set = SetPerformance(
                exercisePerformanceID: exercisePerformanceID,
                setNumber: setNumber,
                targetReps: Int.random(in: 10...15)
            )
            set.endTime = set.startTime.addingTimeInterval(Double.random(in: 60...120))
            set.duration = set.endTime?.timeIntervalSince(set.startTime) ?? 0
            set.completedReps = set.targetReps
            set.averageFormScore = Double.random(in: 70...95)
            set.restDuration = setNumber < targetSets ? Double.random(in: 60...90) : 0
            set.status = .completed

            sets.append(set)
        }

        return sets
    }

    private static func createSampleRepPerformances(for setPerformanceID: UUID, targetReps: Int) -> [RepPerformance] {
        var reps: [RepPerformance] = []

        for repNumber in 1...targetReps {
            let rep = RepPerformance(
                setPerformanceID: setPerformanceID,
                repNumber: repNumber,
                duration: Double.random(in: 2...5),
                formScore: Double.random(in: 70...95)
            )
            rep.peakVelocity = Double.random(in: 0.5...2.0)
            rep.rangeOfMotion = Double.random(in: 80...100)
            rep.steadiness = Double.random(in: 70...95)
            rep.timing = Double.random(in: 80...100)
            rep.feedback = generateRandomFeedback()

            reps.append(rep)
        }

        return reps
    }

    private static func getRandomWorkoutType() -> String {
        let workoutTypes = [
            "Strength Training",
            "HIIT Workout",
            "Cardio Session",
            "Flexibility Training",
            "Full Body Workout",
            "Upper Body Focus",
            "Lower Body Focus"
        ]
        return workoutTypes.randomElement() ?? "General Workout"
    }

    private static func generateRandomFeedback() -> String? {
        let feedbackOptions = [
            "Great form!",
            "Keep your back straight",
            "Good depth on that squat",
            "Excellent control",
            "Try to go a bit slower",
            "Perfect range of motion",
            nil, nil, nil // Some reps don't have feedback
        ]
        return feedbackOptions.randomElement() ?? nil
    }
}