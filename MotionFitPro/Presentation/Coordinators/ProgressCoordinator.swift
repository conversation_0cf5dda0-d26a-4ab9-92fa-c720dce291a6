import SwiftUI
import Combine
import Charts

@MainActor
class ProgressCoordinator: ObservableObject {
    @Published var selectedTimeframe: ProgressTimeframe = .weekly
    @Published var selectedMetric: ProgressMetric = .workouts
    @Published var workoutHistory: [WorkoutHistoryData] = []
    @Published var achievements: [Achievement] = []
    @Published var personalRecords: [PersonalRecord] = []
    @Published var progressStats: ProgressStats = ProgressStats()
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var showingShareSheet = false
    @Published var shareableContent: ShareableContent?
    
    private let logger = Logger.shared
    private var cancellables = Set<AnyCancellable>()
    
    enum ProgressTimeframe: String, CaseIterable {
        case weekly = "Week"
        case monthly = "Month"
        case yearly = "Year"
        case allTime = "All Time"
        
        var dateRange: DateInterval {
            let calendar = Calendar.current
            let now = Date()
            
            switch self {
            case .weekly:
                let startOfWeek = calendar.dateInterval(of: .weekOfYear, for: now)?.start ?? now
                return DateInterval(start: startOfWeek, end: now)
            case .monthly:
                let startOfMonth = calendar.dateInterval(of: .month, for: now)?.start ?? now
                return DateInterval(start: startOfMonth, end: now)
            case .yearly:
                let startOfYear = calendar.dateInterval(of: .year, for: now)?.start ?? now
                return DateInterval(start: startOfYear, end: now)
            case .allTime:
                // Arbitrary start date for all time
                let startDate = calendar.date(byAdding: .year, value: -5, to: now) ?? now
                return DateInterval(start: startDate, end: now)
            }
        }
    }
    
    enum ProgressMetric: String, CaseIterable {
        case workouts = "Workouts"
        case duration = "Duration"
        case calories = "Calories"
        case formScore = "Form Score"
        
        var icon: String {
            switch self {
            case .workouts: return "figure.run"
            case .duration: return "clock.fill"
            case .calories: return "flame.fill"
            case .formScore: return "star.fill"
            }
        }
        
        var unit: String {
            switch self {
            case .workouts: return "workouts"
            case .duration: return "minutes"
            case .calories: return "calories"
            case .formScore: return "score"
            }
        }
    }
    
    init() {
        loadProgressData()
        setupMockData() // Remove in production
    }
    
    // MARK: - Data Loading
    
    func loadProgressData() {
        isLoading = true
        
        Task {
            do {
                // Simulate loading delay
                try await Task.sleep(nanoseconds: 1_000_000_000)
                
                await MainActor.run {
                    // In production, load from your data store
                    self.isLoading = false
                    self.logger.info("Progress data loaded", category: .data)
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "Failed to load progress data"
                    self.isLoading = false
                    self.logger.error("Failed to load progress data: \(error)", category: .data)
                }
            }
        }
    }
    
    func refreshData() {
        logger.info("Refreshing progress data", category: .data)
        loadProgressData()
    }
    
    // MARK: - Chart Data
    
    func getChartData(for timeframe: ProgressTimeframe, metric: ProgressMetric) -> [ChartDataPoint] {
        let filteredHistory = workoutHistory.filter { workout in
            timeframe.dateRange.contains(workout.date)
        }
        
        let calendar = Calendar.current
        var dataPoints: [ChartDataPoint] = []
        
        switch timeframe {
        case .weekly:
            // Group by day
            let groupedByDay = Dictionary(grouping: filteredHistory) { workout in
                calendar.startOfDay(for: workout.date)
            }
            
            for i in 0..<7 {
                let date = calendar.date(byAdding: .day, value: -i, to: Date()) ?? Date()
                let dayStart = calendar.startOfDay(for: date)
                let workouts = groupedByDay[dayStart] ?? []
                
                let value = calculateMetricValue(workouts: workouts, metric: metric)
                dataPoints.append(ChartDataPoint(
                    date: dayStart,
                    value: value,
                    label: calendar.component(.weekday, from: dayStart)
                ))
            }
            
        case .monthly:
            // Group by week
            let groupedByWeek = Dictionary(grouping: filteredHistory) { workout in
                calendar.dateInterval(of: .weekOfYear, for: workout.date)?.start ?? workout.date
            }
            
            for i in 0..<4 {
                let date = calendar.date(byAdding: .weekOfYear, value: -i, to: Date()) ?? Date()
                let weekStart = calendar.dateInterval(of: .weekOfYear, for: date)?.start ?? date
                let workouts = groupedByWeek[weekStart] ?? []
                
                let value = calculateMetricValue(workouts: workouts, metric: metric)
                dataPoints.append(ChartDataPoint(
                    date: weekStart,
                    value: value,
                    label: calendar.component(.weekOfYear, from: weekStart)
                ))
            }
            
        case .yearly:
            // Group by month
            let groupedByMonth = Dictionary(grouping: filteredHistory) { workout in
                calendar.dateInterval(of: .month, for: workout.date)?.start ?? workout.date
            }
            
            for i in 0..<12 {
                let date = calendar.date(byAdding: .month, value: -i, to: Date()) ?? Date()
                let monthStart = calendar.dateInterval(of: .month, for: date)?.start ?? date
                let workouts = groupedByMonth[monthStart] ?? []
                
                let value = calculateMetricValue(workouts: workouts, metric: metric)
                dataPoints.append(ChartDataPoint(
                    date: monthStart,
                    value: value,
                    label: calendar.component(.month, from: monthStart)
                ))
            }
            
        case .allTime:
            // Group by year
            let groupedByYear = Dictionary(grouping: filteredHistory) { workout in
                calendar.dateInterval(of: .year, for: workout.date)?.start ?? workout.date
            }
            
            for i in 0..<5 {
                let date = calendar.date(byAdding: .year, value: -i, to: Date()) ?? Date()
                let yearStart = calendar.dateInterval(of: .year, for: date)?.start ?? date
                let workouts = groupedByYear[yearStart] ?? []
                
                let value = calculateMetricValue(workouts: workouts, metric: metric)
                dataPoints.append(ChartDataPoint(
                    date: yearStart,
                    value: value,
                    label: calendar.component(.year, from: yearStart)
                ))
            }
        }
        
        return dataPoints.reversed() // Show oldest to newest
    }
    
    private func calculateMetricValue(workouts: [WorkoutHistoryData], metric: ProgressMetric) -> Double {
        guard !workouts.isEmpty else { return 0 }
        
        switch metric {
        case .workouts:
            return Double(workouts.count)
        case .duration:
            return workouts.reduce(0) { $0 + $1.duration } / 60 // Convert to minutes
        case .calories:
            return Double(workouts.reduce(0) { $0 + $1.caloriesBurned })
        case .formScore:
            return workouts.reduce(0) { $0 + $1.averageFormScore } / Double(workouts.count)
        }
    }
    
    // MARK: - Achievements
    
    func getRecentAchievements() -> [Achievement] {
        return achievements.filter { achievement in
            guard let unlockedDate = achievement.unlockedDate else { return false }
            return Calendar.current.isDate(unlockedDate, equalTo: Date(), toGranularity: .weekOfYear)
        }.sorted { $0.unlockedDate! > $1.unlockedDate! }
    }
    
    func getProgressTowardsGoals() -> [GoalProgress] {
        // Calculate progress towards various fitness goals
        let weeklyWorkouts = workoutHistory.filter { workout in
            Calendar.current.isDate(workout.date, equalTo: Date(), toGranularity: .weekOfYear)
        }.count
        
        let monthlyDuration = workoutHistory.filter { workout in
            Calendar.current.isDate(workout.date, equalTo: Date(), toGranularity: .month)
        }.reduce(0) { $0 + $1.duration }
        
        return [
            GoalProgress(
                title: "Weekly Workouts",
                current: weeklyWorkouts,
                target: 5,
                unit: "workouts",
                color: .blue
            ),
            GoalProgress(
                title: "Monthly Minutes",
                current: Int(monthlyDuration / 60),
                target: 300,
                unit: "minutes",
                color: .green
            ),
            GoalProgress(
                title: "Form Score",
                current: Int(progressStats.averageFormScore),
                target: 90,
                unit: "score",
                color: .orange
            )
        ]
    }
    
    // MARK: - Sharing
    
    func shareProgress() {
        logger.info("Sharing progress", category: .ui)
        
        let content = ShareableContent(
            title: "My MotionFitPro Progress",
            summary: generateProgressSummary(),
            imageData: nil // Would generate chart image
        )
        
        shareableContent = content
        showingShareSheet = true
    }
    
    private func generateProgressSummary() -> String {
        let totalWorkouts = workoutHistory.count
        let totalDuration = workoutHistory.reduce(0) { $0 + $1.duration }
        let totalCalories = workoutHistory.reduce(0) { $0 + $1.caloriesBurned }
        
        return """
        🏋️‍♀️ Fitness Progress Summary
        
        📊 Total Workouts: \(totalWorkouts)
        ⏱️ Total Time: \(Int(totalDuration / 60)) minutes
        🔥 Calories Burned: \(totalCalories)
        ⭐ Average Form Score: \(Int(progressStats.averageFormScore))%
        
        Keep pushing towards your goals! 💪
        
        #MotionFitPro #FitnessJourney
        """
    }
    
    // MARK: - Mock Data Setup
    
    private func setupMockData() {
        // Generate mock workout history
        workoutHistory = generateMockWorkoutHistory()
        
        // Generate mock achievements
        achievements = generateMockAchievements()
        
        // Generate mock personal records
        personalRecords = generateMockPersonalRecords()
        
        // Calculate progress stats
        calculateProgressStats()
    }
    
    private func generateMockWorkoutHistory() -> [WorkoutHistoryData] {
        var history: [WorkoutHistoryData] = []
        let calendar = Calendar.current
        
        for i in 0..<30 {
            let date = calendar.date(byAdding: .day, value: -i, to: Date()) ?? Date()
            
            // Skip some days to make it realistic
            if Int.random(in: 1...10) <= 7 {
                history.append(WorkoutHistoryData(
                    id: UUID(),
                    date: date,
                    name: ["Morning Flow", "Power Session", "Quick Burst", "Full Body", "Core Focus"].randomElement() ?? "Workout",
                    exercises: Int.random(in: 3...8),
                    duration: TimeInterval.random(in: 300...2400), // 5-40 minutes
                    caloriesBurned: Int.random(in: 50...300),
                    averageFormScore: Double.random(in: 75...95)
                ))
            }
        }
        
        return history.sorted { $0.date > $1.date }
    }
    
    private func generateMockAchievements() -> [Achievement] {
        let allAchievements = [
            Achievement(
                id: UUID(),
                title: "First Workout",
                description: "Complete your first workout",
                icon: "star.fill",
                color: .yellow,
                isUnlocked: true,
                unlockedDate: Calendar.current.date(byAdding: .day, value: -25, to: Date())
            ),
            Achievement(
                id: UUID(),
                title: "Perfect Form",
                description: "Achieve 100% form score",
                icon: "target",
                color: .green,
                isUnlocked: true,
                unlockedDate: Calendar.current.date(byAdding: .day, value: -15, to: Date())
            ),
            Achievement(
                id: UUID(),
                title: "Consistency King",
                description: "Work out 7 days in a row",
                icon: "calendar",
                color: .blue,
                isUnlocked: true,
                unlockedDate: Calendar.current.date(byAdding: .day, value: -5, to: Date())
            ),
            Achievement(
                id: UUID(),
                title: "Calorie Crusher",
                description: "Burn 1000 calories in a week",
                icon: "flame.fill",
                color: .orange,
                isUnlocked: false,
                unlockedDate: nil
            ),
            Achievement(
                id: UUID(),
                title: "Marathon Master",
                description: "Complete 100 workouts",
                icon: "trophy.fill",
                color: .purple,
                isUnlocked: false,
                unlockedDate: nil
            )
        ]
        
        return allAchievements
    }
    
    private func generateMockPersonalRecords() -> [PersonalRecord] {
        return [
            PersonalRecord(
                id: UUID(),
                exercise: "Squat",
                record: "50 perfect reps",
                date: Calendar.current.date(byAdding: .day, value: -10, to: Date()) ?? Date(),
                isNewRecord: false
            ),
            PersonalRecord(
                id: UUID(),
                exercise: "Push-up",
                record: "30 perfect reps",
                date: Calendar.current.date(byAdding: .day, value: -5, to: Date()) ?? Date(),
                isNewRecord: true
            ),
            PersonalRecord(
                id: UUID(),
                exercise: "Plank",
                record: "2:45 duration",
                date: Calendar.current.date(byAdding: .day, value: -2, to: Date()) ?? Date(),
                isNewRecord: true
            )
        ]
    }
    
    private func calculateProgressStats() {
        progressStats = ProgressStats(
            totalWorkouts: workoutHistory.count,
            totalDuration: workoutHistory.reduce(0) { $0 + $1.duration },
            totalCalories: workoutHistory.reduce(0) { $0 + $1.caloriesBurned },
            averageFormScore: workoutHistory.isEmpty ? 0 : 
                workoutHistory.reduce(0) { $0 + $1.averageFormScore } / Double(workoutHistory.count),
            currentStreak: calculateCurrentStreak(),
            longestStreak: calculateLongestStreak()
        )
    }
    
    private func calculateCurrentStreak() -> Int {
        let calendar = Calendar.current
        var streak = 0
        var currentDate = Date()
        
        while true {
            let hasWorkout = workoutHistory.contains { workout in
                calendar.isDate(workout.date, inSameDayAs: currentDate)
            }
            
            if hasWorkout {
                streak += 1
                currentDate = calendar.date(byAdding: .day, value: -1, to: currentDate) ?? currentDate
            } else {
                break
            }
        }
        
        return streak
    }
    
    private func calculateLongestStreak() -> Int {
        // Simplified calculation for mock data
        return Int.random(in: 5...15)
    }
}

// MARK: - Supporting Types

struct WorkoutHistoryData: Identifiable {
    let id: UUID
    let date: Date
    let name: String
    let exercises: Int
    let duration: TimeInterval
    let caloriesBurned: Int
    let averageFormScore: Double
    
    var formattedDuration: String {
        let minutes = Int(duration / 60)
        let seconds = Int(duration.truncatingRemainder(dividingBy: 60))
        if minutes > 0 {
            return "\(minutes)m \(seconds)s"
        } else {
            return "\(seconds)s"
        }
    }
}

struct Achievement: Identifiable {
    let id: UUID
    let title: String
    let description: String
    let icon: String
    let color: Color
    let isUnlocked: Bool
    let unlockedDate: Date?
}

struct PersonalRecord: Identifiable {
    let id: UUID
    let exercise: String
    let record: String
    let date: Date
    let isNewRecord: Bool
}

struct ProgressStats {
    let totalWorkouts: Int
    let totalDuration: TimeInterval
    let totalCalories: Int
    let averageFormScore: Double
    let currentStreak: Int
    let longestStreak: Int
    
    init() {
        self.totalWorkouts = 0
        self.totalDuration = 0
        self.totalCalories = 0
        self.averageFormScore = 0
        self.currentStreak = 0
        self.longestStreak = 0
    }
    
    init(totalWorkouts: Int, totalDuration: TimeInterval, totalCalories: Int, 
         averageFormScore: Double, currentStreak: Int, longestStreak: Int) {
        self.totalWorkouts = totalWorkouts
        self.totalDuration = totalDuration
        self.totalCalories = totalCalories
        self.averageFormScore = averageFormScore
        self.currentStreak = currentStreak
        self.longestStreak = longestStreak
    }
}

struct ChartDataPoint: Identifiable {
    let id = UUID()
    let date: Date
    let value: Double
    let label: Int
}

struct GoalProgress: Identifiable {
    let id = UUID()
    let title: String
    let current: Int
    let target: Int
    let unit: String
    let color: Color
    
    var progress: Double {
        guard target > 0 else { return 0 }
        return min(Double(current) / Double(target), 1.0)
    }
    
    var isComplete: Bool {
        current >= target
    }
}

struct ShareableContent {
    let title: String
    let summary: String
    let imageData: Data?
}