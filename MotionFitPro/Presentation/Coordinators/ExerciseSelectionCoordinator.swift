import SwiftUI
import Combine

@MainActor
class ExerciseSelectionCoordinator: ObservableObject {
    @Published var selectedExercises: [ExerciseData] = []
    @Published var currentView: ExerciseSelectionView = .library
    @Published var selectedCategory: ExerciseCategory? = nil
    @Published var selectedDifficulty: ExerciseDifficulty? = nil
    @Published var selectedEquipment: Equipment? = nil
    @Published var searchText: String = ""
    @Published var quickWorkoutDuration: QuickWorkoutDuration = .short
    @Published var isCreatingCustomWorkout = false
    @Published var customWorkoutName = ""
    @Published var showingWorkoutPreview = false
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let logger = Logger.shared
    private var cancellables = Set<AnyCancellable>()
    
    // Mock exercise library
    let exerciseLibrary: [ExerciseData] = [
        ExerciseData(
            id: UUID(),
            name: "Squat",
            type: .squat,
            category: .strength,
            difficulty: .beginner,
            equipment: .none,
            duration: 30,
            description: "Classic lower body exercise that targets quadriceps, hamstrings, and glutes",
            instructions: ["Stand with feet shoulder-width apart", "Lower your body by bending knees", "Keep back straight", "Return to starting position"],
            muscleGroups: [.quadriceps, .hamstrings, .glutes],
            caloriesBurned: 8,
            videoPreviewURL: nil,
            thumbnailName: "squat_thumbnail"
        ),
        ExerciseData(
            id: UUID(),
            name: "Push-up",
            type: .pushUp,
            category: .strength,
            difficulty: .intermediate,
            equipment: .none,
            duration: 30,
            description: "Upper body exercise targeting chest, shoulders, and triceps",
            instructions: ["Start in plank position", "Lower chest to ground", "Push back up", "Keep body straight"],
            muscleGroups: [.chest, .shoulders, .triceps],
            caloriesBurned: 7,
            videoPreviewURL: nil,
            thumbnailName: "pushup_thumbnail"
        ),
        ExerciseData(
            id: UUID(),
            name: "Plank",
            type: .plank,
            category: .strength,
            difficulty: .beginner,
            equipment: .none,
            duration: 60,
            description: "Core strengthening exercise that improves stability",
            instructions: ["Start in push-up position", "Hold position on forearms", "Keep body straight", "Breathe normally"],
            muscleGroups: [.core, .shoulders],
            caloriesBurned: 5,
            videoPreviewURL: nil,
            thumbnailName: "plank_thumbnail"
        ),
        ExerciseData(
            id: UUID(),
            name: "Jumping Jacks",
            type: .jumpingJack,
            category: .cardio,
            difficulty: .beginner,
            equipment: .none,
            duration: 30,
            description: "Full-body cardio exercise that increases heart rate",
            instructions: ["Start with feet together", "Jump feet apart while raising arms", "Jump back to starting position", "Maintain steady rhythm"],
            muscleGroups: [.fullBody],
            caloriesBurned: 10,
            videoPreviewURL: nil,
            thumbnailName: "jumping_jacks_thumbnail"
        ),
        ExerciseData(
            id: UUID(),
            name: "Lunges",
            type: .squat, // Using squat type for now
            category: .strength,
            difficulty: .intermediate,
            equipment: .none,
            duration: 45,
            description: "Unilateral leg exercise that improves balance and strength",
            instructions: ["Step forward with one leg", "Lower hips until both knees at 90°", "Push back to starting position", "Alternate legs"],
            muscleGroups: [.quadriceps, .hamstrings, .glutes],
            caloriesBurned: 9,
            videoPreviewURL: nil,
            thumbnailName: "lunges_thumbnail"
        ),
        ExerciseData(
            id: UUID(),
            name: "Mountain Climbers",
            type: .jumpingJack, // Using jumping jack type for cardio
            category: .cardio,
            difficulty: .intermediate,
            equipment: .none,
            duration: 30,
            description: "High-intensity cardio exercise with core engagement",
            instructions: ["Start in plank position", "Alternate bringing knees to chest", "Maintain fast pace", "Keep hips level"],
            muscleGroups: [.core, .shoulders, .legs],
            caloriesBurned: 12,
            videoPreviewURL: nil,
            thumbnailName: "mountain_climbers_thumbnail"
        )
    ]
    
    enum ExerciseSelectionView {
        case library
        case quickWorkout
        case customBuilder
        case workoutPreview
    }
    
    enum QuickWorkoutDuration: String, CaseIterable {
        case short = "5 minutes"
        case medium = "15 minutes"
        case long = "30 minutes"
        
        var exercises: Int {
            switch self {
            case .short: return 3
            case .medium: return 6
            case .long: return 10
            }
        }
        
        var restBetweenExercises: TimeInterval {
            switch self {
            case .short: return 30
            case .medium: return 45
            case .long: return 60
            }
        }
    }
    
    init() {
        setupSearchObserver()
    }
    
    // MARK: - Navigation
    
    func showLibrary() {
        withAnimation(.easeInOut(duration: 0.3)) {
            currentView = .library
        }
    }
    
    func showQuickWorkout() {
        withAnimation(.easeInOut(duration: 0.3)) {
            currentView = .quickWorkout
        }
    }
    
    func showCustomBuilder() {
        withAnimation(.easeInOut(duration: 0.3)) {
            currentView = .customBuilder
            isCreatingCustomWorkout = true
        }
    }
    
    func showWorkoutPreview() {
        withAnimation(.easeInOut(duration: 0.3)) {
            currentView = .workoutPreview
            showingWorkoutPreview = true
        }
    }
    
    // MARK: - Exercise Management
    
    func addExercise(_ exercise: ExerciseData) {
        guard !selectedExercises.contains(where: { $0.id == exercise.id }) else {
            return
        }
        
        withAnimation(.bouncy(duration: 0.4)) {
            selectedExercises.append(exercise)
        }
        
        logger.info("Added exercise: \(exercise.name)", category: .ui)
        
        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
    
    func removeExercise(_ exercise: ExerciseData) {
        withAnimation(.easeOut(duration: 0.3)) {
            selectedExercises.removeAll { $0.id == exercise.id }
        }
        
        logger.info("Removed exercise: \(exercise.name)", category: .ui)
    }
    
    func moveExercise(from source: IndexSet, to destination: Int) {
        selectedExercises.move(fromOffsets: source, toOffset: destination)
        logger.info("Reordered exercises", category: .ui)
    }
    
    func clearSelectedExercises() {
        withAnimation(.easeOut(duration: 0.3)) {
            selectedExercises.removeAll()
        }
    }
    
    // MARK: - Quick Workout Generation
    
    func generateQuickWorkout() async {
        isLoading = true
        defer { isLoading = false }
        
        logger.info("Generating quick workout: \(quickWorkoutDuration.rawValue)", category: .ui)
        
        // Simulate network delay
        try? await Task.sleep(nanoseconds: 1_000_000_000)
        
        await MainActor.run {
            let filteredExercises = getFilteredExercises()
            let targetCount = quickWorkoutDuration.exercises
            
            // Select exercises based on variety and difficulty
            var selectedForWorkout: [ExerciseData] = []
            
            // Ensure variety in categories
            let categories = Set(filteredExercises.map { $0.category })
            for category in categories {
                let categoryExercises = filteredExercises.filter { $0.category == category }
                if let exercise = categoryExercises.randomElement() {
                    selectedForWorkout.append(exercise)
                }
                
                if selectedForWorkout.count >= targetCount {
                    break
                }
            }
            
            // Fill remaining slots randomly
            while selectedForWorkout.count < targetCount {
                if let exercise = filteredExercises.randomElement(),
                   !selectedForWorkout.contains(where: { $0.id == exercise.id }) {
                    selectedForWorkout.append(exercise)
                }
            }
            
            selectedExercises = Array(selectedForWorkout.prefix(targetCount))
            showWorkoutPreview()
        }
    }
    
    // MARK: - Filtering
    
    func getFilteredExercises() -> [ExerciseData] {
        var filtered = exerciseLibrary
        
        // Apply category filter
        if let category = selectedCategory {
            filtered = filtered.filter { $0.category == category }
        }
        
        // Apply difficulty filter
        if let difficulty = selectedDifficulty {
            filtered = filtered.filter { $0.difficulty == difficulty }
        }
        
        // Apply equipment filter
        if let equipment = selectedEquipment {
            filtered = filtered.filter { $0.equipment == equipment }
        }
        
        // Apply search filter
        if !searchText.isEmpty {
            filtered = filtered.filter { exercise in
                exercise.name.localizedCaseInsensitiveContains(searchText) ||
                exercise.description.localizedCaseInsensitiveContains(searchText) ||
                exercise.muscleGroups.contains { $0.rawValue.localizedCaseInsensitiveContains(searchText) }
            }
        }
        
        return filtered
    }
    
    func clearFilters() {
        withAnimation(.easeInOut(duration: 0.3)) {
            selectedCategory = nil
            selectedDifficulty = nil
            selectedEquipment = nil
            searchText = ""
        }
    }
    
    // MARK: - Custom Workout
    
    func saveCustomWorkout() async {
        guard !customWorkoutName.isEmpty, !selectedExercises.isEmpty else {
            errorMessage = "Please provide a workout name and select exercises"
            return
        }
        
        isLoading = true
        defer { isLoading = false }
        
        logger.info("Saving custom workout: \(customWorkoutName)", category: .ui)
        
        // Simulate save operation
        try? await Task.sleep(nanoseconds: 500_000_000)
        
        await MainActor.run {
            // Here you would save to your data store
            isCreatingCustomWorkout = false
            customWorkoutName = ""
            
            // Success feedback
            let successFeedback = UINotificationFeedbackGenerator()
            successFeedback.notificationOccurred(.success)
        }
    }
    
    // MARK: - Private Methods
    
    private func setupSearchObserver() {
        $searchText
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .sink { [weak self] _ in
                self?.objectWillChange.send()
            }
            .store(in: &cancellables)
    }
}

// MARK: - Supporting Types

struct ExerciseData: Identifiable, Hashable {
    let id: UUID
    let name: String
    let type: ExerciseType
    let category: ExerciseCategory
    let difficulty: ExerciseDifficulty
    let equipment: Equipment
    let duration: TimeInterval // in seconds
    let description: String
    let instructions: [String]
    let muscleGroups: [MuscleGroup]
    let caloriesBurned: Int // per minute
    let videoPreviewURL: URL?
    let thumbnailName: String
    
    var estimatedCalories: Int {
        Int(duration / 60) * caloriesBurned
    }
    
    var formattedDuration: String {
        if duration < 60 {
            return "\(Int(duration))s"
        } else {
            let minutes = Int(duration / 60)
            let seconds = Int(duration.truncatingRemainder(dividingBy: 60))
            return seconds > 0 ? "\(minutes)m \(seconds)s" : "\(minutes)m"
        }
    }
}

enum MuscleGroup: String, CaseIterable {
    case chest = "Chest"
    case shoulders = "Shoulders"
    case triceps = "Triceps"
    case biceps = "Biceps"
    case back = "Back"
    case core = "Core"
    case quadriceps = "Quadriceps"
    case hamstrings = "Hamstrings"
    case glutes = "Glutes"
    case calves = "Calves"
    case legs = "Legs"
    case fullBody = "Full Body"
    
    var icon: String {
        switch self {
        case .chest: return "figure.strengthtraining.traditional"
        case .shoulders: return "figure.arms.open"
        case .triceps, .biceps: return "figure.strengthtraining.traditional"
        case .back: return "figure.strengthtraining.traditional"
        case .core: return "figure.core.training"
        case .quadriceps, .hamstrings, .glutes, .calves, .legs: return "figure.squat"
        case .fullBody: return "figure.run"
        }
    }
}