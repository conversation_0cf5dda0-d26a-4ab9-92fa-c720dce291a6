import SwiftUI
import Combine
import AVFoundation

@MainActor
class WorkoutCoordinator: ObservableObject {
    @Published var currentState: WorkoutState = .preparing
    @Published var currentExerciseIndex: Int = 0
    @Published var currentSet: Int = 1
    @Published var currentRep: Int = 0
    @Published var targetReps: Int = 12
    @Published var targetSets: Int = 3
    @Published var workoutTime: TimeInterval = 0
    @Published var restTimeRemaining: TimeInterval = 0
    @Published var exerciseTime: TimeInterval = 0
    @Published var isRestingBetweenSets: Bool = false
    @Published var isPaused: Bool = false
    @Published var showingCelebration: Bool = false
    @Published var formFeedback: [FormFeedback] = []
    @Published var workoutPerformance: WorkoutPerformance = WorkoutPerformance()
    @Published var emergencyStopRequested: Bool = false
    @Published var showingExitConfirmation: Bool = false
    
    // Workout configuration
    private(set) var exercises: [ExerciseData] = []
    private(set) var workoutSettings: WorkoutSettings = WorkoutSettings()
    
    // Timers
    private var workoutTimer: Timer?
    private var exerciseTimer: Timer?
    private var restTimer: Timer?
    
    // Dependencies
    private let arSessionManager: ARSessionManager
    private let mlProcessingManager: MLProcessingManager
    private let audioManager: AudioManager
    private let hapticManager = HapticManager.shared
    private let logger = Logger.shared
    
    private var cancellables = Set<AnyCancellable>()
    
    enum WorkoutState {
        case preparing
        case exerciseIntro
        case active
        case resting
        case setComplete
        case exerciseComplete
        case workoutComplete
        case paused
        case emergencyStop
    }
    
    init(
        exercises: [ExerciseData],
        settings: WorkoutSettings = WorkoutSettings(),
        arSessionManager: ARSessionManager,
        mlProcessingManager: MLProcessingManager,
        audioManager: AudioManager
    ) {
        self.exercises = exercises
        self.workoutSettings = settings
        self.arSessionManager = arSessionManager
        self.mlProcessingManager = mlProcessingManager
        self.audioManager = audioManager
        
        setupBindings()
    }
    
    // MARK: - Workout Control
    
    func startWorkout() {
        logger.info("Starting workout with \(exercises.count) exercises", category: .workout)
        
        currentState = .preparing
        resetWorkoutState()
        startWorkoutTimer()
        
        // Start AR session
        arSessionManager.startSession()
        mlProcessingManager.startProcessing()
        
        // Begin with first exercise
        prepareNextExercise()
    }
    
    func pauseWorkout() {
        guard currentState == .active else { return }
        
        logger.info("Pausing workout", category: .workout)
        isPaused = true
        currentState = .paused
        
        pauseAllTimers()
        audioManager.pauseAudio()
        
        // Haptic feedback
        hapticManager.triggerWarning()
    }
    
    func resumeWorkout() {
        guard currentState == .paused else { return }
        
        logger.info("Resuming workout", category: .workout)
        isPaused = false
        currentState = .active
        
        resumeAllTimers()
        audioManager.resumeAudio()
        
        // Haptic feedback
        hapticManager.triggerSuccess()
    }
    
    func stopWorkout() {
        logger.info("Stopping workout", category: .workout)
        
        currentState = .workoutComplete
        stopAllTimers()
        
        // Stop AR session
        arSessionManager.stopSession()
        mlProcessingManager.stopProcessing()
        
        // Save workout data
        saveWorkoutSession()
    }
    
    func requestEmergencyStop() {
        logger.warning("Emergency stop requested", category: .workout)
        emergencyStopRequested = true
        showingExitConfirmation = true
    }
    
    func confirmEmergencyStop() {
        currentState = .emergencyStop
        stopAllTimers()
        
        // Stop AR session immediately
        arSessionManager.stopSession()
        mlProcessingManager.stopProcessing()
        
        // Emergency haptic pattern
        hapticManager.triggerError()
    }
    
    // MARK: - Exercise Flow
    
    private func prepareNextExercise() {
        guard currentExerciseIndex < exercises.count else {
            completeWorkout()
            return
        }
        
        let exercise = exercises[currentExerciseIndex]
        logger.info("Preparing exercise: \(exercise.name)", category: .workout)
        
        currentState = .exerciseIntro
        currentSet = 1
        currentRep = 0
        exerciseTime = 0
        
        // Configure exercise-specific settings
        configureExerciseSettings(for: exercise)
        
        // Show exercise introduction
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.startExercise()
        }
    }
    
    private func startExercise() {
        currentState = .active
        startExerciseTimer()
        
        let exercise = exercises[currentExerciseIndex]
        
        // Voice coaching
        if workoutSettings.enableVoiceCoaching {
            audioManager.speak("Starting \(exercise.name). Get ready!")
        }
        
        logger.info("Started exercise: \(exercise.name)", category: .workout)
    }
    
    func completeRep() {
        currentRep += 1
        
        // Add performance data
        workoutPerformance.addRep(
            exerciseIndex: currentExerciseIndex,
            set: currentSet,
            formScore: calculateCurrentFormScore(),
            duration: exerciseTime
        )
        
        // Haptic feedback
        hapticManager.triggerSuccess()
        
        // Voice coaching
        if workoutSettings.enableVoiceCoaching && currentRep < targetReps {
            let remaining = targetReps - currentRep
            if remaining <= 3 {
                audioManager.speak("\(remaining) more!")
            }
        }
        
        // Check if set is complete
        if currentRep >= targetReps {
            completeSet()
        }
        
        logger.info("Rep completed: \(currentRep)/\(targetReps)", category: .workout)
    }
    
    private func completeSet() {
        currentState = .setComplete
        
        // Celebration for set completion
        showCelebration()
        
        // Voice coaching
        if workoutSettings.enableVoiceCoaching {
            audioManager.speak("Set \(currentSet) complete! Great job!")
        }
        
        // Check if exercise is complete
        if currentSet >= targetSets {
            completeExercise()
        } else {
            // Start rest between sets
            startRestBetweenSets()
        }
        
        logger.info("Set completed: \(currentSet)/\(targetSets)", category: .workout)
    }
    
    private func completeExercise() {
        currentState = .exerciseComplete
        stopExerciseTimer()
        
        let exercise = exercises[currentExerciseIndex]
        logger.info("Exercise completed: \(exercise.name)", category: .workout)
        
        // Voice coaching
        if workoutSettings.enableVoiceCoaching {
            audioManager.speak("\(exercise.name) complete! Well done!")
        }
        
        // Move to next exercise or complete workout
        currentExerciseIndex += 1
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            if self.currentExerciseIndex < self.exercises.count {
                self.startRestBetweenExercises()
            } else {
                self.completeWorkout()
            }
        }
    }
    
    private func completeWorkout() {
        currentState = .workoutComplete
        stopAllTimers()
        
        logger.info("Workout completed!", category: .workout)
        
        // Final celebration
        showCelebration()
        
        // Voice coaching
        if workoutSettings.enableVoiceCoaching {
            audioManager.speak("Workout complete! Amazing effort!")
        }
        
        // Stop AR session
        arSessionManager.stopSession()
        mlProcessingManager.stopProcessing()
        
        // Save workout data
        saveWorkoutSession()
        
        // Success haptic pattern
        hapticManager.triggerSuccess()
    }
    
    // MARK: - Rest Management
    
    private func startRestBetweenSets() {
        isRestingBetweenSets = true
        currentState = .resting
        restTimeRemaining = workoutSettings.restBetweenSets
        
        startRestTimer()
        
        // Voice coaching
        if workoutSettings.enableVoiceCoaching {
            audioManager.speak("Take a \(Int(workoutSettings.restBetweenSets)) second rest")
        }
        
        logger.info("Starting rest between sets: \(workoutSettings.restBetweenSets)s", category: .workout)
    }
    
    private func startRestBetweenExercises() {
        isRestingBetweenSets = false
        currentState = .resting
        restTimeRemaining = workoutSettings.restBetweenExercises
        
        startRestTimer()
        
        let nextExercise = exercises[currentExerciseIndex]
        
        // Voice coaching
        if workoutSettings.enableVoiceCoaching {
            audioManager.speak("Next exercise: \(nextExercise.name). Rest for \(Int(workoutSettings.restBetweenExercises)) seconds")
        }
        
        logger.info("Starting rest between exercises: \(workoutSettings.restBetweenExercises)s", category: .workout)
    }
    
    private func completeRest() {
        restTimeRemaining = 0
        
        if isRestingBetweenSets {
            // Start next set
            currentSet += 1
            currentRep = 0
            exerciseTime = 0
            startExercise()
        } else {
            // Start next exercise
            prepareNextExercise()
        }
        
        logger.info("Rest completed", category: .workout)
    }
    
    func skipRest() {
        guard currentState == .resting else { return }
        
        logger.info("Skipping rest", category: .workout)
        stopRestTimer()
        completeRest()
    }
    
    // MARK: - Form Feedback
    
    func addFormFeedback(_ feedback: FormFeedback) {
        withAnimation(.easeInOut(duration: 0.3)) {
            formFeedback.append(feedback)
        }
        
        // Auto-remove after duration
        DispatchQueue.main.asyncAfter(deadline: .now() + feedback.duration) {
            self.removeFormFeedback(feedback)
        }
        
        // Voice coaching for important feedback
        if workoutSettings.enableVoiceCoaching && feedback.type == .error {
            audioManager.speak(feedback.title)
        }
    }
    
    private func removeFormFeedback(_ feedback: FormFeedback) {
        withAnimation(.easeOut(duration: 0.3)) {
            formFeedback.removeAll { $0.id == feedback.id }
        }
    }
    
    // MARK: - Timer Management
    
    private func startWorkoutTimer() {
        workoutTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            self.workoutTime += 1
        }
    }
    
    private func startExerciseTimer() {
        exerciseTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            self.exerciseTime += 1
        }
    }
    
    private func stopExerciseTimer() {
        exerciseTimer?.invalidate()
        exerciseTimer = nil
    }
    
    private func startRestTimer() {
        restTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            if self.restTimeRemaining > 0 {
                self.restTimeRemaining -= 1
            } else {
                self.stopRestTimer()
                self.completeRest()
            }
        }
    }
    
    private func stopRestTimer() {
        restTimer?.invalidate()
        restTimer = nil
    }
    
    private func pauseAllTimers() {
        // Timers will continue but we track pause state
    }
    
    private func resumeAllTimers() {
        // Timers continue automatically
    }
    
    private func stopAllTimers() {
        workoutTimer?.invalidate()
        exerciseTimer?.invalidate()
        restTimer?.invalidate()
        
        workoutTimer = nil
        exerciseTimer = nil
        restTimer = nil
    }
    
    // MARK: - Private Helpers
    
    private func setupBindings() {
        // Bind to AR and ML processing
        arSessionManager.$latestPose
            .compactMap { $0 }
            .sink { [weak self] poseData in
                self?.processPoseData(poseData)
            }
            .store(in: &cancellables)
    }
    
    private func processPoseData(_ poseData: BodyPoseData) {
        guard currentState == .active else { return }
        
        // Process pose data for current exercise
        let exercise = exercises[currentExerciseIndex]
        
        // This would integrate with your existing rep counting and form analysis
        // For now, simulate rep detection
        if shouldDetectRep(for: exercise, poseData: poseData) {
            completeRep()
        }
        
        // Generate form feedback
        if let feedback = analyzeForm(for: exercise, poseData: poseData) {
            addFormFeedback(feedback)
        }
    }
    
    private func shouldDetectRep(for exercise: ExerciseData, poseData: BodyPoseData) -> Bool {
        // This would integrate with RepCountingEngine
        // Simulate rep detection based on exercise time
        return exerciseTime > 0 && Int(exerciseTime) % 3 == 0 && currentRep < targetReps
    }
    
    private func analyzeForm(for exercise: ExerciseData, poseData: BodyPoseData) -> FormFeedback? {
        // This would integrate with form analysis
        // Simulate occasional form feedback
        if Int(exerciseTime) % 7 == 0 && poseData.confidence > 0.8 {
            return FormFeedback.coaching("Good Form", message: "Keep your core engaged")
        }
        return nil
    }
    
    private func configureExerciseSettings(for exercise: ExerciseData) {
        // Configure exercise-specific settings
        targetReps = getTargetReps(for: exercise)
        targetSets = getTargetSets(for: exercise)
    }
    
    private func getTargetReps(for exercise: ExerciseData) -> Int {
        switch exercise.type {
        case .squat, .pushUp: return 12
        case .plank: return 1 // Duration-based
        case .jumpingJack: return 20
        default: return 10
        }
    }
    
    private func getTargetSets(for exercise: ExerciseData) -> Int {
        return 3 // Default sets
    }
    
    private func calculateCurrentFormScore() -> Int {
        // Calculate form score based on current performance
        return Int.random(in: 80...100) // Simulated
    }
    
    private func showCelebration() {
        showingCelebration = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.showingCelebration = false
        }
    }
    
    private func resetWorkoutState() {
        currentExerciseIndex = 0
        currentSet = 1
        currentRep = 0
        workoutTime = 0
        exerciseTime = 0
        restTimeRemaining = 0
        isRestingBetweenSets = false
        isPaused = false
        formFeedback.removeAll()
        workoutPerformance = WorkoutPerformance()
    }
    
    private func saveWorkoutSession() {
        // Save workout session data
        logger.info("Saving workout session", category: .data)
        
        // This would integrate with your data persistence layer
        workoutPerformance.totalDuration = workoutTime
        workoutPerformance.completedAt = Date()
    }
    
    // MARK: - Public Getters
    
    var currentExercise: ExerciseData? {
        guard currentExerciseIndex < exercises.count else { return nil }
        return exercises[currentExerciseIndex]
    }
    
    var nextExercise: ExerciseData? {
        guard currentExerciseIndex + 1 < exercises.count else { return nil }
        return exercises[currentExerciseIndex + 1]
    }
    
    var workoutProgress: Double {
        guard !exercises.isEmpty else { return 0 }
        let completedExercises = Double(currentExerciseIndex)
        let currentExerciseProgress = Double(currentSet - 1) / Double(targetSets)
        return (completedExercises + currentExerciseProgress) / Double(exercises.count)
    }
}

// MARK: - Supporting Types

struct WorkoutSettings {
    var restBetweenSets: TimeInterval = 30
    var restBetweenExercises: TimeInterval = 60
    var enableVoiceCoaching: Bool = true
    var enableHapticFeedback: Bool = true
    var autoAdvanceExercises: Bool = true
    var formFeedbackSensitivity: Double = 0.8
}

struct WorkoutPerformance {
    var exercisePerformances: [ExercisePerformanceData] = []
    var totalDuration: TimeInterval = 0
    var completedAt: Date?
    var overallFormScore: Double = 0
    
    mutating func addRep(exerciseIndex: Int, set: Int, formScore: Int, duration: TimeInterval) {
        // Add rep data to performance tracking
        while exercisePerformances.count <= exerciseIndex {
            exercisePerformances.append(ExercisePerformanceData())
        }
        
        exercisePerformances[exerciseIndex].addRep(
            set: set,
            formScore: formScore,
            duration: duration
        )
    }
}

struct ExercisePerformanceData {
    var reps: [RepData] = []
    var averageFormScore: Double = 0
    var totalTime: TimeInterval = 0
    
    mutating func addRep(set: Int, formScore: Int, duration: TimeInterval) {
        let rep = RepData(set: set, formScore: formScore, duration: duration)
        reps.append(rep)
        
        // Update averages
        averageFormScore = reps.map { Double($0.formScore) }.reduce(0, +) / Double(reps.count)
        totalTime += duration
    }
}

struct RepData {
    let set: Int
    let formScore: Int
    let duration: TimeInterval
    let timestamp: Date = Date()
}