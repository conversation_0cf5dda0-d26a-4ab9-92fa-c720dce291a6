import SwiftUI
import AVFoundation
import AR<PERSON>it

@MainActor
class OnboardingCoordinator: ObservableObject {
    @Published var currentStep: OnboardingStep = .welcome
    @Published var isOnboardingComplete = false
    @Published var cameraPermissionStatus: AVAuthorizationStatus = .notDetermined
    @Published var calibrationProgress: Double = 0.0
    @Published var fitnessLevel: FitnessLevel = .beginner
    @Published var preferredWorkoutDuration: WorkoutDuration = .short
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let logger = Logger.shared
    
    enum OnboardingStep: CaseIterable {
        case welcome
        case permissions
        case calibration
        case demonstration
        case assessment
        case completion
        
        var title: String {
            switch self {
            case .welcome:
                return "Welcome to MotionFitPro"
            case .permissions:
                return "Camera Access"
            case .calibration:
                return "Body Calibration"
            case .demonstration:
                return "Let's Try a Squat"
            case .assessment:
                return "Fitness Assessment"
            case .completion:
                return "You're All Set!"
            }
        }
        
        var subtitle: String {
            switch self {
            case .welcome:
                return "AI-powered motion tracking for perfect form"
            case .permissions:
                return "We need camera access to track your movements"
            case .calibration:
                return "Position yourself in the camera view"
            case .demonstration:
                return "Follow along for your first exercise"
            case .assessment:
                return "Help us personalize your experience"
            case .completion:
                return "Ready to start your fitness journey"
            }
        }
    }
    
    func start() {
        logger.info("Starting onboarding flow", category: .ui)
        currentStep = .welcome
        checkInitialPermissions()
    }
    
    func nextStep() {
        guard let currentIndex = OnboardingStep.allCases.firstIndex(of: currentStep),
              currentIndex < OnboardingStep.allCases.count - 1 else {
            completeOnboarding()
            return
        }
        
        withAnimation(.easeInOut(duration: 0.4)) {
            currentStep = OnboardingStep.allCases[currentIndex + 1]
        }
        
        handleStepEntry()
    }
    
    func previousStep() {
        guard let currentIndex = OnboardingStep.allCases.firstIndex(of: currentStep),
              currentIndex > 0 else {
            return
        }
        
        withAnimation(.easeInOut(duration: 0.4)) {
            currentStep = OnboardingStep.allCases[currentIndex - 1]
        }
    }
    
    func skipOnboarding() {
        logger.info("Skipping onboarding", category: .ui)
        completeOnboarding()
    }
    
    private func completeOnboarding() {
        logger.info("Completing onboarding", category: .ui)
        
        // Save onboarding completion status
        UserDefaults.standard.set(true, forKey: "HasCompletedOnboarding")
        UserDefaults.standard.set(fitnessLevel.rawValue, forKey: "UserFitnessLevel")
        UserDefaults.standard.set(preferredWorkoutDuration.rawValue, forKey: "PreferredWorkoutDuration")
        
        withAnimation(.bouncy(duration: 0.6)) {
            isOnboardingComplete = true
        }
    }
    
    private func handleStepEntry() {
        switch currentStep {
        case .permissions:
            checkCameraPermissions()
        case .calibration:
            startCalibration()
        case .demonstration:
            prepareDemo()
        case .assessment:
            break // Assessment is handled by user interaction
        default:
            break
        }
    }
    
    private func checkInitialPermissions() {
        cameraPermissionStatus = AVCaptureDevice.authorizationStatus(for: .video)
    }
    
    private func checkCameraPermissions() {
        cameraPermissionStatus = AVCaptureDevice.authorizationStatus(for: .video)
        
        if cameraPermissionStatus == .notDetermined {
            // Permission will be requested when user taps "Allow Camera Access"
        }
    }
    
    func requestCameraPermission() async {
        isLoading = true
        defer { isLoading = false }
        
        let status = await AVCaptureDevice.requestAccess(for: .video)
        
        await MainActor.run {
            cameraPermissionStatus = status ? .authorized : .denied
            
            if status {
                logger.info("Camera permission granted", category: .ui)
                nextStep()
            } else {
                logger.warning("Camera permission denied", category: .ui)
                errorMessage = "Camera access is required for motion tracking"
            }
        }
    }
    
    private func startCalibration() {
        guard ARBodyTrackingConfiguration.isSupported else {
            errorMessage = "AR Body Tracking is not supported on this device"
            return
        }
        
        // Simulate calibration progress
        Task {
            for progress in stride(from: 0.0, through: 1.0, by: 0.1) {
                try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
                await MainActor.run {
                    calibrationProgress = progress
                }
            }
            
            await MainActor.run {
                nextStep()
            }
        }
    }
    
    private func prepareDemo() {
        // This would typically prepare the demo exercise
        logger.info("Preparing exercise demonstration", category: .ui)
    }
    
    func completeDemo() {
        logger.info("Demo exercise completed", category: .ui)
        nextStep()
    }
    
    func updateFitnessAssessment(level: FitnessLevel, duration: WorkoutDuration) {
        fitnessLevel = level
        preferredWorkoutDuration = duration
        logger.info("Fitness assessment updated: \(level.rawValue), \(duration.rawValue)", category: .ui)
    }
}

// MARK: - Supporting Types

enum FitnessLevel: String, CaseIterable {
    case beginner = "Beginner"
    case intermediate = "Intermediate"
    case advanced = "Advanced"
    
    var description: String {
        switch self {
        case .beginner:
            return "New to fitness or getting back into it"
        case .intermediate:
            return "Exercise regularly, familiar with basic movements"
        case .advanced:
            return "Experienced athlete or fitness enthusiast"
        }
    }
    
    var color: Color {
        switch self {
        case .beginner:
            return .green
        case .intermediate:
            return .orange
        case .advanced:
            return .red
        }
    }
}

enum WorkoutDuration: String, CaseIterable {
    case short = "5-10 minutes"
    case medium = "15-20 minutes"
    case long = "30+ minutes"
    
    var timeRange: ClosedRange<Int> {
        switch self {
        case .short:
            return 5...10
        case .medium:
            return 15...20
        case .long:
            return 30...60
        }
    }
    
    var icon: String {
        switch self {
        case .short:
            return "clock"
        case .medium:
            return "clock.fill"
        case .long:
            return "stopwatch"
        }
    }
}