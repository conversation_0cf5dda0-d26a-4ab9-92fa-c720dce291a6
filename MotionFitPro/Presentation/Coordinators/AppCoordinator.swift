import SwiftUI
import Combine

@MainActor
class AppCoordinator: ObservableObject {
    @Published var selectedTab: Tab = .home
    @Published var showingOnboarding = false
    @Published var isDeepLinking = false
    @Published var pendingDeepLink: DeepLink?
    @Published var appState: AppState = .launching
    
    // Navigation paths for each tab
    @Published var homeNavigationPath = NavigationPath()
    @Published var workoutsNavigationPath = NavigationPath()
    @Published var progressNavigationPath = NavigationPath()
    @Published var profileNavigationPath = NavigationPath()
    
    // Child coordinators - simplified for now
    @Published var showingWorkout = false
    @Published var showingExerciseSelection = false
    
    // Dependencies
    private let arSessionManager = ARSessionManager.shared
    private let mlProcessingManager = MLProcessingManager.shared
    private let audioManager = AudioManager.shared
    private let logger = Logger.shared
    
    private var cancellables = Set<AnyCancellable>()
    
    enum AppState {
        case launching
        case onboarding
        case main
        case workout
        case background
    }
    
    enum Tab: String, CaseIterable {
        case home = "Home"
        case workouts = "Workouts"
        case progress = "Progress"
        case profile = "Profile"
        
        var icon: String {
            switch self {
            case .home: return "house.fill"
            case .workouts: return "figure.run"
            case .progress: return "chart.line.uptrend.xyaxis"
            case .profile: return "person.fill"
            }
        }
        
        var displayName: String {
            return rawValue
        }
    }
    
    enum DeepLink {
        case workout(exerciseIds: [String])
        case exercise(id: String)
        case achievement(id: String)
        case profile
        case onboarding
        
        var url: URL? {
            switch self {
            case .workout(let exerciseIds):
                return URL(string: "motionfitpro://workout?exercises=\(exerciseIds.joined(separator: ","))")
            case .exercise(let id):
                return URL(string: "motionfitpro://exercise/\(id)")
            case .achievement(let id):
                return URL(string: "motionfitpro://achievement/\(id)")
            case .profile:
                return URL(string: "motionfitpro://profile")
            case .onboarding:
                return URL(string: "motionfitpro://onboarding")
            }
        }
    }
    
    init() {
        setupNotificationObservers()
        restoreAppState()
    }
    
    // MARK: - App Lifecycle
    
    func start() {
        logger.info("Starting app coordinator", category: .app)
        
        // Check if onboarding is needed
        if shouldShowOnboarding() {
            startOnboarding()
        } else {
            appState = .main
        }
        
        // Process any pending deep links
        if let deepLink = pendingDeepLink {
            handleDeepLink(deepLink)
            pendingDeepLink = nil
        }
    }
    
    func applicationDidEnterBackground() {
        logger.info("App entered background", category: .app)
        appState = .background
        
        // Pause AR session if active
        if showingWorkout {
            // Pause logic here
        }
        
        // Save app state
        saveAppState()
    }
    
    func applicationWillEnterForeground() {
        logger.info("App entering foreground", category: .app)
        
        if appState == .background {
            appState = showingWorkout ? .workout : .main
        }
        
        // Resume AR session if needed
        if showingWorkout {
            // Let user manually resume
        }
    }
    
    func applicationWillTerminate() {
        logger.info("App terminating", category: .app)
        saveAppState()
    }
    
    // MARK: - Navigation
    
    func navigateToTab(_ tab: Tab) {
        withAnimation(.easeInOut(duration: 0.3)) {
            selectedTab = tab
        }
        logger.info("Navigated to tab: \(tab.rawValue)", category: .ui)
    }
    
    func navigateToHome() {
        navigateToTab(.home)
        clearNavigationPath(for: .home)
    }
    
    func navigateToExerciseSelection() {
        navigateToTab(.workouts)
        
        // Coordinator functionality disabled until coordinators are implemented
        // if exerciseSelectionCoordinator == nil {
        //     exerciseSelectionCoordinator = ExerciseSelectionCoordinator()
        // }
        
        workoutsNavigationPath.append("exercise-selection")
    }
    
    func startWorkout(with exercises: [ExerciseType], settings: WorkoutSettings = WorkoutSettings()) {
        logger.info("Starting workout with \(exercises.count) exercises", category: .workout)
        
        showingWorkout = true
        appState = .workout
    }
    
    func endWorkout() {
        logger.info("Ending workout", category: .workout)
        
        showingWorkout = false
        appState = .main
        
        // Navigate back to home
        navigateToHome()
    }
    
    func showProgress() {
        navigateToTab(.progress)
        
        // Coordinator functionality disabled until coordinators are implemented
        // if progressCoordinator == nil {
        //     progressCoordinator = ProgressCoordinator()
        // }
    }
    
    // MARK: - Onboarding
    
    private func shouldShowOnboarding() -> Bool {
        return !UserDefaults.standard.bool(forKey: "HasCompletedOnboarding")
    }
    
    func startOnboarding() {
        logger.info("Starting onboarding", category: .ui)
        
        // onboardingCoordinator = OnboardingCoordinator() // Disabled until implemented
        appState = .onboarding
        showingOnboarding = true
        
        // Subscribe to onboarding completion
        // Onboarding coordinator disabled until implemented
        // onboardingCoordinator?.$isOnboardingComplete
        //     .sink { [weak self] isComplete in
        //         if isComplete {
        //             self?.completeOnboarding()
        //         }
        //     }
        //     .store(in: &cancellables)
        // 
        // onboardingCoordinator?.start()
    }
    
    private func completeOnboarding() {
        logger.info("Onboarding completed", category: .ui)
        
        // onboardingCoordinator = nil // Disabled
        showingOnboarding = false
        appState = .main
        
        // Navigate to home
        navigateToHome()
    }
    
    // MARK: - Deep Linking
    
    func handleDeepLink(_ deepLink: DeepLink) {
        logger.info("Handling deep link: \(deepLink)", category: .ui)
        
        // If app is not ready, store for later
        guard appState == .main else {
            pendingDeepLink = deepLink
            return
        }
        
        isDeepLinking = true
        
        switch deepLink {
        case .workout(let exerciseIds):
            handleWorkoutDeepLink(exerciseIds: exerciseIds)
        case .exercise(let id):
            handleExerciseDeepLink(id: id)
        case .achievement(let id):
            handleAchievementDeepLink(id: id)
        case .profile:
            navigateToTab(.profile)
        case .onboarding:
            startOnboarding()
        }
        
        isDeepLinking = false
    }
    
    func handleIncomingURL(_ url: URL) -> Bool {
        logger.info("Handling incoming URL: \(url)", category: .ui)
        
        guard url.scheme == "motionfitpro" else { return false }
        
        let deepLink = parseDeepLink(from: url)
        handleDeepLink(deepLink)
        
        return true
    }
    
    private func parseDeepLink(from url: URL) -> DeepLink {
        let path = url.path
        let components = URLComponents(url: url, resolvingAgainstBaseURL: false)
        
        switch path {
        case "/workout":
            let exerciseIds = components?.queryItems?.first(where: { $0.name == "exercises" })?.value?.components(separatedBy: ",") ?? []
            return .workout(exerciseIds: exerciseIds)
        case let path where path.hasPrefix("/exercise/"):
            let id = String(path.dropFirst("/exercise/".count))
            return .exercise(id: id)
        case let path where path.hasPrefix("/achievement/"):
            let id = String(path.dropFirst("/achievement/".count))
            return .achievement(id: id)
        case "/profile":
            return .profile
        case "/onboarding":
            return .onboarding
        default:
            return .profile // Default fallback
        }
    }
    
    private func handleWorkoutDeepLink(exerciseIds: [String]) {
        // Convert exercise IDs to ExerciseType
        // This would typically query your exercise database
        let exercises: [ExerciseType] = [] // Implement exercise lookup
        
        if !exercises.isEmpty {
            startWorkout(with: exercises)
        } else {
            navigateToExerciseSelection()
        }
    }
    
    private func handleExerciseDeepLink(id: String) {
        navigateToExerciseSelection()
        // Navigate to specific exercise detail
    }
    
    private func handleAchievementDeepLink(id: String) {
        showProgress()
        // Navigate to specific achievement
    }
    
    // MARK: - State Management
    
    private func clearNavigationPath(for tab: Tab) {
        switch tab {
        case .home:
            homeNavigationPath = NavigationPath()
        case .workouts:
            workoutsNavigationPath = NavigationPath()
        case .progress:
            progressNavigationPath = NavigationPath()
        case .profile:
            profileNavigationPath = NavigationPath()
        }
    }
    
    private func saveAppState() {
        UserDefaults.standard.set(selectedTab.rawValue, forKey: "LastSelectedTab")
        logger.debug("App state saved", category: .app)
    }
    
    private func restoreAppState() {
        if let lastTabRawValue = UserDefaults.standard.string(forKey: "LastSelectedTab"),
           let lastTab = Tab(rawValue: lastTabRawValue) {
            selectedTab = lastTab
            logger.debug("App state restored: \(lastTab.rawValue)", category: .app)
        }
    }
    
    // MARK: - Memory Management
    
    func handleMemoryWarning() {
        logger.warning("Memory warning received", category: .app)
        
        // Clean up non-essential coordinators
        if appState != .onboarding {
            // onboardingCoordinator = nil // Disabled
        }
        
        if selectedTab != .workouts {
            // exerciseSelectionCoordinator = nil // Disabled
        }
        
        if selectedTab != .progress {
            // progressCoordinator = nil // Disabled
        }
        
        // Pause AR session if not in workout
        if appState != .workout {
            arSessionManager.stopSession()
        }
    }
    
    // MARK: - Notification Observers
    
    private func setupNotificationObservers() {
        NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)
            .sink { [weak self] _ in
                self?.applicationDidEnterBackground()
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)
            .sink { [weak self] _ in
                self?.applicationWillEnterForeground()
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: UIApplication.willTerminateNotification)
            .sink { [weak self] _ in
                self?.applicationWillTerminate()
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: UIApplication.didReceiveMemoryWarningNotification)
            .sink { [weak self] _ in
                self?.handleMemoryWarning()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Accessors
    
    var isInWorkout: Bool {
        return appState == .workout && showingWorkout
    }
    
    var canStartNewWorkout: Bool {
        return appState == .main && !showingWorkout
    }
    
    // MARK: - Quick Actions (for iOS 13+ Shortcuts)
    
    func handleQuickAction(_ action: String) {
        logger.info("Handling quick action: \(action)", category: .ui)
        
        switch action {
        case "com.motionfitpro.quickworkout":
            // Coordinator functionality disabled
            // if let coordinator = exerciseSelectionCoordinator {
            //     Task {
            //         await coordinator.generateQuickWorkout()
            //     }
            // } else {
                navigateToExerciseSelection()
            // }
        case "com.motionfitpro.progress":
            showProgress()
        case "com.motionfitpro.profile":
            navigateToTab(.profile)
        default:
            navigateToHome()
        }
    }
}

// MARK: - Supporting Types

struct WorkoutSettings {
    var restBetweenSets: TimeInterval = 30
    var restBetweenExercises: TimeInterval = 60
    var enableVoiceCoaching: Bool = true
    var enableHapticFeedback: Bool = true
    var autoAdvanceExercises: Bool = true
    var formFeedbackSensitivity: Double = 0.8
}