//
//  WorkoutProgramSelectionView.swift
//  MotionFitPro
//
//  Created by <PERSON> on 2025-07-15.
//

import SwiftUI

struct WorkoutProgramSelectionView: View {
    @StateObject private var achievementSystem = AchievementSystem()
    @StateObject private var challengeManager = ChallengeManager()
    @StateObject private var aiCoach = AICoachingSystem()
    
    @State private var selectedProgram: WorkoutProgram?
    @State private var showingProgramDetail = false
    @State private var showingAchievements = false
    @State private var showingSettings = false
    
    // Animation states
    @State private var animateCards = false
    @State private var animateHeader = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // Premium Background
                PremiumBackground()
                
                ScrollView(showsIndicators: false) {
                    VStack(spacing: 24) {
                        // Header Section
                        HeaderSection()
                            .opacity(animateHeader ? 1 : 0)
                            .offset(y: animateHeader ? 0 : -50)
                            .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.2), value: animateHeader)
                        
                        // Stats Dashboard
                        StatsDashboard()
                            .opacity(animateCards ? 1 : 0)
                            .offset(y: animateCards ? 0 : 30)
                            .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.4), value: animateCards)
                        
                        // Daily Challenges
                        DailyChallengesSection()
                            .opacity(animateCards ? 1 : 0)
                            .offset(y: animateCards ? 0 : 30)
                            .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.6), value: animateCards)
                        
                        // Workout Programs
                        WorkoutProgramsSection()
                            .opacity(animateCards ? 1 : 0)
                            .offset(y: animateCards ? 0 : 30)
                            .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.8), value: animateCards)
                        
                        // Recent Achievements
                        RecentAchievementsSection()
                            .opacity(animateCards ? 1 : 0)
                            .offset(y: animateCards ? 0 : 30)
                            .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(1.0), value: animateCards)
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                    .padding(.bottom, 100)
                }
            }
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingProgramDetail) {
            if let program = selectedProgram {
                ProgramDetailView(program: program)
            }
        }
        .sheet(isPresented: $showingAchievements) {
            AchievementView()
                .environmentObject(achievementSystem)
        }
        .sheet(isPresented: $showingSettings) {
            SettingsView()
                .environmentObject(aiCoach)
        }
        .onAppear {
            animateHeader = true
            animateCards = true
        }
        .environmentObject(achievementSystem)
        .environmentObject(challengeManager)
        .environmentObject(aiCoach)
    }
    
    // MARK: - Header Section
    
    @ViewBuilder
    private func HeaderSection() -> some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Welcome back!")
                        .font(.title2)
                        .fontWeight(.medium)
                        .foregroundColor(.white.opacity(0.9))
                    
                    Text("Ready to dominate today?")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                }
                
                Spacer()
                
                HStack(spacing: 12) {
                    // Achievements Button
                    Button(action: { showingAchievements = true }) {
                        ZStack {
                            Circle()
                                .fill(.ultraThinMaterial)
                                .frame(width: 44, height: 44)
                            
                            Image(systemName: "trophy.fill")
                                .font(.system(size: 18, weight: .semibold))
                                .foregroundColor(.yellow)
                        }
                    }
                    
                    // Settings Button
                    Button(action: { showingSettings = true }) {
                        ZStack {
                            Circle()
                                .fill(.ultraThinMaterial)
                                .frame(width: 44, height: 44)
                            
                            Image(systemName: "person.crop.circle.fill")
                                .font(.system(size: 18, weight: .semibold))
                                .foregroundColor(.white)
                        }
                    }
                }
            }
            
            // Level Progress
            LevelProgressCard()
        }
    }
    
    // MARK: - Level Progress Card
    
    @ViewBuilder
    private func LevelProgressCard() -> some View {
        HStack(spacing: 16) {
            // Level Icon
            ZStack {
                Circle()
                    .fill(achievementSystem.currentLevel.color.opacity(0.2))
                    .frame(width: 60, height: 60)
                
                Circle()
                    .stroke(achievementSystem.currentLevel.color, lineWidth: 3)
                    .frame(width: 60, height: 60)
                
                Text("\(achievementSystem.totalPoints)")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(achievementSystem.currentLevel.color)
            }
            
            VStack(alignment: .leading, spacing: 6) {
                Text(achievementSystem.currentLevel.title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Text(achievementSystem.currentLevel.description)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                
                // Progress Bar
                if let nextPoints = achievementSystem.currentLevel.nextLevelPoints {
                    ProgressView(value: achievementSystem.getProgressToNextLevel())
                        .progressViewStyle(LinearProgressViewStyle(tint: achievementSystem.currentLevel.color))
                        .scaleEffect(x: 1, y: 2, anchor: .center)
                }
            }
            
            Spacer()
        }
        .padding(20)
        .background(.ultraThinMaterial)
        .cornerRadius(16)
    }
    
    // MARK: - Stats Dashboard
    
    @ViewBuilder
    private func StatsDashboard() -> some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 12) {
            StatCard(
                title: "Streak",
                value: "\(achievementSystem.currentStreak)",
                subtitle: "days",
                icon: "flame.fill",
                color: .orange
            )
            
            StatCard(
                title: "Level",
                value: achievementSystem.currentLevel.title.components(separatedBy: " ").first ?? "1",
                subtitle: "current",
                icon: "star.fill",
                color: achievementSystem.currentLevel.color
            )
            
            StatCard(
                title: "Achievements",
                value: "\(achievementSystem.unlockedAchievements.count)",
                subtitle: "unlocked",
                icon: "trophy.fill",
                color: .yellow
            )
        }
    }
    
    // MARK: - Daily Challenges Section
    
    @ViewBuilder
    private func DailyChallengesSection() -> some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Daily Challenges")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Spacer()
                
                Text("\(challengeManager.getTotalCompletedToday())/\(challengeManager.dailyChallenges.count)")
                    .font(.headline)
                    .foregroundColor(.white.opacity(0.7))
            }
            
            LazyVStack(spacing: 12) {
                ForEach(challengeManager.dailyChallenges) { challenge in
                    ChallengeCard(challenge: challenge)
                }
            }
        }
    }
    
    // MARK: - Workout Programs Section
    
    @ViewBuilder
    private func WorkoutProgramsSection() -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Premium Programs")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                ForEach(WorkoutProgram.allPrograms) { program in
                    ProgramCard(program: program) {
                        selectedProgram = program
                        showingProgramDetail = true
                    }
                }
            }
        }
    }
    
    // MARK: - Recent Achievements Section
    
    @ViewBuilder
    private func RecentAchievementsSection() -> some View {
        if !achievementSystem.recentlyUnlocked.isEmpty {
            VStack(alignment: .leading, spacing: 16) {
                Text("Recent Achievements")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                LazyVStack(spacing: 12) {
                    ForEach(achievementSystem.recentlyUnlocked) { achievement in
                        RecentAchievementCard(achievement: achievement)
                    }
                }
            }
        }
    }
}

// MARK: - Premium Background

struct PremiumBackground: View {
    var body: some View {
        ZStack {
            // Base gradient
            LinearGradient(
                colors: [
                    Color(red: 0.1, green: 0.1, blue: 0.2),
                    Color(red: 0.05, green: 0.05, blue: 0.15),
                    Color.black
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            // Animated particles
            GeometryReader { geometry in
                ForEach(0..<20, id: \.self) { i in
                    Circle()
                        .fill(.white.opacity(0.02))
                        .frame(width: CGFloat.random(in: 20...60))
                        .position(
                            x: CGFloat.random(in: 0...geometry.size.width),
                            y: CGFloat.random(in: 0...geometry.size.height)
                        )
                        .animation(
                            .linear(duration: Double.random(in: 10...20))
                            .repeatForever(autoreverses: true),
                            value: UUID()
                        )
                }
            }
        }
        .ignoresSafeArea()
    }
}

// MARK: - Stat Card

struct StatCard: View {
    let title: String
    let value: String
    let subtitle: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(color)
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.white.opacity(0.7))
                .multilineTextAlignment(.center)
        }
        .frame(height: 80)
        .frame(maxWidth: .infinity)
        .background(.ultraThinMaterial)
        .cornerRadius(12)
    }
}

// MARK: - Challenge Card

struct ChallengeCard: View {
    let challenge: DailyChallenge
    
    var body: some View {
        HStack(spacing: 16) {
            // Challenge Icon
            ZStack {
                Circle()
                    .fill(challenge.color.opacity(0.2))
                    .frame(width: 50, height: 50)
                
                Image(systemName: challenge.icon)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(challenge.color)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(challenge.title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Text(challenge.description)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                
                // Progress
                HStack {
                    ProgressView(value: challenge.getProgress())
                        .progressViewStyle(LinearProgressViewStyle(tint: challenge.color))
                        .scaleEffect(x: 1, y: 1.5, anchor: .center)
                    
                    Text("\(challenge.current)/\(challenge.target)")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(challenge.color)
                }
            }
            
            Spacer()
            
            if challenge.isCompleted {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 24))
                    .foregroundColor(.green)
            }
        }
        .padding(16)
        .background(.ultraThinMaterial)
        .cornerRadius(12)
    }
}

// MARK: - Program Card

struct ProgramCard: View {
    let program: WorkoutProgram
    let onTap: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                // Header
                HStack {
                    Image(systemName: program.icon)
                        .font(.system(size: 24, weight: .semibold))
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 2) {
                        HStack(spacing: 2) {
                            ForEach(0..<program.difficultyStars, id: \.self) { _ in
                                Image(systemName: "star.fill")
                                    .font(.system(size: 8))
                                    .foregroundColor(program.difficulty.color)
                            }
                        }
                        
                        Text(program.difficulty.displayName)
                            .font(.caption2)
                            .foregroundColor(.white.opacity(0.7))
                    }
                }
                
                Text(program.name)
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.leading)
                
                Text(program.description)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
                    .lineLimit(3)
                    .multilineTextAlignment(.leading)
                
                Spacer()
                
                // Footer
                HStack {
                    HStack(spacing: 4) {
                        Image(systemName: "clock.fill")
                            .font(.caption)
                        Text(program.formattedDuration)
                            .font(.caption)
                    }
                    .foregroundColor(.white.opacity(0.7))
                    
                    Spacer()
                    
                    HStack(spacing: 4) {
                        Image(systemName: "flame.fill")
                            .font(.caption)
                        Text("\(program.estimatedCalories)")
                            .font(.caption)
                    }
                    .foregroundColor(.orange.opacity(0.8))
                }
            }
            .padding(16)
            .frame(height: 160)
            .background(program.color.gradient)
            .cornerRadius(16)
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onPressGesture { pressing in
            isPressed = pressing
        }
    }
}

// MARK: - Recent Achievement Card

struct RecentAchievementCard: View {
    let achievement: Achievement
    
    var body: some View {
        HStack(spacing: 16) {
            ZStack {
                achievement.gradient
                    .frame(width: 50, height: 50)
                    .cornerRadius(25)
                
                Image(systemName: achievement.icon)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.white)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text("Achievement Unlocked!")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.yellow)
                
                Text(achievement.title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Text(achievement.description)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
            }
            
            Spacer()
            
            Text("+\(achievement.points)")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.yellow)
        }
        .padding(16)
        .background(.ultraThinMaterial)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(.yellow.opacity(0.3), lineWidth: 1)
        )
    }
}

// MARK: - Press Gesture Extension

extension View {
    func onPressGesture(perform: @escaping (Bool) -> Void) -> some View {
        self.simultaneousGesture(
            DragGesture(minimumDistance: 0)
                .onChanged { _ in perform(true) }
                .onEnded { _ in perform(false) }
        )
    }
}

#Preview {
    WorkoutProgramSelectionView()
}