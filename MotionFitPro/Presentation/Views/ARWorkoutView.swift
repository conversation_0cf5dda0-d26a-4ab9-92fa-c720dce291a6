import SwiftUI
import ARKit
import RealityKit

struct ARWorkoutView: View {
    @EnvironmentObject private var arSessionManager: ARSessionManager
    @EnvironmentObject private var mlProcessingManager: MLProcessingManager
    @State private var showingError = false
    @State private var errorMessage = ""
    @State private var isSessionActive = false
    
    var body: some View {
        ZStack {
            // AR Camera View
            ARViewContainer()
                .ignoresSafeArea()
            
            // Skeleton Overlay
            if let poseData = arSessionManager.latestPose {
                SkeletonOverlayView(poseData: poseData)
                    .allowsHitTesting(false)
            }
            
            // UI Overlays
            VStack {
                // Top Status Bar
                HStack {
                    TrackingQualityIndicator(quality: arSessionManager.trackingQuality)
                    Spacer()
                    PerformanceIndicator(fps: arSessionManager.performanceMetrics.fps)
                }
                .padding(.horizontal)
                .padding(.top, 8)
                
                Spacer()
                
                // Bottom Controls
                WorkoutControlsView()
                    .padding(.bottom, 34) // Account for home indicator
            }
            
            // Error Alert
            if showingError {
                ErrorOverlayView(message: errorMessage) {
                    showingError = false
                }
            }
        }
        .onAppear {
            startARSession()
        }
        .onDisappear {
            stopARSession()
        }
        .onChange(of: arSessionManager.sessionState) { _, newState in
            handleSessionStateChange(newState)
        }
    }
    
    private func startARSession() {
        guard ARBodyTrackingConfiguration.isSupported else {
            showError("AR Body Tracking is not supported on this device")
            return
        }
        
        arSessionManager.startSession()
        mlProcessingManager.startProcessing()
        isSessionActive = true
    }
    
    private func stopARSession() {
        arSessionManager.stopSession()
        mlProcessingManager.stopProcessing()
        isSessionActive = false
    }
    
    private func handleSessionStateChange(_ state: ARSessionManager.ARSessionState) {
        switch state {
        case .failed(let error):
            showError(error.localizedDescription)
        case .interrupted:
            showError("AR session was interrupted")
        case .running:
            showingError = false
        case .stopped:
            break
        }
    }
    
    private func showError(_ message: String) {
        errorMessage = message
        showingError = true
    }
}

// MARK: - ARViewContainer

struct ARViewContainer: UIViewRepresentable {
    @EnvironmentObject private var arSessionManager: ARSessionManager
    
    func makeUIView(context: Context) -> ARView {
        let arView = ARView(frame: .zero)
        
        // Configure AR view for optimal performance
        arView.renderOptions = [.disablePersonOcclusion, .disableDepthOfField]
        arView.environment.sceneUnderstanding.options = []
        
        // Use the shared AR session
        arView.session = arSessionManager.session
        
        return arView
    }
    
    func updateUIView(_ uiView: ARView, context: Context) {
        // Updates handled by ARSessionManager
    }
}

// MARK: - Tracking Quality Indicator

struct TrackingQualityIndicator: View {
    let quality: ARSessionManager.TrackingQuality
    
    var body: some View {
        HStack(spacing: 6) {
            Circle()
                .fill(qualityColor)
                .frame(width: 8, height: 8)
                .animation(.easeInOut(duration: 0.3), value: quality)
            
            Text(qualityText)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.white)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(.ultraThinMaterial, in: Capsule())
    }
    
    private var qualityColor: Color {
        switch quality {
        case .good:
            return .green
        case .limited:
            return .orange
        case .poor, .initializing:
            return .red
        }
    }
    
    private var qualityText: String {
        switch quality {
        case .good:
            return "Tracking"
        case .limited(let reason):
            return reason
        case .poor(let reason):
            return reason
        case .initializing:
            return "Initializing"
        }
    }
}

// MARK: - Performance Indicator

struct PerformanceIndicator: View {
    let fps: Double
    
    var body: some View {
        Text("\(Int(fps)) FPS")
            .font(.caption)
            .fontWeight(.medium)
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(.ultraThinMaterial, in: Capsule())
            .opacity(fps > 0 ? 1 : 0)
            .animation(.easeInOut(duration: 0.3), value: fps > 0)
    }
}

// MARK: - Error Overlay

struct ErrorOverlayView: View {
    let message: String
    let onDismiss: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 32))
                .foregroundColor(.red)
            
            Text("AR Session Error")
                .font(.headline)
                .fontWeight(.semibold)
            
            Text(message)
                .font(.body)
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
            
            Button("Try Again") {
                onDismiss()
            }
            .buttonStyle(.borderedProminent)
        }
        .padding(24)
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal, 32)
        .transition(.scale.combined(with: .opacity))
    }
}

#Preview("AR Workout View") {
    ARWorkoutView()
        .environmentObject(ARSessionManager.shared)
        .environmentObject(MLProcessingManager.shared)
        .environmentObject(AudioManager.shared)
}

#Preview("Tracking Quality Indicators") {
    VStack(spacing: 12) {
        TrackingQualityIndicator(quality: .good)
        TrackingQualityIndicator(quality: .limited("Excessive motion"))
        TrackingQualityIndicator(quality: .poor("Insufficient features"))
        TrackingQualityIndicator(quality: .initializing)
    }
    .padding()
    .background(.black)
}

#Preview("Error Overlay") {
    ZStack {
        Color.black.ignoresSafeArea()
        
        ErrorOverlayView(message: "AR Body Tracking is not supported on this device. Please use a device with A12 Bionic chip or later.") {
            print("Dismissed")
        }
    }
}