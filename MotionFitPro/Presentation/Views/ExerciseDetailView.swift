//
//  ExerciseDetailView.swift
//  MotionFitPro
//
//  Created by <PERSON> on 2025-07-15.
//

import SwiftUI

struct ExerciseDetailView: View {
    let exercise: ProgramExercise
    @Environment(\.dismiss) private var dismiss
    
    @State private var animateInstructions = false
    @State private var selectedTipIndex = 0
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background gradient based on exercise type
                exerciseGradient
                    .ignoresSafeArea()
                
                ScrollView(showsIndicators: false) {
                    VStack(spacing: 24) {
                        // Header
                        HeaderSection()
                        
                        // 3D Exercise Preview
                        ExercisePreviewSection()
                        
                        // Exercise Details
                        ExerciseDetailsSection()
                        
                        // Instructions
                        InstructionsSection()
                        
                        // Tips
                        TipsSection()
                        
                        // Form Checkpoints
                        FormCheckpointsSection()
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                    .padding(.bottom, 100)
                }
            }
            .navigationBarHidden(true)
        }
        .onAppear {
            animateInstructions = true
        }
    }
    
    // MARK: - Header Section
    
    @ViewBuilder
    private func HeaderSection() -> some View {
        HStack {
            Button(action: { dismiss() }) {
                ZStack {
                    Circle()
                        .fill(.ultraThinMaterial)
                        .frame(width: 44, height: 44)
                    
                    Image(systemName: "xmark")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                }
            }
            
            Spacer()
            
            VStack(spacing: 4) {
                Text(exercise.exerciseType.displayName)
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Text("Exercise Guide")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
            }
            
            Spacer()
            
            ZStack {
                Circle()
                    .fill(.white.opacity(0.2))
                    .frame(width: 44, height: 44)
                
                Image(systemName: exerciseIcon)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
            }
        }
    }
    
    // MARK: - Exercise Preview Section
    
    @ViewBuilder
    private func ExercisePreviewSection() -> some View {
        VStack(spacing: 16) {
            ZStack {
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
                    .frame(height: 200)
                
                Exercise3DAnimation(
                    exerciseType: exercise.exerciseType,
                    isAnimating: true
                )
                .scaleEffect(2.0)
            }
            
            Text("Watch the movement pattern above")
                .font(.subheadline)
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.center)
        }
    }
    
    // MARK: - Exercise Details Section
    
    @ViewBuilder
    private func ExerciseDetailsSection() -> some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 16) {
            if let duration = exercise.formattedDuration {
                DetailCard(
                    title: "Duration",
                    value: duration,
                    icon: "timer",
                    color: .blue
                )
            }
            
            if let reps = exercise.targetReps {
                DetailCard(
                    title: "Target Reps",
                    value: "\(reps)",
                    icon: "number.circle",
                    color: .green
                )
            }
            
            DetailCard(
                title: "Sets",
                value: "\(exercise.sets)",
                icon: "repeat.circle",
                color: .orange
            )
            
            DetailCard(
                title: "Rest",
                value: exercise.formattedRest,
                icon: "pause.circle",
                color: .purple
            )
        }
    }
    
    // MARK: - Instructions Section
    
    @ViewBuilder
    private func InstructionsSection() -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("How to Perform")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            VStack(spacing: 12) {
                ForEach(Array(exercise.instructions.enumerated()), id: \.offset) { index, instruction in
                    InstructionCard(
                        step: index + 1,
                        instruction: instruction,
                        isAnimated: animateInstructions
                    )
                    .opacity(animateInstructions ? 1 : 0)
                    .offset(x: animateInstructions ? 0 : 50)
                    .animation(
                        .spring(response: 0.6, dampingFraction: 0.8)
                        .delay(Double(index) * 0.1),
                        value: animateInstructions
                    )
                }
            }
        }
    }
    
    // MARK: - Tips Section
    
    @ViewBuilder
    private func TipsSection() -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Pro Tips")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            TabView(selection: $selectedTipIndex) {
                ForEach(Array(exercise.tips.enumerated()), id: \.offset) { index, tip in
                    TipCard(tip: tip)
                        .tag(index)
                }
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .automatic))
            .frame(height: 120)
            .background(.ultraThinMaterial)
            .cornerRadius(16)
        }
    }
    
    // MARK: - Form Checkpoints Section
    
    @ViewBuilder
    private func FormCheckpointsSection() -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Form Checkpoints")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            VStack(spacing: 12) {
                ForEach(formCheckpoints, id: \.self) { checkpoint in
                    FormCheckpointCard(checkpoint: checkpoint)
                }
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var exerciseGradient: LinearGradient {
        switch exercise.exerciseType {
        case .squat:
            return LinearGradient(colors: [.blue, .purple], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .pushUp:
            return LinearGradient(colors: [.orange, .red], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .plank:
            return LinearGradient(colors: [.green, .blue], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .jumpingJacks:
            return LinearGradient(colors: [.yellow, .orange], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .lunge:
            return LinearGradient(colors: [.purple, .pink], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .mountainClimbers:
            return LinearGradient(colors: [.red, .orange], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .burpees:
            return LinearGradient(colors: [.black, .red], startPoint: .topLeading, endPoint: .bottomTrailing)
        }
    }
    
    private var exerciseIcon: String {
        switch exercise.exerciseType {
        case .squat: return "figure.strengthtraining.traditional"
        case .pushUp: return "figure.strengthtraining.functional"
        case .plank: return "figure.core.training"
        case .jumpingJacks: return "figure.jumping.jacks"
        case .lunge: return "figure.flexibility"
        case .mountainClimbers: return "figure.climbing"
        case .burpees: return "figure.mixed.cardio"
        }
    }
    
    private var formCheckpoints: [String] {
        switch exercise.exerciseType {
        case .squat:
            return [
                "Keep your chest up and core engaged",
                "Weight should be in your heels",
                "Knees track over your toes",
                "Go down until thighs are parallel to floor",
                "Drive through heels to stand up"
            ]
        case .pushUp:
            return [
                "Start in a strong plank position",
                "Hands under shoulders, body straight",
                "Lower chest toward the ground",
                "Keep core tight throughout",
                "Push back up to starting position"
            ]
        case .plank:
            return [
                "Maintain straight line from head to heels",
                "Keep shoulders over wrists",
                "Engage your core muscles",
                "Don't let hips sag or pike up",
                "Breathe normally throughout hold"
            ]
        case .jumpingJacks:
            return [
                "Start with feet together, arms at sides",
                "Jump feet apart while raising arms overhead",
                "Land softly on balls of feet",
                "Keep core engaged throughout",
                "Maintain steady rhythm"
            ]
        case .lunge:
            return [
                "Step forward into a deep lunge",
                "Keep front knee over ankle",
                "Lower back knee toward ground",
                "Maintain upright torso",
                "Push back to starting position"
            ]
        case .mountainClimbers:
            return [
                "Start in plank position",
                "Keep hands under shoulders",
                "Drive knees toward chest alternately",
                "Maintain straight body line",
                "Keep hips level throughout"
            ]
        case .burpees:
            return [
                "Start standing, squat down",
                "Place hands on ground",
                "Jump feet back to plank",
                "Optional: add push-up",
                "Jump feet forward, stand and jump up"
            ]
        }
    }
}

// MARK: - Detail Card

struct DetailCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(color)
            
            Text(value)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.white.opacity(0.7))
                .multilineTextAlignment(.center)
        }
        .padding()
        .background(.ultraThinMaterial)
        .cornerRadius(12)
    }
}

// MARK: - Instruction Card

struct InstructionCard: View {
    let step: Int
    let instruction: String
    let isAnimated: Bool
    
    var body: some View {
        HStack(spacing: 16) {
            ZStack {
                Circle()
                    .fill(.white.opacity(0.2))
                    .frame(width: 40, height: 40)
                
                Text("\(step)")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
            
            Text(instruction)
                .font(.body)
                .foregroundColor(.white)
                .multilineTextAlignment(.leading)
            
            Spacer()
        }
        .padding(16)
        .background(.ultraThinMaterial)
        .cornerRadius(12)
    }
}

// MARK: - Tip Card

struct TipCard: View {
    let tip: String
    
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: "lightbulb.fill")
                .font(.system(size: 24, weight: .semibold))
                .foregroundColor(.yellow)
            
            Text(tip)
                .font(.body)
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .lineLimit(3)
        }
        .padding(16)
    }
}

// MARK: - Form Checkpoint Card

struct FormCheckpointCard: View {
    let checkpoint: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 16))
                .foregroundColor(.green)
            
            Text(checkpoint)
                .font(.subheadline)
                .foregroundColor(.white)
                .multilineTextAlignment(.leading)
            
            Spacer()
        }
        .padding(12)
        .background(.ultraThinMaterial)
        .cornerRadius(8)
    }
}

#Preview {
    ExerciseDetailView(
        exercise: ProgramExercise(
            exerciseType: .squat,
            duration: nil,
            targetReps: 15,
            sets: 3,
            restDuration: 45,
            instructions: [
                "Stand with feet shoulder-width apart",
                "Lower hips back and down",
                "Return to standing"
            ],
            tips: [
                "Keep your chest up",
                "Weight in heels",
                "Knees track over toes"
            ]
        )
    )
}