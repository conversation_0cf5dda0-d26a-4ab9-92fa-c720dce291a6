import SwiftUI

struct WorkoutSelectionView: View {
    @StateObject private var coordinator = ExerciseSelectionCoordinator()
    @State private var selectedCategory: ExerciseCategory?
    @State private var selectedDifficulty: ExerciseDifficulty?
    @State private var selectedEquipment: Equipment?
    @State private var searchText = ""
    @State private var showingQuickWorkout = false
    @State private var showingCustomWorkout = false
    
    private let gridColumns = [
        GridItem(.flexible()),
        GridItem(.flexible())
    ]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header with quick actions
                headerSection
                
                // Filter bar
                filterSection
                
                // Main content
                ScrollView {
                    LazyVStack(spacing: 16) {
                        // Quick workout section
                        quickWorkoutSection
                        
                        // Exercise categories
                        categoriesSection
                        
                        // Featured workouts
                        featuredWorkoutsSection
                        
                        // Recent workouts
                        recentWorkoutsSection
                    }
                    .padding()
                }
            }
            .navigationTitle("Workouts")
            .navigationBarTitleDisplayMode(.large)
            .sheet(isPresented: $showingQuickWorkout) {
                QuickWorkoutView()
            }
            .sheet(isPresented: $showingCustomWorkout) {
                CustomWorkoutBuilderView()
            }
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 12) {
            HStack(spacing: 12) {
                // Quick Workout Button
                Button(action: { showingQuickWorkout = true }) {
                    HStack {
                        Image(systemName: "bolt.fill")
                        Text("Quick Workout")
                    }
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [.blue, .purple]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(12)
                }
                
                // Custom Workout Button
                Button(action: { showingCustomWorkout = true }) {
                    HStack {
                        Image(systemName: "plus.circle.fill")
                        Text("Custom")
                    }
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.blue)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(12)
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
        .background(Color(.systemBackground))
    }
    
    // MARK: - Filter Section
    private var filterSection: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                // Search
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.gray)
                    TextField("Search exercises...", text: $searchText)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(Color(.systemGray6))
                .cornerRadius(20)
                .frame(width: 200)
                
                // Category filter
                FilterChip(
                    title: selectedCategory?.displayName ?? "All Categories",
                    isSelected: selectedCategory != nil,
                    action: { /* Show category picker */ }
                )
                
                // Difficulty filter
                FilterChip(
                    title: selectedDifficulty?.displayName ?? "All Levels",
                    isSelected: selectedDifficulty != nil,
                    action: { /* Show difficulty picker */ }
                )
                
                // Equipment filter
                FilterChip(
                    title: selectedEquipment?.displayName ?? "Any Equipment",
                    isSelected: selectedEquipment != nil,
                    action: { /* Show equipment picker */ }
                )
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
    }
    
    // MARK: - Quick Workout Section
    private var quickWorkoutSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Quick Start")
                .font(.title2)
                .fontWeight(.bold)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    QuickWorkoutCard(
                        title: "5-Min Energy Boost",
                        duration: "5 min",
                        exercises: 3,
                        difficulty: .beginner,
                        color: .green
                    )
                    
                    QuickWorkoutCard(
                        title: "Core Crusher",
                        duration: "10 min",
                        exercises: 5,
                        difficulty: .intermediate,
                        color: .orange
                    )
                    
                    QuickWorkoutCard(
                        title: "Full Body Burn",
                        duration: "20 min",
                        exercises: 8,
                        difficulty: .advanced,
                        color: .red
                    )
                }
                .padding(.horizontal)
            }
        }
    }
    
    // MARK: - Categories Section
    private var categoriesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Exercise Categories")
                .font(.title2)
                .fontWeight(.bold)
            
            LazyVGrid(columns: gridColumns, spacing: 12) {
                ForEach(ExerciseCategory.allCases, id: \.self) { category in
                    CategoryCard(category: category) {
                        selectedCategory = category
                    }
                }
            }
        }
    }
    
    // MARK: - Featured Workouts Section
    private var featuredWorkoutsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Featured Workouts")
                    .font(.title2)
                    .fontWeight(.bold)
                Spacer()
                Button("See All") {
                    // Navigate to all featured workouts
                }
                .font(.subheadline)
                .foregroundColor(.blue)
            }
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 16) {
                    ForEach(0..<5) { index in
                        FeaturedWorkoutCard(
                            title: "Strength Builder \(index + 1)",
                            description: "Build muscle with compound movements",
                            duration: "30 min",
                            difficulty: .intermediate,
                            imageUrl: nil
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
    }
    
    // MARK: - Recent Workouts Section
    private var recentWorkoutsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Recent Workouts")
                .font(.title2)
                .fontWeight(.bold)
            
            VStack(spacing: 8) {
                ForEach(0..<3) { index in
                    RecentWorkoutRow(
                        title: "Morning Routine \(index + 1)",
                        date: Date().addingTimeInterval(-Double(index) * 86400),
                        duration: "25 min",
                        completion: Double.random(in: 0.7...1.0)
                    )
                }
            }
        }
    }
}

// MARK: - Supporting Views

struct FilterChip: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(isSelected ? .white : .primary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    isSelected ? Color.blue : Color(.systemGray6)
                )
                .cornerRadius(20)
        }
    }
}

struct QuickWorkoutCard: View {
    let title: String
    let duration: String
    let exercises: Int
    let difficulty: ExerciseDifficulty
    let color: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "bolt.fill")
                    .foregroundColor(color)
                Spacer()
                Text(duration)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
            
            Text("\(exercises) exercises • \(difficulty.displayName)")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .frame(width: 160, height: 100)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

struct CategoryCard: View {
    let category: ExerciseCategory
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: category.icon)
                    .font(.system(size: 24))
                    .foregroundColor(.blue)
                
                Text(category.displayName)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 80)
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }
}

struct FeaturedWorkoutCard: View {
    let title: String
    let description: String
    let duration: String
    let difficulty: ExerciseDifficulty
    let imageUrl: URL?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Image placeholder
            Rectangle()
                .fill(LinearGradient(
                    gradient: Gradient(colors: [.blue.opacity(0.6), .purple.opacity(0.8)]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ))
                .frame(height: 120)
                .cornerRadius(8)
                .overlay(
                    Image(systemName: "play.circle.fill")
                        .font(.system(size: 30))
                        .foregroundColor(.white)
                )
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
                
                HStack {
                    Text(duration)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(difficulty.displayName)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(Color(difficulty.color).opacity(0.2))
                        .cornerRadius(4)
                }
            }
            .padding(.horizontal, 4)
        }
        .frame(width: 200)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

struct RecentWorkoutRow: View {
    let title: String
    let date: Date
    let duration: String
    let completion: Double
    
    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter
    }
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                
                Text(dateFormatter.string(from: date))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text(duration)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text("\(Int(completion * 100))% complete")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

// MARK: - Placeholder Views
struct QuickWorkoutView: View {
    var body: some View {
        Text("Quick Workout Builder")
            .font(.title)
            .padding()
    }
}

struct CustomWorkoutBuilderView: View {
    var body: some View {
        Text("Custom Workout Builder")
            .font(.title)
            .padding()
    }
}

#Preview {
    WorkoutSelectionView()
}
