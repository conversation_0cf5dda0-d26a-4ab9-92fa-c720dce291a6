import SwiftUI

struct ActiveWorkoutView: View {
    @ObservedObject var coordinator: WorkoutCoordinator
    @Environment(\.dismiss) private var dismiss
    @State private var showingExitConfirmation = false
    
    var body: some View {
        ZStack {
            // AR Background
            ARWorkoutView()
                .ignoresSafeArea()
            
            // Workout UI Overlay
            VStack(spacing: 0) {
                // Top status bar
                WorkoutStatusBar(coordinator: coordinator) {
                    showingExitConfirmation = true
                }
                .padding(.horizontal, 20)
                .padding(.top, 8)
                
                Spacer()
                
                // Main workout content based on state
                Group {
                    switch coordinator.currentState {
                    case .preparing:
                        WorkoutPreparationOverlay(coordinator: coordinator)
                    case .exerciseIntro:
                        ExerciseIntroOverlay(coordinator: coordinator)
                    case .active:
                        ActiveExerciseOverlay(coordinator: coordinator)
                    case .resting:
                        RestOverlay(coordinator: coordinator)
                    case .setComplete:
                        SetCompleteOverlay(coordinator: coordinator)
                    case .exerciseComplete:
                        ExerciseCompleteOverlay(coordinator: coordinator)
                    case .workoutComplete:
                        WorkoutCompleteOverlay(coordinator: coordinator)
                    case .paused:
                        PausedOver<PERSON>(coordinator: coordinator)
                    case .emergencyStop:
                        EmergencyStopOverlay()
                    }
                }
                .transition(.asymmetric(
                    insertion: .move(edge: .bottom).combined(with: .opacity),
                    removal: .move(edge: .top).combined(with: .opacity)
                ))
                
                Spacer()
                
                // Bottom controls
                WorkoutControlsOverlay(coordinator: coordinator)
                    .padding(.horizontal, 20)
                    .padding(.bottom, 34)
            }
            
            // Form feedback overlay
            if !coordinator.formFeedback.isEmpty {
                VStack {
                    FormFeedbackContainer(feedbackItems: .constant(coordinator.formFeedback))
                        .padding(.top, 120)
                    
                    Spacer()
                }
            }
            
            // Celebration overlay
            if coordinator.showingCelebration {
                CelebrationOverlay()
                    .allowsHitTesting(false)
            }
        }
        .navigationBarHidden(true)
        .confirmationDialog(
            "Exit Workout",
            isPresented: $showingExitConfirmation,
            titleVisibility: .visible
        ) {
            Button("Save & Exit") {
                coordinator.stopWorkout()
                dismiss()
            }
            
            Button("Emergency Stop", role: .destructive) {
                coordinator.confirmEmergencyStop()
                dismiss()
            }
            
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("Your progress will be saved if you exit now.")
        }
        .onChange(of: coordinator.currentState) { _, newState in
            if newState == .workoutComplete {
                // Auto-dismiss after workout completion
                DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                    dismiss()
                }
            }
        }
    }
}

// MARK: - Workout Status Bar

struct WorkoutStatusBar: View {
    @ObservedObject var coordinator: WorkoutCoordinator
    let onExit: () -> Void
    
    var body: some View {
        HStack {
            // Exit button
            Button(action: onExit) {
                Image(systemName: "xmark")
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 44, height: 44)
                    .background(.ultraThinMaterial, in: Circle())
            }
            
            Spacer()
            
            // Workout progress
            VStack(spacing: 4) {
                Text(formatWorkoutTime(coordinator.workoutTime))
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                ProgressView(value: coordinator.workoutProgress)
                    .progressViewStyle(LinearProgressViewStyle(tint: .white))
                    .frame(width: 100)
            }
            
            Spacer()
            
            // Exercise counter
            VStack(spacing: 2) {
                Text("\(coordinator.currentExerciseIndex + 1)/\(coordinator.exercises.count)")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Text("exercises")
                    .font(.caption2)
                    .foregroundColor(.white.opacity(0.8))
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(.ultraThinMaterial, in: Capsule())
        }
    }
    
    private func formatWorkoutTime(_ time: TimeInterval) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}

// MARK: - Workout Preparation Overlay

struct WorkoutPreparationOverlay: View {
    @ObservedObject var coordinator: WorkoutCoordinator
    
    var body: some View {
        VStack(spacing: 24) {
            Text("Get Ready!")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            Text("Position yourself in the camera view")
                .font(.headline)
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.center)
            
            // Countdown or preparation indicator
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(1.5)
        }
        .padding(32)
        .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 20))
    }
}

// MARK: - Exercise Intro Overlay

struct ExerciseIntroOverlay: View {
    @ObservedObject var coordinator: WorkoutCoordinator
    
    var body: some View {
        VStack(spacing: 20) {
            if let exercise = coordinator.currentExercise {
                VStack(spacing: 12) {
                    Text("Next Exercise")
                        .font(.headline)
                        .foregroundColor(.white.opacity(0.8))
                    
                    Text(exercise.name)
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text("\(coordinator.targetSets) sets × \(coordinator.targetReps) reps")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.8))
                }
                
                // Exercise demonstration icon
                Image(systemName: exercise.type.icon)
                    .font(.system(size: 60))
                    .foregroundColor(.white)
                    .background(
                        Circle()
                            .fill(.white.opacity(0.2))
                            .frame(width: 100, height: 100)
                    )
            }
        }
        .padding(32)
        .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 20))
    }
}

// MARK: - Active Exercise Overlay

struct ActiveExerciseOverlay: View {
    @ObservedObject var coordinator: WorkoutCoordinator
    
    var body: some View {
        HStack(spacing: 40) {
            // Rep counter
            RepCounterView(
                repCount: .constant(coordinator.currentRep),
                setCount: .constant(coordinator.currentSet),
                targetReps: coordinator.targetReps,
                targetSets: coordinator.targetSets
            )
            
            Spacer()
            
            // Exercise info
            if let exercise = coordinator.currentExercise {
                VStack(alignment: .trailing, spacing: 8) {
                    Text(exercise.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                    
                    Text("Set \(coordinator.currentSet) of \(coordinator.targetSets)")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.8))
                    
                    Text(formatExerciseTime(coordinator.exerciseTime))
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                }
                .multilineTextAlignment(.trailing)
            }
        }
        .padding(.horizontal, 20)
    }
    
    private func formatExerciseTime(_ time: TimeInterval) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        if minutes > 0 {
            return String(format: "%d:%02d", minutes, seconds)
        } else {
            return String(format: "%d", seconds)
        }
    }
}

// MARK: - Rest Overlay

struct RestOverlay: View {
    @ObservedObject var coordinator: WorkoutCoordinator
    
    var body: some View {
        VStack(spacing: 24) {
            Text(coordinator.isRestingBetweenSets ? "Rest Between Sets" : "Rest Between Exercises")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            // Rest timer
            ZStack {
                Circle()
                    .stroke(.white.opacity(0.3), lineWidth: 8)
                    .frame(width: 120, height: 120)
                
                Circle()
                    .trim(from: 0, to: restProgress)
                    .stroke(.blue, style: StrokeStyle(lineWidth: 8, lineCap: .round))
                    .frame(width: 120, height: 120)
                    .rotationEffect(.degrees(-90))
                    .animation(.linear(duration: 1), value: restProgress)
                
                VStack(spacing: 4) {
                    Text("\(Int(coordinator.restTimeRemaining))")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text("seconds")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                }
            }
            
            // Next exercise info
            if !coordinator.isRestingBetweenSets, let nextExercise = coordinator.nextExercise {
                VStack(spacing: 8) {
                    Text("Next: \(nextExercise.name)")
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    Text("\(coordinator.targetSets) sets × \(coordinator.targetReps) reps")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.8))
                }
            }
            
            // Skip rest button
            Button("Skip Rest") {
                coordinator.skipRest()
            }
            .font(.subheadline)
            .fontWeight(.medium)
            .padding(.horizontal, 20)
            .padding(.vertical, 10)
            .background(.white.opacity(0.2), in: Capsule())
            .foregroundColor(.white)
        }
        .padding(32)
        .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 20))
    }
    
    private var restProgress: Double {
        let totalRestTime = coordinator.isRestingBetweenSets ? 
            coordinator.workoutSettings.restBetweenSets : 
            coordinator.workoutSettings.restBetweenExercises
        
        return max(0, coordinator.restTimeRemaining / totalRestTime)
    }
}

// MARK: - Set Complete Overlay

struct SetCompleteOverlay: View {
    @ObservedObject var coordinator: WorkoutCoordinator
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(.green)
            
            Text("Set Complete!")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            if let exercise = coordinator.currentExercise {
                Text("\(exercise.name) - Set \(coordinator.currentSet)")
                    .font(.headline)
                    .foregroundColor(.white.opacity(0.8))
            }
            
            if coordinator.currentSet < coordinator.targetSets {
                Text("Get ready for set \(coordinator.currentSet + 1)")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
            }
        }
        .padding(32)
        .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 20))
    }
}

// MARK: - Exercise Complete Overlay

struct ExerciseCompleteOverlay: View {
    @ObservedObject var coordinator: WorkoutCoordinator
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "star.fill")
                .font(.system(size: 60))
                .foregroundColor(.yellow)
            
            Text("Exercise Complete!")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            if let exercise = coordinator.currentExercise {
                Text(exercise.name)
                    .font(.headline)
                    .foregroundColor(.white.opacity(0.8))
            }
            
            if let nextExercise = coordinator.nextExercise {
                Text("Next: \(nextExercise.name)")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
            } else {
                Text("Final exercise completed!")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
            }
        }
        .padding(32)
        .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 20))
    }
}

// MARK: - Workout Complete Overlay

struct WorkoutCompleteOverlay: View {
    @ObservedObject var coordinator: WorkoutCoordinator
    
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: "trophy.fill")
                .font(.system(size: 80))
                .foregroundColor(.yellow)
            
            Text("Workout Complete!")
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            Text("Amazing work! You've completed all exercises.")
                .font(.headline)
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.center)
            
            // Workout summary
            HStack(spacing: 30) {
                SummaryStatItem(
                    icon: "clock.fill",
                    value: formatWorkoutTime(coordinator.workoutTime),
                    label: "Duration"
                )
                
                SummaryStatItem(
                    icon: "figure.run",
                    value: "\(coordinator.exercises.count)",
                    label: "Exercises"
                )
                
                SummaryStatItem(
                    icon: "star.fill",
                    value: "100%",
                    label: "Complete"
                )
            }
        }
        .padding(32)
        .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 20))
    }
    
    private func formatWorkoutTime(_ time: TimeInterval) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}

struct SummaryStatItem: View {
    let icon: String
    let value: String
    let label: String
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.white)
            
            Text(value)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            Text(label)
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
        }
    }
}

// MARK: - Paused Overlay

struct PausedOverlay: View {
    @ObservedObject var coordinator: WorkoutCoordinator
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "pause.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(.orange)
            
            Text("Workout Paused")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            Text("Take a break and resume when ready")
                .font(.headline)
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.center)
        }
        .padding(32)
        .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 20))
    }
}

// MARK: - Emergency Stop Overlay

struct EmergencyStopOverlay: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 60))
                .foregroundColor(.red)
            
            Text("Emergency Stop")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            Text("Workout stopped for safety")
                .font(.headline)
                .foregroundColor(.white.opacity(0.8))
        }
        .padding(32)
        .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 20))
    }
}

// MARK: - Workout Controls Overlay

struct WorkoutControlsOverlay: View {
    @ObservedObject var coordinator: WorkoutCoordinator
    
    var body: some View {
        HStack(spacing: 20) {
            // Emergency stop
            Button {
                coordinator.requestEmergencyStop()
            } label: {
                Image(systemName: "stop.fill")
                    .font(.title2)
                    .foregroundColor(.red)
                    .frame(width: 50, height: 50)
                    .background(.ultraThinMaterial, in: Circle())
            }
            
            Spacer()
            
            // Main control button
            Button {
                if coordinator.currentState == .paused {
                    coordinator.resumeWorkout()
                } else if coordinator.currentState == .active {
                    coordinator.pauseWorkout()
                }
            } label: {
                Image(systemName: coordinator.currentState == .paused ? "play.fill" : "pause.fill")
                    .font(.title)
                    .foregroundColor(.white)
                    .frame(width: 70, height: 70)
                    .background(.blue, in: Circle())
            }
            .disabled(coordinator.currentState != .active && coordinator.currentState != .paused)
            
            Spacer()
            
            // Additional actions menu
            Menu {
                Button("Skip Exercise") {
                    // Skip current exercise
                }
                
                Button("Adjust Settings") {
                    // Show settings
                }
            } label: {
                Image(systemName: "ellipsis")
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 50, height: 50)
                    .background(.ultraThinMaterial, in: Circle())
            }
        }
    }
}

#Preview("Active Workout") {
    let exercises = [
        ExerciseData(
            id: UUID(),
            name: "Squat",
            type: .squat,
            category: .strength,
            difficulty: .beginner,
            equipment: .none,
            duration: 30,
            description: "Classic lower body exercise",
            instructions: [],
            muscleGroups: [.quadriceps, .hamstrings, .glutes],
            caloriesBurned: 8,
            videoPreviewURL: nil,
            thumbnailName: "squat_thumbnail"
        )
    ]
    
    let coordinator = WorkoutCoordinator(
        exercises: exercises,
        arSessionManager: ARSessionManager.shared,
        mlProcessingManager: MLProcessingManager.shared,
        audioManager: AudioManager.shared
    )
    
    return ActiveWorkoutView(coordinator: coordinator)
}