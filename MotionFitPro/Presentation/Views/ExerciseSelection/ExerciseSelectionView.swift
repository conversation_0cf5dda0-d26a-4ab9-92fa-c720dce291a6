import SwiftUI

struct ExerciseSelectionView: View {
    @StateObject private var coordinator = ExerciseSelectionCoordinator()
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationStack {
            ZStack {
                // Background
                LinearGradient(
                    colors: [.blue.opacity(0.1), .purple.opacity(0.05)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                // Content based on current view
                Group {
                    switch coordinator.currentView {
                    case .library:
                        ExerciseLibraryView(coordinator: coordinator)
                    case .quickWorkout:
                        QuickWorkoutView(coordinator: coordinator)
                    case .customBuilder:
                        CustomWorkoutBuilderView(coordinator: coordinator)
                    case .workoutPreview:
                        WorkoutPreviewView(coordinator: coordinator)
                    }
                }
                .transition(.asymmetric(
                    insertion: .move(edge: .trailing).combined(with: .opacity),
                    removal: .move(edge: .leading).combined(with: .opacity)
                ))
            }
            .navigationTitle(navigationTitle)
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .topBarLeading) {
                    if coordinator.currentView != .library {
                        Button("Back") {
                            coordinator.showLibrary()
                        }
                    } else {
                        Button("Close") {
                            dismiss()
                        }
                    }
                }
                
                ToolbarItem(placement: .topBarTrailing) {
                    if coordinator.currentView == .library {
                        Menu {
                            Button("Clear Filters") {
                                coordinator.clearFilters()
                            }
                            
                            Button("Clear Selection") {
                                coordinator.clearSelectedExercises()
                            }
                        } label: {
                            Image(systemName: "ellipsis.circle")
                        }
                    }
                }
            }
        }
        .alert("Error", isPresented: .constant(coordinator.errorMessage != nil)) {
            Button("OK") {
                coordinator.errorMessage = nil
            }
        } message: {
            Text(coordinator.errorMessage ?? "")
        }
    }
    
    private var navigationTitle: String {
        switch coordinator.currentView {
        case .library:
            return "Exercise Library"
        case .quickWorkout:
            return "Quick Workout"
        case .customBuilder:
            return "Custom Workout"
        case .workoutPreview:
            return "Workout Preview"
        }
    }
}

// MARK: - Exercise Library View

struct ExerciseLibraryView: View {
    @ObservedObject var coordinator: ExerciseSelectionCoordinator
    
    var body: some View {
        VStack(spacing: 0) {
            // Quick action buttons
            QuickActionBar(coordinator: coordinator)
                .padding(.horizontal)
                .padding(.bottom, 16)
            
            // Search and filters
            SearchAndFilterBar(coordinator: coordinator)
                .padding(.horizontal)
                .padding(.bottom, 16)
            
            // Exercise list
            ScrollView {
                LazyVStack(spacing: 12) {
                    ForEach(coordinator.getFilteredExercises()) { exercise in
                        ExerciseCard(
                            exercise: exercise,
                            isSelected: coordinator.selectedExercises.contains { $0.id == exercise.id }
                        ) {
                            if coordinator.selectedExercises.contains(where: { $0.id == exercise.id }) {
                                coordinator.removeExercise(exercise)
                            } else {
                                coordinator.addExercise(exercise)
                            }
                        }
                    }
                }
                .padding(.horizontal)
                .padding(.bottom, 100) // Space for floating button
            }
            .scrollBounceBehavior(.basedOnSize)
        }
        .overlay(alignment: .bottom) {
            if !coordinator.selectedExercises.isEmpty {
                SelectedExercisesFloatingBar(coordinator: coordinator)
            }
        }
    }
}

// MARK: - Quick Action Bar

struct QuickActionBar: View {
    @ObservedObject var coordinator: ExerciseSelectionCoordinator
    
    var body: some View {
        HStack(spacing: 12) {
            QuickActionButton(
                icon: "bolt.fill",
                title: "Quick Workout",
                subtitle: "AI generated",
                color: .orange
            ) {
                coordinator.showQuickWorkout()
            }
            
            QuickActionButton(
                icon: "plus.circle.fill",
                title: "Custom Workout",
                subtitle: "Build your own",
                color: .blue
            ) {
                coordinator.showCustomBuilder()
            }
        }
    }
}

struct QuickActionButton: View {
    let icon: String
    let title: String
    let subtitle: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(16)
            .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
        }
        .buttonStyle(.plain)
    }
}

// MARK: - Search and Filter Bar

struct SearchAndFilterBar: View {
    @ObservedObject var coordinator: ExerciseSelectionCoordinator
    
    var body: some View {
        VStack(spacing: 12) {
            // Search bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                
                TextField("Search exercises...", text: $coordinator.searchText)
                    .textFieldStyle(.plain)
                
                if !coordinator.searchText.isEmpty {
                    Button("Clear") {
                        coordinator.searchText = ""
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 10))
            
            // Filter chips
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    FilterChip(
                        title: "Category",
                        value: coordinator.selectedCategory?.rawValue,
                        isActive: coordinator.selectedCategory != nil
                    ) {
                        // Show category picker
                    }
                    
                    FilterChip(
                        title: "Difficulty",
                        value: coordinator.selectedDifficulty?.rawValue,
                        isActive: coordinator.selectedDifficulty != nil
                    ) {
                        // Show difficulty picker
                    }
                    
                    FilterChip(
                        title: "Equipment",
                        value: coordinator.selectedEquipment?.rawValue,
                        isActive: coordinator.selectedEquipment != nil
                    ) {
                        // Show equipment picker
                    }
                }
                .padding(.horizontal)
            }
        }
    }
}

struct FilterChip: View {
    let title: String
    let value: String?
    let isActive: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Text(value ?? title)
                    .font(.caption)
                    .fontWeight(.medium)
                
                Image(systemName: "chevron.down")
                    .font(.caption2)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                isActive ? .blue.opacity(0.1) : .clear,
                in: Capsule()
            )
            .overlay(
                Capsule()
                    .stroke(isActive ? .blue : .secondary, lineWidth: 1)
            )
            .foregroundColor(isActive ? .blue : .secondary)
        }
        .buttonStyle(.plain)
    }
}

// MARK: - Exercise Card

struct ExerciseCard: View {
    let exercise: ExerciseData
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // Exercise thumbnail
                ZStack {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(.regularMaterial)
                        .frame(width: 60, height: 60)
                    
                    Image(systemName: exercise.type.icon)
                        .font(.title2)
                        .foregroundColor(.blue)
                }
                
                // Exercise info
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(exercise.name)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                        
                        Spacer()
                        
                        DifficultyBadge(difficulty: exercise.difficulty)
                    }
                    
                    Text(exercise.description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                    
                    HStack(spacing: 16) {
                        ExerciseInfoTag(
                            icon: "clock",
                            text: exercise.formattedDuration
                        )
                        
                        ExerciseInfoTag(
                            icon: "flame",
                            text: "\(exercise.estimatedCalories) cal"
                        )
                        
                        if exercise.equipment != .none {
                            ExerciseInfoTag(
                                icon: "dumbbell",
                                text: exercise.equipment.rawValue
                            )
                        }
                    }
                }
                
                // Selection indicator
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.green)
                } else {
                    Image(systemName: "plus.circle")
                        .font(.title2)
                        .foregroundColor(.blue)
                }
            }
            .padding(16)
            .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? .green : .clear, lineWidth: 2)
            )
            .scaleEffect(isSelected ? 1.02 : 1.0)
            .animation(.bouncy(duration: 0.3), value: isSelected)
        }
        .buttonStyle(.plain)
    }
}

struct DifficultyBadge: View {
    let difficulty: ExerciseDifficulty
    
    var body: some View {
        Text(difficulty.rawValue)
            .font(.caption)
            .fontWeight(.medium)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(difficulty.color.opacity(0.2))
            .foregroundColor(difficulty.color)
            .clipShape(Capsule())
    }
}

struct ExerciseInfoTag: View {
    let icon: String
    let text: String
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: icon)
                .font(.caption2)
            
            Text(text)
                .font(.caption)
        }
        .foregroundColor(.secondary)
    }
}

// MARK: - Selected Exercises Floating Bar

struct SelectedExercisesFloatingBar: View {
    @ObservedObject var coordinator: ExerciseSelectionCoordinator
    
    var body: some View {
        HStack(spacing: 16) {
            // Selected count
            HStack(spacing: 8) {
                Text("\(coordinator.selectedExercises.count)")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Text("exercises selected")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
            }
            
            Spacer()
            
            // Action button
            Button("Preview Workout") {
                coordinator.showWorkoutPreview()
            }
            .font(.subheadline)
            .fontWeight(.semibold)
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(.white.opacity(0.2), in: Capsule())
            .foregroundColor(.white)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(.blue.gradient, in: RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal, 16)
        .padding(.bottom, 34) // Account for home indicator
        .transition(.move(edge: .bottom).combined(with: .opacity))
    }
}

extension ExerciseDifficulty {
    var color: Color {
        switch self {
        case .beginner: return .green
        case .intermediate: return .orange
        case .advanced: return .red
        }
    }
}

#Preview("Exercise Selection") {
    ExerciseSelectionView()
}

#Preview("Exercise Card") {
    VStack(spacing: 12) {
        ExerciseCard(
            exercise: ExerciseData(
                id: UUID(),
                name: "Squat",
                type: .squat,
                category: .strength,
                difficulty: .beginner,
                equipment: .none,
                duration: 30,
                description: "Classic lower body exercise that targets quadriceps, hamstrings, and glutes",
                instructions: [],
                muscleGroups: [.quadriceps, .hamstrings, .glutes],
                caloriesBurned: 8,
                videoPreviewURL: nil,
                thumbnailName: "squat_thumbnail"
            ),
            isSelected: false
        ) { }
        
        ExerciseCard(
            exercise: ExerciseData(
                id: UUID(),
                name: "Push-up",
                type: .pushUp,
                category: .strength,
                difficulty: .intermediate,
                equipment: .none,
                duration: 45,
                description: "Upper body exercise targeting chest, shoulders, and triceps",
                instructions: [],
                muscleGroups: [.chest, .shoulders, .triceps],
                caloriesBurned: 7,
                videoPreviewURL: nil,
                thumbnailName: "pushup_thumbnail"
            ),
            isSelected: true
        ) { }
    }
    .padding()
}