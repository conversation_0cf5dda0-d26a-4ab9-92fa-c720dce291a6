import SwiftUI

struct QuickWorkoutView: View {
    @ObservedObject var coordinator: ExerciseSelectionCoordinator
    @State private var selectedPersonalization: WorkoutPersonalization?
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Header
                VStack(spacing: 12) {
                    Text("Quick Workout")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text("AI will create a personalized workout based on your preferences")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding(.top, 20)
                
                // Duration selection
                DurationSelectionSection(coordinator: coordinator)
                
                // Personalization options
                PersonalizationSection(selectedPersonalization: $selectedPersonalization)
                
                // Quick stats preview
                if coordinator.quickWorkoutDuration != .short {
                    WorkoutStatsPreview(duration: coordinator.quickWorkoutDuration)
                }
                
                // Generate button
                GenerateWorkoutButton(coordinator: coordinator)
                
                // Recent quick workouts
                RecentQuickWorkoutsSection()
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 100)
        }
        .scrollBounceBehavior(.basedOnSize)
    }
}

// MARK: - Duration Selection

struct DurationSelectionSection: View {
    @ObservedObject var coordinator: ExerciseSelectionCoordinator
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Workout Duration")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                ForEach(ExerciseSelectionCoordinator.QuickWorkoutDuration.allCases, id: \.self) { duration in
                    DurationCard(
                        duration: duration,
                        isSelected: coordinator.quickWorkoutDuration == duration
                    ) {
                        withAnimation(.bouncy(duration: 0.3)) {
                            coordinator.quickWorkoutDuration = duration
                        }
                    }
                }
            }
        }
    }
}

struct DurationCard: View {
    let duration: ExerciseSelectionCoordinator.QuickWorkoutDuration
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // Duration icon
                ZStack {
                    Circle()
                        .fill(isSelected ? .blue.opacity(0.2) : .gray.opacity(0.1))
                        .frame(width: 50, height: 50)
                    
                    Image(systemName: durationIcon)
                        .font(.title2)
                        .foregroundColor(isSelected ? .blue : .gray)
                }
                
                // Duration info
                VStack(alignment: .leading, spacing: 4) {
                    Text(duration.rawValue)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text("\(duration.exercises) exercises • Perfect for \(durationDescription)")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Selection indicator
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.blue)
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? .blue.opacity(0.05) : .regularMaterial)
                    .strokeBorder(isSelected ? .blue : .clear, lineWidth: 1.5)
            )
        }
        .buttonStyle(.plain)
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.bouncy(duration: 0.3), value: isSelected)
    }
    
    private var durationIcon: String {
        switch duration {
        case .short: return "clock"
        case .medium: return "clock.fill"
        case .long: return "stopwatch"
        }
    }
    
    private var durationDescription: String {
        switch duration {
        case .short: return "quick energy boost"
        case .medium: return "balanced workout"
        case .long: return "comprehensive session"
        }
    }
}

// MARK: - Personalization Section

struct PersonalizationSection: View {
    @Binding var selectedPersonalization: WorkoutPersonalization?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Focus On")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(WorkoutPersonalization.allCases, id: \.self) { personalization in
                    PersonalizationCard(
                        personalization: personalization,
                        isSelected: selectedPersonalization == personalization
                    ) {
                        if selectedPersonalization == personalization {
                            selectedPersonalization = nil
                        } else {
                            selectedPersonalization = personalization
                        }
                    }
                }
            }
        }
    }
}

struct PersonalizationCard: View {
    let personalization: WorkoutPersonalization
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                ZStack {
                    Circle()
                        .fill(personalization.color.opacity(0.2))
                        .frame(width: 50, height: 50)
                    
                    Image(systemName: personalization.icon)
                        .font(.title2)
                        .foregroundColor(personalization.color)
                }
                
                VStack(spacing: 4) {
                    Text(personalization.title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text(personalization.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
            }
            .frame(maxWidth: .infinity)
            .frame(height: 120)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? personalization.color.opacity(0.1) : .regularMaterial)
                    .strokeBorder(isSelected ? personalization.color : .clear, lineWidth: 1.5)
            )
        }
        .buttonStyle(.plain)
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.bouncy(duration: 0.3), value: isSelected)
    }
}

// MARK: - Workout Stats Preview

struct WorkoutStatsPreview: View {
    let duration: ExerciseSelectionCoordinator.QuickWorkoutDuration
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Workout Preview")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack(spacing: 20) {
                StatItem(
                    icon: "clock.fill",
                    title: "Duration",
                    value: duration.rawValue,
                    color: .blue
                )
                
                StatItem(
                    icon: "flame.fill",
                    title: "Est. Calories",
                    value: "\(estimatedCalories)",
                    color: .orange
                )
                
                StatItem(
                    icon: "figure.run",
                    title: "Exercises",
                    value: "\(duration.exercises)",
                    color: .green
                )
            }
        }
        .padding(16)
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
    }
    
    private var estimatedCalories: Int {
        switch duration {
        case .short: return 25
        case .medium: return 75
        case .long: return 150
        }
    }
}

struct StatItem: View {
    let icon: String
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            VStack(spacing: 2) {
                Text(value)
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .frame(maxWidth: .infinity)
    }
}

// MARK: - Generate Button

struct GenerateWorkoutButton: View {
    @ObservedObject var coordinator: ExerciseSelectionCoordinator
    
    var body: some View {
        Button {
            Task {
                await coordinator.generateQuickWorkout()
            }
        } label: {
            HStack(spacing: 12) {
                if coordinator.isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                } else {
                    Image(systemName: "sparkles")
                        .font(.title3)
                }
                
                Text(coordinator.isLoading ? "Generating..." : "Generate Workout")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 56)
            .foregroundColor(.white)
            .background(.blue.gradient, in: RoundedRectangle(cornerRadius: 16))
        }
        .disabled(coordinator.isLoading)
        .opacity(coordinator.isLoading ? 0.8 : 1.0)
        .scaleEffect(coordinator.isLoading ? 0.98 : 1.0)
        .animation(.bouncy(duration: 0.3), value: coordinator.isLoading)
    }
}

// MARK: - Recent Workouts Section

struct RecentQuickWorkoutsSection: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Recent Quick Workouts")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                RecentWorkoutRow(
                    name: "Morning Energy Boost",
                    duration: "5 min",
                    exercises: 3,
                    date: "Today"
                )
                
                RecentWorkoutRow(
                    name: "Full Body Burn",
                    duration: "15 min",
                    exercises: 6,
                    date: "Yesterday"
                )
                
                RecentWorkoutRow(
                    name: "Quick HIIT",
                    duration: "10 min",
                    exercises: 4,
                    date: "2 days ago"
                )
            }
        }
    }
}

struct RecentWorkoutRow: View {
    let name: String
    let duration: String
    let exercises: Int
    let date: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: "clock.arrow.circlepath")
                .font(.title2)
                .foregroundColor(.blue)
                .frame(width: 32)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Text("\(duration) • \(exercises) exercises")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Text(date)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 8))
    }
}

// MARK: - Supporting Types

enum WorkoutPersonalization: String, CaseIterable {
    case strength = "Strength"
    case cardio = "Cardio"
    case flexibility = "Flexibility"
    case balance = "Balance"
    
    var title: String { rawValue }
    
    var description: String {
        switch self {
        case .strength: return "Build muscle"
        case .cardio: return "Heart health"
        case .flexibility: return "Improve mobility"
        case .balance: return "Stability focus"
        }
    }
    
    var icon: String {
        switch self {
        case .strength: return "dumbbell.fill"
        case .cardio: return "heart.fill"
        case .flexibility: return "figure.yoga"
        case .balance: return "figure.stand"
        }
    }
    
    var color: Color {
        switch self {
        case .strength: return .red
        case .cardio: return .pink
        case .flexibility: return .green
        case .balance: return .purple
        }
    }
}

#Preview("Quick Workout") {
    NavigationStack {
        QuickWorkoutView(coordinator: ExerciseSelectionCoordinator())
    }
}