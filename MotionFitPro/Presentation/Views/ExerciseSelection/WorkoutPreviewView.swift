import SwiftUI

struct WorkoutPreviewView: View {
    @ObservedObject var coordinator: ExerciseSelectionCoordinator
    @State private var showingStartConfirmation = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Header stats
                WorkoutHeaderStats(exercises: coordinator.selectedExercises)
                
                // Exercise list
                ExerciseListSection(coordinator: coordinator)
                
                // Workout settings
                WorkoutSettingsSection()
                
                // Tips section
                WorkoutTipsSection()
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 120) // Space for start button
        }
        .scrollBounceBehavior(.basedOnSize)
        .overlay(alignment: .bottom) {
            StartWorkoutButton {
                showingStartConfirmation = true
            }
        }
        .confirmationDialog("Start Workout", isPresented: $showingStartConfirmation) {
            Button("Start Now") {
                // Start workout
            }
            
            Button("Save for Later") {
                // Save workout
            }
            
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("Ready to begin your \(totalWorkoutTime) workout?")
        }
    }
    
    private var totalWorkoutTime: String {
        let totalSeconds = coordinator.selectedExercises.reduce(0) { $0 + $1.duration }
        let minutes = Int(totalSeconds / 60)
        return "\(minutes) minute"
    }
}

// MARK: - Header Stats

struct WorkoutHeaderStats: View {
    let exercises: [ExerciseData]
    
    var body: some View {
        VStack(spacing: 20) {
            Text("Workout Ready!")
                .font(.title2)
                .fontWeight(.bold)
            
            HStack(spacing: 30) {
                StatCard(
                    icon: "figure.run",
                    title: "Exercises",
                    value: "\(exercises.count)",
                    color: .blue
                )
                
                StatCard(
                    icon: "clock.fill",
                    title: "Duration",
                    value: formatTotalDuration(exercises),
                    color: .green
                )
                
                StatCard(
                    icon: "flame.fill",
                    title: "Est. Calories",
                    value: "\(estimatedCalories)",
                    color: .orange
                )
            }
            .padding(.horizontal, 20)
        }
        .padding(.top, 20)
    }
    
    private func formatTotalDuration(_ exercises: [ExerciseData]) -> String {
        let totalSeconds = exercises.reduce(0) { $0 + $1.duration }
        let minutes = Int(totalSeconds / 60)
        let seconds = Int(totalSeconds.truncatingRemainder(dividingBy: 60))
        
        if minutes > 0 && seconds > 0 {
            return "\(minutes)m"
        } else if minutes > 0 {
            return "\(minutes)m"
        } else {
            return "\(seconds)s"
        }
    }
    
    private var estimatedCalories: Int {
        exercises.reduce(0) { $0 + $1.estimatedCalories }
    }
}

struct StatCard: View {
    let icon: String
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            ZStack {
                Circle()
                    .fill(color.opacity(0.2))
                    .frame(width: 50, height: 50)
                
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
            }
            
            VStack(spacing: 2) {
                Text(value)
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
}

// MARK: - Exercise List Section

struct ExerciseListSection: View {
    @ObservedObject var coordinator: ExerciseSelectionCoordinator
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Exercise Sequence")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("Edit") {
                    coordinator.showLibrary()
                }
                .font(.subheadline)
                .foregroundColor(.blue)
            }
            
            VStack(spacing: 12) {
                ForEach(Array(coordinator.selectedExercises.enumerated()), id: \.element.id) { index, exercise in
                    ExerciseSequenceCard(
                        exercise: exercise,
                        position: index + 1,
                        isLast: index == coordinator.selectedExercises.count - 1
                    )
                }
            }
        }
    }
}

struct ExerciseSequenceCard: View {
    let exercise: ExerciseData
    let position: Int
    let isLast: Bool
    
    var body: some View {
        HStack(spacing: 16) {
            // Position indicator
            ZStack {
                Circle()
                    .fill(.blue.opacity(0.1))
                    .frame(width: 40, height: 40)
                
                Text("\(position)")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.blue)
            }
            
            // Exercise thumbnail
            ZStack {
                RoundedRectangle(cornerRadius: 8)
                    .fill(.regularMaterial)
                    .frame(width: 50, height: 50)
                
                Image(systemName: exercise.type.icon)
                    .font(.title3)
                    .foregroundColor(.blue)
            }
            
            // Exercise info
            VStack(alignment: .leading, spacing: 4) {
                Text(exercise.name)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                HStack(spacing: 12) {
                    Label(exercise.formattedDuration, systemImage: "clock")
                    Label("\(exercise.estimatedCalories) cal", systemImage: "flame")
                }
                .font(.caption)
                .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // Difficulty badge
            DifficultyBadge(difficulty: exercise.difficulty)
        }
        .padding(16)
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
        
        // Rest indicator
        if !isLast {
            HStack {
                Spacer()
                
                VStack(spacing: 8) {
                    Circle()
                        .fill(.gray.opacity(0.3))
                        .frame(width: 8, height: 8)
                    
                    Text("30s rest")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    Circle()
                        .fill(.gray.opacity(0.3))
                        .frame(width: 8, height: 8)
                }
                
                Spacer()
            }
            .padding(.vertical, 8)
        }
    }
}

// MARK: - Workout Settings

struct WorkoutSettingsSection: View {
    @State private var restDuration: Double = 30
    @State private var enableVoiceCoaching = true
    @State private var enableHapticFeedback = true
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Workout Settings")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 16) {
                // Rest duration
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("Rest Between Exercises")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Spacer()
                        
                        Text("\(Int(restDuration))s")
                            .font(.subheadline)
                            .foregroundColor(.blue)
                    }
                    
                    Slider(value: $restDuration, in: 15...120, step: 15)
                        .tint(.blue)
                }
                
                Divider()
                
                // Voice coaching
                HStack {
                    Image(systemName: "speaker.wave.2.fill")
                        .foregroundColor(.blue)
                        .frame(width: 24)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Voice Coaching")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Text("Get audio guidance during exercises")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Toggle("", isOn: $enableVoiceCoaching)
                        .labelsHidden()
                }
                
                Divider()
                
                // Haptic feedback
                HStack {
                    Image(systemName: "iphone.radiowaves.left.and.right")
                        .foregroundColor(.blue)
                        .frame(width: 24)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Haptic Feedback")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Text("Feel vibrations for rep counting")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Toggle("", isOn: $enableHapticFeedback)
                        .labelsHidden()
                }
            }
            .padding(16)
            .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
        }
    }
}

// MARK: - Workout Tips

struct WorkoutTipsSection: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Before You Start")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                WorkoutTip(
                    icon: "figure.stand",
                    title: "Position Yourself",
                    description: "Make sure your full body is visible in the camera"
                )
                
                WorkoutTip(
                    icon: "lightbulb.fill",
                    title: "Good Lighting",
                    description: "Ensure proper lighting for accurate motion tracking"
                )
                
                WorkoutTip(
                    icon: "speaker.wave.2.fill",
                    title: "Audio Setup",
                    description: "Use headphones for the best coaching experience"
                )
                
                WorkoutTip(
                    icon: "drop.fill",
                    title: "Stay Hydrated",
                    description: "Keep water nearby and take sips during rest periods"
                )
            }
        }
    }
}

struct WorkoutTip: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.blue)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
    }
}

// MARK: - Start Button

struct StartWorkoutButton: View {
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: "play.fill")
                    .font(.title3)
                
                Text("Start Workout")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 56)
            .foregroundColor(.white)
            .background(.blue.gradient, in: RoundedRectangle(cornerRadius: 16))
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 34) // Account for home indicator
        .background(.regularMaterial)
    }
}

#Preview("Workout Preview") {
    let coordinator = ExerciseSelectionCoordinator()
    coordinator.selectedExercises = [
        ExerciseData(
            id: UUID(),
            name: "Squat",
            type: .squat,
            category: .strength,
            difficulty: .beginner,
            equipment: .none,
            duration: 30,
            description: "Classic lower body exercise",
            instructions: [],
            muscleGroups: [.quadriceps, .hamstrings, .glutes],
            caloriesBurned: 8,
            videoPreviewURL: nil,
            thumbnailName: "squat_thumbnail"
        ),
        ExerciseData(
            id: UUID(),
            name: "Push-up",
            type: .pushUp,
            category: .strength,
            difficulty: .intermediate,
            equipment: .none,
            duration: 45,
            description: "Upper body exercise",
            instructions: [],
            muscleGroups: [.chest, .shoulders, .triceps],
            caloriesBurned: 7,
            videoPreviewURL: nil,
            thumbnailName: "pushup_thumbnail"
        ),
        ExerciseData(
            id: UUID(),
            name: "Plank",
            type: .plank,
            category: .strength,
            difficulty: .beginner,
            equipment: .none,
            duration: 60,
            description: "Core strengthening exercise",
            instructions: [],
            muscleGroups: [.core, .shoulders],
            caloriesBurned: 5,
            videoPreviewURL: nil,
            thumbnailName: "plank_thumbnail"
        )
    ]
    
    return NavigationStack {
        WorkoutPreviewView(coordinator: coordinator)
    }
}