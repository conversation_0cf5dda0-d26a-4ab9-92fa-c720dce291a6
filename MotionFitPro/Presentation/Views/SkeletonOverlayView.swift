import SwiftUI
import ARKit
import simd

struct SkeletonOverlayView: View {
    let poseData: BodyPoseData
    @State private var animationProgress: Double = 0
    
    private let jointConnections: [(String, String)] = [
        // Spine
        ("root", "spine_1"),
        ("spine_1", "spine_2"),
        ("spine_2", "spine_4"),
        ("spine_4", "spine_6"),
        ("spine_6", "spine_7"),
        ("spine_7", "left_shoulder_1"),
        ("spine_7", "right_shoulder_1"),
        ("spine_7", "neck_1"),
        ("neck_1", "head"),
        
        // Left arm
        ("left_shoulder_1", "left_arm"),
        ("left_arm", "left_forearm"),
        ("left_forearm", "left_hand"),
        
        // Right arm
        ("right_shoulder_1", "right_arm"),
        ("right_arm", "right_forearm"),
        ("right_forearm", "right_hand"),
        
        // Left leg
        ("root", "left_upLeg"),
        ("left_upLeg", "left_leg"),
        ("left_leg", "left_foot"),
        
        // Right leg
        ("root", "right_upLeg"),
        ("right_upLeg", "right_leg"),
        ("right_leg", "right_foot")
    ]
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Draw skeleton connections
                ForEach(Array(jointConnections.enumerated()), id: \.offset) { index, connection in
                    if let startJoint = poseData.joints[connection.0],
                       let endJoint = poseData.joints[connection.1],
                       startJoint.isValid && endJoint.isValid {
                        
                        SkeletonConnection(
                            start: projectToScreen(startJoint.position, geometry: geometry),
                            end: projectToScreen(endJoint.position, geometry: geometry),
                            confidence: min(startJoint.confidence, endJoint.confidence),
                            animationDelay: Double(index) * 0.02
                        )
                    }
                }
                
                // Draw joints
                ForEach(Array(poseData.joints.keys.sorted()), id: \.self) { jointName in
                    if let joint = poseData.joints[jointName], joint.isValid {
                        SkeletonJoint(
                            position: projectToScreen(joint.position, geometry: geometry),
                            confidence: joint.confidence,
                            jointType: JointType(from: jointName),
                            animationDelay: jointAnimationDelay(for: jointName)
                        )
                    }
                }
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 0.5)) {
                animationProgress = 1.0
            }
        }
        .onChange(of: poseData.timestamp) { _, _ in
            // Subtle pulse animation on pose update
            withAnimation(.easeInOut(duration: 0.1)) {
                animationProgress = 0.8
            }
            withAnimation(.easeInOut(duration: 0.1).delay(0.1)) {
                animationProgress = 1.0
            }
        }
    }
    
    private func projectToScreen(_ position: simd_float3, geometry: GeometryProxy) -> CGPoint {
        // Simple 2D projection - in a real app, you'd use ARKit's projection matrix
        let normalizedX = (position.x + 1.0) / 2.0 // Normalize from [-1, 1] to [0, 1]
        let normalizedY = 1.0 - ((position.y + 1.0) / 2.0) // Flip Y and normalize
        
        return CGPoint(
            x: CGFloat(normalizedX) * geometry.size.width,
            y: CGFloat(normalizedY) * geometry.size.height
        )
    }
    
    private func jointAnimationDelay(for jointName: String) -> Double {
        // Stagger joint animations from center outward
        switch jointName {
        case "root", "spine_1": return 0.0
        case "spine_2", "spine_4": return 0.1
        case "spine_6", "spine_7", "neck_1": return 0.2
        case "head": return 0.3
        case _ where jointName.contains("shoulder"): return 0.2
        case _ where jointName.contains("arm"): return 0.3
        case _ where jointName.contains("forearm"), _ where jointName.contains("hand"): return 0.4
        case _ where jointName.contains("upLeg"): return 0.2
        case _ where jointName.contains("leg"): return 0.3
        case _ where jointName.contains("foot"): return 0.4
        default: return 0.5
        }
    }
}

// MARK: - Skeleton Connection

struct SkeletonConnection: View {
    let start: CGPoint
    let end: CGPoint
    let confidence: Float
    let animationDelay: Double
    
    @State private var drawProgress: Double = 0
    
    var body: some View {
        Path { path in
            path.move(to: start)
            path.addLine(to: end)
        }
        .trim(from: 0, to: drawProgress)
        .stroke(
            connectionColor,
            style: StrokeStyle(
                lineWidth: lineWidth,
                lineCap: .round
            )
        )
        .shadow(color: connectionColor.opacity(0.5), radius: 2)
        .onAppear {
            withAnimation(.easeInOut(duration: 0.3).delay(animationDelay)) {
                drawProgress = 1.0
            }
        }
        .onChange(of: start) { _, _ in
            // Smooth position updates
            withAnimation(.interactiveSpring(response: 0.3, dampingFraction: 0.8)) {
                drawProgress = 1.0
            }
        }
    }
    
    private var connectionColor: Color {
        let alpha = Double(confidence)
        switch confidence {
        case 0.8...:
            return .green.opacity(alpha)
        case 0.6..<0.8:
            return .yellow.opacity(alpha)
        case 0.4..<0.6:
            return .orange.opacity(alpha)
        default:
            return .red.opacity(alpha)
        }
    }
    
    private var lineWidth: CGFloat {
        CGFloat(2.0 + confidence * 2.0) // Width based on confidence
    }
}

// MARK: - Skeleton Joint

struct SkeletonJoint: View {
    let position: CGPoint
    let confidence: Float
    let jointType: JointType
    let animationDelay: Double
    
    @State private var scale: Double = 0
    @State private var pulseScale: Double = 1.0
    
    var body: some View {
        Circle()
            .fill(
                RadialGradient(
                    colors: [jointColor, jointColor.opacity(0.7)],
                    center: .center,
                    startRadius: 0,
                    endRadius: jointSize / 2
                )
            )
            .frame(width: jointSize, height: jointSize)
            .scaleEffect(scale * pulseScale)
            .position(position)
            .shadow(color: jointColor.opacity(0.6), radius: 4)
            .onAppear {
                withAnimation(.bouncy(duration: 0.4).delay(animationDelay)) {
                    scale = 1.0
                }
                
                // Subtle pulse for key joints
                if jointType.isKeyJoint {
                    withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
                        pulseScale = 1.1
                    }
                }
            }
            .onChange(of: position) { _, _ in
                // Smooth position interpolation
                withAnimation(.interactiveSpring(response: 0.2, dampingFraction: 0.9)) {
                    scale = 1.0
                }
            }
    }
    
    private var jointColor: Color {
        let alpha = Double(confidence)
        switch jointType {
        case .head:
            return .blue.opacity(alpha)
        case .spine:
            return .purple.opacity(alpha)
        case .shoulder, .hip:
            return .cyan.opacity(alpha)
        case .elbow, .knee:
            return .yellow.opacity(alpha)
        case .hand, .foot:
            return .orange.opacity(alpha)
        case .other:
            return .white.opacity(alpha)
        }
    }
    
    private var jointSize: CGFloat {
        let baseSize: CGFloat = 8.0
        let confidenceMultiplier = CGFloat(confidence * 0.5 + 0.5) // 0.5 to 1.0 range
        return baseSize * confidenceMultiplier * jointType.sizeMultiplier
    }
}

// MARK: - Supporting Types

enum JointType {
    case head
    case spine
    case shoulder
    case elbow
    case hand
    case hip
    case knee
    case foot
    case other
    
    init(from jointName: String) {
        switch jointName {
        case "head":
            self = .head
        case _ where jointName.contains("spine"):
            self = .spine
        case _ where jointName.contains("shoulder"):
            self = .shoulder
        case _ where jointName.contains("arm") && !jointName.contains("forearm"):
            self = .elbow
        case _ where jointName.contains("forearm"), _ where jointName.contains("hand"):
            self = .hand
        case "root", _ where jointName.contains("upLeg"):
            self = .hip
        case _ where jointName.contains("leg") && !jointName.contains("upLeg"):
            self = .knee
        case _ where jointName.contains("foot"):
            self = .foot
        default:
            self = .other
        }
    }
    
    var sizeMultiplier: CGFloat {
        switch self {
        case .head:
            return 1.5
        case .spine, .shoulder, .hip:
            return 1.2
        case .elbow, .knee:
            return 1.1
        case .hand, .foot:
            return 1.0
        case .other:
            return 0.8
        }
    }
    
    var isKeyJoint: Bool {
        switch self {
        case .head, .spine, .shoulder, .hip:
            return true
        default:
            return false
        }
    }
}

// MARK: - Confidence Indicator

struct SkeletonConfidenceIndicator: View {
    let overallConfidence: Float
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: "figure.walk")
                .font(.system(size: 14))
                .foregroundColor(confidenceColor)
            
            Text("Tracking: \(Int(overallConfidence * 100))%")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.white)
            
            // Confidence bars
            HStack(spacing: 2) {
                ForEach(0..<5, id: \.self) { index in
                    Rectangle()
                        .fill(index < Int(overallConfidence * 5) ? confidenceColor : .gray.opacity(0.3))
                        .frame(width: 3, height: 12)
                        .animation(.easeInOut(duration: 0.2), value: overallConfidence)
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(.ultraThinMaterial, in: Capsule())
    }
    
    private var confidenceColor: Color {
        switch overallConfidence {
        case 0.8...:
            return .green
        case 0.6..<0.8:
            return .yellow
        case 0.4..<0.6:
            return .orange
        default:
            return .red
        }
    }
}

#Preview("Skeleton Overlay") {
    // Create sample pose data for preview
    let sampleJoints: [String: BodyPoseData.JointData] = [
        "root": BodyPoseData.JointData(position: simd_float3(0, -0.5, 0), confidence: 0.9, isValid: true),
        "spine_1": BodyPoseData.JointData(position: simd_float3(0, -0.3, 0), confidence: 0.9, isValid: true),
        "spine_2": BodyPoseData.JointData(position: simd_float3(0, -0.1, 0), confidence: 0.9, isValid: true),
        "spine_4": BodyPoseData.JointData(position: simd_float3(0, 0.1, 0), confidence: 0.9, isValid: true),
        "spine_6": BodyPoseData.JointData(position: simd_float3(0, 0.3, 0), confidence: 0.9, isValid: true),
        "spine_7": BodyPoseData.JointData(position: simd_float3(0, 0.5, 0), confidence: 0.9, isValid: true),
        "neck_1": BodyPoseData.JointData(position: simd_float3(0, 0.6, 0), confidence: 0.9, isValid: true),
        "head": BodyPoseData.JointData(position: simd_float3(0, 0.8, 0), confidence: 0.9, isValid: true),
        
        // Arms
        "left_shoulder_1": BodyPoseData.JointData(position: simd_float3(-0.3, 0.4, 0), confidence: 0.8, isValid: true),
        "right_shoulder_1": BodyPoseData.JointData(position: simd_float3(0.3, 0.4, 0), confidence: 0.8, isValid: true),
        "left_arm": BodyPoseData.JointData(position: simd_float3(-0.5, 0.2, 0), confidence: 0.7, isValid: true),
        "right_arm": BodyPoseData.JointData(position: simd_float3(0.5, 0.2, 0), confidence: 0.7, isValid: true),
        "left_forearm": BodyPoseData.JointData(position: simd_float3(-0.6, 0, 0), confidence: 0.6, isValid: true),
        "right_forearm": BodyPoseData.JointData(position: simd_float3(0.6, 0, 0), confidence: 0.6, isValid: true),
        "left_hand": BodyPoseData.JointData(position: simd_float3(-0.7, -0.2, 0), confidence: 0.5, isValid: true),
        "right_hand": BodyPoseData.JointData(position: simd_float3(0.7, -0.2, 0), confidence: 0.5, isValid: true),
        
        // Legs
        "left_upLeg": BodyPoseData.JointData(position: simd_float3(-0.15, -0.5, 0), confidence: 0.8, isValid: true),
        "right_upLeg": BodyPoseData.JointData(position: simd_float3(0.15, -0.5, 0), confidence: 0.8, isValid: true),
        "left_leg": BodyPoseData.JointData(position: simd_float3(-0.2, -0.8, 0), confidence: 0.7, isValid: true),
        "right_leg": BodyPoseData.JointData(position: simd_float3(0.2, -0.8, 0), confidence: 0.7, isValid: true),
        "left_foot": BodyPoseData.JointData(position: simd_float3(-0.2, -1.0, 0), confidence: 0.6, isValid: true),
        "right_foot": BodyPoseData.JointData(position: simd_float3(0.2, -1.0, 0), confidence: 0.6, isValid: true)
    ]
    
    let samplePose = BodyPoseData(
        timestamp: Date().timeIntervalSince1970,
        worldTransform: matrix_identity_float4x4,
        joints: sampleJoints,
        confidence: 0.8
    )
    
    return ZStack {
        Color.black.ignoresSafeArea()
        
        VStack {
            SkeletonConfidenceIndicator(overallConfidence: samplePose.confidence)
                .padding(.top)
            
            Spacer()
            
            SkeletonOverlayView(poseData: samplePose)
                .frame(width: 300, height: 500)
            
            Spacer()
        }
    }
}

#Preview("Confidence Indicator") {
    VStack(spacing: 20) {
        SkeletonConfidenceIndicator(overallConfidence: 0.9)
        SkeletonConfidenceIndicator(overallConfidence: 0.7)
        SkeletonConfidenceIndicator(overallConfidence: 0.5)
        SkeletonConfidenceIndicator(overallConfidence: 0.3)
    }
    .padding()
    .background(.black)
}