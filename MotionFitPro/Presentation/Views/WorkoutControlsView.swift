import SwiftUI

struct WorkoutControlsView: View {
    @State private var workoutState: WorkoutState = .ready
    @State private var currentSet = 1
    @State private var repsInCurrentSet = 0
    @State private var restTimeRemaining: TimeInterval = 0
    @State private var exerciseTime: TimeInterval = 0
    @State private var restTimer: Timer?
    @State private var workoutTimer: Timer?
    @State private var showingExerciseSelector = false
    @State private var currentExercise: ExerciseType = .squat
    
    let targetSets = 3
    let targetRepsPerSet = 12
    let restDuration: TimeInterval = 60
    
    var body: some View {
        VStack(spacing: 16) {
            // Progress indicators
            if workoutState != .ready {
                WorkoutProgressView(
                    currentSet: currentSet,
                    targetSets: targetSets,
                    repsInCurrentSet: repsInCurrentSet,
                    targetReps: targetRepsPerSet,
                    exerciseTime: exerciseTime
                )
            }
            
            // Rest timer
            if workoutState == .resting {
                RestTimerView(timeRemaining: restTimeRemaining)
                    .transition(.scale.combined(with: .opacity))
            }
            
            // Main controls
            HStack(spacing: 20) {
                // Exercise selector
                ExerciseSelectorButton(
                    currentExercise: currentExercise,
                    isEnabled: workoutState == .ready || workoutState == .paused
                ) {
                    showingExerciseSelector = true
                }
                
                Spacer()
                
                // Primary action button
                PrimaryActionButton(
                    state: workoutState,
                    action: handlePrimaryAction
                )
                
                Spacer()
                
                // Secondary controls
                SecondaryControlsMenu(
                    workoutState: workoutState,
                    onStop: stopWorkout,
                    onSkipRest: skipRest,
                    onReset: resetWorkout
                )
            }
            .padding(.horizontal, 24)
        }
        .padding(.vertical, 16)
        .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 20))
        .padding(.horizontal, 20)
        .onReceive(Timer.publish(every: 1, on: .main, in: .common).autoconnect()) { _ in
            updateTimers()
        }
        .sheet(isPresented: $showingExerciseSelector) {
            ExerciseSelectorSheet(selectedExercise: $currentExercise)
        }
    }
    
    private func handlePrimaryAction() {
        switch workoutState {
        case .ready:
            startWorkout()
        case .active:
            pauseWorkout()
        case .paused:
            resumeWorkout()
        case .resting:
            skipRest()
        case .completed:
            resetWorkout()
        }
    }
    
    private func startWorkout() {
        withAnimation(.bouncy(duration: 0.4)) {
            workoutState = .active
        }
        
        startWorkoutTimer()
        
        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
    
    private func pauseWorkout() {
        withAnimation(.easeInOut(duration: 0.3)) {
            workoutState = .paused
        }
        
        stopWorkoutTimer()
    }
    
    private func resumeWorkout() {
        withAnimation(.bouncy(duration: 0.4)) {
            workoutState = .active
        }
        
        startWorkoutTimer()
    }
    
    private func stopWorkout() {
        withAnimation(.easeInOut(duration: 0.5)) {
            workoutState = .ready
        }
        
        stopAllTimers()
        resetProgress()
        
        // Success haptic
        let successFeedback = UINotificationFeedbackGenerator()
        successFeedback.notificationOccurred(.success)
    }
    
    private func completeSet() {
        if currentSet < targetSets {
            startRest()
        } else {
            completeWorkout()
        }
    }
    
    private func startRest() {
        withAnimation(.easeInOut(duration: 0.3)) {
            workoutState = .resting
        }
        
        restTimeRemaining = restDuration
        startRestTimer()
        
        // Move to next set
        currentSet += 1
        repsInCurrentSet = 0
    }
    
    private func skipRest() {
        stopRestTimer()
        withAnimation(.bouncy(duration: 0.4)) {
            workoutState = .active
        }
        startWorkoutTimer()
    }
    
    private func completeWorkout() {
        withAnimation(.bouncy(duration: 0.6)) {
            workoutState = .completed
        }
        
        stopAllTimers()
        
        // Celebration haptic
        let successFeedback = UINotificationFeedbackGenerator()
        successFeedback.notificationOccurred(.success)
    }
    
    private func resetWorkout() {
        withAnimation(.easeInOut(duration: 0.5)) {
            workoutState = .ready
        }
        
        stopAllTimers()
        resetProgress()
    }
    
    private func resetProgress() {
        currentSet = 1
        repsInCurrentSet = 0
        exerciseTime = 0
        restTimeRemaining = 0
    }
    
    private func startWorkoutTimer() {
        workoutTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            exerciseTime += 1
        }
    }
    
    private func stopWorkoutTimer() {
        workoutTimer?.invalidate()
        workoutTimer = nil
    }
    
    private func startRestTimer() {
        restTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            if restTimeRemaining > 0 {
                restTimeRemaining -= 1
            } else {
                skipRest()
            }
        }
    }
    
    private func stopRestTimer() {
        restTimer?.invalidate()
        restTimer = nil
    }
    
    private func stopAllTimers() {
        stopWorkoutTimer()
        stopRestTimer()
    }
    
    private func updateTimers() {
        // This method is called every second to update any time-based UI
        // Individual timer logic is handled by the specific timer methods
    }
}

// MARK: - Primary Action Button

struct PrimaryActionButton: View {
    let state: WorkoutState
    let action: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: action) {
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: buttonColors,
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 80, height: 80)
                    .shadow(color: buttonColors.first?.opacity(0.4) ?? .clear, radius: 8, x: 0, y: 4)
                
                Image(systemName: buttonIcon)
                    .font(.system(size: 28, weight: .semibold))
                    .foregroundColor(.white)
            }
        }
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .animation(.bouncy(duration: 0.2), value: isPressed)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
        .accessibilityLabel(buttonAccessibilityLabel)
        .accessibilityHint("Tap to \(buttonAccessibilityLabel.lowercased())")
    }
    
    private var buttonIcon: String {
        switch state {
        case .ready:
            return "play.fill"
        case .active:
            return "pause.fill"
        case .paused:
            return "play.fill"
        case .resting:
            return "forward.fill"
        case .completed:
            return "arrow.clockwise"
        }
    }
    
    private var buttonColors: [Color] {
        switch state {
        case .ready:
            return [.green, .green.opacity(0.8)]
        case .active:
            return [.orange, .orange.opacity(0.8)]
        case .paused:
            return [.blue, .blue.opacity(0.8)]
        case .resting:
            return [.purple, .purple.opacity(0.8)]
        case .completed:
            return [.cyan, .cyan.opacity(0.8)]
        }
    }
    
    private var buttonAccessibilityLabel: String {
        switch state {
        case .ready:
            return "Start workout"
        case .active:
            return "Pause workout"
        case .paused:
            return "Resume workout"
        case .resting:
            return "Skip rest"
        case .completed:
            return "Start new workout"
        }
    }
}

// MARK: - Exercise Selector Button

struct ExerciseSelectorButton: View {
    let currentExercise: ExerciseType
    let isEnabled: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Image(systemName: currentExercise.icon)
                    .font(.system(size: 20))
                    .foregroundColor(isEnabled ? .white : .gray)
                
                Text(currentExercise.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(isEnabled ? .white : .gray)
            }
            .frame(width: 60, height: 60)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
                    .opacity(isEnabled ? 1.0 : 0.5)
            )
        }
        .disabled(!isEnabled)
        .accessibilityLabel("Current exercise: \(currentExercise.displayName)")
        .accessibilityHint("Tap to change exercise")
    }
}

// MARK: - Secondary Controls Menu

struct SecondaryControlsMenu: View {
    let workoutState: WorkoutState
    let onStop: () -> Void
    let onSkipRest: () -> Void
    let onReset: () -> Void
    
    var body: some View {
        Menu {
            if workoutState == .active || workoutState == .paused {
                Button("Stop Workout", systemImage: "stop.fill", role: .destructive) {
                    onStop()
                }
            }
            
            if workoutState == .resting {
                Button("Skip Rest", systemImage: "forward.fill") {
                    onSkipRest()
                }
            }
            
            if workoutState != .ready {
                Button("Reset", systemImage: "arrow.clockwise") {
                    onReset()
                }
            }
        } label: {
            Image(systemName: "ellipsis")
                .font(.system(size: 20, weight: .medium))
                .foregroundColor(.white)
                .frame(width: 44, height: 44)
                .background(.ultraThinMaterial, in: Circle())
        }
        .accessibilityLabel("More options")
    }
}

// MARK: - Workout Progress View

struct WorkoutProgressView: View {
    let currentSet: Int
    let targetSets: Int
    let repsInCurrentSet: Int
    let targetReps: Int
    let exerciseTime: TimeInterval
    
    var body: some View {
        HStack(spacing: 20) {
            // Set progress
            VStack(spacing: 4) {
                Text("Set")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                
                Text("\(currentSet)/\(targetSets)")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
            }
            
            Divider()
                .frame(height: 30)
                .background(.white.opacity(0.3))
            
            // Rep progress
            VStack(spacing: 4) {
                Text("Reps")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                
                Text("\(repsInCurrentSet)/\(targetReps)")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
            }
            
            Divider()
                .frame(height: 30)
                .background(.white.opacity(0.3))
            
            // Exercise time
            VStack(spacing: 4) {
                Text("Time")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                
                Text(formatTime(exerciseTime))
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
        .background(.ultraThinMaterial, in: Capsule())
    }
    
    private func formatTime(_ time: TimeInterval) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}

// MARK: - Rest Timer View

struct RestTimerView: View {
    let timeRemaining: TimeInterval
    
    @State private var pulseScale: CGFloat = 1.0
    
    var body: some View {
        VStack(spacing: 12) {
            Text("Rest Time")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            ZStack {
                Circle()
                    .stroke(.white.opacity(0.3), lineWidth: 4)
                    .frame(width: 80, height: 80)
                
                Circle()
                    .trim(from: 0, to: progressPercentage)
                    .stroke(.blue, style: StrokeStyle(lineWidth: 4, lineCap: .round))
                    .frame(width: 80, height: 80)
                    .rotationEffect(.degrees(-90))
                    .animation(.linear(duration: 1), value: progressPercentage)
                
                Text(formatRestTime(timeRemaining))
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .scaleEffect(pulseScale)
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true)) {
                pulseScale = 1.1
            }
        }
    }
    
    private var progressPercentage: Double {
        let restDuration: TimeInterval = 60 // This should come from the parent
        return max(0, timeRemaining / restDuration)
    }
    
    private func formatRestTime(_ time: TimeInterval) -> String {
        let seconds = max(0, Int(time))
        return "\(seconds)"
    }
}

// MARK: - Exercise Selector Sheet

struct ExerciseSelectorSheet: View {
    @Binding var selectedExercise: ExerciseType
    @Environment(\.dismiss) private var dismiss
    
    let exercises: [ExerciseType] = [.squat, .pushUp, .plank, .jumpingJack]
    
    var body: some View {
        NavigationView {
            List(exercises, id: \.self) { exercise in
                Button {
                    selectedExercise = exercise
                    dismiss()
                } label: {
                    HStack(spacing: 16) {
                        Image(systemName: exercise.icon)
                            .font(.title2)
                            .foregroundColor(.blue)
                            .frame(width: 32)
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text(exercise.displayName)
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            Text(exercise.description)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        if exercise == selectedExercise {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.blue)
                        }
                    }
                    .padding(.vertical, 8)
                }
                .buttonStyle(.plain)
            }
            .navigationTitle("Select Exercise")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Supporting Types

enum WorkoutState {
    case ready
    case active
    case paused
    case resting
    case completed
}

extension ExerciseType {
    var icon: String {
        switch self {
        case .squat:
            return "figure.squat"
        case .pushUp:
            return "figure.strengthtraining.traditional"
        case .plank:
            return "figure.core.training"
        case .jumpingJack:
            return "figure.jumprope"
        default:
            return "figure.walk"
        }
    }
    
    var displayName: String {
        switch self {
        case .squat:
            return "Squat"
        case .pushUp:
            return "Push-up"
        case .plank:
            return "Plank"
        case .jumpingJack:
            return "Jumping Jack"
        default:
            return "Unknown"
        }
    }
    
    var description: String {
        switch self {
        case .squat:
            return "Lower body strength exercise"
        case .pushUp:
            return "Upper body and core exercise"
        case .plank:
            return "Core stability exercise"
        case .jumpingJack:
            return "Full body cardio exercise"
        default:
            return "Exercise description"
        }
    }
}

#Preview("Workout Controls - Ready") {
    ZStack {
        Color.black.ignoresSafeArea()
        
        VStack {
            Spacer()
            WorkoutControlsView()
        }
    }
}

#Preview("Workout Controls - Active") {
    @State var controls = WorkoutControlsView()
    
    return ZStack {
        Color.black.ignoresSafeArea()
        
        VStack {
            Spacer()
            controls
                .onAppear {
                    // Simulate active state for preview
                }
        }
    }
}

#Preview("Exercise Selector") {
    ExerciseSelectorSheet(selectedExercise: .constant(.squat))
}