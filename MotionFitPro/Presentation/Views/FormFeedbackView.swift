import SwiftUI

struct FormFeedbackView: View {
    let feedback: FormFeedback
    @State private var isVisible = false
    @State private var dismissTimer: Timer?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(spacing: 8) {
                // Feedback icon
                Image(systemName: feedback.type.icon)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(feedback.type.color)
                
                // Feedback title
                Text(feedback.title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Spacer()
                
                // Auto-dismiss indicator
                if feedback.autoDismiss {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white.opacity(0.7)))
                        .scaleEffect(0.7)
                }
            }
            
            // Feedback message
            Text(feedback.message)
                .font(.caption)
                .foregroundColor(.white.opacity(0.9))
                .lineLimit(2)
                .multilineTextAlignment(.leading)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(feedback.type.backgroundColor)
                .shadow(color: .black.opacity(0.3), radius: 8, x: 0, y: 4)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(feedback.type.borderColor, lineWidth: 1.5)
        )
        .scaleEffect(isVisible ? 1.0 : 0.8)
        .opacity(isVisible ? 1.0 : 0.0)
        .animation(.bouncy(duration: 0.4), value: isVisible)
        .onAppear {
            withAnimation(.bouncy(duration: 0.4)) {
                isVisible = true
            }
            
            if feedback.autoDismiss {
                startDismissTimer()
            }
        }
        .onDisappear {
            dismissTimer?.invalidate()
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(feedback.type.accessibilityLabel): \(feedback.title). \(feedback.message)")
        .accessibilityAddTraits(feedback.type == .error ? .isSelected : [])
    }
    
    private func startDismissTimer() {
        dismissTimer = Timer.scheduledTimer(withTimeInterval: feedback.duration) { _ in
            withAnimation(.easeOut(duration: 0.3)) {
                isVisible = false
            }
        }
    }
}

// MARK: - Form Feedback Container

struct FormFeedbackContainer: View {
    @Binding var feedbackItems: [FormFeedback]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            ForEach(feedbackItems) { feedback in
                FormFeedbackView(feedback: feedback)
                    .transition(.asymmetric(
                        insertion: .move(edge: .top).combined(with: .opacity),
                        removal: .move(edge: .trailing).combined(with: .opacity)
                    ))
            }
        }
        .padding(.horizontal, 20)
        .frame(maxWidth: .infinity, alignment: .leading)
        .onChange(of: feedbackItems) { _, _ in
            cleanupExpiredFeedback()
        }
    }
    
    private func cleanupExpiredFeedback() {
        // Remove feedback items after their display duration
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.easeOut(duration: 0.3)) {
                feedbackItems.removeAll { feedback in
                    feedback.timestamp.timeIntervalSinceNow < -feedback.duration
                }
            }
        }
    }
}

// MARK: - Specialized Feedback Views

struct CoachingBubble: View {
    let message: String
    let type: FormFeedbackType
    @State private var isVisible = false
    @State private var pulseScale: CGFloat = 1.0
    
    var body: some View {
        HStack(spacing: 12) {
            // Animated coaching avatar
            ZStack {
                Circle()
                    .fill(type.color.opacity(0.2))
                    .frame(width: 40, height: 40)
                    .scaleEffect(pulseScale)
                    .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: pulseScale)
                
                Image(systemName: "person.fill.checkmark")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(type.color)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text("Coach")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white.opacity(0.7))
                
                Text(message)
                    .font(.subheadline)
                    .foregroundColor(.white)
                    .lineLimit(3)
            }
            
            Spacer()
        }
        .padding(16)
        .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 16))
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(type.color.opacity(0.3), lineWidth: 1)
        )
        .scaleEffect(isVisible ? 1.0 : 0.9)
        .opacity(isVisible ? 1.0 : 0.0)
        .onAppear {
            withAnimation(.bouncy(duration: 0.5)) {
                isVisible = true
            }
            pulseScale = 1.1
        }
    }
}

struct QuickTip: View {
    let tip: String
    @State private var isVisible = false
    
    var body: some View {
        HStack(spacing: 10) {
            Image(systemName: "lightbulb.fill")
                .font(.system(size: 14))
                .foregroundColor(.yellow)
            
            Text(tip)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.white)
            
            Spacer()
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(.black.opacity(0.6), in: Capsule())
        .scaleEffect(isVisible ? 1.0 : 0.8)
        .opacity(isVisible ? 1.0 : 0.0)
        .onAppear {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                isVisible = true
            }
        }
    }
}

// MARK: - Supporting Types

struct FormFeedback: Identifiable, Equatable {
    let id = UUID()
    let type: FormFeedbackType
    let title: String
    let message: String
    let timestamp: Date
    let duration: TimeInterval
    let autoDismiss: Bool
    
    init(type: FormFeedbackType, title: String, message: String, duration: TimeInterval = 3.0, autoDismiss: Bool = true) {
        self.type = type
        self.title = title
        self.message = message
        self.timestamp = Date()
        self.duration = duration
        self.autoDismiss = autoDismiss
    }
    
    static func == (lhs: FormFeedback, rhs: FormFeedback) -> Bool {
        lhs.id == rhs.id
    }
}

enum FormFeedbackType {
    case success
    case warning
    case error
    case info
    case coaching
    
    var color: Color {
        switch self {
        case .success:
            return .green
        case .warning:
            return .orange
        case .error:
            return .red
        case .info:
            return .blue
        case .coaching:
            return .purple
        }
    }
    
    var backgroundColor: Color {
        switch self {
        case .success:
            return .green.opacity(0.9)
        case .warning:
            return .orange.opacity(0.9)
        case .error:
            return .red.opacity(0.9)
        case .info:
            return .blue.opacity(0.9)
        case .coaching:
            return .purple.opacity(0.9)
        }
    }
    
    var borderColor: Color {
        switch self {
        case .success:
            return .green.opacity(0.3)
        case .warning:
            return .orange.opacity(0.3)
        case .error:
            return .red.opacity(0.3)
        case .info:
            return .blue.opacity(0.3)
        case .coaching:
            return .purple.opacity(0.3)
        }
    }
    
    var icon: String {
        switch self {
        case .success:
            return "checkmark.circle.fill"
        case .warning:
            return "exclamationmark.triangle.fill"
        case .error:
            return "xmark.circle.fill"
        case .info:
            return "info.circle.fill"
        case .coaching:
            return "person.fill.checkmark"
        }
    }
    
    var accessibilityLabel: String {
        switch self {
        case .success:
            return "Success"
        case .warning:
            return "Warning"
        case .error:
            return "Error"
        case .info:
            return "Information"
        case .coaching:
            return "Coaching tip"
        }
    }
}

// MARK: - Convenience Extensions

extension FormFeedback {
    static func success(_ title: String, message: String) -> FormFeedback {
        FormFeedback(type: .success, title: title, message: message, duration: 2.0)
    }
    
    static func warning(_ title: String, message: String) -> FormFeedback {
        FormFeedback(type: .warning, title: title, message: message, duration: 4.0)
    }
    
    static func error(_ title: String, message: String) -> FormFeedback {
        FormFeedback(type: .error, title: title, message: message, duration: 5.0, autoDismiss: false)
    }
    
    static func coaching(_ title: String, message: String) -> FormFeedback {
        FormFeedback(type: .coaching, title: title, message: message, duration: 6.0)
    }
}

#Preview("Form Feedback Examples") {
    @State var feedbackItems: [FormFeedback] = [
        .success("Great Form!", "Perfect squat depth achieved"),
        .warning("Adjust Position", "Keep your knees aligned with toes"),
        .coaching("Pro Tip", "Focus on controlled movement for better results")
    ]
    
    return ZStack {
        Color.black.ignoresSafeArea()
        
        VStack(spacing: 40) {
            FormFeedbackContainer(feedbackItems: $feedbackItems)
            
            Spacer()
        }
        .padding(.top, 100)
    }
}

#Preview("Coaching Bubble") {
    ZStack {
        Color.black.ignoresSafeArea()
        
        VStack(spacing: 30) {
            CoachingBubble(message: "Great job! Try to go a bit deeper on your next squat.", type: .coaching)
            
            CoachingBubble(message: "Remember to keep your core engaged throughout the movement.", type: .info)
            
            QuickTip(tip: "Breathe out on the way up")
        }
        .padding()
    }
}

#Preview("Individual Feedback Types") {
    @State var items: [FormFeedback] = []
    
    return ZStack {
        Color.black.ignoresSafeArea()
        
        VStack(spacing: 20) {
            FormFeedbackView(feedback: .success("Perfect!", "Excellent form on that rep"))
            FormFeedbackView(feedback: .warning("Watch Form", "Keep your back straight"))
            FormFeedbackView(feedback: .error("Stop Exercise", "Risk of injury detected"))
            FormFeedbackView(feedback: .coaching("Tip", "Focus on slow, controlled movements"))
        }
        .padding()
    }
}