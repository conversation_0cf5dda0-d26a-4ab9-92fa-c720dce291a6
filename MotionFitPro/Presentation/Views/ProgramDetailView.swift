//
//  ProgramDetailView.swift
//  MotionFitPro
//
//  Created by <PERSON> on 2025-07-15.
//

import SwiftUI

struct ProgramDetailView: View {
    let program: WorkoutProgram
    @Environment(\.dismiss) private var dismiss
    @StateObject private var aiCoach = AICoachingSystem()
    
    @State private var startWorkout = false
    @State private var selectedExercise: ProgramExercise?
    @State private var animateElements = false
    @State private var selectedCoach: CoachPersonality = .motivational
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background
                program.color.gradient
                    .ignoresSafeArea()
                
                ScrollView(showsIndicators: false) {
                    VStack(spacing: 24) {
                        // Hero Section
                        HeroSection()
                            .opacity(animateElements ? 1 : 0)
                            .offset(y: animateElements ? 0 : 50)
                            .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.1), value: animateElements)
                        
                        // Program Stats
                        ProgramStatsSection()
                            .opacity(animateElements ? 1 : 0)
                            .offset(y: animateElements ? 0 : 30)
                            .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.2), value: animateElements)
                        
                        // AI Coach Selection
                        CoachSelectionSection()
                            .opacity(animateElements ? 1 : 0)
                            .offset(y: animateElements ? 0 : 30)
                            .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.3), value: animateElements)
                        
                        // Exercise Preview
                        ExercisePreviewSection()
                            .opacity(animateElements ? 1 : 0)
                            .offset(y: animateElements ? 0 : 30)
                            .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.4), value: animateElements)
                        
                        // Benefits & Details
                        BenefitsSection()
                            .opacity(animateElements ? 1 : 0)
                            .offset(y: animateElements ? 0 : 30)
                            .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.5), value: animateElements)
                        
                        // Start Button
                        StartButtonSection()
                            .opacity(animateElements ? 1 : 0)
                            .offset(y: animateElements ? 0 : 30)
                            .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.6), value: animateElements)
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                    .padding(.bottom, 100)
                }
            }
            .navigationBarHidden(true)
        }
        .onAppear {
            animateElements = true
            aiCoach.setCoachPersonality(selectedCoach)
        }
        .sheet(isPresented: $startWorkout) {
            ProgramWorkoutView(program: program, selectedCoach: selectedCoach)
        }
        .sheet(item: $selectedExercise) { exercise in
            ExerciseDetailView(exercise: exercise)
        }
    }
    
    // MARK: - Hero Section
    
    @ViewBuilder
    private func HeroSection() -> some View {
        VStack(spacing: 20) {
            // Header with close button
            HStack {
                Button(action: { dismiss() }) {
                    ZStack {
                        Circle()
                            .fill(.ultraThinMaterial)
                            .frame(width: 44, height: 44)
                        
                        Image(systemName: "xmark")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.white)
                    }
                }
                
                Spacer()
                
                // Difficulty Stars
                HStack(spacing: 4) {
                    ForEach(0..<program.difficultyStars, id: \.self) { _ in
                        Image(systemName: "star.fill")
                            .font(.system(size: 12))
                            .foregroundColor(.white)
                    }
                    ForEach(program.difficultyStars..<5, id: \.self) { _ in
                        Image(systemName: "star")
                            .font(.system(size: 12))
                            .foregroundColor(.white.opacity(0.3))
                    }
                }
            }
            
            // Program Icon and Title
            VStack(spacing: 16) {
                ZStack {
                    Circle()
                        .fill(.ultraThinMaterial)
                        .frame(width: 100, height: 100)
                    
                    Image(systemName: program.icon)
                        .font(.system(size: 40, weight: .semibold))
                        .foregroundColor(.white)
                }
                .scaleEffect(animateElements ? 1 : 0.8)
                .animation(.spring(response: 0.6, dampingFraction: 0.7).delay(0.3), value: animateElements)
                
                VStack(spacing: 8) {
                    Text(program.name)
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                    
                    Text(program.description)
                        .font(.body)
                        .foregroundColor(.white.opacity(0.9))
                        .multilineTextAlignment(.center)
                        .lineLimit(3)
                }
            }
        }
    }
    
    // MARK: - Program Stats Section
    
    @ViewBuilder
    private func ProgramStatsSection() -> some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 16) {
            StatPill(
                icon: "clock.fill",
                value: program.formattedDuration,
                label: "Duration",
                color: .white
            )
            
            StatPill(
                icon: "flame.fill",
                value: "\(program.estimatedCalories)",
                label: "Calories",
                color: .orange
            )
            
            StatPill(
                icon: "figure.strengthtraining.traditional",
                value: "\(program.exercises.count)",
                label: "Exercises",
                color: .blue
            )
            
            StatPill(
                icon: "target",
                value: program.difficulty.displayName,
                label: "Level",
                color: program.difficulty.color
            )
        }
    }
    
    // MARK: - Coach Selection Section
    
    @ViewBuilder
    private func CoachSelectionSection() -> some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Choose Your AI Coach")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Spacer()
                
                Text(selectedCoach.emoji)
                    .font(.title)
            }
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(CoachPersonality.allCases) { coach in
                        CoachCard(
                            coach: coach,
                            isSelected: selectedCoach == coach
                        ) {
                            selectedCoach = coach
                            aiCoach.setCoachPersonality(coach)
                        }
                    }
                }
                .padding(.horizontal, 2)
            }
            
            // Coach Preview
            Text(selectedCoach.generateMessage(for: .workoutStart))
                .font(.body)
                .fontStyle(.italic)
                .foregroundColor(.white.opacity(0.9))
                .padding(16)
                .background(.ultraThinMaterial)
                .cornerRadius(12)
        }
    }
    
    // MARK: - Exercise Preview Section
    
    @ViewBuilder
    private func ExercisePreviewSection() -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Exercise Preview")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 16) {
                    ForEach(program.exercises) { exercise in
                        Exercise3DPreviewCard(exercise: exercise) {
                            selectedExercise = exercise
                        }
                    }
                }
                .padding(.horizontal, 2)
            }
        }
    }
    
    // MARK: - Benefits Section
    
    @ViewBuilder
    private func BenefitsSection() -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Program Benefits")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                ForEach(program.benefits, id: \.self) { benefit in
                    BenefitCard(benefit: benefit)
                }
            }
            
            // Target Audience
            VStack(alignment: .leading, spacing: 8) {
                Text("Perfect For")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Text(program.targetAudience)
                    .font(.body)
                    .foregroundColor(.white.opacity(0.9))
            }
            .padding(16)
            .background(.ultraThinMaterial)
            .cornerRadius(12)
        }
    }
    
    // MARK: - Start Button Section
    
    @ViewBuilder
    private func StartButtonSection() -> some View {
        VStack(spacing: 16) {
            Button(action: { startWorkout = true }) {
                HStack(spacing: 12) {
                    Image(systemName: "play.fill")
                        .font(.system(size: 20, weight: .semibold))
                    
                    Text("Start Workout")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Spacer()
                    
                    Text(program.formattedDuration)
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                .foregroundColor(.white)
                .padding(20)
                .background(.black.opacity(0.3))
                .cornerRadius(16)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(.white.opacity(0.2), lineWidth: 1)
                )
            }
            .scaleEffect(animateElements ? 1 : 0.9)
            .animation(.spring(response: 0.6, dampingFraction: 0.7).delay(0.8), value: animateElements)
        }
    }
}

// MARK: - Stat Pill

struct StatPill: View {
    let icon: String
    let value: String
    let label: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 6) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(color)
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .lineLimit(1)
                .minimumScaleFactor(0.8)
            
            Text(label)
                .font(.caption2)
                .foregroundColor(.white.opacity(0.7))
                .lineLimit(1)
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 8)
        .background(.ultraThinMaterial)
        .cornerRadius(12)
    }
}

// MARK: - Coach Card

struct CoachCard: View {
    let coach: CoachPersonality
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 8) {
                Text(coach.emoji)
                    .font(.title2)
                
                Text(coach.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            .foregroundColor(isSelected ? .white : .white.opacity(0.7))
            .padding(.vertical, 12)
            .padding(.horizontal, 16)
            .background(
                isSelected ? .white.opacity(0.2) : .clear
            )
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? .white.opacity(0.4) : .clear, lineWidth: 1)
            )
        }
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
}

// MARK: - Exercise 3D Preview Card

struct Exercise3DPreviewCard: View {
    let exercise: ProgramExercise
    let onTap: () -> Void
    
    @State private var animatePreview = false
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // 3D Exercise Preview (Simulated)
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(.ultraThinMaterial)
                        .frame(width: 120, height: 80)
                    
                    // Animated exercise representation
                    Exercise3DAnimation(exerciseType: exercise.exerciseType, isAnimating: animatePreview)
                }
                
                VStack(spacing: 4) {
                    Text(exercise.exerciseType.displayName)
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .lineLimit(1)
                    
                    if let duration = exercise.formattedDuration {
                        Text(duration)
                            .font(.caption2)
                            .foregroundColor(.white.opacity(0.7))
                    } else if let reps = exercise.targetReps {
                        Text("\(reps) reps")
                            .font(.caption2)
                            .foregroundColor(.white.opacity(0.7))
                    }
                }
            }
            .frame(width: 120)
        }
        .onAppear {
            animatePreview = true
        }
    }
}

// MARK: - Exercise 3D Animation (Simulated)

struct Exercise3DAnimation: View {
    let exerciseType: ExerciseType
    let isAnimating: Bool
    
    @State private var animationPhase: Double = 0
    
    var body: some View {
        ZStack {
            // Simulate 3D exercise animation with SF Symbols
            switch exerciseType {
            case .squat:
                Image(systemName: "figure.strengthtraining.traditional")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.white)
                    .scaleEffect(1.0 + sin(animationPhase) * 0.1)
                    .offset(y: sin(animationPhase) * 3)
                
            case .pushUp:
                Image(systemName: "figure.strengthtraining.functional")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.white)
                    .scaleEffect(1.0 + sin(animationPhase) * 0.05)
                    .rotationEffect(.degrees(sin(animationPhase) * 2))
                
            case .plank:
                Image(systemName: "figure.core.training")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.white)
                    .opacity(0.8 + sin(animationPhase) * 0.2)
                
            case .jumpingJacks:
                Image(systemName: "figure.jumping.jacks")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.white)
                    .scaleEffect(1.0 + abs(sin(animationPhase)) * 0.15)
                    .offset(x: sin(animationPhase) * 2)
                
            case .lunge:
                Image(systemName: "figure.flexibility")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.white)
                    .scaleEffect(1.0 + sin(animationPhase) * 0.08)
                    .offset(y: sin(animationPhase) * 2)
                
            case .mountainClimbers:
                Image(systemName: "figure.climbing")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.white)
                    .rotationEffect(.degrees(sin(animationPhase) * 5))
                
            case .burpees:
                Image(systemName: "figure.mixed.cardio")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.white)
                    .scaleEffect(1.0 + abs(sin(animationPhase)) * 0.2)
                    .offset(y: sin(animationPhase) * 4)
            }
            
            // Subtle glow effect
            Circle()
                .fill(.white.opacity(0.1))
                .frame(width: 60, height: 60)
                .scaleEffect(1.0 + sin(animationPhase * 2) * 0.1)
                .blur(radius: 10)
        }
        .onAppear {
            if isAnimating {
                withAnimation(.linear(duration: 2).repeatForever(autoreverses: false)) {
                    animationPhase = .pi * 2
                }
            }
        }
    }
}

// MARK: - Benefit Card

struct BenefitCard: View {
    let benefit: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 16))
                .foregroundColor(.green)
            
            Text(benefit)
                .font(.subheadline)
                .foregroundColor(.white)
                .multilineTextAlignment(.leading)
            
            Spacer()
        }
        .padding(12)
        .background(.ultraThinMaterial)
        .cornerRadius(8)
    }
}

#Preview {
    ProgramDetailView(program: WorkoutProgram.morningBlast)
}