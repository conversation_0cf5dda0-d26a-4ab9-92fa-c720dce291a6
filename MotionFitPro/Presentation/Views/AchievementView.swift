//
//  AchievementView.swift
//  MotionFitPro
//
//  Created by <PERSON> on 2025-07-15.
//

import SwiftUI

struct AchievementView: View {
    @EnvironmentObject var achievementSystem: AchievementSystem
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedCategory: AchievementCategory? = nil
    @State private var animateElements = false
    
    var filteredAchievements: [Achievement] {
        let allAchievements = achievementSystem.unlockedAchievements + achievementSystem.availableAchievements
        
        if let category = selectedCategory {
            return allAchievements.filter { $0.category == category }
        }
        return allAchievements
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // Premium Background
                AchievementBackground()
                
                VStack(spacing: 0) {
                    // Header
                    HeaderSection()
                        .opacity(animateElements ? 1 : 0)
                        .offset(y: animateElements ? 0 : -30)
                        .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.1), value: animateElements)
                    
                    // Stats Overview
                    StatsOverview()
                        .opacity(animateElements ? 1 : 0)
                        .offset(y: animateElements ? 0 : 20)
                        .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.2), value: animateElements)
                    
                    // Category Filter
                    CategoryFilter()
                        .opacity(animateElements ? 1 : 0)
                        .offset(y: animateElements ? 0 : 20)
                        .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.3), value: animateElements)
                    
                    // Achievements Grid
                    AchievementsGrid()
                        .opacity(animateElements ? 1 : 0)
                        .offset(y: animateElements ? 0 : 20)
                        .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.4), value: animateElements)
                }
            }
            .navigationBarHidden(true)
        }
        .onAppear {
            animateElements = true
        }
    }
    
    // MARK: - Header Section
    
    @ViewBuilder
    private func HeaderSection() -> some View {
        HStack {
            Button(action: { dismiss() }) {
                ZStack {
                    Circle()
                        .fill(.ultraThinMaterial)
                        .frame(width: 44, height: 44)
                    
                    Image(systemName: "xmark")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                }
            }
            
            Spacer()
            
            VStack(spacing: 4) {
                Text("Achievements")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Text("\(achievementSystem.unlockedAchievements.count) unlocked")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
            }
            
            Spacer()
            
            // Trophy Icon
            ZStack {
                Circle()
                    .fill(.yellow.opacity(0.2))
                    .frame(width: 44, height: 44)
                
                Image(systemName: "trophy.fill")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.yellow)
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 20)
    }
    
    // MARK: - Stats Overview
    
    @ViewBuilder
    private func StatsOverview() -> some View {
        HStack(spacing: 16) {
            // Total Points
            StatCard(
                title: "Total Points",
                value: "\(achievementSystem.totalPoints)",
                icon: "star.fill",
                color: .yellow
            )
            
            // Current Level
            StatCard(
                title: "Current Level",
                value: achievementSystem.currentLevel.title.components(separatedBy: " ").first ?? "1",
                icon: "crown.fill",
                color: achievementSystem.currentLevel.color
            )
            
            // Progress to Next
            StatCard(
                title: "Progress",
                value: "\(Int(achievementSystem.getProgressToNextLevel() * 100))%",
                icon: "chart.line.uptrend.xyaxis",
                color: .green
            )
        }
        .padding(.horizontal, 20)
        .padding(.top, 16)
    }
    
    // MARK: - Category Filter
    
    @ViewBuilder
    private func CategoryFilter() -> some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                // All Categories
                CategoryButton(
                    title: "All",
                    icon: "square.grid.3x3.fill",
                    color: .white,
                    isSelected: selectedCategory == nil
                ) {
                    selectedCategory = nil
                }
                
                // Individual Categories
                ForEach(AchievementCategory.allCases, id: \.self) { category in
                    CategoryButton(
                        title: category.displayName,
                        icon: categoryIcon(category),
                        color: category.color,
                        isSelected: selectedCategory == category
                    ) {
                        selectedCategory = category
                    }
                }
            }
            .padding(.horizontal, 20)
        }
        .padding(.top, 16)
    }
    
    // MARK: - Achievements Grid
    
    @ViewBuilder
    private func AchievementsGrid() -> some View {
        ScrollView(showsIndicators: false) {
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                ForEach(filteredAchievements) { achievement in
                    AchievementCard(
                        achievement: achievement,
                        isUnlocked: achievementSystem.unlockedAchievements.contains { $0.id == achievement.id },
                        progress: achievementSystem.getAchievementProgress(achievement)
                    )
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 16)
            .padding(.bottom, 100)
        }
    }
    
    private func categoryIcon(_ category: AchievementCategory) -> String {
        switch category {
        case .form: return "target"
        case .endurance: return "timer"
        case .speed: return "bolt.fill"
        case .consistency: return "calendar"
        case .mastery: return "star.circle.fill"
        case .special: return "sparkles"
        }
    }
}

// MARK: - Achievement Background

struct AchievementBackground: View {
    var body: some View {
        ZStack {
            // Base gradient
            LinearGradient(
                colors: [
                    Color(red: 0.15, green: 0.05, blue: 0.25),
                    Color(red: 0.1, green: 0.03, blue: 0.2),
                    Color.black
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            // Floating trophies
            GeometryReader { geometry in
                ForEach(0..<15, id: \.self) { i in
                    Image(systemName: "trophy.fill")
                        .font(.system(size: CGFloat.random(in: 8...16)))
                        .foregroundColor(.yellow.opacity(Double.random(in: 0.02...0.08)))
                        .position(
                            x: CGFloat.random(in: 0...geometry.size.width),
                            y: CGFloat.random(in: 0...geometry.size.height)
                        )
                        .animation(
                            .linear(duration: Double.random(in: 15...25))
                            .repeatForever(autoreverses: true),
                            value: UUID()
                        )
                }
            }
        }
        .ignoresSafeArea()
    }
}

// MARK: - Achievement Stat Card

struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 24, weight: .semibold))
                .foregroundColor(color)
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.white.opacity(0.7))
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(.ultraThinMaterial)
        .cornerRadius(12)
    }
}

// MARK: - Category Button

struct CategoryButton: View {
    let title: String
    let icon: String
    let color: Color
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.system(size: 14, weight: .semibold))
                
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
            .foregroundColor(isSelected ? .black : .white)
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            .background(
                isSelected ? color : .clear
            )
            .cornerRadius(20)
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(color.opacity(0.5), lineWidth: 1)
            )
        }
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
}

// MARK: - Achievement Card

struct AchievementCard: View {
    let achievement: Achievement
    let isUnlocked: Bool
    let progress: Float
    
    @State private var showingDetail = false
    
    var body: some View {
        Button(action: { showingDetail = true }) {
            VStack(spacing: 12) {
                // Achievement Icon
                ZStack {
                    if isUnlocked {
                        achievement.gradient
                            .frame(width: 60, height: 60)
                            .cornerRadius(30)
                    } else {
                        Circle()
                            .fill(.gray.opacity(0.3))
                            .frame(width: 60, height: 60)
                    }
                    
                    Image(systemName: achievement.icon)
                        .font(.system(size: 24, weight: .semibold))
                        .foregroundColor(isUnlocked ? .white : .gray)
                }
                
                VStack(spacing: 6) {
                    Text(achievement.title)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(isUnlocked ? .white : .gray)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)
                    
                    Text(achievement.description)
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                        .multilineTextAlignment(.center)
                        .lineLimit(3)
                    
                    if !isUnlocked && progress > 0 {
                        VStack(spacing: 4) {
                            ProgressView(value: progress)
                                .progressViewStyle(LinearProgressViewStyle(tint: achievement.color))
                                .scaleEffect(x: 1, y: 1.5, anchor: .center)
                            
                            Text("\(Int(progress * 100))%")
                                .font(.caption2)
                                .fontWeight(.medium)
                                .foregroundColor(achievement.color)
                        }
                    }
                }
                
                Spacer()
                
                // Points
                HStack {
                    Spacer()
                    
                    HStack(spacing: 4) {
                        Image(systemName: "star.fill")
                            .font(.caption)
                        Text("\(achievement.points)")
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(isUnlocked ? .yellow : .gray)
                }
            }
            .padding(16)
            .frame(height: 180)
            .background(.ultraThinMaterial)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(isUnlocked ? achievement.color.opacity(0.3) : .clear, lineWidth: 1)
            )
            .scaleEffect(isUnlocked ? 1.0 : 0.95)
            .saturation(isUnlocked ? 1.0 : 0.6)
        }
        .sheet(isPresented: $showingDetail) {
            AchievementDetailView(achievement: achievement, isUnlocked: isUnlocked, progress: progress)
        }
    }
}

// MARK: - Achievement Detail View

struct AchievementDetailView: View {
    let achievement: Achievement
    let isUnlocked: Bool
    let progress: Float
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ZStack {
                achievement.gradient
                    .ignoresSafeArea()
                
                VStack(spacing: 24) {
                    // Large Achievement Icon
                    ZStack {
                        Circle()
                            .fill(.white.opacity(0.2))
                            .frame(width: 120, height: 120)
                        
                        Image(systemName: achievement.icon)
                            .font(.system(size: 48, weight: .semibold))
                            .foregroundColor(.white)
                    }
                    
                    VStack(spacing: 12) {
                        Text(achievement.title)
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .multilineTextAlignment(.center)
                        
                        Text(achievement.description)
                            .font(.title3)
                            .foregroundColor(.white.opacity(0.9))
                            .multilineTextAlignment(.center)
                    }
                    
                    // Status
                    VStack(spacing: 16) {
                        if isUnlocked {
                            HStack(spacing: 8) {
                                Image(systemName: "checkmark.circle.fill")
                                    .font(.title2)
                                    .foregroundColor(.green)
                                
                                Text("Unlocked!")
                                    .font(.headline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)
                            }
                        } else if progress > 0 {
                            VStack(spacing: 8) {
                                Text("Progress: \(Int(progress * 100))%")
                                    .font(.headline)
                                    .foregroundColor(.white)
                                
                                ProgressView(value: progress)
                                    .progressViewStyle(LinearProgressViewStyle(tint: .white))
                                    .scaleEffect(x: 1, y: 2, anchor: .center)
                            }
                        } else {
                            Text("Keep working to unlock this achievement!")
                                .font(.headline)
                                .foregroundColor(.white.opacity(0.8))
                                .multilineTextAlignment(.center)
                        }
                        
                        // Points
                        HStack(spacing: 8) {
                            Image(systemName: "star.fill")
                                .font(.title2)
                                .foregroundColor(.yellow)
                            
                            Text("\(achievement.points) Points")
                                .font(.title2)
                                .fontWeight(.semibold)
                                .foregroundColor(.white)
                        }
                    }
                    
                    Spacer()
                }
                .padding(20)
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(.white)
                }
            }
        }
    }
}

#Preview {
    AchievementView()
        .environmentObject(AchievementSystem())
}