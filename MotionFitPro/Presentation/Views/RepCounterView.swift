import SwiftUI

struct RepCounterView: View {
    @Binding var repCount: Int
    @Binding var setCount: Int
    @State private var animationTrigger = 0
    @State private var celebrationTrigger = 0
    @State private var formQuality: FormQuality = .good
    
    let targetReps: Int
    let targetSets: Int
    
    var body: some View {
        VStack(spacing: 16) {
            // Main Rep Counter
            ZStack {
                // Background circle
                Circle()
                    .fill(.ultraThinMaterial)
                    .frame(width: 120, height: 120)
                
                // Progress ring
                Circle()
                    .stroke(lineWidth: 6)
                    .foregroundColor(.gray.opacity(0.3))
                    .frame(width: 100, height: 100)
                
                Circle()
                    .trim(from: 0, to: progressPercentage)
                    .stroke(
                        formQualityColor,
                        style: StrokeStyle(lineWidth: 6, lineCap: .round)
                    )
                    .frame(width: 100, height: 100)
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut(duration: 0.3), value: progressPercentage)
                
                // Rep count text
                VStack(spacing: 2) {
                    Text("\(repCount)")
                        .font(.system(size: 36, weight: .bold, design: .rounded))
                        .foregroundColor(.white)
                        .scaleEffect(1.0 + scaleEffect)
                        .animation(.bouncy(duration: 0.4), value: animationTrigger)
                    
                    Text("/ \(targetReps)")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                }
            }
            .onChange(of: repCount) { oldValue, newValue in
                if newValue > oldValue {
                    triggerRepAnimation()
                    if newValue == targetReps {
                        triggerCelebration()
                    }
                }
            }
            
            // Set Counter
            HStack(spacing: 12) {
                ForEach(0..<targetSets, id: \.self) { index in
                    Circle()
                        .fill(index < setCount ? formQualityColor : .gray.opacity(0.3))
                        .frame(width: 12, height: 12)
                        .scaleEffect(index == setCount - 1 ? 1.3 : 1.0)
                        .animation(.bouncy(duration: 0.4), value: setCount)
                }
            }
            
            // Set progress text
            Text("Set \(setCount) of \(targetSets)")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.white.opacity(0.8))
        }
        .padding(20)
        .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 20))
        .overlay(alignment: .topTrailing) {
            // Form quality indicator
            FormQualityBadge(quality: formQuality)
                .offset(x: 8, y: -8)
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Rep counter: \(repCount) of \(targetReps) reps, set \(setCount) of \(targetSets)")
        .accessibilityHint("Shows current repetition and set progress")
        
        // Celebration particles
        if celebrationTrigger > 0 {
            CelebrationOverlay()
                .allowsHitTesting(false)
        }
    }
    
    private var progressPercentage: Double {
        guard targetReps > 0 else { return 0 }
        return min(Double(repCount) / Double(targetReps), 1.0)
    }
    
    private var scaleEffect: Double {
        animationTrigger > 0 ? 0.2 : 0.0
    }
    
    private var formQualityColor: Color {
        switch formQuality {
        case .excellent:
            return .green
        case .good:
            return .blue
        case .fair:
            return .yellow
        case .poor:
            return .red
        }
    }
    
    private func triggerRepAnimation() {
        withAnimation(.bouncy(duration: 0.4)) {
            animationTrigger += 1
        }
        
        // Reset scale after animation
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
            withAnimation(.easeOut(duration: 0.2)) {
                animationTrigger = 0
            }
        }
        
        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
    
    private func triggerCelebration() {
        celebrationTrigger += 1
        
        // Success haptic pattern
        let successFeedback = UINotificationFeedbackGenerator()
        successFeedback.notificationOccurred(.success)
        
        // Auto-dismiss celebration after 2 seconds
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            celebrationTrigger = 0
        }
    }
}

// MARK: - Form Quality Badge

struct FormQualityBadge: View {
    let quality: FormQuality
    
    var body: some View {
        Circle()
            .fill(qualityColor)
            .frame(width: 20, height: 20)
            .overlay(
                Image(systemName: qualityIcon)
                    .font(.system(size: 10, weight: .bold))
                    .foregroundColor(.white)
            )
            .shadow(radius: 2)
    }
    
    private var qualityColor: Color {
        switch quality {
        case .excellent:
            return .green
        case .good:
            return .blue
        case .fair:
            return .yellow
        case .poor:
            return .red
        }
    }
    
    private var qualityIcon: String {
        switch quality {
        case .excellent:
            return "star.fill"
        case .good:
            return "checkmark"
        case .fair:
            return "minus"
        case .poor:
            return "exclamationmark"
        }
    }
}

// MARK: - Celebration Overlay

struct CelebrationOverlay: View {
    @State private var particles: [ParticleData] = []
    @State private var animationTimer: Timer?
    
    var body: some View {
        ZStack {
            ForEach(particles, id: \.id) { particle in
                Circle()
                    .fill(particle.color)
                    .frame(width: particle.size, height: particle.size)
                    .position(particle.position)
                    .opacity(particle.opacity)
                    .scaleEffect(particle.scale)
            }
        }
        .onAppear {
            generateParticles()
            startAnimation()
        }
        .onDisappear {
            animationTimer?.invalidate()
        }
    }
    
    private func generateParticles() {
        particles = []
        let colors: [Color] = [.yellow, .orange, .blue, .green, .purple]
        
        for _ in 0..<20 {
            let particle = ParticleData(
                id: UUID(),
                position: CGPoint(x: 60, y: 60), // Center of rep counter
                velocity: CGPoint(
                    x: Double.random(in: -100...100),
                    y: Double.random(in: -150...50)
                ),
                color: colors.randomElement() ?? .yellow,
                size: Double.random(in: 4...12),
                opacity: 1.0,
                scale: 1.0
            )
            particles.append(particle)
        }
    }
    
    private func startAnimation() {
        animationTimer = Timer.scheduledTimer(withTimeInterval: 0.016, repeats: true) { _ in
            updateParticles()
        }
    }
    
    private func updateParticles() {
        for i in particles.indices {
            particles[i].position.x += particles[i].velocity.x * 0.016
            particles[i].position.y += particles[i].velocity.y * 0.016
            particles[i].velocity.y += 200 * 0.016 // Gravity
            particles[i].opacity -= 0.016 * 0.8 // Fade out
            particles[i].scale -= 0.016 * 0.5 // Shrink
        }
        
        // Remove particles that are no longer visible
        particles.removeAll { $0.opacity <= 0 }
        
        // Stop animation when no particles remain
        if particles.isEmpty {
            animationTimer?.invalidate()
        }
    }
}

// MARK: - Supporting Types

enum FormQuality {
    case excellent
    case good
    case fair
    case poor
}

struct ParticleData {
    let id: UUID
    var position: CGPoint
    var velocity: CGPoint
    let color: Color
    let size: Double
    var opacity: Double
    var scale: Double
}

#Preview("Rep Counter - In Progress") {
    VStack(spacing: 40) {
        RepCounterView(
            repCount: .constant(7),
            setCount: .constant(1),
            targetReps: 12,
            targetSets: 3
        )
        
        RepCounterView(
            repCount: .constant(12),
            setCount: .constant(2),
            targetReps: 12,
            targetSets: 3
        )
    }
    .padding()
    .background(.black)
    .preferredColorScheme(.dark)
}

#Preview("Rep Counter - Starting") {
    RepCounterView(
        repCount: .constant(0),
        setCount: .constant(1),
        targetReps: 15,
        targetSets: 4
    )
    .padding()
    .background(.black)
    .preferredColorScheme(.dark)
}

#Preview("Form Quality Badges") {
    HStack(spacing: 20) {
        FormQualityBadge(quality: .excellent)
        FormQualityBadge(quality: .good)
        FormQualityBadge(quality: .fair)
        FormQualityBadge(quality: .poor)
    }
    .padding()
    .background(.black)
}