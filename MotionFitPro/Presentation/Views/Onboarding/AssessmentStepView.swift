import SwiftUI

struct AssessmentStepView: View {
    @ObservedObject var coordinator: OnboardingCoordinator
    @State private var selectedFitnessLevel: FitnessLevel?
    @State private var selectedDuration: WorkoutDuration?
    @State private var selectedGoals: Set<FitnessGoal> = []
    
    var body: some View {
        ScrollView {
            VStack(spacing: 32) {
                // Header
                VStack(spacing: 16) {
                    Text("Personalize Your Experience")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text("Help us create the perfect workout plan for you")
                        .font(.body)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                }
                .padding(.top, 20)
                
                // Fitness Level Selection
                AssessmentSection(title: "What's your fitness level?") {
                    VStack(spacing: 12) {
                        ForEach(FitnessLevel.allCases, id: \.self) { level in
                            FitnessLevelCard(
                                level: level,
                                isSelected: selectedFitnessLevel == level
                            ) {
                                selectedFitnessLevel = level
                                coordinator.updateFitnessAssessment(
                                    level: level,
                                    duration: selectedDuration ?? .medium
                                )
                            }
                        }
                    }
                }
                
                // Workout Duration Preference
                AssessmentSection(title: "How much time do you have?") {
                    VStack(spacing: 12) {
                        ForEach(WorkoutDuration.allCases, id: \.self) { duration in
                            DurationCard(
                                duration: duration,
                                isSelected: selectedDuration == duration
                            ) {
                                selectedDuration = duration
                                coordinator.updateFitnessAssessment(
                                    level: selectedFitnessLevel ?? .beginner,
                                    duration: duration
                                )
                            }
                        }
                    }
                }
                
                // Fitness Goals
                AssessmentSection(title: "What are your goals?") {
                    LazyVGrid(columns: [
                        GridItem(.flexible()),
                        GridItem(.flexible())
                    ], spacing: 12) {
                        ForEach(FitnessGoal.allCases, id: \.self) { goal in
                            GoalCard(
                                goal: goal,
                                isSelected: selectedGoals.contains(goal)
                            ) {
                                if selectedGoals.contains(goal) {
                                    selectedGoals.remove(goal)
                                } else {
                                    selectedGoals.insert(goal)
                                }
                            }
                        }
                    }
                }
                
                // Recommendations
                if let level = selectedFitnessLevel, let duration = selectedDuration {
                    RecommendationView(
                        fitnessLevel: level,
                        duration: duration,
                        goals: selectedGoals
                    )
                }
            }
            .padding(.horizontal, 24)
            .padding(.bottom, 100) // Extra space for navigation
        }
        .scrollBounceBehavior(.basedOnSize)
        .onAppear {
            // Set defaults
            selectedFitnessLevel = coordinator.fitnessLevel
            selectedDuration = coordinator.preferredWorkoutDuration
        }
    }
}

// MARK: - Assessment Section

struct AssessmentSection<Content: View>: View {
    let title: String
    @ViewBuilder let content: () -> Content
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            content()
        }
    }
}

// MARK: - Fitness Level Card

struct FitnessLevelCard: View {
    let level: FitnessLevel
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // Level indicator
                ZStack {
                    Circle()
                        .fill(level.color.opacity(0.2))
                        .frame(width: 40, height: 40)
                    
                    Image(systemName: levelIcon)
                        .font(.title3)
                        .foregroundColor(level.color)
                }
                
                // Content
                VStack(alignment: .leading, spacing: 4) {
                    Text(level.rawValue)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                    
                    Text(level.description)
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
                
                // Selection indicator
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.white)
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? .white.opacity(0.2) : .white.opacity(0.1))
                    .strokeBorder(
                        isSelected ? .white.opacity(0.4) : .clear,
                        lineWidth: 1
                    )
            )
        }
        .buttonStyle(.plain)
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.bouncy(duration: 0.3), value: isSelected)
    }
    
    private var levelIcon: String {
        switch level {
        case .beginner:
            return "leaf.fill"
        case .intermediate:
            return "flame.fill"
        case .advanced:
            return "bolt.fill"
        }
    }
}

// MARK: - Duration Card

struct DurationCard: View {
    let duration: WorkoutDuration
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                Image(systemName: duration.icon)
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 32)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(duration.rawValue)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                    
                    Text("Perfect for \(durationDescription)")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                }
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.white)
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? .white.opacity(0.2) : .white.opacity(0.1))
                    .strokeBorder(
                        isSelected ? .white.opacity(0.4) : .clear,
                        lineWidth: 1
                    )
            )
        }
        .buttonStyle(.plain)
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.bouncy(duration: 0.3), value: isSelected)
    }
    
    private var durationDescription: String {
        switch duration {
        case .short:
            return "busy schedules"
        case .medium:
            return "regular workouts"
        case .long:
            return "dedicated sessions"
        }
    }
}

// MARK: - Goal Card

struct GoalCard: View {
    let goal: FitnessGoal
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                Image(systemName: goal.icon)
                    .font(.title2)
                    .foregroundColor(isSelected ? .white : .white.opacity(0.6))
                
                Text(goal.rawValue)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(isSelected ? .white : .white.opacity(0.6))
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 100)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? .white.opacity(0.2) : .white.opacity(0.1))
                    .strokeBorder(
                        isSelected ? .white.opacity(0.4) : .clear,
                        lineWidth: 1
                    )
            )
        }
        .buttonStyle(.plain)
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.bouncy(duration: 0.3), value: isSelected)
    }
}

// MARK: - Recommendation View

struct RecommendationView: View {
    let fitnessLevel: FitnessLevel
    let duration: WorkoutDuration
    let goals: Set<FitnessGoal>
    
    var body: some View {
        VStack(spacing: 16) {
            Text("Your Personalized Plan")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            VStack(spacing: 12) {
                RecommendationItem(
                    icon: "target",
                    title: "Recommended Frequency",
                    value: recommendedFrequency
                )
                
                RecommendationItem(
                    icon: "chart.line.uptrend.xyaxis",
                    title: "Expected Progress",
                    value: expectedProgress
                )
                
                if !goals.isEmpty {
                    RecommendationItem(
                        icon: "star.fill",
                        title: "Focus Areas",
                        value: goals.map { $0.rawValue }.joined(separator: ", ")
                    )
                }
            }
            .padding(16)
            .background(.white.opacity(0.1), in: RoundedRectangle(cornerRadius: 12))
        }
    }
    
    private var recommendedFrequency: String {
        switch fitnessLevel {
        case .beginner:
            return "3-4 times per week"
        case .intermediate:
            return "4-5 times per week"
        case .advanced:
            return "5-6 times per week"
        }
    }
    
    private var expectedProgress: String {
        switch fitnessLevel {
        case .beginner:
            return "Noticeable improvements in 2-3 weeks"
        case .intermediate:
            return "Significant gains in 3-4 weeks"
        case .advanced:
            return "Refined performance in 4-6 weeks"
        }
    }
}

struct RecommendationItem: View {
    let icon: String
    let title: String
    let value: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.blue)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Text(value)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
            }
            
            Spacer()
        }
    }
}

// MARK: - Supporting Types

enum FitnessGoal: String, CaseIterable, Hashable {
    case strength = "Build Strength"
    case endurance = "Improve Endurance"
    case flexibility = "Increase Flexibility"
    case weightLoss = "Lose Weight"
    case toning = "Tone Muscles"
    case balance = "Better Balance"
    
    var icon: String {
        switch self {
        case .strength:
            return "dumbbell.fill"
        case .endurance:
            return "heart.fill"
        case .flexibility:
            return "figure.yoga"
        case .weightLoss:
            return "scalemass.fill"
        case .toning:
            return "figure.strengthtraining.traditional"
        case .balance:
            return "figure.roll"
        }
    }
}

#Preview("Assessment Step") {
    @StateObject var coordinator = OnboardingCoordinator()
    
    return ZStack {
        LinearGradient(
            colors: [.blue.opacity(0.8), .purple.opacity(0.6)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
        
        AssessmentStepView(coordinator: coordinator)
    }
    .onAppear {
        coordinator.currentStep = .assessment
    }
}