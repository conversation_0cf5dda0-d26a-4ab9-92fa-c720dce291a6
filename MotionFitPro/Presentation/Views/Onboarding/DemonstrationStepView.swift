import SwiftUI

struct DemonstrationStepView: View {
    @ObservedObject var coordinator: OnboardingCoordinator
    @State private var currentPhase: DemoPhase = .instruction
    @State private var squatCount = 0
    @State private var animationTrigger = 0
    @State private var showingCelebration = false
    
    enum DemoPhase {
        case instruction
        case demonstration
        case practice
        case completion
    }
    
    var body: some View {
        VStack(spacing: 24) {
            // Header
            VStack(spacing: 8) {
                Text("Let's Try a Squat")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Text(phaseDescription)
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
            }
            .padding(.top, 20)
            
            Spacer()
            
            // Demo content based on phase
            switch currentPhase {
            case .instruction:
                InstructionView()
            case .demonstration:
                DemonstrationView()
            case .practice:
                PracticeView(squatCount: $squatCount, animationTrigger: $animationTrigger)
            case .completion:
                CompletionView()
            }
            
            Spacer()
            
            // Action button
            Button(action: handleAction) {
                Text(actionButtonTitle)
                    .fontWeight(.semibold)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .foregroundColor(.blue)
                    .background(.white, in: RoundedRectangle(cornerRadius: 12))
            }
            .padding(.horizontal, 32)
            .padding(.bottom, 20)
        }
        .overlay(alignment: .center) {
            if showingCelebration {
                CelebrationOverlay()
                    .allowsHitTesting(false)
            }
        }
        .onChange(of: squatCount) { _, newCount in
            if newCount >= 3 && currentPhase == .practice {
                triggerCompletion()
            }
        }
    }
    
    private var phaseDescription: String {
        switch currentPhase {
        case .instruction:
            return "We'll guide you through your first squat with AI coaching"
        case .demonstration:
            return "Watch the proper squat form"
        case .practice:
            return "Now you try! Complete 3 squats"
        case .completion:
            return "Perfect! You've mastered the basics"
        }
    }
    
    private var actionButtonTitle: String {
        switch currentPhase {
        case .instruction:
            return "Show Me How"
        case .demonstration:
            return "I'm Ready to Try"
        case .practice:
            return "Skip Practice"
        case .completion:
            return "Continue"
        }
    }
    
    private func handleAction() {
        switch currentPhase {
        case .instruction:
            withAnimation(.easeInOut(duration: 0.4)) {
                currentPhase = .demonstration
            }
        case .demonstration:
            withAnimation(.easeInOut(duration: 0.4)) {
                currentPhase = .practice
            }
        case .practice:
            triggerCompletion()
        case .completion:
            coordinator.completeDemo()
        }
    }
    
    private func triggerCompletion() {
        withAnimation(.bouncy(duration: 0.6)) {
            currentPhase = .completion
        }
        showCelebration()
    }
    
    private func showCelebration() {
        showingCelebration = true
        
        // Haptic feedback
        let successFeedback = UINotificationFeedbackGenerator()
        successFeedback.notificationOccurred(.success)
        
        // Auto-hide celebration
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            showingCelebration = false
        }
    }
}

// MARK: - Instruction View

struct InstructionView: View {
    var body: some View {
        VStack(spacing: 24) {
            // Squat icon
            Image(systemName: "figure.squat")
                .font(.system(size: 80))
                .foregroundColor(.white)
                .background(
                    Circle()
                        .fill(.white.opacity(0.1))
                        .frame(width: 120, height: 120)
                )
            
            // Instructions
            VStack(spacing: 16) {
                InstructionStep(
                    number: "1",
                    title: "Stand with feet shoulder-width apart",
                    description: "Keep your toes slightly pointed outward"
                )
                
                InstructionStep(
                    number: "2",
                    title: "Lower your body by bending knees",
                    description: "Keep your back straight and chest up"
                )
                
                InstructionStep(
                    number: "3",
                    title: "Push through heels to stand",
                    description: "Return to starting position"
                )
            }
        }
    }
}

struct InstructionStep: View {
    let number: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 16) {
            // Step number
            Text(number)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.blue)
                .frame(width: 32, height: 32)
                .background(Circle().fill(.white))
            
            // Content
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
            }
            
            Spacer()
        }
        .padding(.horizontal, 16)
    }
}

// MARK: - Demonstration View

struct DemonstrationView: View {
    @State private var animationPhase: SquatPhase = .standing
    @State private var animationTimer: Timer?
    
    enum SquatPhase: CaseIterable {
        case standing
        case descending
        case bottom
        case ascending
        
        var description: String {
            switch self {
            case .standing:
                return "Starting position"
            case .descending:
                return "Lower down slowly"
            case .bottom:
                return "Hold at the bottom"
            case .ascending:
                return "Push up through heels"
            }
        }
        
        var figureScale: CGFloat {
            switch self {
            case .standing, .ascending:
                return 1.0
            case .descending:
                return 0.9
            case .bottom:
                return 0.8
            }
        }
    }
    
    var body: some View {
        VStack(spacing: 32) {
            // Animated figure
            VStack(spacing: 16) {
                Image(systemName: "figure.squat")
                    .font(.system(size: 100))
                    .foregroundColor(.white)
                    .scaleEffect(animationPhase.figureScale)
                    .animation(.easeInOut(duration: 0.8), value: animationPhase)
                
                Text(animationPhase.description)
                    .font(.headline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .transition(.opacity)
            }
            
            // Form tips
            VStack(spacing: 12) {
                FormTip(
                    icon: "arrow.up.and.down",
                    text: "Keep your back straight",
                    isHighlighted: animationPhase == .descending || animationPhase == .bottom
                )
                
                FormTip(
                    icon: "arrow.down.to.line",
                    text: "Lower until thighs are parallel",
                    isHighlighted: animationPhase == .bottom
                )
                
                FormTip(
                    icon: "arrow.up",
                    text: "Push through your heels",
                    isHighlighted: animationPhase == .ascending
                )
            }
        }
        .onAppear {
            startAnimation()
        }
        .onDisappear {
            stopAnimation()
        }
    }
    
    private func startAnimation() {
        animationTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            withAnimation(.easeInOut(duration: 0.8)) {
                let currentIndex = SquatPhase.allCases.firstIndex(of: animationPhase) ?? 0
                let nextIndex = (currentIndex + 1) % SquatPhase.allCases.count
                animationPhase = SquatPhase.allCases[nextIndex]
            }
        }
    }
    
    private func stopAnimation() {
        animationTimer?.invalidate()
        animationTimer = nil
    }
}

struct FormTip: View {
    let icon: String
    let text: String
    let isHighlighted: Bool
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(isHighlighted ? .yellow : .white.opacity(0.6))
                .frame(width: 24)
            
            Text(text)
                .font(.subheadline)
                .foregroundColor(isHighlighted ? .white : .white.opacity(0.6))
            
            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 10)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(isHighlighted ? .yellow.opacity(0.2) : .white.opacity(0.05))
        )
        .animation(.easeInOut(duration: 0.3), value: isHighlighted)
    }
}

// MARK: - Practice View

struct PracticeView: View {
    @Binding var squatCount: Int
    @Binding var animationTrigger: Int
    @State private var encouragementMessages = [
        "Great start!",
        "Perfect form!",
        "One more!"
    ]
    
    var body: some View {
        VStack(spacing: 32) {
            // Rep counter
            ZStack {
                Circle()
                    .stroke(.white.opacity(0.3), lineWidth: 4)
                    .frame(width: 120, height: 120)
                
                Circle()
                    .trim(from: 0, to: Double(squatCount) / 3.0)
                    .stroke(.white, style: StrokeStyle(lineWidth: 4, lineCap: .round))
                    .frame(width: 120, height: 120)
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut(duration: 0.5), value: squatCount)
                
                VStack(spacing: 4) {
                    Text("\(squatCount)")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .scaleEffect(1.0 + (animationTrigger > 0 ? 0.2 : 0.0))
                        .animation(.bouncy(duration: 0.4), value: animationTrigger)
                    
                    Text("/ 3")
                        .font(.headline)
                        .foregroundColor(.white.opacity(0.7))
                }
            }
            
            // Encouragement
            if squatCount > 0 && squatCount <= encouragementMessages.count {
                Text(encouragementMessages[squatCount - 1])
                    .font(.headline)
                    .fontWeight(.medium)
                    .foregroundColor(.yellow)
                    .transition(.scale.combined(with: .opacity))
            }
            
            // Practice tips
            VStack(spacing: 12) {
                Text("Position yourself in front of the camera and start squatting!")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
                
                Button("Simulate Squat") {
                    simulateSquat()
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 8)
                .background(.white.opacity(0.2), in: Capsule())
                .foregroundColor(.white)
                .font(.caption)
            }
        }
    }
    
    private func simulateSquat() {
        if squatCount < 3 {
            squatCount += 1
            animationTrigger += 1
            
            // Reset animation trigger
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
                animationTrigger = 0
            }
            
            // Haptic feedback
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
        }
    }
}

// MARK: - Completion View

struct CompletionView: View {
    var body: some View {
        VStack(spacing: 24) {
            // Success icon
            ZStack {
                Circle()
                    .fill(.green.opacity(0.2))
                    .frame(width: 120, height: 120)
                
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.green)
            }
            
            VStack(spacing: 12) {
                Text("Excellent Work!")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Text("You've completed your first exercise with perfect form. Ready to start your fitness journey?")
                    .font(.body)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
            }
            
            // Achievement badges
            HStack(spacing: 16) {
                AchievementBadge(icon: "target", title: "First Squat")
                AchievementBadge(icon: "star.fill", title: "Perfect Form")
                AchievementBadge(icon: "trophy.fill", title: "Quick Learner")
            }
        }
    }
}

struct AchievementBadge: View {
    let icon: String
    let title: String
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.yellow)
            
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.white)
        }
        .padding(12)
        .background(.white.opacity(0.1), in: RoundedRectangle(cornerRadius: 10))
    }
}

#Preview("Demonstration - Instruction") {
    ZStack {
        LinearGradient(
            colors: [.blue.opacity(0.8), .purple.opacity(0.6)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
        
        DemonstrationStepView(coordinator: OnboardingCoordinator())
    }
}

#Preview("Demonstration - Practice") {
    @StateObject var coordinator = OnboardingCoordinator()
    @State var squatCount = 1
    @State var animationTrigger = 0
    
    return ZStack {
        LinearGradient(
            colors: [.blue.opacity(0.8), .purple.opacity(0.6)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
        
        PracticeView(squatCount: $squatCount, animationTrigger: $animationTrigger)
    }
}