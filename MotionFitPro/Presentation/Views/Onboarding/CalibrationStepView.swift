import SwiftUI

struct CalibrationStepView: View {
    @ObservedObject var coordinator: OnboardingCoordinator
    @State private var pulseScale: CGFloat = 1.0
    @State private var rotationAngle: Double = 0
    
    var body: some View {
        VStack(spacing: 32) {
            Spacer()
            
            // Calibration visualization
            ZStack {
                // Outer ring
                Circle()
                    .stroke(.white.opacity(0.2), lineWidth: 3)
                    .frame(width: 200, height: 200)
                
                // Progress ring
                Circle()
                    .trim(from: 0, to: coordinator.calibrationProgress)
                    .stroke(
                        .white,
                        style: StrokeStyle(lineWidth: 6, lineCap: .round)
                    )
                    .frame(width: 200, height: 200)
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut(duration: 0.5), value: coordinator.calibrationProgress)
                
                // Center figure
                VStack(spacing: 8) {
                    Image(systemName: "figure.stand")
                        .font(.system(size: 40))
                        .foregroundColor(.white)
                        .scaleEffect(pulseScale)
                        .rotationEffect(.degrees(rotationAngle))
                    
                    Text("\(Int(coordinator.calibrationProgress * 100))%")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                }
            }
            .onAppear {
                startPulseAnimation()
                startRotationAnimation()
            }
            
            // Instructions
            VStack(spacing: 16) {
                Text("Body Calibration")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Text("Position yourself in the camera view so we can detect your body for accurate motion tracking.")
                    .font(.body)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
            }
            
            // Calibration tips
            VStack(spacing: 12) {
                CalibrationTip(
                    icon: "arrow.up.and.down",
                    text: "Stand 3-6 feet from your device",
                    isActive: coordinator.calibrationProgress > 0.2
                )
                
                CalibrationTip(
                    icon: "figure.arms.open",
                    text: "Ensure your full body is visible",
                    isActive: coordinator.calibrationProgress > 0.5
                )
                
                CalibrationTip(
                    icon: "lightbulb.fill",
                    text: "Make sure you have good lighting",
                    isActive: coordinator.calibrationProgress > 0.8
                )
            }
            
            // Status message
            if coordinator.calibrationProgress < 1.0 {
                HStack(spacing: 8) {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                    
                    Text("Detecting your body position...")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.8))
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(.white.opacity(0.1), in: Capsule())
            } else {
                HStack(spacing: 8) {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    
                    Text("Calibration complete!")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(.green.opacity(0.2), in: Capsule())
                .transition(.scale.combined(with: .opacity))
            }
            
            Spacer()
        }
        .padding(.horizontal, 32)
    }
    
    private func startPulseAnimation() {
        withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
            pulseScale = 1.1
        }
    }
    
    private func startRotationAnimation() {
        withAnimation(.linear(duration: 8.0).repeatForever(autoreverses: false)) {
            rotationAngle = 360
        }
    }
}

struct CalibrationTip: View {
    let icon: String
    let text: String
    let isActive: Bool
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: isActive ? "checkmark.circle.fill" : icon)
                .font(.title3)
                .foregroundColor(isActive ? .green : .white.opacity(0.6))
                .frame(width: 24)
                .animation(.easeInOut(duration: 0.3), value: isActive)
            
            Text(text)
                .font(.subheadline)
                .foregroundColor(isActive ? .white : .white.opacity(0.6))
                .animation(.easeInOut(duration: 0.3), value: isActive)
            
            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 10)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(isActive ? .white.opacity(0.15) : .white.opacity(0.05))
                .animation(.easeInOut(duration: 0.3), value: isActive)
        )
    }
}

#Preview("Calibration Step - In Progress") {
    @StateObject var coordinator = OnboardingCoordinator()
    
    return ZStack {
        LinearGradient(
            colors: [.blue.opacity(0.8), .purple.opacity(0.6)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
        
        CalibrationStepView(coordinator: coordinator)
    }
    .onAppear {
        coordinator.currentStep = .calibration
        coordinator.calibrationProgress = 0.6
    }
}

#Preview("Calibration Step - Complete") {
    @StateObject var coordinator = OnboardingCoordinator()
    
    return ZStack {
        LinearGradient(
            colors: [.blue.opacity(0.8), .purple.opacity(0.6)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
        
        CalibrationStepView(coordinator: coordinator)
    }
    .onAppear {
        coordinator.currentStep = .calibration
        coordinator.calibrationProgress = 1.0
    }
}