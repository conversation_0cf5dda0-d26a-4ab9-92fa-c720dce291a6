import SwiftUI

struct CompletionStepView: View {
    @State private var showingFeatures = false
    @State private var animationDelay: Double = 0
    
    var body: some View {
        ScrollView {
            VStack(spacing: 32) {
                Spacer()
                
                // Success animation
                VStack(spacing: 24) {
                    ZStack {
                        // Animated rings
                        ForEach(0..<3, id: \.self) { index in
                            Circle()
                                .stroke(.white.opacity(0.3), lineWidth: 2)
                                .frame(width: 120 + CGFloat(index * 30), height: 120 + CGFloat(index * 30))
                                .scaleEffect(showingFeatures ? 1.2 : 0.8)
                                .opacity(showingFeatures ? 0.3 : 0.8)
                                .animation(
                                    .easeInOut(duration: 1.5)
                                    .delay(Double(index) * 0.2)
                                    .repeatForever(autoreverses: true),
                                    value: showingFeatures
                                )
                        }
                        
                        // Center icon
                        ZStack {
                            Circle()
                                .fill(.green)
                                .frame(width: 80, height: 80)
                                .scaleEffect(showingFeatures ? 1.0 : 0.1)
                                .animation(.bouncy(duration: 0.8).delay(0.3), value: showingFeatures)
                            
                            Image(systemName: "checkmark")
                                .font(.system(size: 40, weight: .bold))
                                .foregroundColor(.white)
                                .scaleEffect(showingFeatures ? 1.0 : 0.1)
                                .animation(.bouncy(duration: 0.8).delay(0.5), value: showingFeatures)
                        }
                    }
                    
                    VStack(spacing: 12) {
                        Text("You're All Set!")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .scaleEffect(showingFeatures ? 1.0 : 0.8)
                            .opacity(showingFeatures ? 1.0 : 0.0)
                            .animation(.bouncy(duration: 0.6).delay(0.7), value: showingFeatures)
                        
                        Text("Welcome to MotionFitPro! Your personalized AI fitness coach is ready to help you achieve your goals.")
                            .font(.body)
                            .foregroundColor(.white.opacity(0.8))
                            .multilineTextAlignment(.center)
                            .opacity(showingFeatures ? 1.0 : 0.0)
                            .animation(.easeInOut(duration: 0.6).delay(0.9), value: showingFeatures)
                    }
                }
                
                // Ready features
                if showingFeatures {
                    VStack(spacing: 20) {
                        Text("What's waiting for you:")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .transition(.opacity.combined(with: .move(edge: .top)))
                        
                        VStack(spacing: 16) {
                            ReadyFeature(
                                icon: "figure.run",
                                title: "Exercise Library",
                                description: "50+ exercises with AI form analysis",
                                delay: 0.1
                            )
                            
                            ReadyFeature(
                                icon: "brain.head.profile",
                                title: "Smart Coaching",
                                description: "Real-time feedback and corrections",
                                delay: 0.2
                            )
                            
                            ReadyFeature(
                                icon: "chart.line.uptrend.xyaxis",
                                title: "Progress Tracking",
                                description: "Detailed analytics and achievements",
                                delay: 0.3
                            )
                            
                            ReadyFeature(
                                icon: "person.2.fill",
                                title: "Community",
                                description: "Share progress and get motivated",
                                delay: 0.4
                            )
                        }
                    }
                    .transition(.opacity.combined(with: .move(edge: .bottom)))
                }
                
                Spacer()
                
                // Quick tips
                if showingFeatures {
                    VStack(spacing: 16) {
                        Text("Pro Tips")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                        
                        VStack(spacing: 12) {
                            QuickTip(
                                icon: "lightbulb.fill",
                                text: "Find good lighting for better tracking accuracy"
                            )
                            
                            QuickTip(
                                icon: "speaker.wave.2.fill",
                                text: "Use headphones for the best audio coaching experience"
                            )
                            
                            QuickTip(
                                icon: "figure.stand",
                                text: "Keep your full body visible in the camera frame"
                            )
                        }
                    }
                    .transition(.opacity.combined(with: .move(edge: .bottom)))
                }
            }
            .padding(.horizontal, 32)
            .padding(.bottom, 100) // Space for navigation
        }
        .scrollBounceBehavior(.basedOnSize)
        .onAppear {
            withAnimation(.easeInOut(duration: 0.1)) {
                showingFeatures = true
            }
        }
    }
}

// MARK: - Ready Feature

struct ReadyFeature: View {
    let icon: String
    let title: String
    let description: String
    let delay: Double
    
    @State private var isVisible = false
    
    var body: some View {
        HStack(spacing: 16) {
            ZStack {
                Circle()
                    .fill(.white.opacity(0.2))
                    .frame(width: 50, height: 50)
                
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(.white)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
            }
            
            Spacer()
        }
        .scaleEffect(isVisible ? 1.0 : 0.9)
        .opacity(isVisible ? 1.0 : 0.0)
        .onAppear {
            withAnimation(.bouncy(duration: 0.6).delay(delay)) {
                isVisible = true
            }
        }
    }
}

// MARK: - Quick Tip

struct QuickTip: View {
    let icon: String
    let text: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.subheadline)
                .foregroundColor(.yellow)
                .frame(width: 20)
            
            Text(text)
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
            
            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 10)
        .background(.white.opacity(0.1), in: RoundedRectangle(cornerRadius: 8))
    }
}

// MARK: - Celebration Overlay (reusable from other views)

struct CelebrationOverlay: View {
    @State private var particles: [ParticleData] = []
    @State private var animationTimer: Timer?
    
    var body: some View {
        ZStack {
            ForEach(particles, id: \.id) { particle in
                Circle()
                    .fill(particle.color)
                    .frame(width: particle.size, height: particle.size)
                    .position(particle.position)
                    .opacity(particle.opacity)
                    .scaleEffect(particle.scale)
            }
        }
        .onAppear {
            generateParticles()
            startAnimation()
        }
        .onDisappear {
            animationTimer?.invalidate()
        }
    }
    
    private func generateParticles() {
        particles = []
        let colors: [Color] = [.yellow, .orange, .blue, .green, .purple, .pink]
        
        for _ in 0..<30 {
            let particle = ParticleData(
                id: UUID(),
                position: CGPoint(x: UIScreen.main.bounds.width / 2, y: UIScreen.main.bounds.height / 2),
                velocity: CGPoint(
                    x: Double.random(in: -200...200),
                    y: Double.random(in: -300...100)
                ),
                color: colors.randomElement() ?? .yellow,
                size: Double.random(in: 4...16),
                opacity: 1.0,
                scale: 1.0
            )
            particles.append(particle)
        }
    }
    
    private func startAnimation() {
        animationTimer = Timer.scheduledTimer(withTimeInterval: 0.016, repeats: true) { _ in
            updateParticles()
        }
    }
    
    private func updateParticles() {
        for i in particles.indices {
            particles[i].position.x += particles[i].velocity.x * 0.016
            particles[i].position.y += particles[i].velocity.y * 0.016
            particles[i].velocity.y += 300 * 0.016 // Gravity
            particles[i].opacity -= 0.016 * 0.6 // Fade out
            particles[i].scale -= 0.016 * 0.3 // Shrink
        }
        
        // Remove particles that are no longer visible
        particles.removeAll { $0.opacity <= 0 }
        
        // Stop animation when no particles remain
        if particles.isEmpty {
            animationTimer?.invalidate()
        }
    }
}

#Preview("Completion Step") {
    ZStack {
        LinearGradient(
            colors: [.blue.opacity(0.8), .purple.opacity(0.6)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
        
        CompletionStepView()
    }
}