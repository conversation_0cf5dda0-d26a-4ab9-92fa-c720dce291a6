import SwiftUI
import AVFoundation

struct PermissionStepView: View {
    @ObservedObject var coordinator: OnboardingCoordinator
    
    var body: some View {
        ScrollView {
            VStack(spacing: 32) {
                Spacer()
                
                // Camera icon and animation
                ZStack {
                    Circle()
                        .fill(.white.opacity(0.1))
                        .frame(width: 120, height: 120)
                    
                    Image(systemName: "camera.viewfinder")
                        .font(.system(size: 50))
                        .foregroundColor(.white)
                        .scaleEffect(coordinator.cameraPermissionStatus == .authorized ? 1.2 : 1.0)
                        .animation(.bouncy(duration: 0.6), value: coordinator.cameraPermissionStatus)
                }
                
                // Title and description
                VStack(spacing: 16) {
                    Text("Camera Access Required")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                    
                    Text("MotionFitPro uses your device's camera to track your body movements and provide real-time form feedback.")
                        .font(.body)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                        .lineLimit(nil)
                }
                
                // Privacy assurance
                VStack(spacing: 16) {
                    PrivacyFeature(
                        icon: "lock.shield.fill",
                        title: "Your Privacy is Protected",
                        description: "All processing happens on your device. No video data is ever stored or shared."
                    )
                    
                    PrivacyFeature(
                        icon: "cpu",
                        title: "Local Processing Only",
                        description: "AI analysis runs entirely on your iPhone. Your movements never leave your device."
                    )
                    
                    PrivacyFeature(
                        icon: "eye.slash.fill",
                        title: "No Recording",
                        description: "We only analyze joint positions in real-time. No images or videos are saved."
                    )
                }
                
                // Permission status
                if coordinator.cameraPermissionStatus != .notDetermined {
                    PermissionStatusView(status: coordinator.cameraPermissionStatus)
                }
                
                Spacer()
            }
            .padding(.horizontal, 32)
        }
        .scrollBounceBehavior(.basedOnSize)
    }
}

struct PrivacyFeature: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.green)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
            }
            
            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(.white.opacity(0.1), in: RoundedRectangle(cornerRadius: 12))
    }
}

struct PermissionStatusView: View {
    let status: AVAuthorizationStatus
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: statusIcon)
                .font(.title3)
                .foregroundColor(statusColor)
            
            Text(statusMessage)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.white)
            
            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(statusBackgroundColor, in: RoundedRectangle(cornerRadius: 12))
        .transition(.scale.combined(with: .opacity))
    }
    
    private var statusIcon: String {
        switch status {
        case .authorized:
            return "checkmark.circle.fill"
        case .denied, .restricted:
            return "xmark.circle.fill"
        default:
            return "questionmark.circle.fill"
        }
    }
    
    private var statusColor: Color {
        switch status {
        case .authorized:
            return .green
        case .denied, .restricted:
            return .red
        default:
            return .orange
        }
    }
    
    private var statusMessage: String {
        switch status {
        case .authorized:
            return "Camera access granted! You're ready to continue."
        case .denied:
            return "Camera access denied. Please enable in Settings to continue."
        case .restricted:
            return "Camera access is restricted on this device."
        default:
            return "Camera permission not yet requested."
        }
    }
    
    private var statusBackgroundColor: Color {
        switch status {
        case .authorized:
            return .green.opacity(0.2)
        case .denied, .restricted:
            return .red.opacity(0.2)
        default:
            return .orange.opacity(0.2)
        }
    }
}

#Preview("Permission Step - Not Determined") {
    @StateObject var coordinator = OnboardingCoordinator()
    
    return ZStack {
        LinearGradient(
            colors: [.blue.opacity(0.8), .purple.opacity(0.6)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
        
        PermissionStepView(coordinator: coordinator)
    }
    .onAppear {
        coordinator.currentStep = .permissions
    }
}

#Preview("Permission Step - Authorized") {
    @StateObject var coordinator = OnboardingCoordinator()
    
    return ZStack {
        LinearGradient(
            colors: [.blue.opacity(0.8), .purple.opacity(0.6)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
        
        PermissionStepView(coordinator: coordinator)
    }
    .onAppear {
        coordinator.currentStep = .permissions
        coordinator.cameraPermissionStatus = .authorized
    }
}