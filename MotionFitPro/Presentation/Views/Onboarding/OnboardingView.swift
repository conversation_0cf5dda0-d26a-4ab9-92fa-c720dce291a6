import SwiftUI
import AVFoundation

struct OnboardingView: View {
    @StateObject private var coordinator = OnboardingCoordinator()
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationStack {
            ZStack {
                // Background gradient
                LinearGradient(
                    colors: [.blue.opacity(0.8), .purple.opacity(0.6)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                // Content
                VStack(spacing: 0) {
                    // Progress indicator
                    OnboardingProgressView(
                        currentStep: coordinator.currentStep,
                        totalSteps: OnboardingCoordinator.OnboardingStep.allCases.count
                    )
                    .padding(.top, 20)
                    
                    // Main content
                    TabView(selection: $coordinator.currentStep) {
                        ForEach(OnboardingCoordinator.OnboardingStep.allCases, id: \.self) { step in
                            stepView(for: step)
                                .tag(step)
                        }
                    }
                    .tabViewStyle(.page(indexDisplayMode: .never))
                    .animation(.easeInOut(duration: 0.4), value: coordinator.currentStep)
                    
                    // Navigation controls
                    OnboardingNavigationView(coordinator: coordinator)
                        .padding(.bottom, 34)
                }
            }
        }
        .onAppear {
            coordinator.start()
        }
        .onChange(of: coordinator.isOnboardingComplete) { _, isComplete in
            if isComplete {
                dismiss()
            }
        }
        .alert("Permission Required", isPresented: .constant(coordinator.errorMessage != nil)) {
            Button("Settings") {
                if let settingsURL = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(settingsURL)
                }
            }
            Button("Cancel", role: .cancel) {
                coordinator.errorMessage = nil
            }
        } message: {
            Text(coordinator.errorMessage ?? "")
        }
    }
    
    @ViewBuilder
    private func stepView(for step: OnboardingCoordinator.OnboardingStep) -> some View {
        switch step {
        case .welcome:
            WelcomeStepView()
        case .permissions:
            PermissionStepView(coordinator: coordinator)
        case .calibration:
            CalibrationStepView(coordinator: coordinator)
        case .demonstration:
            DemonstrationStepView(coordinator: coordinator)
        case .assessment:
            AssessmentStepView(coordinator: coordinator)
        case .completion:
            CompletionStepView()
        }
    }
}

// MARK: - Progress View

struct OnboardingProgressView: View {
    let currentStep: OnboardingCoordinator.OnboardingStep
    let totalSteps: Int
    
    private var currentIndex: Int {
        OnboardingCoordinator.OnboardingStep.allCases.firstIndex(of: currentStep) ?? 0
    }
    
    private var progress: Double {
        Double(currentIndex) / Double(totalSteps - 1)
    }
    
    var body: some View {
        VStack(spacing: 8) {
            // Progress bar
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(.white.opacity(0.3))
                        .frame(height: 4)
                        .cornerRadius(2)
                    
                    Rectangle()
                        .fill(.white)
                        .frame(width: geometry.size.width * progress, height: 4)
                        .cornerRadius(2)
                        .animation(.easeInOut(duration: 0.4), value: progress)
                }
            }
            .frame(height: 4)
            
            // Step indicator
            HStack {
                Text("Step \(currentIndex + 1) of \(totalSteps)")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
                
                Spacer()
                
                Text("\(Int(progress * 100))%")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
            }
        }
        .padding(.horizontal, 24)
    }
}

// MARK: - Navigation View

struct OnboardingNavigationView: View {
    @ObservedObject var coordinator: OnboardingCoordinator
    
    var body: some View {
        VStack(spacing: 16) {
            // Primary action button
            Button(action: primaryAction) {
                HStack {
                    if coordinator.isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    }
                    
                    Text(primaryButtonTitle)
                        .fontWeight(.semibold)
                }
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .foregroundColor(primaryButtonTextColor)
                .background(primaryButtonBackground, in: RoundedRectangle(cornerRadius: 12))
            }
            .disabled(isPrimaryButtonDisabled)
            .opacity(isPrimaryButtonDisabled ? 0.6 : 1.0)
            
            // Secondary actions
            HStack {
                if coordinator.currentStep != .welcome {
                    Button("Back") {
                        coordinator.previousStep()
                    }
                    .foregroundColor(.white.opacity(0.8))
                }
                
                Spacer()
                
                if coordinator.currentStep != .completion {
                    Button("Skip") {
                        coordinator.skipOnboarding()
                    }
                    .foregroundColor(.white.opacity(0.8))
                }
            }
        }
        .padding(.horizontal, 24)
    }
    
    private func primaryAction() {
        switch coordinator.currentStep {
        case .permissions:
            Task {
                await coordinator.requestCameraPermission()
            }
        case .demonstration:
            coordinator.completeDemo()
        default:
            coordinator.nextStep()
        }
    }
    
    private var primaryButtonTitle: String {
        switch coordinator.currentStep {
        case .welcome:
            return "Get Started"
        case .permissions:
            return coordinator.cameraPermissionStatus == .authorized ? "Continue" : "Allow Camera Access"
        case .calibration:
            return coordinator.calibrationProgress >= 1.0 ? "Continue" : "Calibrating..."
        case .demonstration:
            return "I Did It!"
        case .assessment:
            return "Start My Journey"
        case .completion:
            return "Let's Go!"
        }
    }
    
    private var primaryButtonTextColor: Color {
        switch coordinator.currentStep {
        case .permissions where coordinator.cameraPermissionStatus != .authorized:
            return .blue
        default:
            return .white
        }
    }
    
    private var primaryButtonBackground: some ShapeStyle {
        switch coordinator.currentStep {
        case .permissions where coordinator.cameraPermissionStatus != .authorized:
            return AnyShapeStyle(.white)
        default:
            return AnyShapeStyle(.white.opacity(0.2))
        }
    }
    
    private var isPrimaryButtonDisabled: Bool {
        switch coordinator.currentStep {
        case .calibration:
            return coordinator.calibrationProgress < 1.0
        case .permissions:
            return coordinator.isLoading
        default:
            return coordinator.isLoading
        }
    }
}

// MARK: - Welcome Step

struct WelcomeStepView: View {
    var body: some View {
        ScrollView {
            VStack(spacing: 32) {
                Spacer()
                
                // App icon and title
                VStack(spacing: 20) {
                    Image(systemName: "figure.run")
                        .font(.system(size: 80))
                        .foregroundColor(.white)
                        .scaleEffect(1.0)
                        .animation(.bouncy(duration: 1.0).delay(0.2), value: true)
                    
                    VStack(spacing: 8) {
                        Text("MotionFitPro")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                        
                        Text("AI-Powered Motion Tracking")
                            .font(.headline)
                            .foregroundColor(.white.opacity(0.8))
                    }
                }
                
                // Features
                VStack(spacing: 24) {
                    FeatureRow(
                        icon: "camera.viewfinder",
                        title: "Real-time Motion Tracking",
                        description: "Advanced AI analyzes your form in real-time"
                    )
                    
                    FeatureRow(
                        icon: "chart.line.uptrend.xyaxis",
                        title: "Personal Progress",
                        description: "Track improvements and achieve your goals"
                    )
                    
                    FeatureRow(
                        icon: "person.fill.checkmark",
                        title: "AI Coaching",
                        description: "Get instant feedback to perfect your technique"
                    )
                }
                
                Spacer()
            }
            .padding(.horizontal, 32)
        }
        .scrollBounceBehavior(.basedOnSize)
    }
}

struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.white)
                .frame(width: 32)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
            }
            
            Spacer()
        }
    }
}

#Preview("Onboarding Flow") {
    OnboardingView()
}

#Preview("Welcome Step") {
    ZStack {
        LinearGradient(
            colors: [.blue.opacity(0.8), .purple.opacity(0.6)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
        
        WelcomeStepView()
    }
}