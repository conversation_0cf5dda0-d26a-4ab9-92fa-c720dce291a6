import SwiftUI

struct ProfileView: View {
    @State private var userProfile = UserProfile.sample
    @State private var showingSettings = false
    @State private var showingAchievements = false
    @State private var showingStats = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Profile Header
                    profileHeader
                    
                    // Quick Stats
                    quickStatsSection
                    
                    // Achievements Preview
                    achievementsPreview
                    
                    // Settings and Options
                    settingsSection
                    
                    // About and Support
                    aboutSection
                }
                .padding()
            }
            .navigationTitle("Profile")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Settings") {
                        showingSettings = true
                    }
                }
            }
            .sheet(isPresented: $showingSettings) {
                SettingsView()
            }
            .sheet(isPresented: $showingAchievements) {
                AchievementsView()
            }
            .sheet(isPresented: $showingStats) {
                DetailedStatsView()
            }
        }
    }
    
    // MARK: - Profile Header
    private var profileHeader: some View {
        VStack(spacing: 16) {
            // Profile Image
            ZStack {
                Circle()
                    .fill(LinearGradient(
                        gradient: Gradient(colors: [.blue, .purple]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
                    .frame(width: 100, height: 100)
                
                Text(userProfile.initials)
                    .font(.system(size: 36, weight: .bold))
                    .foregroundColor(.white)
            }
            
            // User Info
            VStack(spacing: 4) {
                Text(userProfile.name)
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text(userProfile.fitnessLevel.displayName)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 4)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(12)
            }
            
            // Member Since
            Text("Member since \(userProfile.memberSince, formatter: memberSinceFormatter)")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(16)
    }
    
    // MARK: - Quick Stats
    private var quickStatsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("This Week")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack(spacing: 12) {
                StatCard(
                    title: "Workouts",
                    value: "5",
                    icon: "figure.run",
                    color: .blue
                )
                
                StatCard(
                    title: "Minutes",
                    value: "127",
                    icon: "clock.fill",
                    color: .green
                )
                
                StatCard(
                    title: "Calories",
                    value: "890",
                    icon: "flame.fill",
                    color: .orange
                )
            }
        }
    }
    
    // MARK: - Achievements Preview
    private var achievementsPreview: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Recent Achievements")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("View All") {
                    showingAchievements = true
                }
                .font(.subheadline)
                .foregroundColor(.blue)
            }
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(0..<5) { index in
                        AchievementBadge(
                            title: "First Workout",
                            icon: "star.fill",
                            color: .yellow,
                            isUnlocked: true
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
    }
    
    // MARK: - Settings Section
    private var settingsSection: some View {
        VStack(spacing: 0) {
            SettingsRow(
                title: "Workout Preferences",
                icon: "slider.horizontal.3",
                action: { showingSettings = true }
            )
            
            Divider()
            
            SettingsRow(
                title: "Detailed Statistics",
                icon: "chart.bar.fill",
                action: { showingStats = true }
            )
            
            Divider()
            
            SettingsRow(
                title: "Privacy & Data",
                icon: "lock.fill",
                action: { /* Show privacy settings */ }
            )
            
            Divider()
            
            SettingsRow(
                title: "Notifications",
                icon: "bell.fill",
                action: { /* Show notification settings */ }
            )
        }
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
    
    // MARK: - About Section
    private var aboutSection: some View {
        VStack(spacing: 0) {
            SettingsRow(
                title: "Help & Support",
                icon: "questionmark.circle.fill",
                action: { /* Show help */ }
            )
            
            Divider()
            
            SettingsRow(
                title: "Rate MotionFitPro",
                icon: "star.fill",
                action: { /* Show app store rating */ }
            )
            
            Divider()
            
            SettingsRow(
                title: "About",
                icon: "info.circle.fill",
                action: { /* Show about */ }
            )
        }
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
    
    private var memberSinceFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMMM yyyy"
        return formatter
    }
}

// MARK: - Supporting Views

struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 20))
                .foregroundColor(color)
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
}

struct AchievementBadge: View {
    let title: String
    let icon: String
    let color: Color
    let isUnlocked: Bool
    
    var body: some View {
        VStack(spacing: 8) {
            ZStack {
                Circle()
                    .fill(isUnlocked ? color : Color.gray.opacity(0.3))
                    .frame(width: 50, height: 50)
                
                Image(systemName: icon)
                    .font(.system(size: 20))
                    .foregroundColor(isUnlocked ? .white : .gray)
            }
            
            Text(title)
                .font(.caption)
                .foregroundColor(isUnlocked ? .primary : .secondary)
                .multilineTextAlignment(.center)
                .lineLimit(2)
        }
        .frame(width: 80)
        .opacity(isUnlocked ? 1.0 : 0.6)
    }
}

struct SettingsRow: View {
    let title: String
    let icon: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .font(.system(size: 16))
                    .foregroundColor(.blue)
                    .frame(width: 24)
                
                Text(title)
                    .font(.body)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
            }
            .padding()
        }
    }
}

// MARK: - Sample Data Extension
extension UserProfile {
    static let sample = UserProfile(
        id: UUID(),
        name: "John Doe",
        email: "<EMAIL>",
        fitnessLevel: .intermediate,
        preferredWorkoutDuration: .medium,
        coachingPersonality: .encouraging,
        memberSince: Calendar.current.date(byAdding: .month, value: -6, to: Date()) ?? Date()
    )
    
    var initials: String {
        let components = name.components(separatedBy: " ")
        return components.compactMap { $0.first }.map { String($0) }.joined()
    }
}

// MARK: - Placeholder Views
struct SettingsView: View {
    var body: some View {
        NavigationView {
            Text("Settings")
                .font(.title)
                .navigationTitle("Settings")
                .navigationBarTitleDisplayMode(.inline)
        }
    }
}

struct AchievementsView: View {
    var body: some View {
        NavigationView {
            Text("Achievements")
                .font(.title)
                .navigationTitle("Achievements")
                .navigationBarTitleDisplayMode(.inline)
        }
    }
}

struct DetailedStatsView: View {
    var body: some View {
        NavigationView {
            Text("Detailed Statistics")
                .font(.title)
                .navigationTitle("Statistics")
                .navigationBarTitleDisplayMode(.inline)
        }
    }
}

#Preview {
    ProfileView()
}
