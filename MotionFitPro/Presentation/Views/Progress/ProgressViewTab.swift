import SwiftUI
import Charts

struct ProgressViewTab: View {
    @State private var selectedTimeframe: ProgressTimeframe = .week
    @State private var selectedMetric: ProgressMetric = .workouts
    @State private var showingDetailedView = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Timeframe Selector
                    timeframePicker
                    
                    // Key Metrics Overview
                    metricsOverview
                    
                    // Main Chart
                    mainChart
                    
                    // Progress Insights
                    progressInsights
                    
                    // Personal Records
                    personalRecordsSection
                    
                    // Achievements
                    recentAchievements
                }
                .padding()
            }
            .navigationTitle("Progress")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Details") {
                        showingDetailedView = true
                    }
                }
            }
            .sheet(isPresented: $showingDetailedView) {
                DetailedProgressView()
            }
        }
    }
    
    // MARK: - Timeframe Picker
    private var timeframePicker: some View {
        Picker("Timeframe", selection: $selectedTimeframe) {
            ForEach(ProgressTimeframe.allCases, id: \.self) { timeframe in
                Text(timeframe.displayName)
                    .tag(timeframe)
            }
        }
        .pickerStyle(SegmentedPickerStyle())
        .padding(.horizontal)
    }
    
    // MARK: - Metrics Overview
    private var metricsOverview: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Overview")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                MetricCard(
                    title: "Workouts",
                    value: "12",
                    change: "+3",
                    changeType: .positive,
                    icon: "figure.run"
                )
                
                MetricCard(
                    title: "Total Time",
                    value: "6h 45m",
                    change: "+1h 20m",
                    changeType: .positive,
                    icon: "clock.fill"
                )
                
                MetricCard(
                    title: "Avg Form Score",
                    value: "87%",
                    change: "+5%",
                    changeType: .positive,
                    icon: "star.fill"
                )
                
                MetricCard(
                    title: "Calories Burned",
                    value: "2,340",
                    change: "+420",
                    changeType: .positive,
                    icon: "flame.fill"
                )
            }
        }
    }
    
    // MARK: - Main Chart
    private var mainChart: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Trends")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Picker("Metric", selection: $selectedMetric) {
                    ForEach(ProgressMetric.allCases, id: \.self) { metric in
                        Text(metric.displayName)
                            .tag(metric)
                    }
                }
                .pickerStyle(MenuPickerStyle())
            }
            
            // Chart based on selected metric
            chartView
                .frame(height: 200)
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
        }
    }
    
    @ViewBuilder
    private var chartView: some View {
        switch selectedMetric {
        case .workouts:
            workoutChart
        case .duration:
            durationChart
        case .formScore:
            formScoreChart
        case .calories:
            caloriesChart
        case .strength:
            strengthChart
        case .consistency:
            consistencyChart
        }
    }
    
    private var workoutChart: some View {
        Chart(sampleWorkoutData) { data in
            BarMark(
                x: .value("Date", data.date),
                y: .value("Workouts", data.workouts)
            )
            .foregroundStyle(.blue)
        }
        .chartTitle("Daily Workouts")
    }
    
    private var durationChart: some View {
        Chart(sampleDurationData) { data in
            LineMark(
                x: .value("Date", data.date),
                y: .value("Duration", data.duration)
            )
            .foregroundStyle(.green)
            .symbol(Circle())
        }
        .chartTitle("Workout Duration (minutes)")
    }
    
    private var formScoreChart: some View {
        Chart(sampleFormScoreData) { data in
            AreaMark(
                x: .value("Date", data.date),
                y: .value("Score", data.score)
            )
            .foregroundStyle(.orange.opacity(0.3))
        }
        .chartTitle("Form Score (%)")
    }
    
    private var caloriesChart: some View {
        Chart(sampleCaloriesData) { data in
            BarMark(
                x: .value("Date", data.date),
                y: .value("Calories", data.calories)
            )
            .foregroundStyle(.red)
        }
        .chartTitle("Calories Burned")
    }
    
    private var strengthChart: some View {
        Chart(sampleStrengthData) { data in
            LineMark(
                x: .value("Date", data.date),
                y: .value("Strength", data.strength)
            )
            .foregroundStyle(.purple)
        }
        .chartTitle("Strength Progress")
    }
    
    private var consistencyChart: some View {
        Chart(sampleConsistencyData) { data in
            RectangleMark(
                x: .value("Date", data.date),
                y: .value("Consistency", data.isActive ? 1 : 0)
            )
            .foregroundStyle(data.isActive ? .green : .gray.opacity(0.3))
        }
        .chartTitle("Workout Consistency")
    }
    
    // MARK: - Progress Insights
    private var progressInsights: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Insights")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                InsightCard(
                    icon: "arrow.up.circle.fill",
                    title: "Great Progress!",
                    description: "Your form score improved by 12% this week",
                    color: .green
                )
                
                InsightCard(
                    icon: "target",
                    title: "Goal Achievement",
                    description: "You're 80% towards your weekly workout goal",
                    color: .blue
                )
                
                InsightCard(
                    icon: "calendar.badge.checkmark",
                    title: "Consistency Streak",
                    description: "5 days in a row! Keep it up!",
                    color: .orange
                )
            }
        }
    }
    
    // MARK: - Personal Records
    private var personalRecordsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Personal Records")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("View All") {
                    // Show all personal records
                }
                .font(.subheadline)
                .foregroundColor(.blue)
            }
            
            VStack(spacing: 8) {
                PersonalRecordRow(
                    exercise: "Push-ups",
                    record: "25 reps",
                    date: "2 days ago",
                    improvement: "+3"
                )
                
                PersonalRecordRow(
                    exercise: "Plank Hold",
                    record: "2m 30s",
                    date: "1 week ago",
                    improvement: "+15s"
                )
                
                PersonalRecordRow(
                    exercise: "Squats",
                    record: "30 reps",
                    date: "3 days ago",
                    improvement: "+5"
                )
            }
        }
    }
    
    // MARK: - Recent Achievements
    private var recentAchievements: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Recent Achievements")
                .font(.headline)
                .fontWeight(.semibold)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(0..<5) { index in
                        AchievementBadgeView(
                            title: "Week Warrior",
                            description: "Complete 5 workouts in a week",
                            icon: "star.fill",
                            color: .yellow,
                            isNew: index == 0
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
    }
}

// MARK: - Supporting Views

struct MetricCard: View {
    let title: String
    let value: String
    let change: String
    let changeType: ChangeType
    let icon: String
    
    enum ChangeType {
        case positive, negative, neutral
        
        var color: Color {
            switch self {
            case .positive: return .green
            case .negative: return .red
            case .neutral: return .gray
            }
        }
        
        var icon: String {
            switch self {
            case .positive: return "arrow.up"
            case .negative: return "arrow.down"
            case .neutral: return "minus"
            }
        }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.blue)
                Spacer()
                Image(systemName: changeType.icon)
                    .foregroundColor(changeType.color)
                    .font(.caption)
            }
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(change)
                .font(.caption)
                .foregroundColor(changeType.color)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
}

struct InsightCard: View {
    let icon: String
    let title: String
    let description: String
    let color: Color
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

struct PersonalRecordRow: View {
    let exercise: String
    let record: String
    let date: String
    let improvement: String
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(exercise)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(date)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 2) {
                Text(record)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text(improvement)
                    .font(.caption)
                    .foregroundColor(.green)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(8)
    }
}

struct AchievementBadgeView: View {
    let title: String
    let description: String
    let icon: String
    let color: Color
    let isNew: Bool
    
    var body: some View {
        VStack(spacing: 8) {
            ZStack {
                Circle()
                    .fill(color)
                    .frame(width: 50, height: 50)
                
                Image(systemName: icon)
                    .font(.system(size: 20))
                    .foregroundColor(.white)
                
                if isNew {
                    Circle()
                        .fill(.red)
                        .frame(width: 12, height: 12)
                        .offset(x: 20, y: -20)
                }
            }
            
            Text(title)
                .font(.caption)
                .fontWeight(.semibold)
                .multilineTextAlignment(.center)
        }
        .frame(width: 80)
    }
}

// MARK: - Sample Data
struct WorkoutData: Identifiable {
    let id = UUID()
    let date: Date
    let workouts: Int
}

struct DurationData: Identifiable {
    let id = UUID()
    let date: Date
    let duration: Double
}

struct FormScoreData: Identifiable {
    let id = UUID()
    let date: Date
    let score: Double
}

struct CaloriesData: Identifiable {
    let id = UUID()
    let date: Date
    let calories: Int
}

struct StrengthData: Identifiable {
    let id = UUID()
    let date: Date
    let strength: Double
}

struct ConsistencyData: Identifiable {
    let id = UUID()
    let date: Date
    let isActive: Bool
}

// Sample data arrays
private let sampleWorkoutData: [WorkoutData] = (0..<7).map { day in
    WorkoutData(
        date: Calendar.current.date(byAdding: .day, value: -day, to: Date()) ?? Date(),
        workouts: Int.random(in: 0...3)
    )
}

private let sampleDurationData: [DurationData] = (0..<7).map { day in
    DurationData(
        date: Calendar.current.date(byAdding: .day, value: -day, to: Date()) ?? Date(),
        duration: Double.random(in: 20...90)
    )
}

private let sampleFormScoreData: [FormScoreData] = (0..<7).map { day in
    FormScoreData(
        date: Calendar.current.date(byAdding: .day, value: -day, to: Date()) ?? Date(),
        score: Double.random(in: 70...95)
    )
}

private let sampleCaloriesData: [CaloriesData] = (0..<7).map { day in
    CaloriesData(
        date: Calendar.current.date(byAdding: .day, value: -day, to: Date()) ?? Date(),
        calories: Int.random(in: 150...500)
    )
}

private let sampleStrengthData: [StrengthData] = (0..<7).map { day in
    StrengthData(
        date: Calendar.current.date(byAdding: .day, value: -day, to: Date()) ?? Date(),
        strength: Double.random(in: 50...100)
    )
}

private let sampleConsistencyData: [ConsistencyData] = (0..<7).map { day in
    ConsistencyData(
        date: Calendar.current.date(byAdding: .day, value: -day, to: Date()) ?? Date(),
        isActive: Bool.random()
    )
}

// MARK: - Placeholder View
struct DetailedProgressView: View {
    var body: some View {
        NavigationView {
            Text("Detailed Progress View")
                .font(.title)
                .navigationTitle("Detailed Progress")
                .navigationBarTitleDisplayMode(.inline)
        }
    }
}

#Preview {
    ProgressViewTab()
}
