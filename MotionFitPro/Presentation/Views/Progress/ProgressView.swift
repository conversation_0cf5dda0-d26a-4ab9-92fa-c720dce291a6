import SwiftUI
import Charts

struct ProgressView: View {
    @StateObject private var coordinator = ProgressCoordinator()
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 24) {
                    // Header stats
                    ProgressHeaderStats(coordinator: coordinator)
                    
                    // Timeframe selector
                    TimeframeSelectorSection(coordinator: coordinator)
                    
                    // Main chart
                    ProgressChartSection(coordinator: coordinator)
                    
                    // Goal progress
                    GoalProgressSection(coordinator: coordinator)
                    
                    // Recent achievements
                    RecentAchievementsSection(coordinator: coordinator)
                    
                    // Personal records
                    PersonalRecordsSection(coordinator: coordinator)
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 100)
            }
            .scrollBounceBehavior(.basedOnSize)
            .navigationTitle("Progress")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .topBarTrailing) {
                    Menu {
                        Button("Share Progress", systemImage: "square.and.arrow.up") {
                            coordinator.shareProgress()
                        }
                        
                        Button("Refresh Data", systemImage: "arrow.clockwise") {
                            coordinator.refreshData()
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                    }
                }
            }
            .refreshable {
                coordinator.refreshData()
            }
        }
        .overlay {
            if coordinator.isLoading {
                ProgressView("Loading...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(.regularMaterial)
            }
        }
        .sheet(isPresented: $coordinator.showingShareSheet) {
            if let content = coordinator.shareableContent {
                ShareSheet(content: content)
            }
        }
        .alert("Error", isPresented: .constant(coordinator.errorMessage != nil)) {
            Button("OK") {
                coordinator.errorMessage = nil
            }
        } message: {
            Text(coordinator.errorMessage ?? "")
        }
    }
}

// MARK: - Header Stats

struct ProgressHeaderStats: View {
    @ObservedObject var coordinator: ProgressCoordinator
    
    var body: some View {
        VStack(spacing: 20) {
            HStack(spacing: 20) {
                HeaderStatCard(
                    icon: "figure.run",
                    title: "Total Workouts",
                    value: "\(coordinator.progressStats.totalWorkouts)",
                    color: .blue
                )
                
                HeaderStatCard(
                    icon: "clock.fill",
                    title: "Total Time",
                    value: formatDuration(coordinator.progressStats.totalDuration),
                    color: .green
                )
            }
            
            HStack(spacing: 20) {
                HeaderStatCard(
                    icon: "flame.fill",
                    title: "Calories Burned",
                    value: "\(coordinator.progressStats.totalCalories)",
                    color: .orange
                )
                
                HeaderStatCard(
                    icon: "star.fill",
                    title: "Avg Form Score",
                    value: "\(Int(coordinator.progressStats.averageFormScore))%",
                    color: .yellow
                )
            }
            
            // Streak indicator
            StreakCard(
                currentStreak: coordinator.progressStats.currentStreak,
                longestStreak: coordinator.progressStats.longestStreak
            )
        }
        .padding(.top, 8)
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration / 3600)
        let minutes = Int((duration.truncatingRemainder(dividingBy: 3600)) / 60)
        
        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }
}

struct HeaderStatCard: View {
    let icon: String
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 12) {
            ZStack {
                Circle()
                    .fill(color.opacity(0.2))
                    .frame(width: 50, height: 50)
                
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
            }
            
            VStack(spacing: 4) {
                Text(value)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
    }
}

struct StreakCard: View {
    let currentStreak: Int
    let longestStreak: Int
    
    var body: some View {
        HStack(spacing: 20) {
            VStack(spacing: 8) {
                HStack(spacing: 6) {
                    Image(systemName: "flame.fill")
                        .foregroundColor(.orange)
                    
                    Text("Current Streak")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                Text("\(currentStreak) days")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.orange)
            }
            
            Divider()
                .frame(height: 40)
            
            VStack(spacing: 8) {
                HStack(spacing: 6) {
                    Image(systemName: "trophy.fill")
                        .foregroundColor(.yellow)
                    
                    Text("Best Streak")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                Text("\(longestStreak) days")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.yellow)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .padding(.horizontal, 20)
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
    }
}

// MARK: - Timeframe Selector

struct TimeframeSelectorSection: View {
    @ObservedObject var coordinator: ProgressCoordinator
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Analytics")
                .font(.headline)
                .fontWeight(.semibold)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(ProgressCoordinator.ProgressTimeframe.allCases, id: \.self) { timeframe in
                        TimeframeChip(
                            timeframe: timeframe,
                            isSelected: coordinator.selectedTimeframe == timeframe
                        ) {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                coordinator.selectedTimeframe = timeframe
                            }
                        }
                    }
                }
                .padding(.horizontal, 20)
            }
            .padding(.horizontal, -20)
        }
    }
}

struct TimeframeChip: View {
    let timeframe: ProgressCoordinator.ProgressTimeframe
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(timeframe.rawValue)
                .font(.subheadline)
                .fontWeight(.medium)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    isSelected ? .blue : .clear,
                    in: Capsule()
                )
                .foregroundColor(isSelected ? .white : .primary)
                .overlay(
                    Capsule()
                        .stroke(isSelected ? .clear : .secondary, lineWidth: 1)
                )
        }
        .buttonStyle(.plain)
    }
}

// MARK: - Progress Chart

struct ProgressChartSection: View {
    @ObservedObject var coordinator: ProgressCoordinator
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Trends")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Menu {
                    ForEach(ProgressCoordinator.ProgressMetric.allCases, id: \.self) { metric in
                        Button {
                            coordinator.selectedMetric = metric
                        } label: {
                            HStack {
                                Image(systemName: metric.icon)
                                Text(metric.rawValue)
                                
                                if coordinator.selectedMetric == metric {
                                    Spacer()
                                    Image(systemName: "checkmark")
                                }
                            }
                        }
                    }
                } label: {
                    HStack(spacing: 6) {
                        Image(systemName: coordinator.selectedMetric.icon)
                        Text(coordinator.selectedMetric.rawValue)
                        Image(systemName: "chevron.down")
                            .font(.caption)
                    }
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(.regularMaterial, in: Capsule())
                }
            }
            
            // Chart
            ProgressChart(
                data: coordinator.getChartData(
                    for: coordinator.selectedTimeframe,
                    metric: coordinator.selectedMetric
                ),
                metric: coordinator.selectedMetric
            )
            .frame(height: 200)
            .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
        }
    }
}

struct ProgressChart: View {
    let data: [ChartDataPoint]
    let metric: ProgressCoordinator.ProgressMetric
    
    var body: some View {
        Chart(data) { point in
            LineMark(
                x: .value("Period", point.date),
                y: .value(metric.unit, point.value)
            )
            .foregroundStyle(.blue.gradient)
            .lineStyle(StrokeStyle(lineWidth: 3, lineCap: .round))
            
            AreaMark(
                x: .value("Period", point.date),
                y: .value(metric.unit, point.value)
            )
            .foregroundStyle(.blue.opacity(0.2))
        }
        .chartYAxis {
            AxisMarks(position: .leading) { value in
                AxisValueLabel()
                AxisGridLine()
            }
        }
        .chartXAxis {
            AxisMarks { value in
                AxisValueLabel(format: .dateTime.weekday(.abbreviated))
                AxisGridLine()
            }
        }
        .padding(16)
    }
}

// MARK: - Goal Progress

struct GoalProgressSection: View {
    @ObservedObject var coordinator: ProgressCoordinator
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Goals")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                ForEach(coordinator.getProgressTowardsGoals()) { goal in
                    GoalProgressCard(goal: goal)
                }
            }
        }
    }
}

struct GoalProgressCard: View {
    let goal: GoalProgress
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text(goal.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Spacer()
                
                Text("\(goal.current)/\(goal.target) \(goal.unit)")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            ProgressView(value: goal.progress)
                .progressViewStyle(LinearProgressViewStyle(tint: goal.color))
                .scaleEffect(y: 1.5)
            
            if goal.isComplete {
                HStack(spacing: 6) {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .font(.caption)
                    
                    Text("Goal achieved!")
                        .font(.caption)
                        .foregroundColor(.green)
                        .fontWeight(.medium)
                }
            }
        }
        .padding(16)
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
    }
}

// MARK: - Recent Achievements

struct RecentAchievementsSection: View {
    @ObservedObject var coordinator: ProgressCoordinator
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Recent Achievements")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("View All") {
                    // Navigate to full achievements view
                }
                .font(.subheadline)
                .foregroundColor(.blue)
            }
            
            let recentAchievements = coordinator.getRecentAchievements()
            
            if recentAchievements.isEmpty {
                EmptyAchievementsView()
            } else {
                VStack(spacing: 12) {
                    ForEach(recentAchievements.prefix(3)) { achievement in
                        AchievementCard(achievement: achievement)
                    }
                }
            }
        }
    }
}

struct AchievementCard: View {
    let achievement: Achievement
    
    var body: some View {
        HStack(spacing: 16) {
            ZStack {
                Circle()
                    .fill(achievement.color.opacity(0.2))
                    .frame(width: 50, height: 50)
                
                Image(systemName: achievement.icon)
                    .font(.title2)
                    .foregroundColor(achievement.color)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(achievement.title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text(achievement.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            if let date = achievement.unlockedDate {
                Text(formatAchievementDate(date))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(16)
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
    }
    
    private func formatAchievementDate(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .short
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}

struct EmptyAchievementsView: View {
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: "trophy")
                .font(.title)
                .foregroundColor(.secondary)
            
            Text("No recent achievements")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Text("Keep working out to unlock achievements!")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 32)
    }
}

// MARK: - Personal Records

struct PersonalRecordsSection: View {
    @ObservedObject var coordinator: ProgressCoordinator
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Personal Records")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("View All") {
                    // Navigate to full records view
                }
                .font(.subheadline)
                .foregroundColor(.blue)
            }
            
            VStack(spacing: 12) {
                ForEach(coordinator.personalRecords.prefix(3)) { record in
                    PersonalRecordCard(record: record)
                }
            }
        }
    }
}

struct PersonalRecordCard: View {
    let record: PersonalRecord
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: "medal.fill")
                .font(.title2)
                .foregroundColor(record.isNewRecord ? .yellow : .orange)
            
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(record.exercise)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    
                    if record.isNewRecord {
                        Text("NEW!")
                            .font(.caption)
                            .fontWeight(.bold)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(.yellow.opacity(0.2))
                            .foregroundColor(.yellow)
                            .clipShape(Capsule())
                    }
                }
                
                Text(record.record)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Text(formatRecordDate(record.date))
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(16)
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
    }
    
    private func formatRecordDate(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .short
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}

// MARK: - Share Sheet

struct ShareSheet: UIViewControllerRepresentable {
    let content: ShareableContent
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        let items: [Any] = [content.summary]
        return UIActivityViewController(activityItems: items, applicationActivities: nil)
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

#Preview("Progress View") {
    ProgressView()
}

#Preview("Progress Chart") {
    let sampleData = [
        ChartDataPoint(date: Date(), value: 3, label: 1),
        ChartDataPoint(date: Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date(), value: 2, label: 2),
        ChartDataPoint(date: Calendar.current.date(byAdding: .day, value: -2, to: Date()) ?? Date(), value: 4, label: 3),
        ChartDataPoint(date: Calendar.current.date(byAdding: .day, value: -3, to: Date()) ?? Date(), value: 1, label: 4),
        ChartDataPoint(date: Calendar.current.date(byAdding: .day, value: -4, to: Date()) ?? Date(), value: 3, label: 5)
    ]
    
    return ProgressChart(data: sampleData, metric: .workouts)
        .frame(height: 200)
        .padding()
}