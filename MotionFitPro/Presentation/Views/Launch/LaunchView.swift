import SwiftUI

struct LaunchView: View {
    @State private var animationProgress: Double = 0
    @State private var showingLogo = false
    @State private var showingText = false
    @State private var rotationAngle: Double = 0
    @State private var scale: Double = 0.5
    @State private var opacity: Double = 0
    
    var body: some View {
        ZStack {
            // Gradient background
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.blue.opacity(0.8),
                    Color.purple.opacity(0.6),
                    Color.indigo.opacity(0.9)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            // Animated background particles
            ForEach(0..<20, id: \.self) { index in
                Circle()
                    .fill(Color.white.opacity(0.1))
                    .frame(width: CGFloat.random(in: 4...12))
                    .position(
                        x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                        y: CGFloat.random(in: 0...UIScreen.main.bounds.height)
                    )
                    .animation(
                        .easeInOut(duration: Double.random(in: 2...4))
                        .repeatForever(autoreverses: true)
                        .delay(Double(index) * 0.1),
                        value: animationProgress
                    )
            }
            
            VStack(spacing: 30) {
                Spacer()
                
                // App Logo
                VStack(spacing: 20) {
                    ZStack {
                        // Outer ring
                        Circle()
                            .stroke(Color.white.opacity(0.3), lineWidth: 3)
                            .frame(width: 120, height: 120)
                            .rotationEffect(.degrees(rotationAngle))
                        
                        // Inner logo
                        Image(systemName: "figure.run.circle.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.white)
                            .scaleEffect(scale)
                            .opacity(opacity)
                    }
                    .opacity(showingLogo ? 1 : 0)
                    
                    // App name
                    VStack(spacing: 8) {
                        Text("MotionFitPro")
                            .font(.system(size: 32, weight: .bold, design: .rounded))
                            .foregroundColor(.white)
                            .opacity(showingText ? 1 : 0)
                        
                        Text("AI-Powered Motion Tracking")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white.opacity(0.8))
                            .opacity(showingText ? 1 : 0)
                    }
                }
                
                Spacer()
                
                // Loading indicator
                VStack(spacing: 16) {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.2)
                    
                    Text("Initializing...")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }
                .opacity(showingText ? 1 : 0)
                
                Spacer()
            }
            .padding()
        }
        .onAppear {
            startAnimations()
        }
    }
    
    private func startAnimations() {
        // Logo appearance
        withAnimation(.easeOut(duration: 0.8)) {
            showingLogo = true
            opacity = 1
            scale = 1
        }
        
        // Rotation animation
        withAnimation(.linear(duration: 3).repeatForever(autoreverses: false)) {
            rotationAngle = 360
        }
        
        // Text appearance
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            withAnimation(.easeOut(duration: 0.6)) {
                showingText = true
            }
        }
        
        // Background particles animation
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            withAnimation(.easeInOut(duration: 2)) {
                animationProgress = 1
            }
        }
    }
}

// MARK: - Logo Animation View
struct LogoAnimationView: View {
    let progress: Double
    @State private var innerRotation: Double = 0
    @State private var outerRotation: Double = 0
    
    var body: some View {
        ZStack {
            // Outer ring
            Circle()
                .stroke(
                    AngularGradient(
                        gradient: Gradient(colors: [.blue, .purple, .blue]),
                        center: .center
                    ),
                    lineWidth: 4
                )
                .frame(width: 100, height: 100)
                .rotationEffect(.degrees(outerRotation))
            
            // Middle ring
            Circle()
                .stroke(Color.white.opacity(0.5), lineWidth: 2)
                .frame(width: 80, height: 80)
                .rotationEffect(.degrees(-innerRotation))
            
            // Center icon
            Image(systemName: "figure.run")
                .font(.system(size: 30, weight: .bold))
                .foregroundColor(.white)
                .scaleEffect(0.8 + (progress * 0.2))
        }
        .onAppear {
            withAnimation(.linear(duration: 4).repeatForever(autoreverses: false)) {
                outerRotation = 360
            }
            withAnimation(.linear(duration: 3).repeatForever(autoreverses: false)) {
                innerRotation = 360
            }
        }
    }
}

// MARK: - Loading States
enum LaunchState {
    case initializing
    case loadingData
    case checkingPermissions
    case setupComplete
    case error(String)
    
    var message: String {
        switch self {
        case .initializing:
            return "Initializing MotionFitPro..."
        case .loadingData:
            return "Loading exercise data..."
        case .checkingPermissions:
            return "Checking permissions..."
        case .setupComplete:
            return "Setup complete!"
        case .error(let message):
            return "Error: \(message)"
        }
    }
    
    var showProgress: Bool {
        switch self {
        case .error, .setupComplete:
            return false
        default:
            return true
        }
    }
}

// MARK: - Enhanced Launch View with States
struct EnhancedLaunchView: View {
    @State private var currentState: LaunchState = .initializing
    @State private var progress: Double = 0
    @State private var animationProgress: Double = 0
    
    var body: some View {
        ZStack {
            // Background
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.blue.opacity(0.8),
                    Color.purple.opacity(0.6),
                    Color.indigo.opacity(0.9)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: 40) {
                Spacer()
                
                // Logo
                LogoAnimationView(progress: animationProgress)
                
                // App Title
                VStack(spacing: 8) {
                    Text("MotionFitPro")
                        .font(.system(size: 36, weight: .bold, design: .rounded))
                        .foregroundColor(.white)
                    
                    Text("AI-Powered Motion Tracking Workouts")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                }
                
                Spacer()
                
                // Loading Section
                VStack(spacing: 20) {
                    if currentState.showProgress {
                        ProgressView(value: progress, total: 1.0)
                            .progressViewStyle(LinearProgressViewStyle(tint: .white))
                            .frame(width: 200)
                    }
                    
                    Text(currentState.message)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white.opacity(0.9))
                        .multilineTextAlignment(.center)
                }
                
                Spacer()
            }
            .padding()
        }
        .onAppear {
            startLaunchSequence()
        }
    }
    
    private func startLaunchSequence() {
        // Start logo animation
        withAnimation(.easeOut(duration: 1)) {
            animationProgress = 1
        }
        
        // Simulate launch sequence
        Task {
            await performLaunchSteps()
        }
    }
    
    private func performLaunchSteps() async {
        // Step 1: Initialize
        await updateState(.initializing, progress: 0.2)
        try? await Task.sleep(nanoseconds: 800_000_000)
        
        // Step 2: Load data
        await updateState(.loadingData, progress: 0.5)
        try? await Task.sleep(nanoseconds: 1_000_000_000)
        
        // Step 3: Check permissions
        await updateState(.checkingPermissions, progress: 0.8)
        try? await Task.sleep(nanoseconds: 600_000_000)
        
        // Step 4: Complete
        await updateState(.setupComplete, progress: 1.0)
        try? await Task.sleep(nanoseconds: 500_000_000)
    }
    
    @MainActor
    private func updateState(_ state: LaunchState, progress: Double) {
        withAnimation(.easeInOut(duration: 0.3)) {
            currentState = state
            self.progress = progress
        }
    }
}

#Preview("Launch View") {
    LaunchView()
}

#Preview("Enhanced Launch View") {
    EnhancedLaunchView()
}
