import SwiftUI
import ARKit

// MARK: - Workout View

/// Main workout interface with AR tracking and real-time feedback
struct WorkoutView: View {
    
    @StateObject private var workoutManager = WorkoutSessionManager()
    @EnvironmentObject private var arSessionManager: ARSessionManager
    
    @State private var showingExerciseSelection = false
    @State private var selectedExercises: [ExerciseType] = [.squat] // Default to squats
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        ZStack {
            // AR Camera View
            ARCameraView()
                .ignoresSafeArea()
            
            // Overlay UI
            VStack {
                // Top Status Bar with Close Button
                HStack {
                    Button("Close") {
                        dismiss()
                    }
                    .foregroundColor(.white)
                    .padding()
                    
                    Spacer()
                }
                
                topStatusBar
                
                Spacer()
                
                // Real-time Feedback
                feedbackOverlay
                
                Spacer()
                
                // Bottom Controls
                bottomControls
            }
            .padding()
        }
        .sheet(isPresented: $showingExerciseSelection) {
            ExerciseSelectionView(selectedExercises: $selectedExercises)
        }
    }
    
    // MARK: - Top Status Bar
    
    private var topStatusBar: some View {
        HStack {
            // Session Info
            VStack(alignment: .leading, spacing: 4) {
                Text("Workout Session")
                    .font(.headline)
                    .foregroundColor(.white)
                
                if workoutManager.sessionState == .active {
                    Text(formatDuration(workoutManager.sessionStats.duration))
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.8))
                }
            }
            
            Spacer()
            
            // Current Exercise
            if workoutManager.currentExercise != .unknown {
                VStack(alignment: .trailing, spacing: 4) {
                    Text(workoutManager.currentExercise.displayName)
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    Text("Reps: \(workoutManager.repCount)")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.8))
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
    
    // MARK: - Feedback Overlay
    
    private var feedbackOverlay: some View {
        VStack(spacing: 12) {
            // Form Score
            if workoutManager.sessionState == .active {
                FormScoreView(score: workoutManager.currentFormScore)
            }
            
            // Real-time Feedback
            ForEach(workoutManager.realtimeFeedback.prefix(3), id: \.id) { feedback in
                FeedbackCard(feedback: feedback)
                    .transition(.asymmetric(
                        insertion: .move(edge: .trailing).combined(with: .opacity),
                        removal: .move(edge: .leading).combined(with: .opacity)
                    ))
            }
        }
        .animation(.easeInOut(duration: 0.3), value: workoutManager.realtimeFeedback)
    }
    
    // MARK: - Bottom Controls
    
    private var bottomControls: some View {
        HStack(spacing: 20) {
            // Session Control Button
            sessionControlButton
            
            Spacer()
            
            // Exercise Selection
            if workoutManager.sessionState == .notStarted {
                Button("Select Exercises") {
                    showingExerciseSelection = true
                }
                .buttonStyle(.borderedProminent)
            }
            
            // Stats Button
            if workoutManager.sessionState != .notStarted {
                Button("Stats") {
                    // Show stats view
                }
                .buttonStyle(.bordered)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var sessionControlButton: some View {
        Button(action: sessionControlAction) {
            HStack {
                Image(systemName: sessionControlIcon)
                Text(sessionControlText)
            }
            .font(.headline)
            .foregroundColor(.white)
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
        }
        .background(sessionControlColor)
        .cornerRadius(25)
    }
    
    // MARK: - Session Control Logic
    
    private var sessionControlIcon: String {
        switch workoutManager.sessionState {
        case .notStarted: return "play.fill"
        case .active: return "pause.fill"
        case .paused: return "play.fill"
        case .completed: return "checkmark.circle.fill"
        }
    }
    
    private var sessionControlText: String {
        switch workoutManager.sessionState {
        case .notStarted: return "Start Workout"
        case .active: return "Pause"
        case .paused: return "Resume"
        case .completed: return "Completed"
        }
    }
    
    private var sessionControlColor: Color {
        switch workoutManager.sessionState {
        case .notStarted: return .green
        case .active: return .orange
        case .paused: return .green
        case .completed: return .blue
        }
    }
    
    private func sessionControlAction() {
        Task {
            do {
                switch workoutManager.sessionState {
                case .notStarted:
                    try await workoutManager.startSession(exercises: selectedExercises)
                case .active:
                    workoutManager.pauseSession()
                case .paused:
                    workoutManager.resumeSession()
                case .completed:
                    break
                }
            } catch {
                print("Error managing workout session: \(error)")
            }
        }
    }
    
    // MARK: - Utility Methods
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
}

// MARK: - Form Score View

struct FormScoreView: View {
    let score: Float
    
    var body: some View {
        VStack(spacing: 8) {
            Text("Form Score")
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
            
            ZStack {
                Circle()
                    .stroke(Color.white.opacity(0.3), lineWidth: 8)
                    .frame(width: 80, height: 80)
                
                Circle()
                    .trim(from: 0, to: CGFloat(score))
                    .stroke(scoreColor, style: StrokeStyle(lineWidth: 8, lineCap: .round))
                    .frame(width: 80, height: 80)
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut(duration: 0.5), value: score)
                
                Text("\(Int(score * 100))%")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var scoreColor: Color {
        switch score {
        case 0.8...1.0: return .green
        case 0.6..<0.8: return .yellow
        case 0.4..<0.6: return .orange
        default: return .red
        }
    }
}

// MARK: - Feedback Card

struct FeedbackCard: View {
    let feedback: FormFeedback
    
    var body: some View {
        HStack {
            Image(systemName: feedback.type.iconName)
                .foregroundColor(feedback.severity.color)
                .font(.title2)
            
            VStack(alignment: .leading, spacing: 2) {
                if let title = feedback.title {
                    Text(title)
                        .font(.headline)
                        .foregroundColor(.white)
                }
                
                Text(feedback.message)
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.9))
            }
            
            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(feedback.severity.color.opacity(0.5), lineWidth: 2)
                )
        )
    }
}

// MARK: - AR Camera View

struct ARCameraView: UIViewRepresentable {
    
    func makeUIView(context: Context) -> ARSCNView {
        let arView = ARSCNView()
        
        // Configure AR view
        arView.automaticallyUpdatesLighting = true
        arView.antialiasingMode = .multisampling4X
        
        return arView
    }
    
    func updateUIView(_ uiView: ARSCNView, context: Context) {
        // Updates handled by ARTrackingService
    }
}

// MARK: - Exercise Selection View

struct ExerciseSelectionView: View {
    @Binding var selectedExercises: [ExerciseType]
    @Environment(\.dismiss) private var dismiss
    
    private let availableExercises: [ExerciseType] = [
        .squat, .pushUp, .lunge, .plank, .burpee, .jumpingJacks
    ]
    
    var body: some View {
        NavigationView {
            List {
                ForEach(availableExercises, id: \.self) { exercise in
                    ExerciseSelectionRow(
                        exercise: exercise,
                        isSelected: selectedExercises.contains(exercise)
                    ) { isSelected in
                        if isSelected {
                            selectedExercises.append(exercise)
                        } else {
                            selectedExercises.removeAll { $0 == exercise }
                        }
                    }
                }
            }
            .navigationTitle("Select Exercises")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Exercise Selection Row

struct ExerciseSelectionRow: View {
    let exercise: ExerciseType
    let isSelected: Bool
    let onToggle: (Bool) -> Void
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(exercise.displayName)
                    .font(.headline)
                
                Text(exercise.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Toggle("", isOn: .init(
                get: { isSelected },
                set: onToggle
            ))
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Preview

#Preview {
    WorkoutView()
        .withDependencyInjection()
}
