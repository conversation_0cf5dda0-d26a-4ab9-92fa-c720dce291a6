//
//  ProgramWorkoutView.swift
//  MotionFitPro
//
//  Created by <PERSON> on 2025-07-15.
//

import SwiftUI
import ARKit

struct ProgramWorkoutView: View {
    let program: WorkoutProgram
    let selectedCoach: CoachPersonality
    
    @StateObject private var workoutManager = WorkoutSessionManager()
    @StateObject private var arManager = ARSessionManager.shared
    @StateObject private var aiCoach = AICoachingSystem()
    @StateObject private var achievementSystem = AchievementSystem()
    
    @Environment(\.dismiss) private var dismiss
    
    @State private var currentExerciseIndex = 0
    @State private var showingPauseMenu = false
    @State private var showingCompletionSummary = false
    @State private var sessionStartTime = Date()
    @State private var elapsedTime: TimeInterval = 0
    @State private var timer: Timer?
    
    // Animation states
    @State private var repAnimationScale: CGFloat = 1.0
    @State private var showRepBurst = false
    @State private var lastRepCount = 0
    @State private var showAchievementPopup = false
    @State private var newAchievement: Achievement?
    
    // AI Coaching states
    @State private var currentFeedback: AIFeedback?
    @State private var adaptiveDifficulty: AdaptiveDifficulty = .analyzing
    
    var currentExercise: ProgramExercise {
        program.exercises[min(currentExerciseIndex, program.exercises.count - 1)]
    }
    
    var isLastExercise: Bool {
        currentExerciseIndex >= program.exercises.count - 1
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // AR Camera View
                ARCameraView()
                    .ignoresSafeArea()
                
                // Premium Workout Overlay
                VStack {
                    // Top HUD
                    TopHUD()
                        .padding(.top, 10)
                    
                    Spacer()
                    
                    // Center: AI Coaching Display
                    AICoachingDisplay()
                        .padding(.horizontal, 20)
                    
                    Spacer()
                    
                    // Bottom: Exercise Info & Controls
                    BottomControls()
                        .padding(.bottom, 20)
                }
            }
            .navigationBarHidden(true)
        }
        .onAppear {
            setupWorkoutSession()
        }
        .onDisappear {
            cleanup()
        }
        .sheet(isPresented: $showingPauseMenu) {
            PauseMenuView(
                onResume: resumeWorkout,
                onEndWorkout: endWorkout
            )
        }
        .sheet(isPresented: $showingCompletionSummary) {
            WorkoutCompletionView(
                program: program,
                duration: elapsedTime,
                totalReps: workoutManager.repCount,
                averageFormScore: workoutManager.currentFormScore
            ) {
                dismiss()
            }
        }
        .overlay(
            // Achievement Popup
            Group {
                if showAchievementPopup, let achievement = newAchievement {
                    AchievementPopupView(achievement: achievement) {
                        showAchievementPopup = false
                    }
                    .transition(.scale.combined(with: .opacity))
                    .animation(.spring(response: 0.6, dampingFraction: 0.7), value: showAchievementPopup)
                }
            }
        )
        .overlay(
            // Rep Burst Animation
            Group {
                if showRepBurst {
                    RepBurstView()
                        .transition(.scale.combined(with: .opacity))
                        .animation(.spring(response: 0.5, dampingFraction: 0.7), value: showRepBurst)
                }
            }
        )
    }
    
    // MARK: - Top HUD
    
    @ViewBuilder
    private func TopHUD() -> some View {
        HStack {
            // Program Info
            VStack(alignment: .leading, spacing: 4) {
                Text(program.name)
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Text("Exercise \(currentExerciseIndex + 1) of \(program.exercises.count)")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(.ultraThinMaterial)
            .cornerRadius(20)
            
            Spacer()
            
            // Status Indicators
            HStack(spacing: 12) {
                // Timer
                VStack(spacing: 2) {
                    Text(formatTime(elapsedTime))
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text("TIME")
                        .font(.caption2)
                        .foregroundColor(.white.opacity(0.7))
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(.ultraThinMaterial)
                .cornerRadius(12)
                
                // AR Status
                Circle()
                    .fill(arManager.isBodyDetected ? .green : .red)
                    .frame(width: 12, height: 12)
                    .padding(8)
                    .background(.ultraThinMaterial)
                    .cornerRadius(16)
                
                // Pause Button
                Button(action: pauseWorkout) {
                    Image(systemName: "pause.fill")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                        .padding(8)
                        .background(.ultraThinMaterial)
                        .cornerRadius(16)
                }
            }
        }
        .padding(.horizontal, 20)
    }
    
    // MARK: - AI Coaching Display
    
    @ViewBuilder
    private func AICoachingDisplay() -> some View {
        VStack(spacing: 16) {
            // Current Exercise Display
            ExerciseDisplayCard()
            
            // AI Feedback
            if let feedback = currentFeedback {
                AIFeedbackCard(feedback: feedback)
            }
            
            // Adaptive Difficulty Indicator
            AdaptiveDifficultyIndicator()
        }
    }
    
    @ViewBuilder
    private func ExerciseDisplayCard() -> some View {
        HStack(spacing: 16) {
            // Exercise Animation
            ZStack {
                Circle()
                    .fill(.ultraThinMaterial)
                    .frame(width: 80, height: 80)
                
                Exercise3DAnimation(
                    exerciseType: currentExercise.exerciseType,
                    isAnimating: workoutManager.sessionState == .active
                )
            }
            
            VStack(alignment: .leading, spacing: 8) {
                Text(currentExercise.exerciseType.displayName)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                if let duration = currentExercise.formattedDuration {
                    Text("Duration: \(duration)")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.8))
                } else if let reps = currentExercise.targetReps {
                    HStack {
                        Text("Target: \(reps) reps")
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.8))
                        
                        Spacer()
                        
                        Text("Current: \(workoutManager.repCount)")
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .scaleEffect(repAnimationScale)
                            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: repAnimationScale)
                    }
                }
                
                // Form Score Bar
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text("Form Score")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.7))
                        
                        Spacer()
                        
                        Text("\(Int(workoutManager.currentFormScore * 100))%")
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(formScoreColor)
                    }
                    
                    ProgressView(value: workoutManager.currentFormScore)
                        .progressViewStyle(LinearProgressViewStyle(tint: formScoreColor))
                        .scaleEffect(x: 1, y: 2, anchor: .center)
                }
            }
            
            Spacer()
        }
        .padding(20)
        .background(.ultraThinMaterial)
        .cornerRadius(16)
    }
    
    @ViewBuilder
    private func AIFeedbackCard(feedback: AIFeedback) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            // Coach Avatar & Name
            HStack {
                Text(selectedCoach.emoji)
                    .font(.title2)
                
                Text(selectedCoach.displayName)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Spacer()
                
                // Priority Indicator
                Circle()
                    .fill(Color(hex: feedback.correctionPriority.color))
                    .frame(width: 12, height: 12)
            }
            
            // Primary Message
            Text(feedback.primaryMessage)
                .font(.body)
                .fontWeight(.medium)
                .foregroundColor(.white)
            
            // Encouragement
            Text(feedback.encouragement)
                .font(.subheadline)
                .fontStyle(.italic)
                .foregroundColor(.white.opacity(0.8))
            
            // Secondary Tips
            if !feedback.secondaryTips.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    ForEach(feedback.secondaryTips, id: \.self) { tip in
                        HStack {
                            Image(systemName: "lightbulb.fill")
                                .font(.caption)
                                .foregroundColor(.yellow)
                            
                            Text(tip)
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.9))
                        }
                    }
                }
            }
            
            // Injury Warnings
            if !feedback.injuryWarnings.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    ForEach(feedback.injuryWarnings, id: \.message) { warning in
                        HStack {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .font(.caption)
                                .foregroundColor(Color(hex: warning.severity.color))
                            
                            Text(warning.message)
                                .font(.caption)
                                .foregroundColor(.white)
                        }
                    }
                }
            }
        }
        .padding(16)
        .background(.ultraThinMaterial)
        .cornerRadius(12)
    }
    
    @ViewBuilder
    private func AdaptiveDifficultyIndicator() -> some View {
        HStack {
            Image(systemName: "brain.head.profile")
                .font(.caption)
                .foregroundColor(.blue)
            
            Text("AI: \(adaptiveDifficulty.description)")
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(.ultraThinMaterial)
        .cornerRadius(8)
    }
    
    // MARK: - Bottom Controls
    
    @ViewBuilder
    private func BottomControls() -> some View {
        HStack(spacing: 16) {
            // Previous Exercise
            Button(action: previousExercise) {
                Image(systemName: "backward.fill")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                    .padding(16)
                    .background(.ultraThinMaterial)
                    .cornerRadius(25)
            }
            .disabled(currentExerciseIndex == 0)
            .opacity(currentExerciseIndex == 0 ? 0.5 : 1.0)
            
            Spacer()
            
            // Main Action Button
            Button(action: mainAction) {
                HStack(spacing: 8) {
                    Image(systemName: mainActionIcon)
                        .font(.system(size: 16, weight: .semibold))
                    
                    Text(mainActionText)
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 16)
                .background(mainActionColor)
                .cornerRadius(25)
            }
            
            Spacer()
            
            // Next Exercise / Complete
            Button(action: nextExercise) {
                HStack(spacing: 4) {
                    Text(isLastExercise ? "Complete" : "Next")
                        .font(.system(size: 14, weight: .semibold))
                    
                    Image(systemName: isLastExercise ? "checkmark" : "forward.fill")
                        .font(.system(size: 14, weight: .semibold))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(.ultraThinMaterial)
                .cornerRadius(20)
            }
        }
        .padding(.horizontal, 20)
    }
    
    // MARK: - Computed Properties
    
    private var formScoreColor: Color {
        let score = workoutManager.currentFormScore
        if score > 0.8 { return .green }
        if score > 0.6 { return .yellow }
        if score > 0.4 { return .orange }
        return .red
    }
    
    private var mainActionText: String {
        switch workoutManager.sessionState {
        case .notStarted: return "Start Exercise"
        case .active: return "Pause"
        case .paused: return "Resume"
        case .completed: return "Completed"
        }
    }
    
    private var mainActionIcon: String {
        switch workoutManager.sessionState {
        case .notStarted: return "play.fill"
        case .active: return "pause.fill"
        case .paused: return "play.fill"
        case .completed: return "checkmark.circle.fill"
        }
    }
    
    private var mainActionColor: Color {
        switch workoutManager.sessionState {
        case .notStarted: return .green
        case .active: return .orange
        case .paused: return .blue
        case .completed: return .green
        }
    }
    
    // MARK: - Actions
    
    private func setupWorkoutSession() {
        aiCoach.setCoachPersonality(selectedCoach)
        sessionStartTime = Date()
        startTimer()
    }
    
    private func startTimer() {
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            Task { @MainActor in
                elapsedTime = Date().timeIntervalSince(sessionStartTime)
            }
        }
    }
    
    private func stopTimer() {
        timer?.invalidate()
        timer = nil
    }
    
    private func cleanup() {
        stopTimer()
    }
    
    private func mainAction() {
        switch workoutManager.sessionState {
        case .notStarted:
            startCurrentExercise()
        case .active:
            pauseWorkout()
        case .paused:
            resumeWorkout()
        case .completed:
            break
        }
    }
    
    private func startCurrentExercise() {
        Task {
            do {
                try await workoutManager.startSession(exercises: [currentExercise.exerciseType])
            } catch {
                print("Failed to start exercise: \(error)")
            }
        }
    }
    
    private func pauseWorkout() {
        workoutManager.pauseSession()
        showingPauseMenu = true
        stopTimer()
    }
    
    private func resumeWorkout() {
        workoutManager.resumeSession()
        startTimer()
    }
    
    private func endWorkout() {
        Task {
            await workoutManager.stopSession()
            stopTimer()
            showingCompletionSummary = true
        }
    }
    
    private func previousExercise() {
        if currentExerciseIndex > 0 {
            currentExerciseIndex -= 1
            resetCurrentExercise()
        }
    }
    
    private func nextExercise() {
        if isLastExercise {
            completeWorkout()
        } else {
            currentExerciseIndex += 1
            resetCurrentExercise()
        }
    }
    
    private func resetCurrentExercise() {
        Task {
            await workoutManager.stopSession()
            // Small delay before starting next exercise
            try? await Task.sleep(nanoseconds: 500_000_000)
            try? await workoutManager.startSession(exercises: [currentExercise.exerciseType])
        }
    }
    
    private func completeWorkout() {
        Task {
            await workoutManager.stopSession()
            
            // Process achievements
            let sessionStats = SessionStats(
                totalReps: workoutManager.repCount,
                averageFormScore: workoutManager.currentFormScore,
                estimatedCalories: Int(elapsedTime / 60 * 10), // Rough calculation
                repsPerExercise: [currentExercise.exerciseType: workoutManager.repCount],
                perfectFormReps: Int(Float(workoutManager.repCount) * workoutManager.currentFormScore)
            )
            
            let workoutSession = WorkoutSession(
                date: sessionStartTime,
                duration: elapsedTime,
                exercises: [currentExercise.exerciseType],
                program: program.name
            )
            
            achievementSystem.processWorkoutCompletion(workoutSession, stats: sessionStats)
            
            showingCompletionSummary = true
        }
    }
    
    private func formatTime(_ time: TimeInterval) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
}

// MARK: - Simple AR Camera View

struct ARCameraView: UIViewRepresentable {
    func makeUIView(context: Context) -> ARSCNView {
        let arView = ARSCNView()
        arView.automaticallyUpdatesLighting = true
        arView.antialiasingMode = .multisampling4X
        arView.session = ARSessionManager.shared.session
        
        #if targetEnvironment(simulator)
        arView.backgroundColor = .black
        #endif
        
        return arView
    }
    
    func updateUIView(_ uiView: ARSCNView, context: Context) {
        // Updates handled by ARSessionManager
    }
}

// MARK: - Achievement Popup View

struct AchievementPopupView: View {
    let achievement: Achievement
    let onDismiss: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            Text("🎉 Achievement Unlocked! 🎉")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.yellow)
            
            ZStack {
                achievement.gradient
                    .frame(width: 60, height: 60)
                    .cornerRadius(30)
                
                Image(systemName: achievement.icon)
                    .font(.system(size: 24, weight: .semibold))
                    .foregroundColor(.white)
            }
            
            VStack(spacing: 4) {
                Text(achievement.title)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Text(achievement.description)
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
            }
            
            Text("+\(achievement.points) points")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.yellow)
        }
        .padding(24)
        .background(.ultraThinMaterial)
        .cornerRadius(20)
        .overlay(
            RoundedRectangle(cornerRadius: 20)
                .stroke(.yellow.opacity(0.3), lineWidth: 2)
        )
        .onTapGesture {
            onDismiss()
        }
        .onAppear {
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                onDismiss()
            }
        }
    }
}

// MARK: - Pause Menu View

struct PauseMenuView: View {
    let onResume: () -> Void
    let onEndWorkout: () -> Void
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.black.opacity(0.9)
                    .ignoresSafeArea()
                
                VStack(spacing: 32) {
                    VStack(spacing: 8) {
                        Text("Workout Paused")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                        
                        Text("Take a moment to rest")
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.7))
                    }
                    
                    VStack(spacing: 16) {
                        Button(action: {
                            dismiss()
                            onResume()
                        }) {
                            HStack {
                                Image(systemName: "play.fill")
                                Text("Resume Workout")
                            }
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .padding()
                            .frame(maxWidth: .infinity)
                            .background(.green)
                            .cornerRadius(12)
                        }
                        
                        Button(action: {
                            dismiss()
                            onEndWorkout()
                        }) {
                            HStack {
                                Image(systemName: "stop.fill")
                                Text("End Workout")
                            }
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .padding()
                            .frame(maxWidth: .infinity)
                            .background(.red)
                            .cornerRadius(12)
                        }
                    }
                    .padding(.horizontal, 40)
                }
            }
            .navigationBarHidden(true)
        }
    }
}

// MARK: - Workout Completion View

struct WorkoutCompletionView: View {
    let program: WorkoutProgram
    let duration: TimeInterval
    let totalReps: Int
    let averageFormScore: Float
    let onDismiss: () -> Void
    
    var body: some View {
        NavigationView {
            ZStack {
                program.color.gradient
                    .ignoresSafeArea()
                
                VStack(spacing: 32) {
                    VStack(spacing: 16) {
                        Text("🎉")
                            .font(.system(size: 60))
                        
                        Text("Workout Complete!")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                        
                        Text(program.name)
                            .font(.title2)
                            .foregroundColor(.white.opacity(0.9))
                    }
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                        CompletionStatCard(
                            title: "Duration",
                            value: formatDuration(duration),
                            icon: "clock.fill",
                            color: .blue
                        )
                        
                        CompletionStatCard(
                            title: "Total Reps",
                            value: "\(totalReps)",
                            icon: "number.circle.fill",
                            color: .green
                        )
                        
                        CompletionStatCard(
                            title: "Form Score",
                            value: "\(Int(averageFormScore * 100))%",
                            icon: "target",
                            color: .orange
                        )
                        
                        CompletionStatCard(
                            title: "Calories",
                            value: "\(program.estimatedCalories)",
                            icon: "flame.fill",
                            color: .red
                        )
                    }
                    .padding(.horizontal, 20)
                    
                    Button(action: onDismiss) {
                        Text("Done")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .padding()
                            .frame(maxWidth: .infinity)
                            .background(.black.opacity(0.3))
                            .cornerRadius(12)
                    }
                    .padding(.horizontal, 40)
                }
            }
            .navigationBarHidden(true)
        }
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
}

// MARK: - Completion Stat Card

struct CompletionStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 24, weight: .semibold))
                .foregroundColor(color)
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.white.opacity(0.7))
        }
        .padding()
        .background(.white.opacity(0.1))
        .cornerRadius(12)
    }
}

#Preview {
    ProgramWorkoutView(
        program: WorkoutProgram.morningBlast,
        selectedCoach: .motivational
    )
}