import SwiftUI
import ARKit
import RealityKit

struct ARViewContainer: UIViewRepresentable {
    @EnvironmentObject private var arSessionManager: ARSessionManager
    @State private var arView: ARView?
    
    func makeUIView(context: Context) -> ARView {
        let arView = ARView(frame: .zero)
        
        // Configure AR view
        arView.automaticallyConfigureSession = false
        arView.renderOptions.insert(.disablePersonOcclusion)
        arView.renderOptions.insert(.disableMotionBlur)
        
        // Set up session delegate
        arView.session.delegate = context.coordinator
        
        // Store reference
        self.arView = arView
        
        return arView
    }
    
    func updateUIView(_ uiView: ARView, context: Context) {
        // Update AR view based on session state
        if arSessionManager.sessionState == .running && uiView.session.configuration == nil {
            startARSession(in: uiView)
        } else if arSessionManager.sessionState == .stopped {
            uiView.session.pause()
        }
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    private func startARSession(in arView: ARView) {
        guard ARBodyTrackingConfiguration.isSupported else {
            print("AR Body Tracking not supported on this device")
            return
        }
        
        let configuration = ARBodyTrackingConfiguration()
        configuration.frameSemantics = .bodyDetection
        configuration.isAutoFocusEnabled = true
        
        arView.session.run(configuration, options: [.resetTracking, .removeExistingAnchors])
    }
    
    class Coordinator: NSObject, ARSessionDelegate {
        let parent: ARViewContainer
        
        init(_ parent: ARViewContainer) {
            self.parent = parent
        }
        
        func session(_ session: ARSession, didUpdate frame: ARFrame) {
            // Forward frame updates to AR session manager
            parent.arSessionManager.processFrame(frame)
        }
        
        func session(_ session: ARSession, didAdd anchors: [ARAnchor]) {
            for anchor in anchors {
                if let bodyAnchor = anchor as? ARBodyAnchor {
                    parent.arSessionManager.processBodyAnchor(bodyAnchor)
                }
            }
        }
        
        func session(_ session: ARSession, didUpdate anchors: [ARAnchor]) {
            for anchor in anchors {
                if let bodyAnchor = anchor as? ARBodyAnchor {
                    parent.arSessionManager.processBodyAnchor(bodyAnchor)
                }
            }
        }
        
        func session(_ session: ARSession, didFailWithError error: Error) {
            print("AR Session failed: \(error)")
            parent.arSessionManager.handleSessionError(error)
        }
        
        func sessionWasInterrupted(_ session: ARSession) {
            parent.arSessionManager.handleSessionInterruption()
        }
        
        func sessionInterruptionEnded(_ session: ARSession) {
            parent.arSessionManager.handleSessionInterruptionEnded()
        }
    }
}

// MARK: - Skeleton Overlay View
struct SkeletonOverlayView: View {
    let poseData: BodyPoseData
    @State private var animationPhase: Double = 0
    
    var body: some View {
        Canvas { context, size in
            drawSkeleton(context: context, size: size, poseData: poseData)
        }
        .onAppear {
            withAnimation(.linear(duration: 2).repeatForever(autoreverses: false)) {
                animationPhase = 1
            }
        }
    }
    
    private func drawSkeleton(context: GraphicsContext, size: CGSize, poseData: BodyPoseData) {
        let joints = poseData.joints
        
        // Draw skeleton connections
        drawSkeletonConnections(context: context, size: size, joints: joints)
        
        // Draw joints
        drawJoints(context: context, size: size, joints: joints)
        
        // Draw confidence indicators
        drawConfidenceIndicators(context: context, size: size, poseData: poseData)
    }
    
    private func drawSkeletonConnections(context: GraphicsContext, size: CGSize, joints: [JointName: Joint3D]) {
        let connections: [(JointName, JointName)] = [
            // Spine
            (.root, .spine1),
            (.spine1, .spine2),
            (.spine2, .spine3),
            (.spine3, .spine4),
            (.spine4, .spine5),
            (.spine5, .spine6),
            (.spine6, .spine7),
            (.spine7, .neck),
            (.neck, .head),
            
            // Left arm
            (.spine6, .leftShoulder),
            (.leftShoulder, .leftUpperArm),
            (.leftUpperArm, .leftLowerArm),
            (.leftLowerArm, .leftHand),
            
            // Right arm
            (.spine6, .rightShoulder),
            (.rightShoulder, .rightUpperArm),
            (.rightUpperArm, .rightLowerArm),
            (.rightLowerArm, .rightHand),
            
            // Left leg
            (.root, .leftUpperLeg),
            (.leftUpperLeg, .leftLowerLeg),
            (.leftLowerLeg, .leftFoot),
            (.leftFoot, .leftToes),
            
            // Right leg
            (.root, .rightUpperLeg),
            (.rightUpperLeg, .rightLowerLeg),
            (.rightLowerLeg, .rightFoot),
            (.rightFoot, .rightToes)
        ]
        
        for (startJoint, endJoint) in connections {
            guard let start = joints[startJoint],
                  let end = joints[endJoint],
                  start.isTracked && end.isTracked else { continue }
            
            let startPoint = projectToScreen(start.position, size: size)
            let endPoint = projectToScreen(end.position, size: size)
            
            let confidence = min(start.confidence, end.confidence)
            let color = Color.blue.opacity(Double(confidence))
            
            context.stroke(
                Path { path in
                    path.move(to: startPoint)
                    path.addLine(to: endPoint)
                },
                with: .color(color),
                lineWidth: 3
            )
        }
    }
    
    private func drawJoints(context: GraphicsContext, size: CGSize, joints: [JointName: Joint3D]) {
        for (jointName, joint) in joints {
            guard joint.isTracked else { continue }
            
            let point = projectToScreen(joint.position, size: size)
            let radius = jointRadius(for: jointName)
            let color = jointColor(for: jointName, confidence: joint.confidence)
            
            context.fill(
                Path { path in
                    path.addEllipse(in: CGRect(
                        x: point.x - radius,
                        y: point.y - radius,
                        width: radius * 2,
                        height: radius * 2
                    ))
                },
                with: .color(color)
            )
            
            // Add pulsing effect for low confidence joints
            if joint.confidence < 0.7 {
                let pulseRadius = radius * (1 + sin(animationPhase * .pi * 4) * 0.3)
                context.stroke(
                    Path { path in
                        path.addEllipse(in: CGRect(
                            x: point.x - pulseRadius,
                            y: point.y - pulseRadius,
                            width: pulseRadius * 2,
                            height: pulseRadius * 2
                        ))
                    },
                    with: .color(.red.opacity(0.5)),
                    lineWidth: 2
                )
            }
        }
    }
    
    private func drawConfidenceIndicators(context: GraphicsContext, size: CGSize, poseData: BodyPoseData) {
        // Overall confidence indicator
        let confidenceText = String(format: "%.0f%%", poseData.confidence * 100)
        let confidenceColor = confidenceColor(for: poseData.confidence)
        
        context.draw(
            Text(confidenceText)
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(confidenceColor),
            at: CGPoint(x: size.width - 50, y: 30)
        )
        
        // Tracking quality indicator
        let qualityColor = trackingQualityColor(for: poseData.trackingQuality)
        context.fill(
            Path { path in
                path.addEllipse(in: CGRect(x: size.width - 80, y: 20, width: 20, height: 20))
            },
            with: .color(qualityColor)
        )
    }
    
    // MARK: - Helper Functions
    
    private func projectToScreen(_ position: simd_float3, size: CGSize) -> CGPoint {
        // Simple projection - in a real app, you'd use the camera's projection matrix
        let x = CGFloat(position.x) * size.width * 0.5 + size.width * 0.5
        let y = CGFloat(-position.y) * size.height * 0.5 + size.height * 0.5
        return CGPoint(x: x, y: y)
    }
    
    private func jointRadius(for jointName: JointName) -> CGFloat {
        switch jointName {
        case .head: return 8
        case .root, .spine3, .spine6: return 6
        case .leftShoulder, .rightShoulder, .leftUpperLeg, .rightUpperLeg: return 5
        default: return 4
        }
    }
    
    private func jointColor(for jointName: JointName, confidence: Float) -> Color {
        let baseColor: Color
        
        if jointName.isCore {
            baseColor = .blue
        } else if jointName.isArm {
            baseColor = .green
        } else if jointName.isLeg {
            baseColor = .orange
        } else {
            baseColor = .purple
        }
        
        return baseColor.opacity(Double(confidence))
    }
    
    private func confidenceColor(for confidence: Float) -> Color {
        switch confidence {
        case 0.8...1.0: return .green
        case 0.6..<0.8: return .yellow
        case 0.4..<0.6: return .orange
        default: return .red
        }
    }
    
    private func trackingQualityColor(for quality: TrackingQuality) -> Color {
        switch quality {
        case .excellent: return .green
        case .good: return .blue
        case .limited: return .yellow
        case .poor: return .orange
        case .initializing: return .gray
        }
    }
}

// MARK: - AR Session Manager Extensions
extension ARSessionManager {
    func processFrame(_ frame: ARFrame) {
        // Process the AR frame and extract pose data
        // This would be implemented in the actual ARSessionManager
    }
    
    func processBodyAnchor(_ bodyAnchor: ARBodyAnchor) {
        // Process body anchor and update pose data
        // This would be implemented in the actual ARSessionManager
    }
    
    func handleSessionError(_ error: Error) {
        // Handle AR session errors
        DispatchQueue.main.async {
            // Update session state and show error to user
        }
    }
    
    func handleSessionInterruption() {
        DispatchQueue.main.async {
            self.sessionState = .interrupted
        }
    }
    
    func handleSessionInterruptionEnded() {
        DispatchQueue.main.async {
            self.sessionState = .running
        }
    }
}

// MARK: - Supporting Views

struct TrackingQualityIndicator: View {
    let quality: TrackingQuality
    
    var body: some View {
        HStack(spacing: 8) {
            Circle()
                .fill(Color(quality.color))
                .frame(width: 12, height: 12)
            
            Text(quality.displayName)
                .font(.caption)
                .foregroundColor(.white)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(Color.black.opacity(0.6))
        .cornerRadius(16)
    }
}

struct PerformanceIndicator: View {
    let fps: Double
    
    var body: some View {
        Text("\(Int(fps)) FPS")
            .font(.caption)
            .foregroundColor(.white)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color.black.opacity(0.6))
            .cornerRadius(8)
    }
}

struct WorkoutControlsView: View {
    @State private var isPaused = false
    
    var body: some View {
        HStack(spacing: 20) {
            Button(action: { isPaused.toggle() }) {
                Image(systemName: isPaused ? "play.fill" : "pause.fill")
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 50, height: 50)
                    .background(Color.blue)
                    .clipShape(Circle())
            }
            
            Button(action: { /* Stop workout */ }) {
                Image(systemName: "stop.fill")
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 50, height: 50)
                    .background(Color.red)
                    .clipShape(Circle())
            }
        }
        .padding()
        .background(Color.black.opacity(0.3))
        .cornerRadius(25)
    }
}

struct ErrorOverlayView: View {
    let message: String
    let onDismiss: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 40))
                .foregroundColor(.red)
            
            Text("Error")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            Text(message)
                .font(.body)
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            Button("Dismiss") {
                onDismiss()
            }
            .font(.headline)
            .foregroundColor(.white)
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(Color.blue)
            .cornerRadius(8)
        }
        .padding(24)
        .background(Color.black.opacity(0.8))
        .cornerRadius(16)
        .padding()
    }
}

#Preview("AR View Container") {
    ARViewContainer()
        .environmentObject(ARSessionManager.shared)
}

#Preview("Skeleton Overlay") {
    SkeletonOverlayView(poseData: BodyPoseData.sample)
        .background(Color.black)
}

// MARK: - Sample Data
extension BodyPoseData {
    static let sample = BodyPoseData(
        joints: [
            .root: Joint3D(position: simd_float3(0, 0, 0), confidence: 0.9),
            .spine3: Joint3D(position: simd_float3(0, 0.3, 0), confidence: 0.8),
            .leftShoulder: Joint3D(position: simd_float3(-0.2, 0.4, 0), confidence: 0.7),
            .rightShoulder: Joint3D(position: simd_float3(0.2, 0.4, 0), confidence: 0.7)
        ],
        trackingQuality: .good,
        confidence: 0.85,
        boundingBox: CGRect(x: 0, y: 0, width: 100, height: 200),
        isFullBodyVisible: true
    )
}
