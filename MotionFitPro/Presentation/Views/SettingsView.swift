//
//  SettingsView.swift
//  MotionFitPro
//
//  Created by <PERSON> on 2025-07-15.
//

import SwiftUI

struct SettingsView: View {
    @EnvironmentObject var aiCoach: AICoachingSystem
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedCoach: CoachPersonality = .motivational
    @State private var enableHaptics = true
    @State private var enableSounds = true
    @State private var darkModeEnabled = true
    @State private var autoStartWorkouts = false
    @State private var showAdvancedSettings = false
    @State private var animateElements = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // Premium Dark Background
                PremiumSettingsBackground()
                
                ScrollView(showsIndicators: false) {
                    VStack(spacing: 24) {
                        // Header
                        HeaderSection()
                            .opacity(animateElements ? 1 : 0)
                            .offset(y: animateElements ? 0 : -30)
                            .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.1), value: animateElements)
                        
                        // AI Coach Settings
                        CoachSettingsSection()
                            .opacity(animateElements ? 1 : 0)
                            .offset(y: animateElements ? 0 : 20)
                            .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.2), value: animateElements)
                        
                        // Workout Preferences
                        WorkoutPreferencesSection()
                            .opacity(animateElements ? 1 : 0)
                            .offset(y: animateElements ? 0 : 20)
                            .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.3), value: animateElements)
                        
                        // Appearance Settings
                        AppearanceSection()
                            .opacity(animateElements ? 1 : 0)
                            .offset(y: animateElements ? 0 : 20)
                            .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.4), value: animateElements)
                        
                        // Feedback Settings
                        FeedbackSection()
                            .opacity(animateElements ? 1 : 0)
                            .offset(y: animateElements ? 0 : 20)
                            .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.5), value: animateElements)
                        
                        // App Info
                        AppInfoSection()
                            .opacity(animateElements ? 1 : 0)
                            .offset(y: animateElements ? 0 : 20)
                            .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.6), value: animateElements)
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                    .padding(.bottom, 100)
                }
            }
            .navigationBarHidden(true)
        }
        .onAppear {
            animateElements = true
            selectedCoach = aiCoach.currentCoach
        }
    }
    
    // MARK: - Header Section
    
    @ViewBuilder
    private func HeaderSection() -> some View {
        HStack {
            Button(action: { dismiss() }) {
                ZStack {
                    Circle()
                        .fill(.ultraThinMaterial)
                        .frame(width: 44, height: 44)
                    
                    Image(systemName: "xmark")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                }
            }
            
            Spacer()
            
            Text("Settings")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            Spacer()
            
            ZStack {
                Circle()
                    .fill(.blue.opacity(0.2))
                    .frame(width: 44, height: 44)
                
                Image(systemName: "gearshape.fill")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.blue)
            }
        }
    }
    
    // MARK: - AI Coach Settings Section
    
    @ViewBuilder
    private func CoachSettingsSection() -> some View {
        SettingsSection(title: "AI Coach", icon: "brain.head.profile") {
            VStack(spacing: 16) {
                // Current Coach Display
                HStack {
                    Text("Current Coach")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.8))
                    
                    Spacer()
                    
                    HStack(spacing: 8) {
                        Text(selectedCoach.emoji)
                            .font(.title2)
                        
                        Text(selectedCoach.displayName)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.white)
                    }
                }
                
                // Coach Selection
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                    ForEach(CoachPersonality.allCases) { coach in
                        CoachSelectionCard(
                            coach: coach,
                            isSelected: selectedCoach == coach
                        ) {
                            selectedCoach = coach
                            aiCoach.setCoachPersonality(coach)
                        }
                    }
                }
                
                // Coach Preview
                VStack(alignment: .leading, spacing: 8) {
                    Text("Preview")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.white.opacity(0.8))
                    
                    Text(selectedCoach.generateMessage(for: .workoutStart))
                        .font(.body)
                        .fontStyle(.italic)
                        .foregroundColor(.white.opacity(0.9))
                        .padding(12)
                        .background(.white.opacity(0.1))
                        .cornerRadius(8)
                }
            }
        }
    }
    
    // MARK: - Workout Preferences Section
    
    @ViewBuilder
    private func WorkoutPreferencesSection() -> some View {
        SettingsSection(title: "Workout Preferences", icon: "figure.strengthtraining.traditional") {
            VStack(spacing: 16) {
                SettingsToggle(
                    title: "Auto-start Workouts",
                    description: "Automatically begin exercises when form is detected",
                    isOn: $autoStartWorkouts,
                    color: .green
                )
                
                SettingsRow(
                    title: "Default Difficulty",
                    value: "Intermediate",
                    icon: "target",
                    color: .orange
                ) {
                    // Handle difficulty selection
                }
                
                SettingsRow(
                    title: "Rest Time",
                    value: "60 seconds",
                    icon: "timer",
                    color: .blue
                ) {
                    // Handle rest time selection
                }
            }
        }
    }
    
    // MARK: - Appearance Section
    
    @ViewBuilder
    private func AppearanceSection() -> some View {
        SettingsSection(title: "Appearance", icon: "paintbrush") {
            VStack(spacing: 16) {
                SettingsToggle(
                    title: "Dark Mode",
                    description: "Use dark theme for better focus during workouts",
                    isOn: $darkModeEnabled,
                    color: .purple
                )
                
                SettingsRow(
                    title: "Theme Color",
                    value: "Premium Blue",
                    icon: "circle.fill",
                    color: .blue
                ) {
                    // Handle theme color selection
                }
                
                SettingsRow(
                    title: "Animation Speed",
                    value: "Normal",
                    icon: "speedometer",
                    color: .yellow
                ) {
                    // Handle animation speed
                }
            }
        }
    }
    
    // MARK: - Feedback Section
    
    @ViewBuilder
    private func FeedbackSection() -> some View {
        SettingsSection(title: "Feedback & Notifications", icon: "speaker.wave.2") {
            VStack(spacing: 16) {
                SettingsToggle(
                    title: "Haptic Feedback",
                    description: "Feel vibrations for rep completions and achievements",
                    isOn: $enableHaptics,
                    color: .orange
                )
                
                SettingsToggle(
                    title: "Sound Effects",
                    description: "Play audio cues and encouragement",
                    isOn: $enableSounds,
                    color: .green
                )
                
                SettingsRow(
                    title: "Voice Coaching",
                    value: "Enabled",
                    icon: "mic.fill",
                    color: .red
                ) {
                    // Handle voice coaching settings
                }
            }
        }
    }
    
    // MARK: - App Info Section
    
    @ViewBuilder
    private func AppInfoSection() -> some View {
        SettingsSection(title: "About", icon: "info.circle") {
            VStack(spacing: 16) {
                SettingsRow(
                    title: "Version",
                    value: "1.0.0",
                    icon: "app.badge",
                    color: .blue
                ) { }
                
                SettingsRow(
                    title: "Privacy Policy",
                    value: "",
                    icon: "hand.raised",
                    color: .green
                ) {
                    // Open privacy policy
                }
                
                SettingsRow(
                    title: "Terms of Service",
                    value: "",
                    icon: "doc.text",
                    color: .orange
                ) {
                    // Open terms of service
                }
                
                SettingsRow(
                    title: "Support",
                    value: "",
                    icon: "questionmark.circle",
                    color: .purple
                ) {
                    // Open support
                }
            }
        }
    }
}

// MARK: - Premium Settings Background

struct PremiumSettingsBackground: View {
    var body: some View {
        ZStack {
            // Base gradient
            LinearGradient(
                colors: [
                    Color(red: 0.05, green: 0.1, blue: 0.2),
                    Color(red: 0.02, green: 0.05, blue: 0.15),
                    Color.black
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            // Subtle geometric patterns
            GeometryReader { geometry in
                ForEach(0..<10, id: \.self) { i in
                    RoundedRectangle(cornerRadius: 8)
                        .fill(.white.opacity(0.01))
                        .frame(
                            width: CGFloat.random(in: 40...80),
                            height: CGFloat.random(in: 40...80)
                        )
                        .position(
                            x: CGFloat.random(in: 0...geometry.size.width),
                            y: CGFloat.random(in: 0...geometry.size.height)
                        )
                        .rotationEffect(.degrees(Double.random(in: 0...360)))
                        .animation(
                            .linear(duration: Double.random(in: 20...30))
                            .repeatForever(autoreverses: true),
                            value: UUID()
                        )
                }
            }
        }
        .ignoresSafeArea()
    }
}

// MARK: - Settings Section

struct SettingsSection<Content: View>: View {
    let title: String
    let icon: String
    @ViewBuilder let content: Content
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.blue)
                
                Text(title)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
            
            content
        }
        .padding(20)
        .background(.ultraThinMaterial)
        .cornerRadius(16)
    }
}

// MARK: - Settings Toggle

struct SettingsToggle: View {
    let title: String
    let description: String
    @Binding var isOn: Bool
    let color: Color
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
            }
            
            Spacer()
            
            Toggle("", isOn: $isOn)
                .tint(color)
        }
    }
}

// MARK: - Settings Row

struct SettingsRow: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(color)
                    .frame(width: 24)
                
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Spacer()
                
                if !value.isEmpty {
                    Text(value)
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.7))
                }
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.white.opacity(0.5))
            }
        }
    }
}

// MARK: - Coach Selection Card

struct CoachSelectionCard: View {
    let coach: CoachPersonality
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 8) {
                Text(coach.emoji)
                    .font(.title2)
                
                Text(coach.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            .foregroundColor(isSelected ? .white : .white.opacity(0.7))
            .padding(.vertical, 12)
            .padding(.horizontal, 8)
            .background(
                isSelected ? Color(hex: coach.primaryColor).opacity(0.3) : .clear
            )
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(
                        isSelected ? Color(hex: coach.primaryColor) : .clear,
                        lineWidth: 1
                    )
            )
        }
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
}

#Preview {
    SettingsView()
        .environmentObject(AICoachingSystem())
}