import Foundation
import CoreHaptics
import UIKit
import Combine

@MainActor
class HapticFeedbackManager: ObservableObject {
    @Published var isEnabled = true
    @Published var currentPattern: HapticPattern?
    @Published var isPlaying = false
    
    // Haptic engine management
    private var hapticEngine: CHHapticEngine?
    private var currentPlayer: CHHapticPatternPlayer?
    private var isEngineStarted = false
    
    // Pattern library
    private var patternLibrary: [HapticPattern.PatternType: CHHapticPattern] = [:]
    
    // Settings
    private var feedbackIntensity: Float = 1.0
    private var rhythmEnabled = true
    private var isQuietMode = false
    
    private let logger = Logger.shared
    
    init() {
        setupHapticEngine()
        preloadPatterns()
        setupNotificationObservers()
    }
    
    deinit {
        stopEngine()
    }
    
    // MARK: - Public Methods
    
    /// Delivers haptic feedback for coaching scenarios
    func deliverFeedback(for feedbackType: CoachingFeedback.FeedbackType) {
        guard isEnabled && !isQuietMode else { return }
        
        let patternType = mapFeedbackToPattern(feedbackType)
        playPattern(patternType)
    }
    
    /// Plays a success pattern for completed reps
    func playRepSuccessPattern() {
        guard isEnabled else { return }
        playPattern(.repSuccess)
    }
    
    /// Plays a warning pattern for form issues
    func playFormWarningPattern(severity: FormIssue.IssueSeverity) {
        guard isEnabled else { return }
        
        switch severity {
        case .minor:
            playPattern(.formCorrection)
        case .moderate:
            playPattern(.formWarning)
        case .major:
            playPattern(.safetyAlert)
        }
    }
    
    /// Plays rhythm patterns for tempo guidance
    func playTempoGuidance(bpm: Double, duration: TimeInterval) {
        guard isEnabled && rhythmEnabled else { return }
        
        Task {
            await playRhythmPattern(bpm: bpm, duration: duration)
        }
    }
    
    /// Plays celebration pattern for achievements
    func playCelebrationPattern() {
        guard isEnabled else { return }
        playPattern(.celebration)
    }
    
    /// Plays emergency stop pattern
    func playEmergencyStopPattern() {
        // Emergency patterns bypass settings
        playPattern(.emergencyStop)
    }
    
    /// Updates haptic settings
    func updateSettings(intensity: Float, rhythmEnabled: Bool, quietMode: Bool) {
        self.feedbackIntensity = max(0.0, min(1.0, intensity))
        self.rhythmEnabled = rhythmEnabled
        self.isQuietMode = quietMode
        
        logger.info("Haptic settings updated - Intensity: \(intensity), Rhythm: \(rhythmEnabled), Quiet: \(quietMode)", category: .audio)
    }
    
    /// Tests haptic capability and plays test pattern
    func testHaptics() -> Bool {
        guard CHHapticEngine.capabilitiesForHardware().supportsHaptics else {
            logger.warning("Device does not support haptics", category: .audio)
            return false
        }
        
        playPattern(.test)
        return true
    }
    
    // MARK: - Private Methods
    
    private func setupHapticEngine() {
        guard CHHapticEngine.capabilitiesForHardware().supportsHaptics else {
            logger.warning("Haptic engine not supported on this device", category: .audio)
            return
        }
        
        do {
            hapticEngine = try CHHapticEngine()
            
            hapticEngine?.stoppedHandler = { [weak self] reason in
                Task { @MainActor in
                    self?.handleEngineStop(reason: reason)
                }
            }
            
            hapticEngine?.resetHandler = { [weak self] in
                Task { @MainActor in
                    self?.handleEngineReset()
                }
            }
            
            logger.info("Haptic engine initialized successfully", category: .audio)
        } catch {
            logger.error("Failed to initialize haptic engine: \(error)", category: .audio)
        }
    }
    
    private func startEngine() {
        guard let engine = hapticEngine, !isEngineStarted else { return }
        
        do {
            try engine.start()
            isEngineStarted = true
            logger.debug("Haptic engine started", category: .audio)
        } catch {
            logger.error("Failed to start haptic engine: \(error)", category: .audio)
        }
    }
    
    private func stopEngine() {
        hapticEngine?.stop()
        isEngineStarted = false
        currentPlayer = nil
        logger.debug("Haptic engine stopped", category: .audio)
    }
    
    private func handleEngineStop(reason: CHHapticEngine.StoppedReason) {
        isEngineStarted = false
        isPlaying = false
        currentPlayer = nil
        
        logger.warning("Haptic engine stopped: \(reason)", category: .audio)
        
        // Attempt restart for certain conditions
        switch reason {
        case .audioSessionInterrupt, .applicationSuspended:
            // Will restart when needed
            break
        case .idleTimeout:
            // Engine stopped due to inactivity, normal behavior
            break
        case .systemError:
            logger.error("Haptic engine system error", category: .audio)
        case .notifyWhenFinished:
            // Normal completion
            break
        @unknown default:
            logger.warning("Unknown haptic engine stop reason", category: .audio)
        }
    }
    
    private func handleEngineReset() {
        logger.info("Haptic engine reset", category: .audio)
        isEngineStarted = false
        currentPlayer = nil
        preloadPatterns()
    }
    
    private func preloadPatterns() {
        Task {
            await withTaskGroup(of: Void.self) { group in
                for patternType in HapticPattern.PatternType.allCases {
                    group.addTask {
                        await self.createAndCachePattern(patternType)
                    }
                }
            }
            
            await MainActor.run {
                self.logger.info("Haptic patterns preloaded", category: .audio)
            }
        }
    }
    
    private func createAndCachePattern(_ type: HapticPattern.PatternType) async {
        do {
            let pattern = try createHapticPattern(for: type)
            await MainActor.run {
                self.patternLibrary[type] = pattern
            }
        } catch {
            await MainActor.run {
                self.logger.error("Failed to create haptic pattern \(type): \(error)", category: .audio)
            }
        }
    }
    
    private func createHapticPattern(for type: HapticPattern.PatternType) throws -> CHHapticPattern {
        let events = createHapticEvents(for: type)
        return try CHHapticPattern(events: events, parameters: [])
    }
    
    private func createHapticEvents(for type: HapticPattern.PatternType) -> [CHHapticEvent] {
        switch type {
        case .repSuccess:
            return createRepSuccessEvents()
        case .setComplete:
            return createSetCompleteEvents()
        case .formCorrection:
            return createFormCorrectionEvents()
        case .formWarning:
            return createFormWarningEvents()
        case .safetyAlert:
            return createSafetyAlertEvents()
        case .celebration:
            return createCelebrationEvents()
        case .emergencyStop:
            return createEmergencyStopEvents()
        case .rhythmBeat:
            return createRhythmBeatEvents()
        case .encouragement:
            return createEncouragementEvents()
        case .test:
            return createTestEvents()
        }
    }
    
    private func createRepSuccessEvents() -> [CHHapticEvent] {
        return [
            CHHapticEvent(
                eventType: .hapticTransient,
                parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: 0.8 * feedbackIntensity),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: 0.7)
                ],
                relativeTime: 0
            )
        ]
    }
    
    private func createSetCompleteEvents() -> [CHHapticEvent] {
        return [
            CHHapticEvent(
                eventType: .hapticTransient,
                parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: 1.0 * feedbackIntensity),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: 0.8)
                ],
                relativeTime: 0
            ),
            CHHapticEvent(
                eventType: .hapticTransient,
                parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: 0.6 * feedbackIntensity),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: 0.5)
                ],
                relativeTime: 0.2
            )
        ]
    }
    
    private func createFormCorrectionEvents() -> [CHHapticEvent] {
        return [
            CHHapticEvent(
                eventType: .hapticContinuous,
                parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: 0.5 * feedbackIntensity),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: 0.3)
                ],
                relativeTime: 0,
                duration: 0.3
            )
        ]
    }
    
    private func createFormWarningEvents() -> [CHHapticEvent] {
        return [
            CHHapticEvent(
                eventType: .hapticTransient,
                parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: 0.8 * feedbackIntensity),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: 1.0)
                ],
                relativeTime: 0
            ),
            CHHapticEvent(
                eventType: .hapticTransient,
                parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: 0.8 * feedbackIntensity),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: 1.0)
                ],
                relativeTime: 0.2
            )
        ]
    }
    
    private func createSafetyAlertEvents() -> [CHHapticEvent] {
        var events: [CHHapticEvent] = []
        
        // Urgent triple pulse
        for i in 0..<3 {
            events.append(
                CHHapticEvent(
                    eventType: .hapticTransient,
                    parameters: [
                        CHHapticEventParameter(parameterID: .hapticIntensity, value: 1.0 * feedbackIntensity),
                        CHHapticEventParameter(parameterID: .hapticSharpness, value: 1.0)
                    ],
                    relativeTime: Double(i) * 0.15
                )
            )
        }
        
        return events
    }
    
    private func createCelebrationEvents() -> [CHHapticEvent] {
        var events: [CHHapticEvent] = []
        
        // Ascending celebration pattern
        let intensities: [Float] = [0.6, 0.8, 1.0, 0.8]
        let sharpnesses: [Float] = [0.3, 0.5, 0.7, 0.9]
        
        for (index, intensity) in intensities.enumerated() {
            events.append(
                CHHapticEvent(
                    eventType: .hapticTransient,
                    parameters: [
                        CHHapticEventParameter(parameterID: .hapticIntensity, value: intensity * feedbackIntensity),
                        CHHapticEventParameter(parameterID: .hapticSharpness, value: sharpnesses[index])
                    ],
                    relativeTime: Double(index) * 0.1
                )
            )
        }
        
        return events
    }
    
    private func createEmergencyStopEvents() -> [CHHapticEvent] {
        var events: [CHHapticEvent] = []
        
        // Immediate intense pattern that cuts through everything
        for i in 0..<5 {
            events.append(
                CHHapticEvent(
                    eventType: .hapticTransient,
                    parameters: [
                        CHHapticEventParameter(parameterID: .hapticIntensity, value: 1.0),
                        CHHapticEventParameter(parameterID: .hapticSharpness, value: 1.0)
                    ],
                    relativeTime: Double(i) * 0.1
                )
            )
        }
        
        return events
    }
    
    private func createRhythmBeatEvents() -> [CHHapticEvent] {
        return [
            CHHapticEvent(
                eventType: .hapticTransient,
                parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: 0.6 * feedbackIntensity),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: 0.4)
                ],
                relativeTime: 0
            )
        ]
    }
    
    private func createEncouragementEvents() -> [CHHapticEvent] {
        return [
            CHHapticEvent(
                eventType: .hapticContinuous,
                parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: 0.4 * feedbackIntensity),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: 0.2)
                ],
                relativeTime: 0,
                duration: 0.5
            )
        ]
    }
    
    private func createTestEvents() -> [CHHapticEvent] {
        return [
            CHHapticEvent(
                eventType: .hapticTransient,
                parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: 0.8),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: 0.5)
                ],
                relativeTime: 0
            )
        ]
    }
    
    private func playPattern(_ type: HapticPattern.PatternType) {
        guard let engine = hapticEngine else { return }
        
        if !isEngineStarted {
            startEngine()
        }
        
        guard isEngineStarted else { return }
        
        currentPattern = HapticPattern(type: type, timestamp: Date())
        
        do {
            let pattern = patternLibrary[type] ?? (try createHapticPattern(for: type))
            let player = try engine.makePlayer(with: pattern)
            
            currentPlayer?.stop(atTime: CHHapticTimeImmediate)
            currentPlayer = player
            
            isPlaying = true
            
            try player.start(atTime: CHHapticTimeImmediate)
            
            // Schedule cleanup
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                if self.currentPlayer === player {
                    self.isPlaying = false
                    self.currentPlayer = nil
                }
            }
            
            logger.debug("Haptic pattern played: \(type)", category: .audio)
            
        } catch {
            logger.error("Failed to play haptic pattern \(type): \(error)", category: .audio)
            isPlaying = false
        }
    }
    
    private func playRhythmPattern(bpm: Double, duration: TimeInterval) async {
        guard rhythmEnabled && isEnabled else { return }
        
        let beatInterval = 60.0 / bpm
        let beatCount = Int(duration / beatInterval)
        
        for i in 0..<beatCount {
            playPattern(.rhythmBeat)
            
            if i < beatCount - 1 {
                try? await Task.sleep(nanoseconds: UInt64(beatInterval * 1_000_000_000))
            }
        }
    }
    
    private func mapFeedbackToPattern(_ feedbackType: CoachingFeedback.FeedbackType) -> HapticPattern.PatternType {
        switch feedbackType {
        case .encouragement:
            return .encouragement
        case .formCorrection:
            return .formCorrection
        case .safety:
            return .safetyAlert
        case .celebration:
            return .celebration
        case .rest:
            return .encouragement
        case .information:
            return .encouragement
        }
    }
    
    private func setupNotificationObservers() {
        NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)
            .sink { [weak self] _ in
                self?.stopEngine()
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)
            .sink { [weak self] _ in
                // Engine will restart when needed
                self?.isEngineStarted = false
            }
            .store(in: &cancellables)
    }
    
    private var cancellables = Set<AnyCancellable>()
}

// MARK: - Supporting Types

struct HapticPattern {
    let type: PatternType
    let timestamp: Date
    
    enum PatternType: String, CaseIterable {
        case repSuccess = "rep_success"
        case setComplete = "set_complete"
        case formCorrection = "form_correction"
        case formWarning = "form_warning"
        case safetyAlert = "safety_alert"
        case celebration = "celebration"
        case emergencyStop = "emergency_stop"
        case rhythmBeat = "rhythm_beat"
        case encouragement = "encouragement"
        case test = "test"
        
        var displayName: String {
            switch self {
            case .repSuccess:
                return "Rep Success"
            case .setComplete:
                return "Set Complete"
            case .formCorrection:
                return "Form Correction"
            case .formWarning:
                return "Form Warning"
            case .safetyAlert:
                return "Safety Alert"
            case .celebration:
                return "Celebration"
            case .emergencyStop:
                return "Emergency Stop"
            case .rhythmBeat:
                return "Rhythm Beat"
            case .encouragement:
                return "Encouragement"
            case .test:
                return "Test Pattern"
            }
        }
        
        var description: String {
            switch self {
            case .repSuccess:
                return "Quick tap for successful repetition"
            case .setComplete:
                return "Double tap for completed set"
            case .formCorrection:
                return "Gentle pulse for form adjustment"
            case .formWarning:
                return "Double pulse for form warning"
            case .safetyAlert:
                return "Triple urgent pulse for safety"
            case .celebration:
                return "Ascending celebration pattern"
            case .emergencyStop:
                return "Urgent repeated pattern for emergency"
            case .rhythmBeat:
                return "Rhythmic beat for tempo guidance"
            case .encouragement:
                return "Gentle continuous pulse for encouragement"
            case .test:
                return "Standard test pattern"
            }
        }
    }
}

// MARK: - Haptic Settings

struct HapticSettings: Codable {
    var isEnabled: Bool = true
    var intensity: Float = 1.0
    var rhythmEnabled: Bool = true
    var quietMode: Bool = false
    var enableSuccessPatterns: Bool = true
    var enableWarningPatterns: Bool = true
    var enableCelebrationPatterns: Bool = true
    var enableRhythmPatterns: Bool = true
    
    enum CodingKeys: String, CodingKey {
        case isEnabled
        case intensity
        case rhythmEnabled
        case quietMode
        case enableSuccessPatterns
        case enableWarningPatterns
        case enableCelebrationPatterns
        case enableRhythmPatterns
    }
}