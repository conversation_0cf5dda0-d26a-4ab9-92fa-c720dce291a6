import Foundation
import AVFoundation
import Combine

@MainActor
class SpeechSynthesisManager: NSObject, ObservableObject {
    @Published var isSpeaking = false
    @Published var speechSettings = SpeechSettings()
    @Published var availableVoices: [VoiceOption] = []
    @Published var currentVoice: VoiceOption?
    
    // Speech queue management
    private var speechQueue: [SpeechItem] = []
    private var isProcessingQueue = false
    private let synthesizer = AVSpeechSynthesizer()
    
    // Audio session management
    private var audioSession = AVAudioSession.sharedInstance()
    private var previousAudioCategory: AVAudioSession.Category?
    private var previousAudioMode: AVAudioSession.Mode?
    
    // Spatial audio (for future AR integration)
    private var spatialAudioEnabled = false
    private var listenerPosition: simd_float3 = simd_float3(0, 0, 0)
    
    private let logger = Logger.shared
    
    override init() {
        super.init()
        synthesizer.delegate = self
        setupAudioSession()
        loadAvailableVoices()
        loadSpeechSettings()
    }
    
    // MARK: - Public Methods
    
    /// Speaks the given text with specified priority
    func speak(_ text: String, priority: CoachingPriority = .normal, voice: VoiceOption? = nil) {
        guard speechSettings.isEnabled else { return }
        
        let speechItem = SpeechItem(
            text: text,
            priority: priority,
            voice: voice ?? currentVoice,
            settings: speechSettings
        )
        
        // Handle priority insertion
        switch priority {
        case .immediate:
            // Stop current speech and insert at front
            stopSpeaking()
            speechQueue.insert(speechItem, at: 0)
        case .high:
            // Insert after any immediate priority items
            let insertIndex = speechQueue.firstIndex { $0.priority != .immediate } ?? speechQueue.count
            speechQueue.insert(speechItem, at: insertIndex)
        case .normal, .low:
            speechQueue.append(speechItem)
        }
        
        processNextInQueue()
        logger.debug("Speech queued: \(text) (Priority: \(priority))", category: .audio)
    }
    
    /// Stops current speech synthesis
    func stopSpeaking() {
        if synthesizer.isSpeaking {
            synthesizer.stopSpeaking(at: .immediate)
        }
        isSpeaking = false
    }
    
    /// Pauses speech synthesis
    func pauseSpeaking() {
        if synthesizer.isSpeaking {
            synthesizer.pauseSpeaking(at: .word)
        }
    }
    
    /// Resumes paused speech
    func resumeSpeaking() {
        if synthesizer.isPaused {
            synthesizer.continueSpeaking()
        }
    }
    
    /// Clears all queued speech
    func clearQueue() {
        speechQueue.removeAll()
        stopSpeaking()
        logger.debug("Speech queue cleared", category: .audio)
    }
    
    /// Updates speech settings
    func updateSettings(_ newSettings: SpeechSettings) {
        speechSettings = newSettings
        saveSpeechSettings()
        
        // Update current voice if needed
        if let voiceId = newSettings.preferredVoiceId,
           let voice = availableVoices.first(where: { $0.id == voiceId }) {
            currentVoice = voice
        }
        
        logger.info("Speech settings updated", category: .audio)
    }
    
    /// Enables spatial audio positioning
    func enableSpatialAudio(listenerPosition: simd_float3 = simd_float3(0, 0, 0)) {
        spatialAudioEnabled = true
        self.listenerPosition = listenerPosition
        logger.info("Spatial audio enabled", category: .audio)
    }
    
    /// Disables spatial audio
    func disableSpatialAudio() {
        spatialAudioEnabled = false
        logger.info("Spatial audio disabled", category: .audio)
    }
    
    /// Updates listener position for spatial audio
    func updateListenerPosition(_ position: simd_float3) {
        listenerPosition = position
    }
    
    // MARK: - Private Methods
    
    private func setupAudioSession() {
        do {
            // Store previous session configuration
            previousAudioCategory = audioSession.category
            previousAudioMode = audioSession.mode
            
            // Configure audio session for speech synthesis
            try audioSession.setCategory(.playAndRecord, mode: .spokenAudio, options: [.defaultToSpeaker, .allowBluetooth])
            try audioSession.setActive(true)
            
            logger.info("Audio session configured for speech synthesis", category: .audio)
        } catch {
            logger.error("Failed to configure audio session: \(error)", category: .audio)
        }
    }
    
    private func restoreAudioSession() {
        do {
            if let category = previousAudioCategory, let mode = previousAudioMode {
                try audioSession.setCategory(category, mode: mode)
            }
            logger.debug("Audio session restored", category: .audio)
        } catch {
            logger.error("Failed to restore audio session: \(error)", category: .audio)
        }
    }
    
    private func loadAvailableVoices() {
        let systemVoices = AVSpeechSynthesisVoice.speechVoices()
        
        availableVoices = systemVoices.compactMap { voice in
            guard voice.language.hasPrefix("en") else { return nil } // English voices only
            
            return VoiceOption(
                id: voice.identifier,
                name: voice.name,
                language: voice.language,
                gender: determineGender(from: voice),
                personality: determinePersonality(from: voice),
                quality: determineQuality(from: voice),
                systemVoice: voice
            )
        }.sorted { $0.quality.rawValue > $1.quality.rawValue }
        
        // Set default voice
        currentVoice = availableVoices.first { $0.quality == .enhanced } ?? availableVoices.first
        
        logger.info("Loaded \(availableVoices.count) available voices", category: .audio)
    }
    
    private func determineGender(from voice: AVSpeechSynthesisVoice) -> VoiceGender {
        let femaleNames = ["Samantha", "Alex", "Victoria", "Allison", "Ava", "Susan", "Karen"]
        return femaleNames.contains(voice.name) ? .female : .male
    }
    
    private func determinePersonality(from voice: AVSpeechSynthesisVoice) -> VoicePersonality {
        switch voice.name.lowercased() {
        case let name where name.contains("alex"):
            return .friendly
        case let name where name.contains("daniel"):
            return .professional
        case let name where name.contains("samantha"):
            return .warm
        default:
            return .neutral
        }
    }
    
    private func determineQuality(from voice: AVSpeechSynthesisVoice) -> VoiceQuality {
        if voice.quality == .enhanced {
            return .enhanced
        } else {
            return .standard
        }
    }
    
    private func processNextInQueue() {
        guard !isProcessingQueue, !speechQueue.isEmpty, speechSettings.isEnabled else { return }
        
        isProcessingQueue = true
        
        let nextItem = speechQueue.removeFirst()
        speakImmediately(nextItem)
    }
    
    private func speakImmediately(_ item: SpeechItem) {
        let utterance = createUtterance(from: item)
        
        // Apply speech modifications based on settings
        configureUtterance(utterance, with: item.settings)
        
        // Set voice
        if let voiceOption = item.voice ?? currentVoice {
            utterance.voice = voiceOption.systemVoice
        }
        
        isSpeaking = true
        synthesizer.speak(utterance)
        
        logger.debug("Speaking: \(item.text)", category: .audio)
    }
    
    private func createUtterance(from item: SpeechItem) -> AVSpeechUtterance {
        var processedText = item.text
        
        // Apply text preprocessing for better speech
        processedText = preprocessTextForSpeech(processedText)
        
        return AVSpeechUtterance(string: processedText)
    }
    
    private func preprocessTextForSpeech(_ text: String) -> String {
        var processed = text
        
        // Add pauses for better delivery
        processed = processed.replacingOccurrences(of: "!", with: "![[slnc 200]]")
        processed = processed.replacingOccurrences(of: ".", with: ".[[slnc 150]]")
        processed = processed.replacingOccurrences(of: ",", with: ",[[slnc 100]]")
        
        // Handle exercise-specific terminology
        processed = processed.replacingOccurrences(of: "reps", with: "repetitions")
        processed = processed.replacingOccurrences(of: "sets", with: "sets")
        
        // Emphasize important words
        processed = processed.replacingOccurrences(of: "Stop", with: "[[emph +]]Stop[[emph -]]")
        processed = processed.replacingOccurrences(of: "Great", with: "[[emph +]]Great[[emph -]]")
        processed = processed.replacingOccurrences(of: "Perfect", with: "[[emph +]]Perfect[[emph -]]")
        
        return processed
    }
    
    private func configureUtterance(_ utterance: AVSpeechUtterance, with settings: SpeechSettings) {
        utterance.rate = Float(settings.speechRate)
        utterance.pitchMultiplier = Float(settings.pitch)
        utterance.volume = Float(settings.volume)
        
        // Apply workout-specific timing
        if settings.workoutOptimized {
            // Slightly faster rate for workout environment
            utterance.rate = min(utterance.rate * 1.1, AVSpeechUtteranceMaximumSpeechRate)
            
            // Clearer pronunciation
            utterance.pitchMultiplier = max(utterance.pitchMultiplier, 1.0)
        }
        
        // Pre-utterance delay for better timing
        utterance.preUtteranceDelay = settings.preUtteranceDelay
        utterance.postUtteranceDelay = settings.postUtteranceDelay
    }
    
    private func loadSpeechSettings() {
        if let data = UserDefaults.standard.data(forKey: "SpeechSettings"),
           let settings = try? JSONDecoder().decode(SpeechSettings.self, data: data) {
            speechSettings = settings
        }
        
        // Load preferred voice
        if let voiceId = speechSettings.preferredVoiceId,
           let voice = availableVoices.first(where: { $0.id == voiceId }) {
            currentVoice = voice
        }
    }
    
    private func saveSpeechSettings() {
        if let data = try? JSONEncoder().encode(speechSettings) {
            UserDefaults.standard.set(data, forKey: "SpeechSettings")
        }
    }
}

// MARK: - AVSpeechSynthesizerDelegate

extension SpeechSynthesisManager: AVSpeechSynthesizerDelegate {
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didStart utterance: AVSpeechUtterance) {
        logger.debug("Speech synthesis started", category: .audio)
    }
    
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didFinish utterance: AVSpeechUtterance) {
        isSpeaking = false
        isProcessingQueue = false
        
        logger.debug("Speech synthesis completed", category: .audio)
        
        // Process next item in queue
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.processNextInQueue()
        }
    }
    
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didPause utterance: AVSpeechUtterance) {
        logger.debug("Speech synthesis paused", category: .audio)
    }
    
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didContinue utterance: AVSpeechUtterance) {
        logger.debug("Speech synthesis resumed", category: .audio)
    }
    
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didCancel utterance: AVSpeechUtterance) {
        isSpeaking = false
        isProcessingQueue = false
        logger.debug("Speech synthesis cancelled", category: .audio)
    }
    
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, willSpeakRangeOfSpeechString characterRange: NSRange, utterance: AVSpeechUtterance) {
        // Could be used for word highlighting in future
    }
}

// MARK: - Supporting Types

struct SpeechItem {
    let text: String
    let priority: CoachingPriority
    let voice: VoiceOption?
    let settings: SpeechSettings
    let timestamp: Date = Date()
}

struct VoiceOption: Identifiable, Codable {
    let id: String
    let name: String
    let language: String
    let gender: VoiceGender
    let personality: VoicePersonality
    let quality: VoiceQuality
    
    // Not stored in Codable
    let systemVoice: AVSpeechSynthesisVoice?
    
    enum CodingKeys: String, CodingKey {
        case id, name, language, gender, personality, quality
    }
    
    init(id: String, name: String, language: String, gender: VoiceGender, 
         personality: VoicePersonality, quality: VoiceQuality, systemVoice: AVSpeechSynthesisVoice?) {
        self.id = id
        self.name = name
        self.language = language
        self.gender = gender
        self.personality = personality
        self.quality = quality
        self.systemVoice = systemVoice
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(String.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        language = try container.decode(String.self, forKey: .language)
        gender = try container.decode(VoiceGender.self, forKey: .gender)
        personality = try container.decode(VoicePersonality.self, forKey: .personality)
        quality = try container.decode(VoiceQuality.self, forKey: .quality)
        
        // Find system voice by identifier
        systemVoice = AVSpeechSynthesisVoice(identifier: id)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(language, forKey: .language)
        try container.encode(gender, forKey: .gender)
        try container.encode(personality, forKey: .personality)
        try container.encode(quality, forKey: .quality)
    }
}

enum VoiceGender: String, Codable, CaseIterable {
    case male = "Male"
    case female = "Female"
    case neutral = "Neutral"
}

enum VoicePersonality: String, Codable, CaseIterable {
    case professional = "Professional"
    case friendly = "Friendly"
    case warm = "Warm"
    case energetic = "Energetic"
    case calm = "Calm"
    case neutral = "Neutral"
    
    var description: String {
        switch self {
        case .professional:
            return "Clear and authoritative"
        case .friendly:
            return "Approachable and encouraging"
        case .warm:
            return "Caring and supportive"
        case .energetic:
            return "Upbeat and motivating"
        case .calm:
            return "Soothing and relaxed"
        case .neutral:
            return "Balanced and clear"
        }
    }
}

enum VoiceQuality: String, Codable, CaseIterable {
    case standard = "Standard"
    case enhanced = "Enhanced"
    
    var rawValue: Int {
        switch self {
        case .standard: return 1
        case .enhanced: return 2
        }
    }
}

struct SpeechSettings: Codable {
    var isEnabled: Bool = true
    var speechRate: Double = 0.5 // 0.0 to 1.0
    var pitch: Double = 1.0 // 0.5 to 2.0
    var volume: Double = 0.8 // 0.0 to 1.0
    var preferredVoiceId: String?
    var workoutOptimized: Bool = true
    var preUtteranceDelay: TimeInterval = 0.1
    var postUtteranceDelay: TimeInterval = 0.2
    var enableSpatialAudio: Bool = false
    
    // Coaching-specific settings
    var enableEncouragement: Bool = true
    var enableFormCorrection: Bool = true
    var enableSafetyWarnings: Bool = true
    var enableCelebration: Bool = true
    
    // Frequency settings
    var feedbackFrequency: FeedbackFrequency = .normal
    
    enum FeedbackFrequency: String, Codable, CaseIterable {
        case minimal = "Minimal"
        case normal = "Normal"
        case frequent = "Frequent"
        
        var description: String {
            switch self {
            case .minimal:
                return "Only essential feedback"
            case .normal:
                return "Balanced coaching"
            case .frequent:
                return "Detailed guidance"
            }
        }
    }
}

enum CoachingPriority: String, Codable {
    case immediate // Safety warnings, stop commands
    case high // Form corrections, important guidance
    case normal // Encouragement, tips
    case low // Background information
}