import Foundation
import UIKit
import SwiftUI
import Combine

/// Manages accessibility features including VoiceOver, Dynamic Type, and compliance
@MainActor
class AccessibilityManager: ObservableObject {
    
    // MARK: - Published Properties
    @Published var isVoiceOverEnabled: Bool = false
    @Published var isSwitchControlEnabled: Bool = false
    @Published var isReduceMotionEnabled: Bool = false
    @Published var isIncreaseContrastEnabled: Bool = false
    @Published var isDifferentiateWithoutColorEnabled: Bool = false
    @Published var isReduceTransparencyEnabled: Bool = false
    @Published var isAssistiveTouchEnabled: Bool = false
    @Published var preferredContentSizeCategory: ContentSizeCategory = .medium
    @Published var currentAccessibilityMode: AccessibilityMode = .standard
    @Published var voiceOverAnnouncements: [VoiceOverAnnouncement] = []
    
    // MARK: - Private Properties
    private let logger = Logger.shared
    private var cancellables = Set<AnyCancellable>()
    private let audioManager = AudioManager.shared
    private let hapticManager = HapticManager.shared
    
    // Accessibility settings
    private var customAccessibilityLabels: [String: String] = [:]
    private var accessibilityHints: [String: String] = [:]
    private var accessibilityValues: [String: String] = [:]
    
    // VoiceOver management
    private var voiceOverQueue: [String] = []
    private var isVoiceOverSpeaking: Bool = false
    private var voiceOverTimer: Timer?
    
    // MARK: - Initialization
    init() {
        setupAccessibilityMonitoring()
        loadAccessibilitySettings()
        configureInitialState()
    }
    
    // MARK: - Public Interface
    
    /// Configure accessibility for workout mode
    func configureForWorkout() {
        if isVoiceOverEnabled {
            announceWorkoutStart()
        }
        
        if isReduceMotionEnabled {
            disableAnimations()
        }
        
        if isIncreaseContrastEnabled {
            applyHighContrastTheme()
        }
        
        logger.info("Accessibility configured for workout mode", category: .accessibility)
    }
    
    /// Announce workout progress
    func announceWorkoutProgress(_ message: String, priority: AnnouncementPriority = .medium) {
        if isVoiceOverEnabled {
            queueVoiceOverAnnouncement(message, priority: priority)
        }
        
        // Also provide haptic feedback for important announcements
        if priority == .high || priority == .critical {
            hapticManager.trigger(.achievement)
        }
    }
    
    /// Announce exercise instructions
    func announceExerciseInstructions(_ exercise: ExerciseData) {
        guard isVoiceOverEnabled else { return }
        
        let instructions = generateExerciseInstructions(exercise)
        queueVoiceOverAnnouncement(instructions, priority: .high)
    }
    
    /// Announce form feedback
    func announceFormFeedback(_ feedback: FormFeedback) {
        guard isVoiceOverEnabled else { return }
        
        let announcement = generateFormFeedbackAnnouncement(feedback)
        queueVoiceOverAnnouncement(announcement, priority: .high)
    }
    
    /// Set custom accessibility label for element
    func setAccessibilityLabel(_ label: String, for identifier: String) {
        customAccessibilityLabels[identifier] = label
    }
    
    /// Set accessibility hint for element
    func setAccessibilityHint(_ hint: String, for identifier: String) {
        accessibilityHints[identifier] = hint
    }
    
    /// Set accessibility value for element
    func setAccessibilityValue(_ value: String, for identifier: String) {
        accessibilityValues[identifier] = value
    }
    
    /// Get accessibility label for element
    func getAccessibilityLabel(for identifier: String) -> String? {
        return customAccessibilityLabels[identifier]
    }
    
    /// Get accessibility hint for element
    func getAccessibilityHint(for identifier: String) -> String? {
        return accessibilityHints[identifier]
    }
    
    /// Get accessibility value for element
    func getAccessibilityValue(for identifier: String) -> String? {
        return accessibilityValues[identifier]
    }
    
    /// Configure accessibility for specific mode
    func setAccessibilityMode(_ mode: AccessibilityMode) {
        currentAccessibilityMode = mode
        applyAccessibilityMode(mode)
        logger.info("Accessibility mode set to: \(mode)", category: .accessibility)
    }
    
    /// Get recommended font size for current accessibility settings
    func getRecommendedFontSize(for textStyle: Font.TextStyle) -> CGFloat {
        let baseSize = getBaseFontSize(for: textStyle)
        let scaleFactor = getContentSizeScaleFactor()
        return baseSize * scaleFactor
    }
    
    /// Check if high contrast is needed
    func shouldUseHighContrast() -> Bool {
        return isIncreaseContrastEnabled || currentAccessibilityMode == .highContrast
    }
    
    /// Check if reduced motion is needed
    func shouldReduceMotion() -> Bool {
        return isReduceMotionEnabled || currentAccessibilityMode == .reducedMotion
    }
    
    /// Check if large text is needed
    func shouldUseLargeText() -> Bool {
        return preferredContentSizeCategory.isAccessibilityCategory || currentAccessibilityMode == .largeText
    }
    
    // MARK: - Private Methods
    
    private func setupAccessibilityMonitoring() {
        // Monitor VoiceOver status
        NotificationCenter.default.publisher(for: UIAccessibility.voiceOverStatusDidChangeNotification)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.updateVoiceOverStatus()
            }
            .store(in: &cancellables)
        
        // Monitor Switch Control status
        NotificationCenter.default.publisher(for: UIAccessibility.switchControlStatusDidChangeNotification)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.updateSwitchControlStatus()
            }
            .store(in: &cancellables)
        
        // Monitor Reduce Motion
        NotificationCenter.default.publisher(for: UIAccessibility.reduceMotionStatusDidChangeNotification)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.updateReduceMotionStatus()
            }
            .store(in: &cancellables)
        
        // Monitor Increase Contrast
        NotificationCenter.default.publisher(for: UIAccessibility.increaseContrastDidChangeNotification)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.updateIncreaseContrastStatus()
            }
            .store(in: &cancellables)
        
        // Monitor Differentiate Without Color
        NotificationCenter.default.publisher(for: UIAccessibility.differentiateWithoutColorDidChangeNotification)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.updateDifferentiateWithoutColorStatus()
            }
            .store(in: &cancellables)
        
        // Monitor Reduce Transparency
        NotificationCenter.default.publisher(for: UIAccessibility.reduceTransparencyStatusDidChangeNotification)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.updateReduceTransparencyStatus()
            }
            .store(in: &cancellables)
        
        // Monitor Assistive Touch
        NotificationCenter.default.publisher(for: UIAccessibility.assistiveTouchStatusDidChangeNotification)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.updateAssistiveTouchStatus()
            }
            .store(in: &cancellables)
        
        // Monitor Content Size Category
        NotificationCenter.default.publisher(for: UIContentSizeCategory.didChangeNotification)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.updateContentSizeCategory()
            }
            .store(in: &cancellables)
    }
    
    private func loadAccessibilitySettings() {
        // Load custom accessibility settings from UserDefaults
        if let savedLabels = UserDefaults.standard.dictionary(forKey: "accessibility_labels") as? [String: String] {
            customAccessibilityLabels = savedLabels
        }
        
        if let savedHints = UserDefaults.standard.dictionary(forKey: "accessibility_hints") as? [String: String] {
            accessibilityHints = savedHints
        }
        
        if let savedValues = UserDefaults.standard.dictionary(forKey: "accessibility_values") as? [String: String] {
            accessibilityValues = savedValues
        }
        
        // Load accessibility mode
        if let modeRawValue = UserDefaults.standard.string(forKey: "accessibility_mode"),
           let mode = AccessibilityMode(rawValue: modeRawValue) {
            currentAccessibilityMode = mode
        }
    }
    
    private func configureInitialState() {
        updateVoiceOverStatus()
        updateSwitchControlStatus()
        updateReduceMotionStatus()
        updateIncreaseContrastStatus()
        updateDifferentiateWithoutColorStatus()
        updateReduceTransparencyStatus()
        updateAssistiveTouchStatus()
        updateContentSizeCategory()
        
        applyAccessibilityMode(currentAccessibilityMode)
    }
    
    private func updateVoiceOverStatus() {
        isVoiceOverEnabled = UIAccessibility.isVoiceOverRunning
        
        if isVoiceOverEnabled {
            setupVoiceOverSupport()
        }
    }
    
    private func updateSwitchControlStatus() {
        isSwitchControlEnabled = UIAccessibility.isSwitchControlRunning
    }
    
    private func updateReduceMotionStatus() {
        isReduceMotionEnabled = UIAccessibility.isReduceMotionEnabled
        
        if isReduceMotionEnabled {
            disableAnimations()
        }
    }
    
    private func updateIncreaseContrastStatus() {
        isIncreaseContrastEnabled = UIAccessibility.isDarkerSystemColorsEnabled
        
        if isIncreaseContrastEnabled {
            applyHighContrastTheme()
        }
    }
    
    private func updateDifferentiateWithoutColorStatus() {
        isDifferentiateWithoutColorEnabled = UIAccessibility.shouldDifferentiateWithoutColor
    }
    
    private func updateReduceTransparencyStatus() {
        isReduceTransparencyEnabled = UIAccessibility.isReduceTransparencyEnabled
    }
    
    private func updateAssistiveTouchStatus() {
        isAssistiveTouchEnabled = UIAccessibility.isAssistiveTouchRunning
    }
    
    private func updateContentSizeCategory() {
        let category = UIApplication.shared.preferredContentSizeCategory
        preferredContentSizeCategory = ContentSizeCategory(category)
    }
    
    private func applyAccessibilityMode(_ mode: AccessibilityMode) {
        switch mode {
        case .standard:
            applyStandardAccessibility()
        case .voiceOver:
            applyVoiceOverOptimizations()
        case .largeText:
            applyLargeTextOptimizations()
        case .highContrast:
            applyHighContrastOptimizations()
        case .reducedMotion:
            applyReducedMotionOptimizations()
        case .assistive:
            applyAssistiveOptimizations()
        }
        
        saveAccessibilitySettings()
    }
    
    private func applyStandardAccessibility() {
        // Apply standard accessibility features
    }
    
    private func applyVoiceOverOptimizations() {
        setupVoiceOverSupport()
        enableDetailedAnnouncements()
    }
    
    private func applyLargeTextOptimizations() {
        // Apply large text optimizations
        NotificationCenter.default.post(name: .accessibilityLargeTextEnabled, object: nil)
    }
    
    private func applyHighContrastOptimizations() {
        applyHighContrastTheme()
        NotificationCenter.default.post(name: .accessibilityHighContrastEnabled, object: nil)
    }
    
    private func applyReducedMotionOptimizations() {
        disableAnimations()
        NotificationCenter.default.post(name: .accessibilityReducedMotionEnabled, object: nil)
    }
    
    private func applyAssistiveOptimizations() {
        // Apply comprehensive assistive technology optimizations
        applyVoiceOverOptimizations()
        applyLargeTextOptimizations()
        applyHighContrastOptimizations()
        applyReducedMotionOptimizations()
    }
    
    private func setupVoiceOverSupport() {
        // Configure VoiceOver timer for announcements
        voiceOverTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { [weak self] _ in
            self?.processVoiceOverQueue()
        }
    }
    
    private func queueVoiceOverAnnouncement(_ message: String, priority: AnnouncementPriority) {
        let announcement = VoiceOverAnnouncement(
            message: message,
            priority: priority,
            timestamp: Date()
        )
        
        voiceOverAnnouncements.append(announcement)
        
        // Sort by priority and timestamp
        voiceOverAnnouncements.sort { first, second in
            if first.priority.rawValue != second.priority.rawValue {
                return first.priority.rawValue > second.priority.rawValue
            }
            return first.timestamp < second.timestamp
        }
        
        // Limit queue size
        if voiceOverAnnouncements.count > 10 {
            voiceOverAnnouncements = Array(voiceOverAnnouncements.prefix(10))
        }
    }
    
    private func processVoiceOverQueue() {
        guard !isVoiceOverSpeaking && !voiceOverAnnouncements.isEmpty else { return }
        
        let announcement = voiceOverAnnouncements.removeFirst()
        speakVoiceOverAnnouncement(announcement.message)
    }
    
    private func speakVoiceOverAnnouncement(_ message: String) {
        isVoiceOverSpeaking = true
        
        UIAccessibility.post(notification: .announcement, argument: message)
        
        // Estimate speaking time and reset flag
        let estimatedDuration = Double(message.count) * 0.05 // ~50ms per character
        DispatchQueue.main.asyncAfter(deadline: .now() + estimatedDuration) {
            self.isVoiceOverSpeaking = false
        }
    }
    
    private func announceWorkoutStart() {
        queueVoiceOverAnnouncement("Workout starting. Follow the instructions and maintain proper form.", priority: .high)
    }
    
    private func generateExerciseInstructions(_ exercise: ExerciseData) -> String {
        var instructions = "Next exercise: \(exercise.name). "
        instructions += "Target: \(exercise.targetSets) sets of \(exercise.targetReps) repetitions. "
        
        if !exercise.instructions.isEmpty {
            instructions += "Instructions: \(exercise.instructions.first ?? "")"
        }
        
        return instructions
    }
    
    private func generateFormFeedbackAnnouncement(_ feedback: FormFeedback) -> String {
        switch feedback.severity {
        case .high:
            return "Form correction needed: \(feedback.message)"
        case .medium:
            return "Form adjustment: \(feedback.message)"
        case .low:
            return feedback.message
        }
    }
    
    private func enableDetailedAnnouncements() {
        // Enable more detailed VoiceOver announcements
    }
    
    private func disableAnimations() {
        // Disable or reduce animations
        NotificationCenter.default.post(name: .accessibilityAnimationsDisabled, object: nil)
    }
    
    private func applyHighContrastTheme() {
        // Apply high contrast theme
        NotificationCenter.default.post(name: .accessibilityHighContrastThemeApplied, object: nil)
    }
    
    private func getBaseFontSize(for textStyle: Font.TextStyle) -> CGFloat {
        switch textStyle {
        case .largeTitle: return 34
        case .title: return 28
        case .title2: return 22
        case .title3: return 20
        case .headline: return 17
        case .body: return 17
        case .callout: return 16
        case .subheadline: return 15
        case .footnote: return 13
        case .caption: return 12
        case .caption2: return 11
        @unknown default: return 17
        }
    }
    
    private func getContentSizeScaleFactor() -> CGFloat {
        switch preferredContentSizeCategory {
        case .extraSmall: return 0.8
        case .small: return 0.9
        case .medium: return 1.0
        case .large: return 1.1
        case .extraLarge: return 1.2
        case .extraExtraLarge: return 1.3
        case .extraExtraExtraLarge: return 1.4
        case .accessibilityMedium: return 1.6
        case .accessibilityLarge: return 1.8
        case .accessibilityExtraLarge: return 2.0
        case .accessibilityExtraExtraLarge: return 2.2
        case .accessibilityExtraExtraExtraLarge: return 2.4
        @unknown default: return 1.0
        }
    }
    
    private func saveAccessibilitySettings() {
        UserDefaults.standard.set(customAccessibilityLabels, forKey: "accessibility_labels")
        UserDefaults.standard.set(accessibilityHints, forKey: "accessibility_hints")
        UserDefaults.standard.set(accessibilityValues, forKey: "accessibility_values")
        UserDefaults.standard.set(currentAccessibilityMode.rawValue, forKey: "accessibility_mode")
    }
}

// MARK: - Supporting Types

struct VoiceOverAnnouncement {
    let message: String
    let priority: AnnouncementPriority
    let timestamp: Date
}

enum AnnouncementPriority: Int, CaseIterable {
    case low = 1
    case medium = 2
    case high = 3
    case critical = 4
}

extension ContentSizeCategory {
    init(_ uiContentSizeCategory: UIContentSizeCategory) {
        switch uiContentSizeCategory {
        case .extraSmall: self = .extraSmall
        case .small: self = .small
        case .medium: self = .medium
        case .large: self = .large
        case .extraLarge: self = .extraLarge
        case .extraExtraLarge: self = .extraExtraLarge
        case .extraExtraExtraLarge: self = .extraExtraExtraLarge
        case .accessibilityMedium: self = .accessibilityMedium
        case .accessibilityLarge: self = .accessibilityLarge
        case .accessibilityExtraLarge: self = .accessibilityExtraLarge
        case .accessibilityExtraExtraLarge: self = .accessibilityExtraExtraLarge
        case .accessibilityExtraExtraExtraLarge: self = .accessibilityExtraExtraExtraLarge
        default: self = .medium
        }
    }
    
    var isAccessibilityCategory: Bool {
        switch self {
        case .accessibilityMedium, .accessibilityLarge, .accessibilityExtraLarge,
             .accessibilityExtraExtraLarge, .accessibilityExtraExtraExtraLarge:
            return true
        default:
            return false
        }
    }
}

// MARK: - Notification Extensions

extension Notification.Name {
    static let accessibilityLargeTextEnabled = Notification.Name("accessibilityLargeTextEnabled")
    static let accessibilityHighContrastEnabled = Notification.Name("accessibilityHighContrastEnabled")
    static let accessibilityReducedMotionEnabled = Notification.Name("accessibilityReducedMotionEnabled")
    static let accessibilityAnimationsDisabled = Notification.Name("accessibilityAnimationsDisabled")
    static let accessibilityHighContrastThemeApplied = Notification.Name("accessibilityHighContrastThemeApplied")
}

// MARK: - Logger Category Extension
extension Logger.Category {
    static let accessibility = Logger.Category(rawValue: "accessibility")
}
