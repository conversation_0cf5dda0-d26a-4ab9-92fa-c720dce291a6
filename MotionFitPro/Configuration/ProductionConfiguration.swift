import Foundation

// MARK: - Production Configuration

/// Production-ready configuration settings for App Store release
struct ProductionConfiguration {
    
    // MARK: - App Information
    
    static let appVersion = "1.0.0"
    static let buildNumber = "1"
    static let bundleIdentifier = "com.motionfitpro.app"
    static let appName = "MotionFit Pro"
    
    // MARK: - Environment Settings
    
    static let environment: AppEnvironment = .production
    static let isDebugMode = false
    static let enableLogging = true
    static let logLevel: LogLevel = .info
    
    // MARK: - Performance Settings
    
    static let performanceConfiguration = PerformanceConfiguration(
        maxConcurrentMLTasks: 2,
        targetFrameRate: 30.0,
        memoryWarningThreshold: 200.0, // MB
        thermalThrottlingEnabled: true,
        adaptiveQualityEnabled: true,
        backgroundProcessingEnabled: false
    )
    
    // MARK: - ML Model Configuration
    
    static let mlConfiguration = MLConfiguration(
        exerciseClassificationThreshold: 0.7,
        formAnalysisThreshold: 0.6,
        confidenceThreshold: 0.5,
        temporalSmoothingEnabled: true,
        adaptiveLearningEnabled: false, // Disabled for v1.0
        modelOptimizationLevel: .balanced
    )
    
    // MARK: - AR Configuration
    
    static let arConfiguration = ARConfiguration(
        trackingQualityThreshold: 0.6,
        bodyTrackingEnabled: true,
        faceTrackingEnabled: false,
        handTrackingEnabled: false,
        environmentalUnderstandingEnabled: false,
        lightEstimationEnabled: true,
        occlusionEnabled: false
    )
    
    // MARK: - Privacy Settings
    
    static let privacyConfiguration = PrivacyConfiguration(
        dataCollectionEnabled: true,
        analyticsEnabled: false, // Disabled by default
        crashReportingEnabled: true,
        personalizedAdsEnabled: false,
        dataRetentionDays: 365,
        automaticDataDeletionEnabled: true,
        encryptionEnabled: true,
        biometricProtectionEnabled: true
    )
    
    // MARK: - Feature Flags
    
    static let featureFlags = FeatureFlags(
        advancedAnalyticsEnabled: false,
        socialFeaturesEnabled: false,
        cloudSyncEnabled: false,
        premiumFeaturesEnabled: false,
        betaFeaturesEnabled: false,
        experimentalMLModelsEnabled: false,
        advancedCoachingEnabled: true,
        multiUserSupportEnabled: false
    )
    
    // MARK: - API Configuration
    
    static let apiConfiguration = APIConfiguration(
        baseURL: "https://api.motionfitpro.com",
        timeout: 30.0,
        retryAttempts: 3,
        enableCaching: true,
        compressionEnabled: true,
        certificatePinningEnabled: true
    )
    
    // MARK: - Analytics Configuration
    
    static let analyticsConfiguration = AnalyticsConfiguration(
        enabled: false, // User opt-in required
        crashReportingEnabled: true,
        performanceMonitoringEnabled: true,
        userBehaviorTrackingEnabled: false,
        dataRetentionDays: 90,
        anonymizationEnabled: true,
        batchUploadEnabled: true,
        realtimeEnabled: false
    )
    
    // MARK: - Security Configuration
    
    static let securityConfiguration = SecurityConfiguration(
        encryptionAlgorithm: "AES-256-GCM",
        keyRotationIntervalDays: 90,
        certificatePinningEnabled: true,
        jailbreakDetectionEnabled: true,
        debuggerDetectionEnabled: true,
        tamperDetectionEnabled: true,
        biometricAuthenticationEnabled: true,
        sessionTimeoutMinutes: 30
    )
    
    // MARK: - Notification Configuration
    
    static let notificationConfiguration = NotificationConfiguration(
        workoutRemindersEnabled: true,
        achievementNotificationsEnabled: true,
        formTipsEnabled: true,
        weeklyProgressEnabled: true,
        marketingNotificationsEnabled: false,
        quietHoursEnabled: true,
        quietHoursStart: "22:00",
        quietHoursEnd: "08:00"
    )
    
    // MARK: - Accessibility Configuration
    
    static let accessibilityConfiguration = AccessibilityConfiguration(
        voiceOverSupport: true,
        dynamicTypeSupport: true,
        highContrastSupport: true,
        reducedMotionSupport: true,
        audioDescriptionsEnabled: true,
        hapticFeedbackEnabled: true,
        visualIndicatorsEnabled: true,
        alternativeInputMethodsEnabled: true
    )
    
    // MARK: - Localization Configuration
    
    static let localizationConfiguration = LocalizationConfiguration(
        defaultLanguage: "en",
        supportedLanguages: [
            "en", "es", "fr", "de", "it", "pt", "ja", "ko", "zh-Hans", "zh-Hant"
        ],
        fallbackLanguage: "en",
        rightToLeftSupport: false,
        numberFormatLocalization: true,
        dateFormatLocalization: true,
        currencyFormatLocalization: true
    )
    
    // MARK: - Quality Assurance
    
    static let qaConfiguration = QAConfiguration(
        crashReportingEnabled: true,
        performanceMonitoringEnabled: true,
        memoryLeakDetectionEnabled: true,
        networkMonitoringEnabled: true,
        userFeedbackEnabled: true,
        automaticBugReportingEnabled: false,
        testFlightBetaEnabled: false,
        internalTestingEnabled: false
    )
    
    // MARK: - App Store Configuration
    
    static let appStoreConfiguration = AppStoreConfiguration(
        reviewPromptEnabled: true,
        reviewPromptMinSessions: 10,
        reviewPromptCooldownDays: 365,
        ratingThreshold: 4.0,
        feedbackPromptEnabled: true,
        appStoreRatingEnabled: true,
        inAppPurchasesEnabled: false,
        subscriptionsEnabled: false
    )
}

// MARK: - Configuration Structures

struct PerformanceConfiguration {
    let maxConcurrentMLTasks: Int
    let targetFrameRate: Double
    let memoryWarningThreshold: Double
    let thermalThrottlingEnabled: Bool
    let adaptiveQualityEnabled: Bool
    let backgroundProcessingEnabled: Bool
}

struct MLConfiguration {
    let exerciseClassificationThreshold: Float
    let formAnalysisThreshold: Float
    let confidenceThreshold: Float
    let temporalSmoothingEnabled: Bool
    let adaptiveLearningEnabled: Bool
    let modelOptimizationLevel: ModelOptimizationLevel
    
    enum ModelOptimizationLevel {
        case performance, balanced, quality
    }
}

struct ARConfiguration {
    let trackingQualityThreshold: Float
    let bodyTrackingEnabled: Bool
    let faceTrackingEnabled: Bool
    let handTrackingEnabled: Bool
    let environmentalUnderstandingEnabled: Bool
    let lightEstimationEnabled: Bool
    let occlusionEnabled: Bool
}

struct PrivacyConfiguration {
    let dataCollectionEnabled: Bool
    let analyticsEnabled: Bool
    let crashReportingEnabled: Bool
    let personalizedAdsEnabled: Bool
    let dataRetentionDays: Int
    let automaticDataDeletionEnabled: Bool
    let encryptionEnabled: Bool
    let biometricProtectionEnabled: Bool
}

struct FeatureFlags {
    let advancedAnalyticsEnabled: Bool
    let socialFeaturesEnabled: Bool
    let cloudSyncEnabled: Bool
    let premiumFeaturesEnabled: Bool
    let betaFeaturesEnabled: Bool
    let experimentalMLModelsEnabled: Bool
    let advancedCoachingEnabled: Bool
    let multiUserSupportEnabled: Bool
}

struct APIConfiguration {
    let baseURL: String
    let timeout: TimeInterval
    let retryAttempts: Int
    let enableCaching: Bool
    let compressionEnabled: Bool
    let certificatePinningEnabled: Bool
}

struct AnalyticsConfiguration {
    let enabled: Bool
    let crashReportingEnabled: Bool
    let performanceMonitoringEnabled: Bool
    let userBehaviorTrackingEnabled: Bool
    let dataRetentionDays: Int
    let anonymizationEnabled: Bool
    let batchUploadEnabled: Bool
    let realtimeEnabled: Bool
}

struct SecurityConfiguration {
    let encryptionAlgorithm: String
    let keyRotationIntervalDays: Int
    let certificatePinningEnabled: Bool
    let jailbreakDetectionEnabled: Bool
    let debuggerDetectionEnabled: Bool
    let tamperDetectionEnabled: Bool
    let biometricAuthenticationEnabled: Bool
    let sessionTimeoutMinutes: Int
}

struct NotificationConfiguration {
    let workoutRemindersEnabled: Bool
    let achievementNotificationsEnabled: Bool
    let formTipsEnabled: Bool
    let weeklyProgressEnabled: Bool
    let marketingNotificationsEnabled: Bool
    let quietHoursEnabled: Bool
    let quietHoursStart: String
    let quietHoursEnd: String
}

struct AccessibilityConfiguration {
    let voiceOverSupport: Bool
    let dynamicTypeSupport: Bool
    let highContrastSupport: Bool
    let reducedMotionSupport: Bool
    let audioDescriptionsEnabled: Bool
    let hapticFeedbackEnabled: Bool
    let visualIndicatorsEnabled: Bool
    let alternativeInputMethodsEnabled: Bool
}

struct LocalizationConfiguration {
    let defaultLanguage: String
    let supportedLanguages: [String]
    let fallbackLanguage: String
    let rightToLeftSupport: Bool
    let numberFormatLocalization: Bool
    let dateFormatLocalization: Bool
    let currencyFormatLocalization: Bool
}

struct QAConfiguration {
    let crashReportingEnabled: Bool
    let performanceMonitoringEnabled: Bool
    let memoryLeakDetectionEnabled: Bool
    let networkMonitoringEnabled: Bool
    let userFeedbackEnabled: Bool
    let automaticBugReportingEnabled: Bool
    let testFlightBetaEnabled: Bool
    let internalTestingEnabled: Bool
}

struct AppStoreConfiguration {
    let reviewPromptEnabled: Bool
    let reviewPromptMinSessions: Int
    let reviewPromptCooldownDays: Int
    let ratingThreshold: Double
    let feedbackPromptEnabled: Bool
    let appStoreRatingEnabled: Bool
    let inAppPurchasesEnabled: Bool
    let subscriptionsEnabled: Bool
}

// MARK: - Environment Enum

enum AppEnvironment: String, CaseIterable {
    case development = "development"
    case staging = "staging"
    case production = "production"
    
    var isProduction: Bool {
        return self == .production
    }
    
    var allowsDebugging: Bool {
        return self != .production
    }
}

// MARK: - Configuration Validation

extension ProductionConfiguration {
    
    static func validateConfiguration() -> [String] {
        var issues: [String] = []
        
        // Validate critical settings for production
        if environment != .production {
            issues.append("Environment must be set to production")
        }
        
        if isDebugMode {
            issues.append("Debug mode must be disabled in production")
        }
        
        if !privacyConfiguration.encryptionEnabled {
            issues.append("Encryption must be enabled in production")
        }
        
        if featureFlags.betaFeaturesEnabled {
            issues.append("Beta features must be disabled in production")
        }
        
        if analyticsConfiguration.enabled && !privacyConfiguration.analyticsEnabled {
            issues.append("Analytics configuration mismatch")
        }
        
        return issues
    }
    
    static func isProductionReady() -> Bool {
        return validateConfiguration().isEmpty
    }
}
