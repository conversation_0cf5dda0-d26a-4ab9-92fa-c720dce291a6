import Foundation

/// Generates sample data for development and testing purposes
class SampleDataGenerator {
    static let shared = SampleDataGenerator()
    
    private init() {}
    
    // MARK: - Exercise Data Generation
    
    /// Generate sample exercise data
    func generateSampleExercises() -> [ExerciseData] {
        return [
            // Strength Exercises
            ExerciseData(
                name: "Push-ups",
                type: .pushUp,
                category: .strength,
                difficulty: .beginner,
                equipment: .none,
                duration: 180,
                description: "Classic upper body exercise targeting chest, shoulders, and triceps",
                instructions: [
                    "Start in plank position with hands shoulder-width apart",
                    "Lower your body until chest nearly touches the floor",
                    "Push back up to starting position",
                    "Keep your core engaged throughout the movement"
                ],
                muscleGroups: [.chest, .shoulders, .triceps, .core],
                caloriesBurned: 8.5,
                thumbnailName: "pushup_thumbnail",
                biomechanicalPoints: [
                    BiomechanicalPoint(jointName: "leftShoulder", targetAngle: 90, tolerance: 15, phase: .middle),
                    BiomechanicalPoint(jointName: "rightShoulder", targetAngle: 90, tolerance: 15, phase: .middle)
                ],
                commonMistakes: [
                    "Sagging hips",
                    "Flaring elbows too wide",
                    "Not going low enough",
                    "Rushing the movement"
                ],
                modifications: [
                    ExerciseModification(name: "<PERSON><PERSON>ush-ups", description: "Perform on knees for easier variation", difficultyChange: -1, equipmentRequired: nil),
                    ExerciseModification(name: "Incline Push-ups", description: "Hands elevated on bench or step", difficultyChange: -1, equipmentRequired: .bench)
                ],
                targetReps: 12,
                targetSets: 3
            ),
            
            ExerciseData(
                name: "Squats",
                type: .squat,
                category: .strength,
                difficulty: .beginner,
                equipment: .none,
                duration: 120,
                description: "Fundamental lower body exercise for legs and glutes",
                instructions: [
                    "Stand with feet shoulder-width apart",
                    "Lower your body as if sitting back into a chair",
                    "Keep your chest up and knees behind toes",
                    "Return to standing position"
                ],
                muscleGroups: [.quadriceps, .glutes, .hamstrings, .core],
                caloriesBurned: 7.2,
                thumbnailName: "squat_thumbnail",
                biomechanicalPoints: [
                    BiomechanicalPoint(jointName: "leftUpperLeg", targetAngle: 90, tolerance: 20, phase: .middle),
                    BiomechanicalPoint(jointName: "rightUpperLeg", targetAngle: 90, tolerance: 20, phase: .middle)
                ],
                commonMistakes: [
                    "Knees caving inward",
                    "Not going deep enough",
                    "Leaning too far forward",
                    "Rising on toes"
                ],
                targetReps: 15,
                targetSets: 3
            ),
            
            ExerciseData(
                name: "Plank",
                type: .plank,
                category: .core,
                difficulty: .intermediate,
                equipment: .none,
                duration: 60,
                description: "Isometric core strengthening exercise",
                instructions: [
                    "Start in push-up position",
                    "Lower to forearms",
                    "Keep body in straight line from head to heels",
                    "Hold position while breathing normally"
                ],
                muscleGroups: [.core, .shoulders, .back],
                caloriesBurned: 4.5,
                thumbnailName: "plank_thumbnail",
                targetReps: 1,
                targetSets: 3
            ),
            
            // Cardio Exercises
            ExerciseData(
                name: "Jumping Jacks",
                type: .jumpingJack,
                category: .cardio,
                difficulty: .beginner,
                equipment: .none,
                duration: 60,
                description: "Full-body cardio exercise",
                instructions: [
                    "Start standing with feet together, arms at sides",
                    "Jump feet apart while raising arms overhead",
                    "Jump back to starting position",
                    "Maintain steady rhythm"
                ],
                muscleGroups: [.fullBody],
                caloriesBurned: 12.0,
                thumbnailName: "jumping_jacks_thumbnail",
                targetReps: 30,
                targetSets: 3
            ),
            
            ExerciseData(
                name: "Burpees",
                type: .burpee,
                category: .plyometric,
                difficulty: .advanced,
                equipment: .none,
                duration: 90,
                description: "High-intensity full-body exercise",
                instructions: [
                    "Start standing",
                    "Drop into squat, place hands on floor",
                    "Jump feet back into plank",
                    "Do a push-up",
                    "Jump feet back to squat",
                    "Jump up with arms overhead"
                ],
                muscleGroups: [.fullBody],
                caloriesBurned: 15.0,
                thumbnailName: "burpee_thumbnail",
                targetReps: 8,
                targetSets: 3
            )
        ]
    }
    
    // MARK: - Workout Performance Data
    
    /// Generate sample workout performance data
    func generateSampleWorkoutPerformances() -> [WorkoutPerformance] {
        let baseDate = Date()
        var performances: [WorkoutPerformance] = []
        
        for i in 0..<10 {
            let date = Calendar.current.date(byAdding: .day, value: -i, to: baseDate) ?? baseDate
            
            let performance = WorkoutPerformance(
                workoutSessionId: UUID(),
                startTime: date,
                endTime: date.addingTimeInterval(Double.random(in: 1200...3600)),
                totalDuration: Double.random(in: 1200...3600),
                exercisesCompleted: Int.random(in: 3...8),
                totalExercises: Int.random(in: 5...10),
                averageFormScore: Double.random(in: 0.7...0.95),
                caloriesBurned: Double.random(in: 200...500),
                averageHeartRate: Int.random(in: 120...160),
                maxHeartRate: Int.random(in: 160...190),
                totalReps: Int.random(in: 50...150),
                totalSets: Int.random(in: 10...25),
                overallRating: Int.random(in: 3...5),
                notes: i % 3 == 0 ? "Great workout today!" : nil,
                achievements: i % 4 == 0 ? [generateSampleAchievement()] : [],
                personalRecords: i % 5 == 0 ? [generateSamplePersonalRecord()] : []
            )
            
            performances.append(performance)
        }
        
        return performances
    }
    
    // MARK: - Achievement Data
    
    /// Generate sample achievements
    func generateSampleAchievements() -> [Achievement] {
        return [
            Achievement(
                name: "First Workout",
                description: "Complete your first workout",
                category: .milestone,
                iconName: "star.fill",
                unlockedDate: Date(),
                progress: 1.0,
                target: 1.0,
                current: 1.0,
                isUnlocked: true,
                rarity: .common,
                points: 10
            ),
            
            Achievement(
                name: "Week Warrior",
                description: "Complete 5 workouts in a week",
                category: .consistency,
                iconName: "calendar.badge.checkmark",
                unlockedDate: nil,
                progress: 0.6,
                target: 5.0,
                current: 3.0,
                isUnlocked: false,
                rarity: .uncommon,
                points: 25
            ),
            
            Achievement(
                name: "Perfect Form",
                description: "Achieve 95% form score in a workout",
                category: .form,
                iconName: "target",
                unlockedDate: Date().addingTimeInterval(-86400),
                progress: 1.0,
                target: 1.0,
                current: 1.0,
                isUnlocked: true,
                rarity: .rare,
                points: 50
            ),
            
            Achievement(
                name: "Strength Master",
                description: "Complete 100 strength exercises",
                category: .strength,
                iconName: "dumbbell.fill",
                unlockedDate: nil,
                progress: 0.75,
                target: 100.0,
                current: 75.0,
                isUnlocked: false,
                rarity: .epic,
                points: 100
            ),
            
            Achievement(
                name: "Endurance Legend",
                description: "Complete 10 hours of cardio workouts",
                category: .endurance,
                iconName: "heart.fill",
                unlockedDate: nil,
                progress: 0.3,
                target: 36000.0, // 10 hours in seconds
                current: 10800.0, // 3 hours
                isUnlocked: false,
                rarity: .legendary,
                points: 200
            )
        ]
    }
    
    // MARK: - Personal Records
    
    /// Generate sample personal records
    func generateSamplePersonalRecords() -> [PersonalRecord] {
        return [
            PersonalRecord(
                exerciseType: .pushUp,
                recordType: .maxReps,
                value: 25,
                unit: "reps",
                achievedDate: Date().addingTimeInterval(-172800), // 2 days ago
                previousRecord: 22,
                improvement: 3,
                workoutSessionId: UUID(),
                notes: "New personal best!"
            ),
            
            PersonalRecord(
                exerciseType: .plank,
                recordType: .longestHold,
                value: 150, // 2.5 minutes
                unit: "seconds",
                achievedDate: Date().addingTimeInterval(-604800), // 1 week ago
                previousRecord: 120,
                improvement: 30,
                workoutSessionId: UUID()
            ),
            
            PersonalRecord(
                exerciseType: .squat,
                recordType: .maxReps,
                value: 30,
                unit: "reps",
                achievedDate: Date().addingTimeInterval(-259200), // 3 days ago
                previousRecord: 25,
                improvement: 5,
                workoutSessionId: UUID()
            )
        ]
    }
    
    // MARK: - User Profile Data
    
    /// Generate sample user profile
    func generateSampleUserProfile() -> UserProfile {
        return UserProfile(
            id: UUID(),
            name: "Alex Johnson",
            email: "<EMAIL>",
            fitnessLevel: .intermediate,
            preferredWorkoutDuration: .medium,
            coachingPersonality: .encouraging,
            memberSince: Calendar.current.date(byAdding: .month, value: -3, to: Date()) ?? Date()
        )
    }
    
    // MARK: - Helper Methods
    
    private func generateSampleAchievement() -> Achievement {
        let achievements = generateSampleAchievements()
        return achievements.randomElement() ?? achievements[0]
    }
    
    private func generateSamplePersonalRecord() -> PersonalRecord {
        let records = generateSamplePersonalRecords()
        return records.randomElement() ?? records[0]
    }
}

// ExerciseType is now defined in Data/Models/SwiftData/ExerciseType.swift

// UserProfile is now defined in Data/Models/SwiftData/UserProfile.swift

// MARK: - Sample Data Extensions
extension ExerciseData {
    static var samples: [ExerciseData] {
        return SampleDataGenerator.shared.generateSampleExercises()
    }
}

extension WorkoutPerformance {
    static var samples: [WorkoutPerformance] {
        return SampleDataGenerator.shared.generateSampleWorkoutPerformances()
    }
}

extension Achievement {
    static var samples: [Achievement] {
        return SampleDataGenerator.shared.generateSampleAchievements()
    }
}

extension PersonalRecord {
    static var samples: [PersonalRecord] {
        return SampleDataGenerator.shared.generateSamplePersonalRecords()
    }
}

extension UserProfile {
    static var sample: UserProfile {
        return SampleDataGenerator.shared.generateSampleUserProfile()
    }
}
