import UserNotifications
import Foundation

/// Manages local notifications for workout reminders and achievements
@MainActor
class NotificationManager: ObservableObject {
    static let shared = NotificationManager()
    
    // MARK: - Properties
    @Published var isNotificationsEnabled = false
    @Published var workoutRemindersEnabled = true
    @Published var achievementNotificationsEnabled = true
    @Published var dailyMotivationEnabled = true
    
    private let notificationCenter = UNUserNotificationCenter.current()
    private let logger = Logger.shared
    
    // Notification identifiers
    private enum NotificationIdentifier {
        static let workoutReminder = "workout_reminder"
        static let dailyMotivation = "daily_motivation"
        static let achievement = "achievement"
        static let streakReminder = "streak_reminder"
        static let weeklyProgress = "weekly_progress"
    }
    
    // MARK: - Initialization
    private init() {
        setupNotificationDelegate()
        loadUserPreferences()
        checkNotificationPermissions()
    }
    
    // MARK: - Public Interface
    
    /// Request notification permissions
    func requestPermissions() async -> Bool {
        do {
            let granted = try await notificationCenter.requestAuthorization(options: [.alert, .badge, .sound])
            await MainActor.run {
                isNotificationsEnabled = granted
                if granted {
                    logger.info("Notification permissions granted", category: .notifications)
                    scheduleDefaultNotifications()
                } else {
                    logger.warning("Notification permissions denied", category: .notifications)
                }
            }
            return granted
        } catch {
            logger.error("Failed to request notification permissions: \(error)", category: .notifications)
            return false
        }
    }
    
    /// Schedule workout reminder notification
    func scheduleWorkoutReminder(at time: DateComponents, message: String? = nil) {
        guard isNotificationsEnabled && workoutRemindersEnabled else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "Time to Work Out!"
        content.body = message ?? "Your body is ready for some movement. Let's get started!"
        content.sound = .default
        content.badge = 1
        
        let trigger = UNCalendarNotificationTrigger(dateMatching: time, repeats: true)
        let request = UNNotificationRequest(
            identifier: NotificationIdentifier.workoutReminder,
            content: content,
            trigger: trigger
        )
        
        notificationCenter.add(request) { [weak self] error in
            if let error = error {
                self?.logger.error("Failed to schedule workout reminder: \(error)", category: .notifications)
            } else {
                self?.logger.info("Scheduled workout reminder", category: .notifications)
            }
        }
    }
    
    /// Schedule achievement notification
    func scheduleAchievementNotification(for achievement: Achievement) {
        guard isNotificationsEnabled && achievementNotificationsEnabled else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "Achievement Unlocked! 🏆"
        content.body = "\(achievement.name): \(achievement.description)"
        content.sound = .default
        content.badge = 1
        
        // Add custom data
        content.userInfo = [
            "type": "achievement",
            "achievementId": achievement.id.uuidString
        ]
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false)
        let request = UNNotificationRequest(
            identifier: "\(NotificationIdentifier.achievement)_\(achievement.id.uuidString)",
            content: content,
            trigger: trigger
        )
        
        notificationCenter.add(request) { [weak self] error in
            if let error = error {
                self?.logger.error("Failed to schedule achievement notification: \(error)", category: .notifications)
            } else {
                self?.logger.info("Scheduled achievement notification for: \(achievement.name)", category: .notifications)
            }
        }
    }
    
    /// Schedule daily motivation notification
    func scheduleDailyMotivation(at time: DateComponents) {
        guard isNotificationsEnabled && dailyMotivationEnabled else { return }
        
        let motivationalMessages = [
            "Every workout counts! Start your day strong 💪",
            "Your future self will thank you for today's effort!",
            "Progress, not perfection. Let's move! 🔥",
            "Champions are made in the gym. Are you ready?",
            "Your only competition is who you were yesterday!",
            "Strong body, strong mind. Time to train! 🧠",
            "The hardest part is showing up. You've got this!",
            "Sweat today, smile tomorrow! 😊"
        ]
        
        let content = UNMutableNotificationContent()
        content.title = "Daily Motivation"
        content.body = motivationalMessages.randomElement() ?? "Time to get moving!"
        content.sound = .default
        
        let trigger = UNCalendarNotificationTrigger(dateMatching: time, repeats: true)
        let request = UNNotificationRequest(
            identifier: NotificationIdentifier.dailyMotivation,
            content: content,
            trigger: trigger
        )
        
        notificationCenter.add(request) { [weak self] error in
            if let error = error {
                self?.logger.error("Failed to schedule daily motivation: \(error)", category: .notifications)
            } else {
                self?.logger.info("Scheduled daily motivation", category: .notifications)
            }
        }
    }
    
    /// Schedule streak reminder notification
    func scheduleStreakReminder(streakDays: Int) {
        guard isNotificationsEnabled else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "Don't Break Your Streak! 🔥"
        content.body = "You're on a \(streakDays)-day workout streak! Keep it going!"
        content.sound = .default
        content.badge = 1
        
        // Schedule for evening if no workout today
        var dateComponents = DateComponents()
        dateComponents.hour = 19 // 7 PM
        dateComponents.minute = 0
        
        let trigger = UNCalendarNotificationTrigger(dateMatching: dateComponents, repeats: false)
        let request = UNNotificationRequest(
            identifier: NotificationIdentifier.streakReminder,
            content: content,
            trigger: trigger
        )
        
        notificationCenter.add(request) { [weak self] error in
            if let error = error {
                self?.logger.error("Failed to schedule streak reminder: \(error)", category: .notifications)
            }
        }
    }
    
    /// Schedule weekly progress notification
    func scheduleWeeklyProgress(workoutsCompleted: Int, totalWorkouts: Int) {
        guard isNotificationsEnabled else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "Weekly Progress Report 📊"
        content.body = "This week: \(workoutsCompleted)/\(totalWorkouts) workouts completed. Keep pushing!"
        content.sound = .default
        
        // Schedule for Sunday evening
        var dateComponents = DateComponents()
        dateComponents.weekday = 1 // Sunday
        dateComponents.hour = 18
        dateComponents.minute = 0
        
        let trigger = UNCalendarNotificationTrigger(dateMatching: dateComponents, repeats: true)
        let request = UNNotificationRequest(
            identifier: NotificationIdentifier.weeklyProgress,
            content: content,
            trigger: trigger
        )
        
        notificationCenter.add(request) { [weak self] error in
            if let error = error {
                self?.logger.error("Failed to schedule weekly progress: \(error)", category: .notifications)
            }
        }
    }
    
    /// Cancel specific notification
    func cancelNotification(withIdentifier identifier: String) {
        notificationCenter.removePendingNotificationRequests(withIdentifiers: [identifier])
        logger.info("Cancelled notification: \(identifier)", category: .notifications)
    }
    
    /// Cancel all notifications
    func cancelAllNotifications() {
        notificationCenter.removeAllPendingNotificationRequests()
        notificationCenter.removeAllDeliveredNotifications()
        logger.info("Cancelled all notifications", category: .notifications)
    }
    
    /// Get pending notifications
    func getPendingNotifications() async -> [UNNotificationRequest] {
        return await notificationCenter.pendingNotificationRequests()
    }
    
    // MARK: - Settings
    
    func setWorkoutRemindersEnabled(_ enabled: Bool) {
        workoutRemindersEnabled = enabled
        UserDefaults.standard.set(enabled, forKey: "workout_reminders_enabled")
        
        if !enabled {
            cancelNotification(withIdentifier: NotificationIdentifier.workoutReminder)
        }
    }
    
    func setAchievementNotificationsEnabled(_ enabled: Bool) {
        achievementNotificationsEnabled = enabled
        UserDefaults.standard.set(enabled, forKey: "achievement_notifications_enabled")
    }
    
    func setDailyMotivationEnabled(_ enabled: Bool) {
        dailyMotivationEnabled = enabled
        UserDefaults.standard.set(enabled, forKey: "daily_motivation_enabled")
        
        if !enabled {
            cancelNotification(withIdentifier: NotificationIdentifier.dailyMotivation)
        } else {
            // Reschedule with default time (8 AM)
            var dateComponents = DateComponents()
            dateComponents.hour = 8
            dateComponents.minute = 0
            scheduleDailyMotivation(at: dateComponents)
        }
    }
    
    // MARK: - Private Methods
    
    private func setupNotificationDelegate() {
        notificationCenter.delegate = self
    }
    
    private func loadUserPreferences() {
        workoutRemindersEnabled = UserDefaults.standard.bool(forKey: "workout_reminders_enabled")
        achievementNotificationsEnabled = UserDefaults.standard.bool(forKey: "achievement_notifications_enabled")
        dailyMotivationEnabled = UserDefaults.standard.bool(forKey: "daily_motivation_enabled")
        
        // Set defaults if first time
        if !UserDefaults.standard.bool(forKey: "notifications_preferences_set") {
            workoutRemindersEnabled = true
            achievementNotificationsEnabled = true
            dailyMotivationEnabled = true
            UserDefaults.standard.set(true, forKey: "notifications_preferences_set")
        }
    }
    
    private func checkNotificationPermissions() {
        notificationCenter.getNotificationSettings { [weak self] settings in
            DispatchQueue.main.async {
                self?.isNotificationsEnabled = settings.authorizationStatus == .authorized
            }
        }
    }
    
    private func scheduleDefaultNotifications() {
        // Schedule default workout reminder for 6 PM
        var workoutTime = DateComponents()
        workoutTime.hour = 18
        workoutTime.minute = 0
        scheduleWorkoutReminder(at: workoutTime)
        
        // Schedule default daily motivation for 8 AM
        var motivationTime = DateComponents()
        motivationTime.hour = 8
        motivationTime.minute = 0
        scheduleDailyMotivation(at: motivationTime)
    }
}

// MARK: - UNUserNotificationCenterDelegate
extension NotificationManager: UNUserNotificationCenterDelegate {
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        // Show notification even when app is in foreground
        completionHandler([.banner, .sound, .badge])
    }
    
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        let userInfo = response.notification.request.content.userInfo
        
        // Handle notification tap
        if let type = userInfo["type"] as? String {
            switch type {
            case "achievement":
                if let achievementId = userInfo["achievementId"] as? String {
                    handleAchievementNotificationTap(achievementId: achievementId)
                }
            default:
                break
            }
        }
        
        completionHandler()
    }
    
    private func handleAchievementNotificationTap(achievementId: String) {
        // Navigate to achievements view or show achievement details
        logger.info("User tapped achievement notification: \(achievementId)", category: .notifications)
        // This would typically post a notification or use a coordinator to navigate
    }
}

// MARK: - Logger Category Extension
extension Logger.Category {
    static let notifications = Logger.Category(rawValue: "notifications")
}
