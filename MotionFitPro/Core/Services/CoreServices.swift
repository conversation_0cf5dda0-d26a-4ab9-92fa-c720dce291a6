import Foundation
import Combine
import AVFoundation
import CoreHaptics

// MARK: - Core Service Implementations

/// Concrete implementation of logging service
@MainActor
final class LoggerService: LoggerServiceProtocol, ObservableObject {
    private let logger = Logger()
    
    func log(_ message: String, level: LogLevel, category: Logger.Category) {
        switch level {
        case .debug:
            logger.debug("\(message)", category: category)
        case .info:
            logger.info("\(message)", category: category)
        case .warning:
            logger.warning("\(message)", category: category)
        case .error, .critical:
            logger.error("\(message)", category: category)
        }
    }
    
    func debug(_ message: String, category: Logger.Category) {
        log(message, level: .debug, category: category)
    }
    
    func info(_ message: String, category: Logger.Category) {
        log(message, level: .info, category: category)
    }
    
    func warning(_ message: String, category: Logger.Category) {
        log(message, level: .warning, category: category)
    }
    
    func error(_ message: String, category: Logger.Category) {
        log(message, level: .error, category: category)
    }
}

/// Concrete implementation of haptic feedback service
@MainActor
final class HapticService: HapticServiceProtocol, ObservableObject {
    private var hapticEngine: CHHapticEngine?
    private let logger = Logger()
    
    init() {
        setupHapticEngine()
    }
    
    private func setupHapticEngine() {
        guard CHHapticEngine.capabilitiesForHardware().supportsHaptics else {
            logger.warning("Haptics not supported on this device", category: .haptics)
            return
        }
        
        do {
            hapticEngine = try CHHapticEngine()
            try hapticEngine?.start()
        } catch {
            logger.error("Failed to create haptic engine: \(error)", category: .haptics)
        }
    }
    
    func trigger(_ type: HapticType) {
        guard let engine = hapticEngine else { return }
        
        do {
            let pattern = createHapticPattern(for: type)
            let player = try engine.makePlayer(with: pattern)
            try player.start(atTime: 0)
        } catch {
            logger.error("Failed to play haptic: \(error)", category: .haptics)
        }
    }
    
    func triggerSuccess() {
        trigger(.success)
    }
    
    func triggerWarning() {
        trigger(.warning)
    }
    
    func triggerError() {
        trigger(.error)
    }
    
    func triggerSelection() {
        trigger(.selection)
    }
    
    private func createHapticPattern(for type: HapticType) -> CHHapticPattern {
        var events: [CHHapticEvent] = []
        
        switch type {
        case .success:
            events = [
                CHHapticEvent(eventType: .hapticTransient, parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: 0.8),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: 0.5)
                ], relativeTime: 0)
            ]
        case .warning:
            events = [
                CHHapticEvent(eventType: .hapticTransient, parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: 0.6),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: 0.8)
                ], relativeTime: 0),
                CHHapticEvent(eventType: .hapticTransient, parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: 0.6),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: 0.8)
                ], relativeTime: 0.1)
            ]
        case .error:
            events = [
                CHHapticEvent(eventType: .hapticTransient, parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: 1.0),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: 1.0)
                ], relativeTime: 0),
                CHHapticEvent(eventType: .hapticTransient, parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: 1.0),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: 1.0)
                ], relativeTime: 0.1),
                CHHapticEvent(eventType: .hapticTransient, parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: 1.0),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: 1.0)
                ], relativeTime: 0.2)
            ]
        case .selection:
            events = [
                CHHapticEvent(eventType: .hapticTransient, parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: 0.4),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: 0.3)
                ], relativeTime: 0)
            ]
        default:
            events = [
                CHHapticEvent(eventType: .hapticTransient, parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: 0.5),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: 0.5)
                ], relativeTime: 0)
            ]
        }
        
        do {
            return try CHHapticPattern(events: events, parameters: [])
        } catch {
            logger.error("Failed to create haptic pattern: \(error)", category: .haptics)
            // Return empty pattern as fallback
            return try! CHHapticPattern(events: [], parameters: [])
        }
    }
}

/// Concrete implementation of audio service
@MainActor
final class AudioService: NSObject, AudioServiceProtocol, ObservableObject {
    private let synthesizer = AVSpeechSynthesizer()
    private let audioEngine = AVAudioEngine()
    private var speechQueue: [SpeechItem] = []
    private let logger = Logger()
    
    @Published var isSpeaking = false
    
    override init() {
        super.init()
        synthesizer.delegate = self
        setupAudioSession()
    }
    
    private func setupAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default)
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            logger.error("Failed to setup audio session: \(error)", category: .audio)
        }
    }
    
    func playSound(_ effect: SoundEffect) {
        // Implementation for playing sound effects
        logger.info("Playing sound effect: \(effect.displayName)", category: .audio)
        // TODO: Implement actual sound playback
    }
    
    func speak(_ text: String, priority: SpeechPriority) {
        let speechItem = SpeechItem(text: text, priority: priority)
        
        if priority == .urgent {
            // Insert at beginning for urgent messages
            speechQueue.insert(speechItem, at: 0)
            if isSpeaking {
                synthesizer.stopSpeaking(at: .immediate)
            }
        } else {
            speechQueue.append(speechItem)
        }
        
        processNextSpeechItem()
    }
    
    func pauseSpeech() {
        synthesizer.pauseSpeaking(at: .immediate)
    }
    
    func resumeSpeech() {
        synthesizer.continueSpeaking()
    }
    
    func stopSpeech() {
        synthesizer.stopSpeaking(at: .immediate)
        speechQueue.removeAll()
        isSpeaking = false
    }
    
    private func processNextSpeechItem() {
        guard !isSpeaking, !speechQueue.isEmpty else { return }
        
        let nextItem = speechQueue.removeFirst()
        let utterance = AVSpeechUtterance(string: nextItem.text)
        utterance.rate = 0.5
        utterance.volume = 0.8
        
        isSpeaking = true
        synthesizer.speak(utterance)
    }
}

// MARK: - AVSpeechSynthesizerDelegate

extension AudioService: AVSpeechSynthesizerDelegate {
    nonisolated func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didFinish utterance: AVSpeechUtterance) {
        Task { @MainActor in
            isSpeaking = false
            processNextSpeechItem()
        }
    }
    
    nonisolated func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didCancel utterance: AVSpeechUtterance) {
        Task { @MainActor in
            isSpeaking = false
            processNextSpeechItem()
        }
    }
}

// MARK: - Supporting Types

private struct SpeechItem {
    let text: String
    let priority: SpeechPriority
    let timestamp: Date
    
    init(text: String, priority: SpeechPriority) {
        self.text = text
        self.priority = priority
        self.timestamp = Date()
    }
}
