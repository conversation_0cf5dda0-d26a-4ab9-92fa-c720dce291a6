import Foundation

struct AppConstants {
    
    // MARK: - App Configuration
    struct App {
        static let name = "MotionFitPro"
        static let version = "1.0.0"
        static let bundleIdentifier = "com.motionfitpro.app"
        static let minimumIOSVersion = "17.0"
    }
    
    // MARK: - UI Configuration
    struct UI {
        static let animationDuration: TimeInterval = 0.3
        static let cornerRadius: CGFloat = 12
        static let shadowRadius: CGFloat = 4
        static let primarySpacing: CGFloat = 16
        static let secondarySpacing: CGFloat = 8
        static let buttonHeight: CGFloat = 50
        static let cardPadding: CGFloat = 20
    }
}