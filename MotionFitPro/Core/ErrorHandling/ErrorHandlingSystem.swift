import Foundation
import Combine
import SwiftUI

// MARK: - Application Error Types

/// Comprehensive error types for the application
enum AppError: LocalizedError, Equatable {
    // Data Errors
    case dataCorrupted
    case dataNotFound
    case dataSaveFailed(String)
    case dataLoadFailed(String)
    case invalidData(String)
    
    // Network Errors
    case networkUnavailable
    case networkTimeout
    case serverError(Int)
    case invalidResponse
    case authenticationFailed
    
    // AR/ML Errors
    case arNotSupported
    case arTrackingFailed
    case mlModelNotFound
    case mlInferenceFailed(String)
    case poseDetectionFailed
    
    // Permission Errors
    case cameraPermissionDenied
    case microphonePermissionDenied
    case motionPermissionDenied
    case healthKitPermissionDenied
    
    // User Errors
    case invalidInput(String)
    case userNotFound
    case profileIncomplete
    case workoutInProgress
    
    // System Errors
    case memoryWarning
    case diskSpaceLow
    case batteryLow
    case deviceNotSupported
    case unknownError(String)
    
    var errorDescription: String? {
        switch self {
        // Data Errors
        case .dataCorrupted:
            return "Data has been corrupted and cannot be read"
        case .dataNotFound:
            return "Requested data could not be found"
        case .dataSaveFailed(let details):
            return "Failed to save data: \(details)"
        case .dataLoadFailed(let details):
            return "Failed to load data: \(details)"
        case .invalidData(let details):
            return "Invalid data format: \(details)"
            
        // Network Errors
        case .networkUnavailable:
            return "Network connection is not available"
        case .networkTimeout:
            return "Network request timed out"
        case .serverError(let code):
            return "Server error occurred (Code: \(code))"
        case .invalidResponse:
            return "Invalid response received from server"
        case .authenticationFailed:
            return "Authentication failed"
            
        // AR/ML Errors
        case .arNotSupported:
            return "AR is not supported on this device"
        case .arTrackingFailed:
            return "AR tracking failed to initialize"
        case .mlModelNotFound:
            return "Required ML model could not be found"
        case .mlInferenceFailed(let details):
            return "ML inference failed: \(details)"
        case .poseDetectionFailed:
            return "Failed to detect body pose"
            
        // Permission Errors
        case .cameraPermissionDenied:
            return "Camera permission is required for AR tracking"
        case .microphonePermissionDenied:
            return "Microphone permission is required for voice feedback"
        case .motionPermissionDenied:
            return "Motion permission is required for activity tracking"
        case .healthKitPermissionDenied:
            return "HealthKit permission is required for health data"
            
        // User Errors
        case .invalidInput(let details):
            return "Invalid input: \(details)"
        case .userNotFound:
            return "User profile not found"
        case .profileIncomplete:
            return "User profile is incomplete"
        case .workoutInProgress:
            return "A workout is already in progress"
            
        // System Errors
        case .memoryWarning:
            return "Low memory warning - some features may be limited"
        case .diskSpaceLow:
            return "Low disk space - please free up storage"
        case .batteryLow:
            return "Low battery - consider charging your device"
        case .deviceNotSupported:
            return "This device is not supported"
        case .unknownError(let details):
            return "An unknown error occurred: \(details)"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .dataCorrupted, .dataNotFound:
            return "Try restarting the app or contact support if the problem persists"
        case .networkUnavailable, .networkTimeout:
            return "Check your internet connection and try again"
        case .cameraPermissionDenied:
            return "Go to Settings > Privacy > Camera to enable camera access"
        case .microphonePermissionDenied:
            return "Go to Settings > Privacy > Microphone to enable microphone access"
        case .arNotSupported:
            return "This feature requires a device with AR capabilities"
        case .memoryWarning:
            return "Close other apps to free up memory"
        case .diskSpaceLow:
            return "Delete unused files or apps to free up storage space"
        case .batteryLow:
            return "Connect your device to a charger"
        default:
            return "Please try again or contact support if the problem persists"
        }
    }
    
    var severity: ErrorSeverity {
        switch self {
        case .memoryWarning, .diskSpaceLow, .batteryLow:
            return .warning
        case .dataCorrupted, .arTrackingFailed, .mlInferenceFailed:
            return .high
        case .deviceNotSupported, .arNotSupported:
            return .critical
        default:
            return .medium
        }
    }
}

/// Error severity levels
enum ErrorSeverity: String, CaseIterable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case warning = "warning"
    case critical = "critical"
    
    var displayName: String {
        switch self {
        case .low: return "Minor"
        case .medium: return "Moderate"
        case .high: return "Important"
        case .warning: return "Warning"
        case .critical: return "Critical"
        }
    }
    
    var color: Color {
        switch self {
        case .low: return .blue
        case .medium: return .yellow
        case .high: return .orange
        case .warning: return .yellow
        case .critical: return .red
        }
    }
}

// MARK: - Error Handler

/// Centralized error handling service
@MainActor
final class ErrorHandler: ObservableObject {
    static let shared = ErrorHandler()
    
    @Published var currentError: AppError?
    @Published var errorHistory: [ErrorEvent] = []
    
    private let logger = Logger()
    private let maxHistoryCount = 100
    
    private init() {}
    
    /// Handle an error with optional user notification
    func handle(_ error: AppError, showToUser: Bool = true, context: String? = nil) {
        let errorEvent = ErrorEvent(
            error: error,
            context: context,
            timestamp: Date()
        )
        
        // Log the error
        logError(errorEvent)
        
        // Add to history
        addToHistory(errorEvent)
        
        // Show to user if requested
        if showToUser {
            currentError = error
        }
        
        // Handle specific error types
        handleSpecificError(error)
    }
    
    /// Handle errors from Result types
    func handle<T>(_ result: Result<T, Error>, context: String? = nil) -> T? {
        switch result {
        case .success(let value):
            return value
        case .failure(let error):
            let appError = convertToAppError(error)
            handle(appError, context: context)
            return nil
        }
    }
    
    /// Handle errors from async operations
    func handleAsync<T>(_ operation: @escaping () async throws -> T, context: String? = nil) async -> T? {
        do {
            return try await operation()
        } catch {
            let appError = convertToAppError(error)
            handle(appError, context: context)
            return nil
        }
    }
    
    /// Clear current error
    func clearCurrentError() {
        currentError = nil
    }
    
    /// Clear error history
    func clearHistory() {
        errorHistory.removeAll()
    }
    
    private func logError(_ errorEvent: ErrorEvent) {
        let message = "Error: \(errorEvent.error.localizedDescription)"
        let contextMessage = errorEvent.context.map { " Context: \($0)" } ?? ""
        
        switch errorEvent.error.severity {
        case .low:
            logger.debug("\(message)\(contextMessage)", category: .error)
        case .medium, .warning:
            logger.warning("\(message)\(contextMessage)", category: .error)
        case .high, .critical:
            logger.error("\(message)\(contextMessage)", category: .error)
        }
    }
    
    private func addToHistory(_ errorEvent: ErrorEvent) {
        errorHistory.insert(errorEvent, at: 0)
        
        // Limit history size
        if errorHistory.count > maxHistoryCount {
            errorHistory.removeLast()
        }
    }
    
    private func handleSpecificError(_ error: AppError) {
        switch error {
        case .memoryWarning:
            // Trigger memory cleanup
            NotificationCenter.default.post(name: .memoryWarning, object: nil)
            
        case .batteryLow:
            // Reduce performance intensive operations
            NotificationCenter.default.post(name: .batteryLow, object: nil)
            
        case .arTrackingFailed:
            // Attempt to restart AR session
            NotificationCenter.default.post(name: .restartARSession, object: nil)
            
        default:
            break
        }
    }
    
    private func convertToAppError(_ error: Error) -> AppError {
        if let appError = error as? AppError {
            return appError
        }
        
        // Convert common system errors
        if let urlError = error as? URLError {
            switch urlError.code {
            case .notConnectedToInternet:
                return .networkUnavailable
            case .timedOut:
                return .networkTimeout
            default:
                return .unknownError(urlError.localizedDescription)
            }
        }
        
        return .unknownError(error.localizedDescription)
    }
}

// MARK: - Error Event

struct ErrorEvent: Identifiable {
    let id = UUID()
    let error: AppError
    let context: String?
    let timestamp: Date
}

// MARK: - Notification Names

extension Notification.Name {
    static let memoryWarning = Notification.Name("memoryWarning")
    static let batteryLow = Notification.Name("batteryLow")
    static let restartARSession = Notification.Name("restartARSession")
}

// MARK: - Error View Modifier

struct ErrorHandlingModifier: ViewModifier {
    @StateObject private var errorHandler = ErrorHandler.shared
    @State private var showingError = false
    
    func body(content: Content) -> some View {
        content
            .alert("Error", isPresented: $showingError, presenting: errorHandler.currentError) { error in
                Button("OK") {
                    errorHandler.clearCurrentError()
                }
                
                if error.recoverySuggestion != nil {
                    Button("Help") {
                        // Show help or recovery options
                    }
                }
            } message: { error in
                VStack(alignment: .leading, spacing: 8) {
                    Text(error.localizedDescription)
                    
                    if let suggestion = error.recoverySuggestion {
                        Text(suggestion)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .onChange(of: errorHandler.currentError) { _, newError in
                showingError = newError != nil
            }
    }
}

extension View {
    func withErrorHandling() -> some View {
        modifier(ErrorHandlingModifier())
    }
}
