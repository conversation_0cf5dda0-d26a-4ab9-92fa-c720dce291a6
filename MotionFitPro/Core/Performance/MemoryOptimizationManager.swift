import Foundation
import UIKit
import ARKit
import CoreML
import AVFoundation

@MainActor
class MemoryOptimizationManager: ObservableObject {
    static let shared = MemoryOptimizationManager()
    
    @Published var currentMemoryUsage: MemoryUsage = MemoryUsage(used: 0, available: 0)
    @Published var optimizationLevel: MemoryOptimizationLevel = .balanced
    @Published var isLowMemoryMode = false
    
    // Memory pools
    private let imageAssetPool = AssetPool<UIImage>()
    private let texturePool = AssetPool<MTLTexture>()
    private let audioBufferPool = AssetPool<AVAudioPCMBuffer>()
    private let arFramePool = ARFramePool()
    
    // Cache management
    private let cacheManager = CacheManager()
    private let assetManager = OptimizedAssetManager()
    
    // Memory monitoring
    private var memoryPressureSource: DispatchSourceMemoryPressure?
    private var memoryCheckTimer: Timer?
    private var lastCleanupTime = Date()
    
    // Optimization thresholds
    private let memoryThresholds = MemoryThresholds()
    
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        setupMemoryPressureMonitoring()
        setupPeriodicCleanup()
        setupNotificationObservers()
    }
    
    deinit {
        cleanup()
    }
    
    // MARK: - Public Methods
    
    func optimizeMemoryUsage() {
        Logger.shared.info("Starting memory optimization", category: .performance)
        
        let startMemory = getCurrentMemoryUsage()
        
        // Perform optimization based on current level
        switch optimizationLevel {
        case .aggressive:
            performAggressiveOptimization()
        case .balanced:
            performBalancedOptimization()
        case .conservative:
            performConservativeOptimization()
        }
        
        let endMemory = getCurrentMemoryUsage()
        let savedMB = Double(startMemory.used - endMemory.used) / (1024 * 1024)
        
        Logger.shared.info("Memory optimization completed. Saved: \(String(format: "%.1f", savedMB))MB", category: .performance)
    }
    
    func setOptimizationLevel(_ level: MemoryOptimizationLevel) {
        optimizationLevel = level
        applyOptimizationLevel(level)
        Logger.shared.info("Memory optimization level set to: \(level)", category: .performance)
    }
    
    func enableLowMemoryMode(_ enabled: Bool) {
        isLowMemoryMode = enabled
        
        if enabled {
            performEmergencyCleanup()
            setOptimizationLevel(.aggressive)
        } else {
            setOptimizationLevel(.balanced)
        }
        
        Logger.shared.info("Low memory mode: \(enabled ? "enabled" : "disabled")", category: .performance)
    }
    
    func preloadAssets(for workoutType: WorkoutType) async {
        Logger.shared.info("Preloading assets for workout type: \(workoutType)", category: .performance)
        
        await withTaskGroup(of: Void.self) { group in
            group.addTask {
                await self.assetManager.preloadImages(for: workoutType)
            }
            
            group.addTask {
                await self.assetManager.preloadAudioAssets(for: workoutType)
            }
            
            group.addTask {
                await self.assetManager.preloadMLModels(for: workoutType)
            }
        }
        
        Logger.shared.info("Asset preloading completed", category: .performance)
    }
    
    func optimizeForARSession() {
        Logger.shared.info("Optimizing memory for AR session", category: .performance)
        
        // Clear non-essential caches
        cacheManager.clearNonEssentialCaches()
        
        // Optimize AR frame buffer size
        arFramePool.optimizeBufferSize()
        
        // Pre-allocate AR resources
        arFramePool.preallocateBuffers()
        
        // Reduce image cache size
        if isLowMemoryMode {
            imageAssetPool.setMaxSize(50) // 50 images
        } else {
            imageAssetPool.setMaxSize(100) // 100 images
        }
    }
    
    func optimizeForMLInference() {
        Logger.shared.info("Optimizing memory for ML inference", category: .performance)
        
        // Clear unnecessary model caches
        cacheManager.clearMLModelCaches(keepActive: true)
        
        // Optimize input/output buffers
        texturePool.optimizeForMLUsage()
        
        // Pre-warm ML model inputs
        Task {
            await assetManager.prewarmMLInputBuffers()
        }
    }
    
    func handleMemoryWarning() {
        Logger.shared.warning("Memory warning received", category: .performance)
        
        performEmergencyCleanup()
        
        // Temporary aggressive optimization
        let previousLevel = optimizationLevel
        setOptimizationLevel(.aggressive)
        
        // Restore optimization level after cleanup
        DispatchQueue.main.asyncAfter(deadline: .now() + 10) {
            self.setOptimizationLevel(previousLevel)
        }
    }
    
    func getMemoryReport() -> MemoryReport {
        let usage = getCurrentMemoryUsage()
        let leaks = detectMemoryLeaks()
        let recommendations = generateMemoryRecommendations()
        
        return MemoryReport(
            currentUsage: usage,
            optimizationLevel: optimizationLevel,
            isLowMemoryMode: isLowMemoryMode,
            detectedLeaks: leaks,
            cacheStatistics: cacheManager.getStatistics(),
            poolStatistics: getPoolStatistics(),
            recommendations: recommendations
        )
    }
    
    // MARK: - Private Methods
    
    private func setupMemoryPressureMonitoring() {
        memoryPressureSource = DispatchSource.makeMemoryPressureSource(
            eventMask: [.warning, .critical],
            queue: DispatchQueue.main
        )
        
        memoryPressureSource?.setEventHandler { [weak self] in
            guard let self = self else { return }
            
            let event = self.memoryPressureSource?.mask
            
            if event?.contains(.critical) == true {
                Logger.shared.critical("Critical memory pressure detected", category: .performance)
                self.handleCriticalMemoryPressure()
            } else if event?.contains(.warning) == true {
                Logger.shared.warning("Memory pressure warning", category: .performance)
                self.handleMemoryPressureWarning()
            }
        }
        
        memoryPressureSource?.resume()
    }
    
    private func setupPeriodicCleanup() {
        memoryCheckTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            self?.performPeriodicCleanup()
        }
    }
    
    private func setupNotificationObservers() {
        NotificationCenter.default.publisher(for: UIApplication.didReceiveMemoryWarningNotification)
            .sink { [weak self] _ in
                self?.handleMemoryWarning()
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)
            .sink { [weak self] _ in
                self?.performBackgroundCleanup()
            }
            .store(in: &cancellables)
    }
    
    private func getCurrentMemoryUsage() -> MemoryUsage {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let kerr = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        let used = kerr == KERN_SUCCESS ? info.resident_size : 0
        let available = ProcessInfo.processInfo.physicalMemory - used
        
        currentMemoryUsage = MemoryUsage(used: used, available: available)
        return currentMemoryUsage
    }
    
    private func performAggressiveOptimization() {
        // Clear all non-essential caches
        cacheManager.clearAllNonEssentialCaches()
        
        // Minimize asset pools
        imageAssetPool.setMaxSize(25)
        texturePool.setMaxSize(10)
        audioBufferPool.setMaxSize(5)
        
        // Optimize AR frame buffers
        arFramePool.setBufferSize(.minimal)
        
        // Force garbage collection
        autoreleasepool {
            // Trigger immediate memory cleanup
        }
        
        Logger.shared.info("Aggressive memory optimization completed", category: .performance)
    }
    
    private func performBalancedOptimization() {
        // Clear oldest cache entries
        cacheManager.trimCaches(keepPercentage: 0.7)
        
        // Moderate asset pool sizes
        imageAssetPool.setMaxSize(75)
        texturePool.setMaxSize(25)
        audioBufferPool.setMaxSize(15)
        
        // Optimize frame buffers
        arFramePool.setBufferSize(.balanced)
        
        Logger.shared.info("Balanced memory optimization completed", category: .performance)
    }
    
    private func performConservativeOptimization() {
        // Only clear expired cache entries
        cacheManager.clearExpiredCaches()
        
        // Keep generous asset pool sizes
        imageAssetPool.setMaxSize(150)
        texturePool.setMaxSize(50)
        audioBufferPool.setMaxSize(30)
        
        // Full frame buffers
        arFramePool.setBufferSize(.full)
        
        Logger.shared.info("Conservative memory optimization completed", category: .performance)
    }
    
    private func performEmergencyCleanup() {
        Logger.shared.warning("Performing emergency memory cleanup", category: .performance)
        
        // Immediately clear all caches
        cacheManager.clearAllCaches()
        
        // Minimal asset pools
        imageAssetPool.clear()
        texturePool.clear()
        audioBufferPool.clear()
        
        // Minimal AR buffers
        arFramePool.setBufferSize(.minimal)
        arFramePool.clearUnusedBuffers()
        
        // Stop non-essential background tasks
        NotificationCenter.default.post(name: .emergencyMemoryCleanup, object: nil)
    }
    
    private func performPeriodicCleanup() {
        let currentUsage = getCurrentMemoryUsage()
        
        // Check if cleanup is needed
        if shouldPerformCleanup(currentUsage) {
            Logger.shared.debug("Performing periodic memory cleanup", category: .performance)
            
            cacheManager.performPeriodicCleanup()
            imageAssetPool.removeUnusedAssets()
            texturePool.removeUnusedAssets()
            audioBufferPool.removeUnusedAssets()
            
            lastCleanupTime = Date()
        }
    }
    
    private func performBackgroundCleanup() {
        Logger.shared.info("Performing background memory cleanup", category: .performance)
        
        // Aggressive cleanup when app enters background
        cacheManager.clearAllNonEssentialCaches()
        imageAssetPool.setMaxSize(10) // Keep minimal images
        texturePool.clear() // Clear all textures
        audioBufferPool.setMaxSize(3) // Keep minimal audio buffers
        arFramePool.clearUnusedBuffers()
    }
    
    private func shouldPerformCleanup(_ usage: MemoryUsage) -> Bool {
        let timeSinceLastCleanup = Date().timeIntervalSince(lastCleanupTime)
        let memoryPressureHigh = usage.usagePercentage > 70
        let periodicCleanupDue = timeSinceLastCleanup > 300 // 5 minutes
        
        return memoryPressureHigh || periodicCleanupDue
    }
    
    private func handleMemoryPressureWarning() {
        Logger.shared.warning("Handling memory pressure warning", category: .performance)
        
        performBalancedOptimization()
        
        // Notify components to reduce memory usage
        NotificationCenter.default.post(name: .memoryPressureWarning, object: nil)
    }
    
    private func handleCriticalMemoryPressure() {
        Logger.shared.critical("Handling critical memory pressure", category: .performance)
        
        enableLowMemoryMode(true)
        performEmergencyCleanup()
        
        // Notify components of critical situation
        NotificationCenter.default.post(name: .criticalMemoryPressure, object: nil)
    }
    
    private func applyOptimizationLevel(_ level: MemoryOptimizationLevel) {
        switch level {
        case .aggressive:
            cacheManager.setAggressiveMode(true)
            assetManager.setPreloadingEnabled(false)
        case .balanced:
            cacheManager.setAggressiveMode(false)
            assetManager.setPreloadingEnabled(true)
        case .conservative:
            cacheManager.setAggressiveMode(false)
            assetManager.setPreloadingEnabled(true)
        }
    }
    
    private func detectMemoryLeaks() -> [MemoryLeak] {
        var leaks: [MemoryLeak] = []
        
        // Check for excessive cache growth
        let cacheStats = cacheManager.getStatistics()
        if cacheStats.totalSizeMB > 100 {
            leaks.append(MemoryLeak(
                component: "Cache System",
                size: UInt64(cacheStats.totalSizeMB * 1024 * 1024),
                description: "Cache size exceeds expected limits"
            ))
        }
        
        // Check for excessive asset pool growth
        if imageAssetPool.getCurrentSizeMB() > 50 {
            leaks.append(MemoryLeak(
                component: "Image Asset Pool",
                size: UInt64(imageAssetPool.getCurrentSizeMB() * 1024 * 1024),
                description: "Image pool size exceeds limits"
            ))
        }
        
        return leaks
    }
    
    private func generateMemoryRecommendations() -> [MemoryRecommendation] {
        var recommendations: [MemoryRecommendation] = []
        
        let usage = getCurrentMemoryUsage()
        
        if usage.usagePercentage > 80 {
            recommendations.append(.enableAggressiveOptimization)
            recommendations.append(.reduceAssetQuality)
        }
        
        if imageAssetPool.getCurrentSizeMB() > 30 {
            recommendations.append(.optimizeImageAssets)
        }
        
        if arFramePool.getBufferCount() > 10 {
            recommendations.append(.optimizeARBuffers)
        }
        
        return recommendations
    }
    
    private func getPoolStatistics() -> PoolStatistics {
        return PoolStatistics(
            imagePoolSizeMB: imageAssetPool.getCurrentSizeMB(),
            texturePoolSizeMB: texturePool.getCurrentSizeMB(),
            audioPoolSizeMB: audioBufferPool.getCurrentSizeMB(),
            arFrameBufferCount: arFramePool.getBufferCount()
        )
    }
    
    private func cleanup() {
        memoryCheckTimer?.invalidate()
        memoryPressureSource?.cancel()
        
        imageAssetPool.clear()
        texturePool.clear()
        audioBufferPool.clear()
        arFramePool.clear()
        cacheManager.clearAllCaches()
    }
}

// MARK: - Supporting Classes

class AssetPool<T> {
    private var assets: [String: (asset: T, lastAccess: Date)] = [:]
    private var maxSize = 100
    private let queue = DispatchQueue(label: "asset-pool", qos: .utility)
    
    func store(_ asset: T, withKey key: String) {
        queue.async {
            self.assets[key] = (asset, Date())
            self.enforceMaxSize()
        }
    }
    
    func retrieve(key: String) -> T? {
        return queue.sync {
            if var entry = assets[key] {
                entry.lastAccess = Date()
                assets[key] = entry
                return entry.asset
            }
            return nil
        }
    }
    
    func setMaxSize(_ size: Int) {
        queue.async {
            self.maxSize = size
            self.enforceMaxSize()
        }
    }
    
    func clear() {
        queue.async {
            self.assets.removeAll()
        }
    }
    
    func removeUnusedAssets() {
        queue.async {
            let cutoffTime = Date().addingTimeInterval(-300) // 5 minutes
            self.assets = self.assets.filter { $0.value.lastAccess > cutoffTime }
        }
    }
    
    func getCurrentSizeMB() -> Double {
        return queue.sync {
            // Estimate based on asset count (simplified)
            return Double(assets.count) * 0.5 // Assume 0.5MB per asset
        }
    }
    
    func optimizeForMLUsage() {
        queue.async {
            // Keep only recently used assets for ML
            let cutoffTime = Date().addingTimeInterval(-60) // 1 minute
            self.assets = self.assets.filter { $0.value.lastAccess > cutoffTime }
        }
    }
    
    private func enforceMaxSize() {
        guard assets.count > maxSize else { return }
        
        let sortedAssets = assets.sorted { $0.value.lastAccess < $1.value.lastAccess }
        let assetsToRemove = sortedAssets.prefix(assets.count - maxSize)
        
        for (key, _) in assetsToRemove {
            assets.removeValue(forKey: key)
        }
    }
}

class ARFramePool {
    private var buffers: [CVPixelBuffer] = []
    private var maxBuffers = 10
    private var bufferSize: BufferSize = .balanced
    private let queue = DispatchQueue(label: "ar-frame-pool", qos: .userInteractive)
    
    enum BufferSize {
        case minimal, balanced, full
        
        var maxBuffers: Int {
            switch self {
            case .minimal: return 3
            case .balanced: return 6
            case .full: return 10
            }
        }
    }
    
    func setBufferSize(_ size: BufferSize) {
        queue.async {
            self.bufferSize = size
            self.maxBuffers = size.maxBuffers
            self.trimBuffers()
        }
    }
    
    func preallocateBuffers() {
        queue.async {
            // Pre-allocate pixel buffers for AR frames
            for _ in 0..<self.maxBuffers {
                if let buffer = self.createPixelBuffer() {
                    self.buffers.append(buffer)
                }
            }
        }
    }
    
    func optimizeBufferSize() {
        queue.async {
            self.trimBuffers()
        }
    }
    
    func clearUnusedBuffers() {
        queue.async {
            self.buffers.removeAll()
        }
    }
    
    func getBufferCount() -> Int {
        return queue.sync { buffers.count }
    }
    
    func clear() {
        queue.async {
            self.buffers.removeAll()
        }
    }
    
    private func trimBuffers() {
        while buffers.count > maxBuffers {
            buffers.removeFirst()
        }
    }
    
    private func createPixelBuffer() -> CVPixelBuffer? {
        var pixelBuffer: CVPixelBuffer?
        let attrs = [
            kCVPixelBufferPixelFormatTypeKey: kCVPixelFormatType_32BGRA,
            kCVPixelBufferWidthKey: 1920,
            kCVPixelBufferHeightKey: 1080,
            kCVPixelBufferIOSurfacePropertiesKey: [:]
        ] as CFDictionary
        
        let status = CVPixelBufferCreate(
            kCFAllocatorDefault,
            1920, 1080,
            kCVPixelFormatType_32BGRA,
            attrs,
            &pixelBuffer
        )
        
        return status == kCVReturnSuccess ? pixelBuffer : nil
    }
}

class CacheManager {
    private var caches: [String: Any] = [:]
    private var cacheMetadata: [String: CacheMetadata] = [:]
    private var isAggressiveMode = false
    private let queue = DispatchQueue(label: "cache-manager", qos: .utility)
    
    struct CacheMetadata {
        let createdAt: Date
        let lastAccessed: Date
        let sizeBytes: Int
        let isEssential: Bool
    }
    
    func setAggressiveMode(_ enabled: Bool) {
        queue.async {
            self.isAggressiveMode = enabled
        }
    }
    
    func clearAllCaches() {
        queue.async {
            self.caches.removeAll()
            self.cacheMetadata.removeAll()
        }
    }
    
    func clearAllNonEssentialCaches() {
        queue.async {
            let nonEssentialKeys = self.cacheMetadata.compactMap { key, metadata in
                metadata.isEssential ? nil : key
            }
            
            for key in nonEssentialKeys {
                self.caches.removeValue(forKey: key)
                self.cacheMetadata.removeValue(forKey: key)
            }
        }
    }
    
    func clearNonEssentialCaches() {
        queue.async {
            let cutoffTime = Date().addingTimeInterval(-600) // 10 minutes
            let keysToRemove = self.cacheMetadata.compactMap { key, metadata in
                (!metadata.isEssential && metadata.lastAccessed < cutoffTime) ? key : nil
            }
            
            for key in keysToRemove {
                self.caches.removeValue(forKey: key)
                self.cacheMetadata.removeValue(forKey: key)
            }
        }
    }
    
    func clearExpiredCaches() {
        queue.async {
            let expiredTime = Date().addingTimeInterval(-3600) // 1 hour
            let keysToRemove = self.cacheMetadata.compactMap { key, metadata in
                metadata.createdAt < expiredTime ? key : nil
            }
            
            for key in keysToRemove {
                self.caches.removeValue(forKey: key)
                self.cacheMetadata.removeValue(forKey: key)
            }
        }
    }
    
    func clearMLModelCaches(keepActive: Bool) {
        queue.async {
            let mlKeys = self.cacheMetadata.keys.filter { $0.contains("ml-model") }
            
            for key in mlKeys {
                if !keepActive || !key.contains("active") {
                    self.caches.removeValue(forKey: key)
                    self.cacheMetadata.removeValue(forKey: key)
                }
            }
        }
    }
    
    func trimCaches(keepPercentage: Double) {
        queue.async {
            let targetCount = Int(Double(self.caches.count) * keepPercentage)
            guard self.caches.count > targetCount else { return }
            
            let sortedCaches = self.cacheMetadata.sorted { $0.value.lastAccessed < $1.value.lastAccessed }
            let cachesToRemove = sortedCaches.prefix(self.caches.count - targetCount)
            
            for (key, _) in cachesToRemove {
                self.caches.removeValue(forKey: key)
                self.cacheMetadata.removeValue(forKey: key)
            }
        }
    }
    
    func performPeriodicCleanup() {
        if isAggressiveMode {
            clearAllNonEssentialCaches()
        } else {
            clearExpiredCaches()
        }
    }
    
    func getStatistics() -> CacheStatistics {
        return queue.sync {
            let totalSize = cacheMetadata.values.reduce(0) { $0 + $1.sizeBytes }
            let essentialCount = cacheMetadata.values.filter { $0.isEssential }.count
            
            return CacheStatistics(
                totalCount: caches.count,
                essentialCount: essentialCount,
                totalSizeMB: Double(totalSize) / (1024 * 1024)
            )
        }
    }
}

class OptimizedAssetManager {
    private var preloadingEnabled = true
    private var preloadedAssets: Set<String> = []
    
    func setPreloadingEnabled(_ enabled: Bool) {
        preloadingEnabled = enabled
    }
    
    func preloadImages(for workoutType: WorkoutType) async {
        guard preloadingEnabled else { return }
        
        // Preload workout-specific images
        let imagesToPreload = getRequiredImages(for: workoutType)
        
        await withTaskGroup(of: Void.self) { group in
            for imageName in imagesToPreload {
                group.addTask {
                    await self.loadImageAsync(imageName)
                }
            }
        }
    }
    
    func preloadAudioAssets(for workoutType: WorkoutType) async {
        guard preloadingEnabled else { return }
        
        // Preload workout-specific audio
        let audioToPreload = getRequiredAudio(for: workoutType)
        
        await withTaskGroup(of: Void.self) { group in
            for audioName in audioToPreload {
                group.addTask {
                    await self.loadAudioAsync(audioName)
                }
            }
        }
    }
    
    func preloadMLModels(for workoutType: WorkoutType) async {
        guard preloadingEnabled else { return }
        
        // Preload workout-specific ML models
        let modelsToPreload = getRequiredMLModels(for: workoutType)
        
        await withTaskGroup(of: Void.self) { group in
            for modelName in modelsToPreload {
                group.addTask {
                    await self.loadMLModelAsync(modelName)
                }
            }
        }
    }
    
    func prewarmMLInputBuffers() async {
        // Pre-allocate common ML input buffer sizes
        // This helps reduce allocation overhead during inference
    }
    
    private func getRequiredImages(for workoutType: WorkoutType) -> [String] {
        switch workoutType {
        case .highIntensity:
            return ["hiit-background", "intensity-indicators", "progress-bars"]
        case .precision:
            return ["form-overlays", "alignment-guides", "precision-indicators"]
        case .endurance:
            return ["endurance-background", "heart-rate-zones", "time-indicators"]
        }
    }
    
    private func getRequiredAudio(for workoutType: WorkoutType) -> [String] {
        switch workoutType {
        case .highIntensity:
            return ["countdown-beeps", "intensity-music", "completion-sounds"]
        case .precision:
            return ["form-cues", "metronome", "correction-alerts"]
        case .endurance:
            return ["endurance-music", "pace-cues", "milestone-celebrations"]
        }
    }
    
    private func getRequiredMLModels(for workoutType: WorkoutType) -> [String] {
        switch workoutType {
        case .highIntensity:
            return ["movement-classifier", "intensity-detector"]
        case .precision:
            return ["pose-analyzer", "form-checker"]
        case .endurance:
            return ["fatigue-detector", "pace-analyzer"]
        }
    }
    
    private func loadImageAsync(_ imageName: String) async {
        // Simulate async image loading
        await Task.sleep(nanoseconds: 10_000_000) // 10ms
        preloadedAssets.insert(imageName)
    }
    
    private func loadAudioAsync(_ audioName: String) async {
        // Simulate async audio loading
        await Task.sleep(nanoseconds: 20_000_000) // 20ms
        preloadedAssets.insert(audioName)
    }
    
    private func loadMLModelAsync(_ modelName: String) async {
        // Simulate async ML model loading
        await Task.sleep(nanoseconds: 100_000_000) // 100ms
        preloadedAssets.insert(modelName)
    }
}

// MARK: - Supporting Types

enum MemoryOptimizationLevel: String, CaseIterable {
    case aggressive = "Aggressive"
    case balanced = "Balanced" 
    case conservative = "Conservative"
}

enum MemoryRecommendation: String, CaseIterable {
    case enableAggressiveOptimization = "Enable aggressive optimization"
    case reduceAssetQuality = "Reduce asset quality"
    case optimizeImageAssets = "Optimize image assets"
    case optimizeARBuffers = "Optimize AR buffers"
    case clearUnusedCaches = "Clear unused caches"
    case reducePreloading = "Reduce asset preloading"
}

struct CacheStatistics {
    let totalCount: Int
    let essentialCount: Int
    let totalSizeMB: Double
}

struct PoolStatistics {
    let imagePoolSizeMB: Double
    let texturePoolSizeMB: Double
    let audioPoolSizeMB: Double
    let arFrameBufferCount: Int
}

struct MemoryReport {
    let currentUsage: MemoryUsage
    let optimizationLevel: MemoryOptimizationLevel
    let isLowMemoryMode: Bool
    let detectedLeaks: [MemoryLeak]
    let cacheStatistics: CacheStatistics
    let poolStatistics: PoolStatistics
    let recommendations: [MemoryRecommendation]
}

struct MemoryThresholds {
    let warningPercentage: Double = 70.0
    let criticalPercentage: Double = 85.0
    let maxCacheSizeMB: Double = 100.0
    let maxPoolSizeMB: Double = 50.0
}

// MARK: - Notification Extensions

extension Notification.Name {
    static let memoryPressureWarning = Notification.Name("MemoryPressureWarning")
    static let criticalMemoryPressure = Notification.Name("CriticalMemoryPressure")
    static let emergencyMemoryCleanup = Notification.Name("EmergencyMemoryCleanup")
}