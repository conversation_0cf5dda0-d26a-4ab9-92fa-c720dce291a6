import Foundation
import UIKit
import Combine

// MARK: - Memory Manager

/// Centralized memory management and optimization system
@MainActor
final class MemoryManager: ObservableObject {
    
    static let shared = MemoryManager()
    
    // MARK: - Published Properties
    
    @Published var memoryUsage: MemoryUsage = MemoryUsage()
    @Published var memoryWarningLevel: MemoryWarningLevel = .normal
    @Published var isOptimizationActive = false
    
    // MARK: - Private Properties
    
    private let logger = Logger()
    private var cancellables = Set<AnyCancellable>()
    private var memoryMonitoringTimer: Timer?
    
    // Memory thresholds (in MB)
    private let warningThreshold: Double = 200.0
    private let criticalThreshold: Double = 300.0
    private let emergencyThreshold: Double = 400.0
    
    // Cache management
    private var imageCache = NSCache<NSString, UIImage>()
    private var dataCache = NSCache<NSString, NSData>()
    private var objectCache = NSCache<NSString, AnyObject>()
    
    // Memory optimization strategies
    private var optimizationStrategies: [MemoryOptimizationStrategy] = []
    
    // MARK: - Initialization
    
    private init() {
        setupMemoryMonitoring()
        setupCacheConfiguration()
        setupOptimizationStrategies()
        registerForMemoryWarnings()
    }
    
    deinit {
        stopMemoryMonitoring()
    }
    
    // MARK: - Setup
    
    private func setupMemoryMonitoring() {
        // Monitor memory usage every 5 seconds
        memoryMonitoringTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateMemoryUsage()
            }
        }
        
        logger.info("Memory monitoring started", category: .performance)
    }
    
    private func setupCacheConfiguration() {
        // Configure image cache
        imageCache.countLimit = 50 // Maximum 50 images
        imageCache.totalCostLimit = 50 * 1024 * 1024 // 50MB limit
        
        // Configure data cache
        dataCache.countLimit = 100
        dataCache.totalCostLimit = 20 * 1024 * 1024 // 20MB limit
        
        // Configure object cache
        objectCache.countLimit = 200
        objectCache.totalCostLimit = 10 * 1024 * 1024 // 10MB limit
        
        logger.info("Cache configuration completed", category: .performance)
    }
    
    private func setupOptimizationStrategies() {
        optimizationStrategies = [
            CacheClearingStrategy(),
            ImageCompressionStrategy(),
            DataPruningStrategy(),
            BackgroundTaskTerminationStrategy(),
            ModelOptimizationStrategy()
        ]
        
        logger.info("Memory optimization strategies configured", category: .performance)
    }
    
    private func registerForMemoryWarnings() {
        NotificationCenter.default.publisher(for: UIApplication.didReceiveMemoryWarningNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.handleMemoryWarning()
                }
            }
            .store(in: &cancellables)
        
        // Custom memory warning notifications
        NotificationCenter.default.publisher(for: .memoryWarning)
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.handleMemoryWarning()
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Memory Monitoring
    
    private func updateMemoryUsage() {
        let usage = getCurrentMemoryUsage()
        memoryUsage = usage
        
        // Update warning level
        let previousLevel = memoryWarningLevel
        memoryWarningLevel = determineWarningLevel(for: usage.usedMemoryMB)
        
        // Log significant changes
        if memoryWarningLevel != previousLevel {
            logger.warning("Memory warning level changed: \(previousLevel.rawValue) -> \(memoryWarningLevel.rawValue)", category: .performance)
        }
        
        // Trigger optimization if needed
        if memoryWarningLevel.shouldOptimize {
            triggerMemoryOptimization()
        }
    }
    
    private func getCurrentMemoryUsage() -> MemoryUsage {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        guard result == KERN_SUCCESS else {
            logger.error("Failed to get memory usage", category: .performance)
            return MemoryUsage()
        }
        
        let usedMemoryBytes = Double(info.resident_size)
        let usedMemoryMB = usedMemoryBytes / (1024 * 1024)
        
        // Get available memory
        let physicalMemory = Double(ProcessInfo.processInfo.physicalMemory)
        let physicalMemoryMB = physicalMemory / (1024 * 1024)
        
        return MemoryUsage(
            usedMemoryMB: usedMemoryMB,
            availableMemoryMB: physicalMemoryMB - usedMemoryMB,
            totalMemoryMB: physicalMemoryMB,
            memoryPressure: calculateMemoryPressure(usedMemoryMB, total: physicalMemoryMB)
        )
    }
    
    private func calculateMemoryPressure(_ used: Double, total: Double) -> Double {
        return min(used / total, 1.0)
    }
    
    private func determineWarningLevel(for usedMemoryMB: Double) -> MemoryWarningLevel {
        switch usedMemoryMB {
        case 0..<warningThreshold:
            return .normal
        case warningThreshold..<criticalThreshold:
            return .warning
        case criticalThreshold..<emergencyThreshold:
            return .critical
        default:
            return .emergency
        }
    }
    
    // MARK: - Memory Optimization
    
    private func triggerMemoryOptimization() {
        guard !isOptimizationActive else { return }
        
        isOptimizationActive = true
        
        Task {
            await performMemoryOptimization()
            await MainActor.run {
                isOptimizationActive = false
            }
        }
    }
    
    private func performMemoryOptimization() async {
        logger.info("Starting memory optimization", category: .performance)
        
        let startMemory = memoryUsage.usedMemoryMB
        
        // Execute optimization strategies based on severity
        for strategy in optimizationStrategies {
            if strategy.shouldExecute(for: memoryWarningLevel) {
                await strategy.execute()
                
                // Check if we've freed enough memory
                updateMemoryUsage()
                if memoryWarningLevel == .normal {
                    break
                }
            }
        }
        
        let endMemory = memoryUsage.usedMemoryMB
        let freedMemory = startMemory - endMemory
        
        logger.info("Memory optimization completed. Freed: \(String(format: "%.1f", freedMemory))MB", category: .performance)
    }
    
    private func handleMemoryWarning() {
        logger.warning("System memory warning received", category: .performance)
        
        // Immediate cache clearing
        clearAllCaches()
        
        // Trigger aggressive optimization
        memoryWarningLevel = .critical
        triggerMemoryOptimization()
        
        // Notify other components
        NotificationCenter.default.post(name: .memoryOptimizationRequired, object: nil)
    }
    
    // MARK: - Cache Management
    
    func clearAllCaches() {
        imageCache.removeAllObjects()
        dataCache.removeAllObjects()
        objectCache.removeAllObjects()
        
        logger.info("All caches cleared", category: .performance)
    }
    
    func clearImageCache() {
        imageCache.removeAllObjects()
        logger.debug("Image cache cleared", category: .performance)
    }
    
    func clearDataCache() {
        dataCache.removeAllObjects()
        logger.debug("Data cache cleared", category: .performance)
    }
    
    func clearObjectCache() {
        objectCache.removeAllObjects()
        logger.debug("Object cache cleared", category: .performance)
    }
    
    // MARK: - Cache Access
    
    func cacheImage(_ image: UIImage, forKey key: String) {
        let cost = Int(image.size.width * image.size.height * 4) // Approximate memory cost
        imageCache.setObject(image, forKey: key as NSString, cost: cost)
    }
    
    func getCachedImage(forKey key: String) -> UIImage? {
        return imageCache.object(forKey: key as NSString)
    }
    
    func cacheData(_ data: Data, forKey key: String) {
        dataCache.setObject(data as NSData, forKey: key as NSString, cost: data.count)
    }
    
    func getCachedData(forKey key: String) -> Data? {
        return dataCache.object(forKey: key as NSString) as Data?
    }
    
    func cacheObject(_ object: AnyObject, forKey key: String) {
        objectCache.setObject(object, forKey: key as NSString)
    }
    
    func getCachedObject(forKey key: String) -> AnyObject? {
        return objectCache.object(forKey: key as NSString)
    }
    
    // MARK: - Utility Methods
    
    func stopMemoryMonitoring() {
        memoryMonitoringTimer?.invalidate()
        memoryMonitoringTimer = nil
        logger.info("Memory monitoring stopped", category: .performance)
    }
    
    func getMemoryReport() -> String {
        return """
        Memory Usage Report:
        - Used: \(String(format: "%.1f", memoryUsage.usedMemoryMB))MB
        - Available: \(String(format: "%.1f", memoryUsage.availableMemoryMB))MB
        - Total: \(String(format: "%.1f", memoryUsage.totalMemoryMB))MB
        - Pressure: \(String(format: "%.1f", memoryUsage.memoryPressure * 100))%
        - Warning Level: \(memoryWarningLevel.displayName)
        - Optimization Active: \(isOptimizationActive)
        """
    }
}

// MARK: - Supporting Types

struct MemoryUsage {
    let usedMemoryMB: Double
    let availableMemoryMB: Double
    let totalMemoryMB: Double
    let memoryPressure: Double // 0.0 to 1.0
    
    init() {
        self.usedMemoryMB = 0
        self.availableMemoryMB = 0
        self.totalMemoryMB = 0
        self.memoryPressure = 0
    }
    
    init(usedMemoryMB: Double, availableMemoryMB: Double, totalMemoryMB: Double, memoryPressure: Double) {
        self.usedMemoryMB = usedMemoryMB
        self.availableMemoryMB = availableMemoryMB
        self.totalMemoryMB = totalMemoryMB
        self.memoryPressure = memoryPressure
    }
}

enum MemoryWarningLevel: String, CaseIterable {
    case normal = "normal"
    case warning = "warning"
    case critical = "critical"
    case emergency = "emergency"
    
    var displayName: String {
        switch self {
        case .normal: return "Normal"
        case .warning: return "Warning"
        case .critical: return "Critical"
        case .emergency: return "Emergency"
        }
    }
    
    var shouldOptimize: Bool {
        switch self {
        case .normal: return false
        case .warning, .critical, .emergency: return true
        }
    }
}

// MARK: - Memory Optimization Strategies

protocol MemoryOptimizationStrategy {
    func shouldExecute(for level: MemoryWarningLevel) -> Bool
    func execute() async
}

struct CacheClearingStrategy: MemoryOptimizationStrategy {
    func shouldExecute(for level: MemoryWarningLevel) -> Bool {
        return level != .normal
    }

    func execute() async {
        await MainActor.run {
            MemoryManager.shared.clearAllCaches()
        }
    }
}

struct ImageCompressionStrategy: MemoryOptimizationStrategy {
    func shouldExecute(for level: MemoryWarningLevel) -> Bool {
        return level == .critical || level == .emergency
    }

    func execute() async {
        // Compress images in cache or reduce quality
        await MainActor.run {
            // Implementation would compress cached images
            Logger().info("Image compression strategy executed", category: .performance)
        }
    }
}

struct DataPruningStrategy: MemoryOptimizationStrategy {
    func shouldExecute(for level: MemoryWarningLevel) -> Bool {
        return level == .critical || level == .emergency
    }

    func execute() async {
        // Remove old or unnecessary data
        await MainActor.run {
            // Implementation would prune old workout data, logs, etc.
            Logger().info("Data pruning strategy executed", category: .performance)
        }
    }
}

struct BackgroundTaskTerminationStrategy: MemoryOptimizationStrategy {
    func shouldExecute(for level: MemoryWarningLevel) -> Bool {
        return level == .emergency
    }

    func execute() async {
        // Terminate non-essential background tasks
        await MainActor.run {
            NotificationCenter.default.post(name: .terminateBackgroundTasks, object: nil)
            Logger().info("Background task termination strategy executed", category: .performance)
        }
    }
}

struct ModelOptimizationStrategy: MemoryOptimizationStrategy {
    func shouldExecute(for level: MemoryWarningLevel) -> Bool {
        return level == .critical || level == .emergency
    }

    func execute() async {
        // Optimize ML models and reduce precision if needed
        await MainActor.run {
            NotificationCenter.default.post(name: .optimizeMLModels, object: nil)
            Logger().info("Model optimization strategy executed", category: .performance)
        }
    }
}

// MARK: - Notification Names

extension Notification.Name {
    static let memoryOptimizationRequired = Notification.Name("memoryOptimizationRequired")
    static let terminateBackgroundTasks = Notification.Name("terminateBackgroundTasks")
    static let optimizeMLModels = Notification.Name("optimizeMLModels")
}
