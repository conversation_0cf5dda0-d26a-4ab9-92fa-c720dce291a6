import Foundation
import UIKit
import Combine
import QuartzCore

// MARK: - Performance Monitor

/// Comprehensive performance monitoring and optimization system
@MainActor
final class PerformanceMonitor: ObservableObject {
    
    static let shared = PerformanceMonitor()
    
    // MARK: - Published Properties
    
    @Published var currentMetrics = PerformanceMetrics()
    @Published var isMonitoring = false
    @Published var performanceLevel: PerformanceLevel = .optimal
    @Published var recommendations: [PerformanceRecommendation] = []
    
    // MARK: - Private Properties
    
    private let logger = Logger()
    private var cancellables = Set<AnyCancellable>()
    private var monitoringTimer: Timer?
    private var displayLink: CADisplayLink?
    
    // Metrics collection
    private var frameTimeHistory: [TimeInterval] = []
    private var cpuUsageHistory: [Double] = []
    private var memoryUsageHistory: [Double] = []
    private var thermalStateHistory: [ProcessInfo.ThermalState] = []
    
    // Performance tracking
    private var lastFrameTime: CFTimeInterval = 0
    private var frameCount = 0
    private var droppedFrames = 0
    
    // Configuration
    private let maxHistorySize = 100
    private let monitoringInterval: TimeInterval = 1.0
    private let targetFPS: Double = 60.0
    
    // Performance thresholds
    private let fpsWarningThreshold: Double = 45.0
    private let fpsCriticalThreshold: Double = 30.0
    private let cpuWarningThreshold: Double = 70.0
    private let cpuCriticalThreshold: Double = 85.0
    
    // MARK: - Initialization
    
    private init() {
        setupNotifications()
    }
    
    deinit {
        stopMonitoring()
    }
    
    // MARK: - Setup
    
    private func setupNotifications() {
        // Listen for thermal state changes
        NotificationCenter.default.publisher(for: ProcessInfo.thermalStateDidChangeNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.handleThermalStateChange()
                }
            }
            .store(in: &cancellables)
        
        // Listen for memory optimization requests
        NotificationCenter.default.publisher(for: .memoryOptimizationRequired)
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.handleMemoryPressure()
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Monitoring Control
    
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        
        // Start frame rate monitoring
        setupDisplayLink()
        
        // Start periodic metrics collection
        monitoringTimer = Timer.scheduledTimer(withTimeInterval: monitoringInterval, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.collectMetrics()
            }
        }
        
        logger.info("Performance monitoring started", category: .performance)
    }
    
    func stopMonitoring() {
        guard isMonitoring else { return }
        
        isMonitoring = false
        
        // Stop display link
        displayLink?.invalidate()
        displayLink = nil
        
        // Stop timer
        monitoringTimer?.invalidate()
        monitoringTimer = nil
        
        logger.info("Performance monitoring stopped", category: .performance)
    }
    
    private func setupDisplayLink() {
        displayLink = CADisplayLink(target: self, selector: #selector(displayLinkCallback))
        displayLink?.add(to: .main, forMode: .common)
    }
    
    @objc private func displayLinkCallback() {
        let currentTime = CACurrentMediaTime()
        
        if lastFrameTime > 0 {
            let frameTime = currentTime - lastFrameTime
            frameTimeHistory.append(frameTime)
            
            // Maintain history size
            if frameTimeHistory.count > maxHistorySize {
                frameTimeHistory.removeFirst()
            }
            
            // Check for dropped frames
            let expectedFrameTime = 1.0 / targetFPS
            if frameTime > expectedFrameTime * 1.5 {
                droppedFrames += 1
            }
        }
        
        lastFrameTime = currentTime
        frameCount += 1
    }
    
    // MARK: - Metrics Collection
    
    private func collectMetrics() {
        let metrics = PerformanceMetrics(
            fps: calculateCurrentFPS(),
            averageFrameTime: calculateAverageFrameTime(),
            droppedFramePercentage: calculateDroppedFramePercentage(),
            cpuUsage: getCurrentCPUUsage(),
            memoryUsage: MemoryManager.shared.memoryUsage,
            thermalState: ProcessInfo.processInfo.thermalState,
            batteryLevel: UIDevice.current.batteryLevel,
            lowPowerModeEnabled: ProcessInfo.processInfo.isLowPowerModeEnabled,
            timestamp: Date()
        )
        
        currentMetrics = metrics
        
        // Update history
        updateHistory(with: metrics)
        
        // Analyze performance
        analyzePerformance(metrics)
        
        // Generate recommendations
        updateRecommendations(based: metrics)
    }
    
    private func calculateCurrentFPS() -> Double {
        guard !frameTimeHistory.isEmpty else { return 0 }
        
        let recentFrameTimes = Array(frameTimeHistory.suffix(30)) // Last 30 frames
        let averageFrameTime = recentFrameTimes.reduce(0, +) / Double(recentFrameTimes.count)
        
        return averageFrameTime > 0 ? 1.0 / averageFrameTime : 0
    }
    
    private func calculateAverageFrameTime() -> TimeInterval {
        guard !frameTimeHistory.isEmpty else { return 0 }
        
        return frameTimeHistory.reduce(0, +) / Double(frameTimeHistory.count)
    }
    
    private func calculateDroppedFramePercentage() -> Double {
        guard frameCount > 0 else { return 0 }
        
        return Double(droppedFrames) / Double(frameCount) * 100.0
    }
    
    private func getCurrentCPUUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        guard result == KERN_SUCCESS else {
            return 0
        }
        
        // This is a simplified CPU usage calculation
        // In a real implementation, you'd want more accurate CPU monitoring
        return Double(info.resident_size) / Double(ProcessInfo.processInfo.physicalMemory) * 100.0
    }
    
    private func updateHistory(with metrics: PerformanceMetrics) {
        cpuUsageHistory.append(metrics.cpuUsage)
        memoryUsageHistory.append(metrics.memoryUsage.usedMemoryMB)
        thermalStateHistory.append(metrics.thermalState)
        
        // Maintain history size
        if cpuUsageHistory.count > maxHistorySize {
            cpuUsageHistory.removeFirst()
        }
        if memoryUsageHistory.count > maxHistorySize {
            memoryUsageHistory.removeFirst()
        }
        if thermalStateHistory.count > maxHistorySize {
            thermalStateHistory.removeFirst()
        }
    }
    
    // MARK: - Performance Analysis
    
    private func analyzePerformance(_ metrics: PerformanceMetrics) {
        let previousLevel = performanceLevel
        performanceLevel = determinePerformanceLevel(from: metrics)
        
        if performanceLevel != previousLevel {
            logger.info("Performance level changed: \(previousLevel.rawValue) -> \(performanceLevel.rawValue)", category: .performance)
            
            // Trigger optimization if performance degraded
            if performanceLevel.priority > previousLevel.priority {
                triggerPerformanceOptimization()
            }
        }
    }
    
    private func determinePerformanceLevel(from metrics: PerformanceMetrics) -> PerformanceLevel {
        var issues = 0
        
        // Check FPS
        if metrics.fps < fpsCriticalThreshold {
            issues += 3
        } else if metrics.fps < fpsWarningThreshold {
            issues += 1
        }
        
        // Check CPU usage
        if metrics.cpuUsage > cpuCriticalThreshold {
            issues += 3
        } else if metrics.cpuUsage > cpuWarningThreshold {
            issues += 1
        }
        
        // Check memory pressure
        if metrics.memoryUsage.memoryPressure > 0.8 {
            issues += 2
        } else if metrics.memoryUsage.memoryPressure > 0.6 {
            issues += 1
        }
        
        // Check thermal state
        switch metrics.thermalState {
        case .critical:
            issues += 4
        case .serious:
            issues += 3
        case .fair:
            issues += 1
        case .nominal:
            break
        @unknown default:
            break
        }
        
        // Check battery and power mode
        if metrics.lowPowerModeEnabled {
            issues += 1
        }
        
        // Determine level based on total issues
        switch issues {
        case 0...1:
            return .optimal
        case 2...3:
            return .good
        case 4...6:
            return .degraded
        default:
            return .poor
        }
    }
    
    private func triggerPerformanceOptimization() {
        logger.info("Triggering performance optimization", category: .performance)
        
        // Notify components to optimize
        NotificationCenter.default.post(name: .performanceOptimizationRequired, object: performanceLevel)
        
        // Trigger memory optimization if needed
        if currentMetrics.memoryUsage.memoryPressure > 0.7 {
            NotificationCenter.default.post(name: .memoryOptimizationRequired, object: nil)
        }
    }
    
    // MARK: - Recommendations
    
    private func updateRecommendations(based metrics: PerformanceMetrics) {
        var newRecommendations: [PerformanceRecommendation] = []
        
        // FPS recommendations
        if metrics.fps < fpsWarningThreshold {
            newRecommendations.append(
                PerformanceRecommendation(
                    type: .reduceFrameRate,
                    priority: .high,
                    description: "Consider reducing frame rate or visual effects to improve performance",
                    impact: .performance
                )
            )
        }
        
        // CPU recommendations
        if metrics.cpuUsage > cpuWarningThreshold {
            newRecommendations.append(
                PerformanceRecommendation(
                    type: .optimizeCPU,
                    priority: .medium,
                    description: "High CPU usage detected. Consider reducing background processing",
                    impact: .performance
                )
            )
        }
        
        // Memory recommendations
        if metrics.memoryUsage.memoryPressure > 0.7 {
            newRecommendations.append(
                PerformanceRecommendation(
                    type: .reduceMemoryUsage,
                    priority: .high,
                    description: "High memory usage. Clear caches or reduce data retention",
                    impact: .memory
                )
            )
        }
        
        // Thermal recommendations
        if metrics.thermalState == .serious || metrics.thermalState == .critical {
            newRecommendations.append(
                PerformanceRecommendation(
                    type: .reduceThermalLoad,
                    priority: .critical,
                    description: "Device is overheating. Reduce intensive operations",
                    impact: .thermal
                )
            )
        }
        
        // Battery recommendations
        if metrics.lowPowerModeEnabled {
            newRecommendations.append(
                PerformanceRecommendation(
                    type: .optimizeForBattery,
                    priority: .medium,
                    description: "Low power mode enabled. Optimize for battery life",
                    impact: .battery
                )
            )
        }
        
        recommendations = newRecommendations
    }
    
    // MARK: - Event Handlers
    
    private func handleThermalStateChange() {
        let thermalState = ProcessInfo.processInfo.thermalState
        logger.info("Thermal state changed: \(thermalState)", category: .performance)
        
        if thermalState == .serious || thermalState == .critical {
            triggerPerformanceOptimization()
        }
    }
    
    private func handleMemoryPressure() {
        logger.warning("Memory pressure detected", category: .performance)
        triggerPerformanceOptimization()
    }
    
    // MARK: - Utility Methods
    
    func getPerformanceReport() -> String {
        return """
        Performance Report:
        - FPS: \(String(format: "%.1f", currentMetrics.fps))
        - CPU Usage: \(String(format: "%.1f", currentMetrics.cpuUsage))%
        - Memory: \(String(format: "%.1f", currentMetrics.memoryUsage.usedMemoryMB))MB
        - Thermal State: \(currentMetrics.thermalState)
        - Performance Level: \(performanceLevel.displayName)
        - Dropped Frames: \(String(format: "%.1f", currentMetrics.droppedFramePercentage))%
        - Recommendations: \(recommendations.count)
        """
    }
    
    func resetMetrics() {
        frameTimeHistory.removeAll()
        cpuUsageHistory.removeAll()
        memoryUsageHistory.removeAll()
        thermalStateHistory.removeAll()
        frameCount = 0
        droppedFrames = 0
        lastFrameTime = 0
        
        logger.info("Performance metrics reset", category: .performance)
    }
}

// MARK: - Supporting Types

struct PerformanceMetrics {
    let fps: Double
    let averageFrameTime: TimeInterval
    let droppedFramePercentage: Double
    let cpuUsage: Double
    let memoryUsage: MemoryUsage
    let thermalState: ProcessInfo.ThermalState
    let batteryLevel: Float
    let lowPowerModeEnabled: Bool
    let timestamp: Date

    init() {
        self.fps = 0
        self.averageFrameTime = 0
        self.droppedFramePercentage = 0
        self.cpuUsage = 0
        self.memoryUsage = MemoryUsage()
        self.thermalState = .nominal
        self.batteryLevel = 1.0
        self.lowPowerModeEnabled = false
        self.timestamp = Date()
    }

    init(fps: Double, averageFrameTime: TimeInterval, droppedFramePercentage: Double, cpuUsage: Double, memoryUsage: MemoryUsage, thermalState: ProcessInfo.ThermalState, batteryLevel: Float, lowPowerModeEnabled: Bool, timestamp: Date) {
        self.fps = fps
        self.averageFrameTime = averageFrameTime
        self.droppedFramePercentage = droppedFramePercentage
        self.cpuUsage = cpuUsage
        self.memoryUsage = memoryUsage
        self.thermalState = thermalState
        self.batteryLevel = batteryLevel
        self.lowPowerModeEnabled = lowPowerModeEnabled
        self.timestamp = timestamp
    }
}

enum PerformanceLevel: String, CaseIterable {
    case optimal = "optimal"
    case good = "good"
    case degraded = "degraded"
    case poor = "poor"

    var displayName: String {
        switch self {
        case .optimal: return "Optimal"
        case .good: return "Good"
        case .degraded: return "Degraded"
        case .poor: return "Poor"
        }
    }

    var priority: Int {
        switch self {
        case .optimal: return 0
        case .good: return 1
        case .degraded: return 2
        case .poor: return 3
        }
    }

    var color: String {
        switch self {
        case .optimal: return "green"
        case .good: return "blue"
        case .degraded: return "orange"
        case .poor: return "red"
        }
    }
}

struct PerformanceRecommendation: Identifiable {
    let id = UUID()
    let type: RecommendationType
    let priority: RecommendationPriority
    let description: String
    let impact: ImpactArea
    let timestamp: Date

    init(type: RecommendationType, priority: RecommendationPriority, description: String, impact: ImpactArea) {
        self.type = type
        self.priority = priority
        self.description = description
        self.impact = impact
        self.timestamp = Date()
    }
}

enum RecommendationType: String, CaseIterable {
    case reduceFrameRate = "reduce_frame_rate"
    case optimizeCPU = "optimize_cpu"
    case reduceMemoryUsage = "reduce_memory_usage"
    case reduceThermalLoad = "reduce_thermal_load"
    case optimizeForBattery = "optimize_for_battery"
    case clearCaches = "clear_caches"
    case reduceQuality = "reduce_quality"
    case pauseBackgroundTasks = "pause_background_tasks"

    var displayName: String {
        switch self {
        case .reduceFrameRate: return "Reduce Frame Rate"
        case .optimizeCPU: return "Optimize CPU Usage"
        case .reduceMemoryUsage: return "Reduce Memory Usage"
        case .reduceThermalLoad: return "Reduce Thermal Load"
        case .optimizeForBattery: return "Optimize for Battery"
        case .clearCaches: return "Clear Caches"
        case .reduceQuality: return "Reduce Quality"
        case .pauseBackgroundTasks: return "Pause Background Tasks"
        }
    }
}

enum RecommendationPriority: String, CaseIterable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case critical = "critical"

    var displayName: String {
        switch self {
        case .low: return "Low"
        case .medium: return "Medium"
        case .high: return "High"
        case .critical: return "Critical"
        }
    }

    var urgency: Int {
        switch self {
        case .low: return 1
        case .medium: return 2
        case .high: return 3
        case .critical: return 4
        }
    }
}

enum ImpactArea: String, CaseIterable {
    case performance = "performance"
    case memory = "memory"
    case thermal = "thermal"
    case battery = "battery"
    case quality = "quality"

    var displayName: String {
        switch self {
        case .performance: return "Performance"
        case .memory: return "Memory"
        case .thermal: return "Thermal"
        case .battery: return "Battery"
        case .quality: return "Quality"
        }
    }
}

// MARK: - Notification Names

extension Notification.Name {
    static let performanceOptimizationRequired = Notification.Name("performanceOptimizationRequired")
}
