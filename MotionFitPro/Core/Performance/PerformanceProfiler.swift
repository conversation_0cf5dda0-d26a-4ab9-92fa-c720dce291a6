import Foundation
import UIKit
import QuartzCore
import ARKit
import CoreML
import Combine
import os.signpost

@MainActor
class PerformanceProfiler: ObservableObject {
    static let shared = PerformanceProfiler()
    
    @Published var currentMetrics = PerformanceMetrics()
    @Published var isMonitoring = false
    @Published var thermalState: ProcessInfo.ThermalState = .nominal
    @Published var performanceMode: PerformanceMode = .adaptive
    
    // Real-time monitoring
    private var fpsCounter = FPSCounter()
    private var memoryMonitor = MemoryMonitor()
    private var cpuMonitor = CPUMonitor()
    private var batteryMonitor = BatteryMonitor()
    private var networkMonitor = NetworkMonitor()
    
    // Performance logging
    private let performanceLogger = OSLog(subsystem: "com.motionfitpro.performance", category: "monitoring")
    private let signpostID = OSSignpostID(log: OSLog(subsystem: "com.motionfitpro.performance", category: "signposts"))
    
    // Monitoring state
    private var monitoringTimer: Timer?
    private var metricsHistory: [PerformanceMetrics] = []
    private let maxHistoryCount = 300 // 5 minutes at 1Hz sampling
    
    // Performance thresholds
    private let performanceThresholds = PerformanceThresholds()
    
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        setupThermalStateMonitoring()
        setupNotificationObservers()
    }
    
    // MARK: - Public Methods
    
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        
        fpsCounter.startMonitoring()
        memoryMonitor.startMonitoring()
        cpuMonitor.startMonitoring()
        batteryMonitor.startMonitoring()
        networkMonitor.startMonitoring()
        
        monitoringTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateMetrics()
            }
        }
        
        os_signpost(.begin, log: performanceLogger, name: "Performance Monitoring Started")
        Logger.shared.info("Performance monitoring started", category: .performance)
    }
    
    func stopMonitoring() {
        guard isMonitoring else { return }
        
        isMonitoring = false
        
        monitoringTimer?.invalidate()
        monitoringTimer = nil
        
        fpsCounter.stopMonitoring()
        memoryMonitor.stopMonitoring()
        cpuMonitor.stopMonitoring()
        batteryMonitor.stopMonitoring()
        networkMonitor.stopMonitoring()
        
        os_signpost(.end, log: performanceLogger, name: "Performance Monitoring Stopped")
        Logger.shared.info("Performance monitoring stopped", category: .performance)
    }
    
    func recordFrameTime(_ duration: TimeInterval, for component: PerformanceComponent) {
        fpsCounter.recordFrameTime(duration, for: component)
    }
    
    func beginSignpost(name: StaticString, category: String = "operation") {
        os_signpost(.begin, log: performanceLogger, name: name)
    }
    
    func endSignpost(name: StaticString, category: String = "operation") {
        os_signpost(.end, log: performanceLogger, name: name)
    }
    
    func measureTime<T>(operation: StaticString, block: () throws -> T) rethrows -> T {
        let startTime = CFAbsoluteTimeGetCurrent()
        defer {
            let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
            recordOperationTime(timeElapsed, operation: String(describing: operation))
        }
        
        os_signpost(.begin, log: performanceLogger, name: operation)
        defer { os_signpost(.end, log: performanceLogger, name: operation) }
        
        return try block()
    }
    
    func measureTimeAsync<T>(operation: StaticString, block: () async throws -> T) async rethrows -> T {
        let startTime = CFAbsoluteTimeGetCurrent()
        defer {
            let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
            recordOperationTime(timeElapsed, operation: String(describing: operation))
        }
        
        os_signpost(.begin, log: performanceLogger, name: operation)
        defer { os_signpost(.end, log: performanceLogger, name: operation) }
        
        return try await block()
    }
    
    func getPerformanceReport() -> PerformanceReport {
        let recentMetrics = Array(metricsHistory.suffix(60)) // Last minute
        return PerformanceReport(
            currentMetrics: currentMetrics,
            averageMetrics: calculateAverageMetrics(recentMetrics),
            violations: detectPerformanceViolations(),
            recommendations: generateOptimizationRecommendations()
        )
    }
    
    func optimizeForThermalState(_ state: ProcessInfo.ThermalState) {
        thermalState = state
        
        switch state {
        case .nominal:
            performanceMode = .maximum
        case .fair:
            performanceMode = .balanced
        case .serious:
            performanceMode = .efficient
        case .critical:
            performanceMode = .emergency
        @unknown default:
            performanceMode = .balanced
        }
        
        Logger.shared.info("Performance mode adjusted for thermal state: \(state)", category: .performance)
        NotificationCenter.default.post(name: .thermalStateChanged, object: state)
    }
    
    // MARK: - Private Methods
    
    private func updateMetrics() {
        let newMetrics = PerformanceMetrics(
            fps: fpsCounter.getCurrentFPS(),
            arFPS: fpsCounter.getARFPS(),
            memoryUsage: memoryMonitor.getCurrentUsage(),
            cpuUsage: cpuMonitor.getCurrentUsage(),
            batteryLevel: batteryMonitor.getBatteryLevel(),
            batteryState: batteryMonitor.getBatteryState(),
            thermalState: thermalState,
            networkBytesReceived: networkMonitor.getBytesReceived(),
            networkBytesSent: networkMonitor.getBytesSent(),
            timestamp: Date()
        )
        
        currentMetrics = newMetrics
        metricsHistory.append(newMetrics)
        
        // Trim history to maintain memory efficiency
        if metricsHistory.count > maxHistoryCount {
            metricsHistory.removeFirst(metricsHistory.count - maxHistoryCount)
        }
        
        // Check for performance violations
        checkPerformanceThresholds(newMetrics)
    }
    
    private func checkPerformanceThresholds(_ metrics: PerformanceMetrics) {
        // FPS violations
        if metrics.fps < performanceThresholds.minFPS {
            Logger.shared.warning("FPS below threshold: \(metrics.fps)", category: .performance)
            NotificationCenter.default.post(name: .performanceViolation, object: PerformanceViolation.lowFPS(metrics.fps))
        }
        
        // Memory violations
        if metrics.memoryUsage.used > performanceThresholds.maxMemoryMB * 1024 * 1024 {
            Logger.shared.warning("Memory usage above threshold: \(metrics.memoryUsage.used)", category: .performance)
            NotificationCenter.default.post(name: .performanceViolation, object: PerformanceViolation.highMemory(metrics.memoryUsage.used))
        }
        
        // CPU violations
        if metrics.cpuUsage > performanceThresholds.maxCPUPercentage {
            Logger.shared.warning("CPU usage above threshold: \(metrics.cpuUsage)", category: .performance)
            NotificationCenter.default.post(name: .performanceViolation, object: PerformanceViolation.highCPU(metrics.cpuUsage))
        }
    }
    
    private func recordOperationTime(_ duration: TimeInterval, operation: String) {
        if duration > 0.016 { // Longer than one frame at 60 FPS
            Logger.shared.debug("Slow operation detected: \(operation) took \(duration * 1000)ms", category: .performance)
        }
    }
    
    private func calculateAverageMetrics(_ metrics: [PerformanceMetrics]) -> PerformanceMetrics {
        guard !metrics.isEmpty else { return PerformanceMetrics() }
        
        let avgFPS = metrics.reduce(0) { $0 + $1.fps } / Double(metrics.count)
        let avgARFPS = metrics.reduce(0) { $0 + $1.arFPS } / Double(metrics.count)
        let avgCPU = metrics.reduce(0) { $0 + $1.cpuUsage } / Double(metrics.count)
        
        let totalMemory = metrics.reduce(0) { $0 + $1.memoryUsage.used }
        let avgMemoryUsed = totalMemory / UInt64(metrics.count)
        
        return PerformanceMetrics(
            fps: avgFPS,
            arFPS: avgARFPS,
            memoryUsage: MemoryUsage(used: avgMemoryUsed, available: metrics.last?.memoryUsage.available ?? 0),
            cpuUsage: avgCPU,
            batteryLevel: metrics.last?.batteryLevel ?? 0,
            batteryState: metrics.last?.batteryState ?? .unknown,
            thermalState: metrics.last?.thermalState ?? .nominal,
            networkBytesReceived: metrics.last?.networkBytesReceived ?? 0,
            networkBytesSent: metrics.last?.networkBytesSent ?? 0,
            timestamp: Date()
        )
    }
    
    private func detectPerformanceViolations() -> [PerformanceViolation] {
        var violations: [PerformanceViolation] = []
        
        if currentMetrics.fps < performanceThresholds.minFPS {
            violations.append(.lowFPS(currentMetrics.fps))
        }
        
        if currentMetrics.memoryUsage.used > performanceThresholds.maxMemoryMB * 1024 * 1024 {
            violations.append(.highMemory(currentMetrics.memoryUsage.used))
        }
        
        if currentMetrics.cpuUsage > performanceThresholds.maxCPUPercentage {
            violations.append(.highCPU(currentMetrics.cpuUsage))
        }
        
        if thermalState == .serious || thermalState == .critical {
            violations.append(.thermalThrottling(thermalState))
        }
        
        return violations
    }
    
    private func generateOptimizationRecommendations() -> [OptimizationRecommendation] {
        var recommendations: [OptimizationRecommendation] = []
        
        // FPS optimization
        if currentMetrics.fps < 50 {
            recommendations.append(.reduceFidelity)
            recommendations.append(.optimizeARProcessing)
        }
        
        // Memory optimization
        if currentMetrics.memoryUsage.used > 512 * 1024 * 1024 { // 512MB
            recommendations.append(.reduceMemoryUsage)
            recommendations.append(.optimizeAssetLoading)
        }
        
        // CPU optimization
        if currentMetrics.cpuUsage > 80 {
            recommendations.append(.reduceCPUUsage)
            recommendations.append(.optimizeMLInference)
        }
        
        // Thermal optimization
        if thermalState != .nominal {
            recommendations.append(.reduceThermalLoad)
        }
        
        return recommendations
    }
    
    private func setupThermalStateMonitoring() {
        NotificationCenter.default.publisher(for: ProcessInfo.thermalStateDidChangeNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.optimizeForThermalState(ProcessInfo.processInfo.thermalState)
                }
            }
            .store(in: &cancellables)
    }
    
    private func setupNotificationObservers() {
        NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)
            .sink { [weak self] _ in
                self?.stopMonitoring()
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)
            .sink { [weak self] _ in
                self?.startMonitoring()
            }
            .store(in: &cancellables)
    }
}

// MARK: - Supporting Classes

class FPSCounter {
    private var frameCount = 0
    private var lastTimestamp = CFAbsoluteTimeGetCurrent()
    private var currentFPS: Double = 60.0
    private var arFrameCount = 0
    private var arFPS: Double = 60.0
    private var componentFrameTimes: [PerformanceComponent: [TimeInterval]] = [:]
    
    func startMonitoring() {
        frameCount = 0
        arFrameCount = 0
        lastTimestamp = CFAbsoluteTimeGetCurrent()
    }
    
    func stopMonitoring() {
        componentFrameTimes.removeAll()
    }
    
    func recordFrame() {
        frameCount += 1
        
        let currentTime = CFAbsoluteTimeGetCurrent()
        let elapsed = currentTime - lastTimestamp
        
        if elapsed >= 1.0 {
            currentFPS = Double(frameCount) / elapsed
            frameCount = 0
            lastTimestamp = currentTime
        }
    }
    
    func recordARFrame() {
        arFrameCount += 1
    }
    
    func recordFrameTime(_ duration: TimeInterval, for component: PerformanceComponent) {
        if componentFrameTimes[component] == nil {
            componentFrameTimes[component] = []
        }
        
        componentFrameTimes[component]?.append(duration)
        
        // Keep only recent frame times
        if let count = componentFrameTimes[component]?.count, count > 60 {
            componentFrameTimes[component]?.removeFirst()
        }
    }
    
    func getCurrentFPS() -> Double {
        return currentFPS
    }
    
    func getARFPS() -> Double {
        return arFPS
    }
    
    func getAverageFrameTime(for component: PerformanceComponent) -> TimeInterval {
        guard let frameTimes = componentFrameTimes[component], !frameTimes.isEmpty else {
            return 0
        }
        return frameTimes.reduce(0, +) / Double(frameTimes.count)
    }
}

class MemoryMonitor {
    func startMonitoring() {}
    func stopMonitoring() {}
    
    func getCurrentUsage() -> MemoryUsage {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let kerr = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        let used = kerr == KERN_SUCCESS ? info.resident_size : 0
        let available = ProcessInfo.processInfo.physicalMemory - used
        
        return MemoryUsage(used: used, available: available)
    }
    
    func detectMemoryLeaks() -> [MemoryLeak] {
        // Simplified leak detection - in production, this would be more sophisticated
        let usage = getCurrentUsage()
        var leaks: [MemoryLeak] = []
        
        if usage.used > 1024 * 1024 * 1024 { // 1GB threshold
            leaks.append(MemoryLeak(
                component: "General",
                size: usage.used,
                description: "High memory usage detected"
            ))
        }
        
        return leaks
    }
}

class CPUMonitor {
    private var previousCPUTime: UInt64 = 0
    private var previousWallTime: UInt64 = 0
    
    func startMonitoring() {
        previousCPUTime = mach_absolute_time()
        previousWallTime = mach_absolute_time()
    }
    
    func stopMonitoring() {}
    
    func getCurrentUsage() -> Double {
        var info = processor_info_array_t.allocate(capacity: 1)
        var numCpuInfo: mach_msg_type_number_t = 0
        var numCpus: natural_t = 0
        
        let result = host_processor_info(mach_host_self(), PROCESSOR_CPU_LOAD_INFO, &numCpus, &info, &numCpuInfo)
        
        guard result == KERN_SUCCESS else {
            return 0.0
        }
        
        defer {
            vm_deallocate(mach_task_self_, vm_address_t(bitPattern: info), vm_size_t(numCpuInfo))
        }
        
        let cpuLoad = info.withMemoryRebound(to: processor_cpu_load_info.self, capacity: Int(numCpus)) { ptr in
            var totalUser: natural_t = 0
            var totalSystem: natural_t = 0
            var totalIdle: natural_t = 0
            
            for i in 0..<Int(numCpus) {
                totalUser += ptr[i].cpu_ticks.0 // CPU_STATE_USER
                totalSystem += ptr[i].cpu_ticks.1 // CPU_STATE_SYSTEM
                totalIdle += ptr[i].cpu_ticks.2 // CPU_STATE_IDLE
            }
            
            let total = totalUser + totalSystem + totalIdle
            return total > 0 ? Double(totalUser + totalSystem) / Double(total) * 100.0 : 0.0
        }
        
        return cpuLoad
    }
}

class BatteryMonitor {
    func startMonitoring() {
        UIDevice.current.isBatteryMonitoringEnabled = true
    }
    
    func stopMonitoring() {
        UIDevice.current.isBatteryMonitoringEnabled = false
    }
    
    func getBatteryLevel() -> Float {
        return UIDevice.current.batteryLevel
    }
    
    func getBatteryState() -> UIDevice.BatteryState {
        return UIDevice.current.batteryState
    }
}

class NetworkMonitor {
    private var previousBytesReceived: UInt64 = 0
    private var previousBytesSent: UInt64 = 0
    
    func startMonitoring() {
        let (received, sent) = getNetworkUsage()
        previousBytesReceived = received
        previousBytesSent = sent
    }
    
    func stopMonitoring() {}
    
    func getBytesReceived() -> UInt64 {
        return getNetworkUsage().0
    }
    
    func getBytesSent() -> UInt64 {
        return getNetworkUsage().1
    }
    
    private func getNetworkUsage() -> (received: UInt64, sent: UInt64) {
        var ifaddr: UnsafeMutablePointer<ifaddrs>?
        var received: UInt64 = 0
        var sent: UInt64 = 0
        
        guard getifaddrs(&ifaddr) == 0 else { return (0, 0) }
        defer { freeifaddrs(ifaddr) }
        
        var ptr = ifaddr
        while ptr != nil {
            defer { ptr = ptr?.pointee.ifa_next }
            
            guard let addr = ptr?.pointee.ifa_addr,
                  addr.pointee.sa_family == UInt8(AF_LINK) else { continue }
            
            let data = unsafeBitCast(addr, to: UnsafeMutablePointer<sockaddr_dl>.self)
            
            if data.pointee.sdl_type == IFT_ETHER || data.pointee.sdl_type == IFT_CELLULAR {
                if let networkData = ptr?.pointee.ifa_data?.assumingMemoryBound(to: if_data.self) {
                    received += UInt64(networkData.pointee.ifi_ibytes)
                    sent += UInt64(networkData.pointee.ifi_obytes)
                }
            }
        }
        
        return (received, sent)
    }
}

// MARK: - Supporting Types

struct PerformanceMetrics {
    let fps: Double
    let arFPS: Double
    let memoryUsage: MemoryUsage
    let cpuUsage: Double
    let batteryLevel: Float
    let batteryState: UIDevice.BatteryState
    let thermalState: ProcessInfo.ThermalState
    let networkBytesReceived: UInt64
    let networkBytesSent: UInt64
    let timestamp: Date
    
    init(fps: Double = 60.0,
         arFPS: Double = 60.0,
         memoryUsage: MemoryUsage = MemoryUsage(used: 0, available: 0),
         cpuUsage: Double = 0.0,
         batteryLevel: Float = 1.0,
         batteryState: UIDevice.BatteryState = .unknown,
         thermalState: ProcessInfo.ThermalState = .nominal,
         networkBytesReceived: UInt64 = 0,
         networkBytesSent: UInt64 = 0,
         timestamp: Date = Date()) {
        self.fps = fps
        self.arFPS = arFPS
        self.memoryUsage = memoryUsage
        self.cpuUsage = cpuUsage
        self.batteryLevel = batteryLevel
        self.batteryState = batteryState
        self.thermalState = thermalState
        self.networkBytesReceived = networkBytesReceived
        self.networkBytesSent = networkBytesSent
        self.timestamp = timestamp
    }
}

struct MemoryUsage {
    let used: UInt64
    let available: UInt64
    
    var usedMB: Double {
        return Double(used) / (1024 * 1024)
    }
    
    var availableMB: Double {
        return Double(available) / (1024 * 1024)
    }
    
    var usagePercentage: Double {
        let total = used + available
        return total > 0 ? Double(used) / Double(total) * 100.0 : 0.0
    }
}

struct MemoryLeak {
    let component: String
    let size: UInt64
    let description: String
}

enum PerformanceComponent: String, CaseIterable {
    case ui = "UI"
    case arKit = "ARKit"
    case coreML = "CoreML"
    case audio = "Audio"
    case networking = "Networking"
    case database = "Database"
}

enum PerformanceMode: String, CaseIterable {
    case maximum = "Maximum"
    case balanced = "Balanced"
    case efficient = "Efficient"
    case emergency = "Emergency"
    case adaptive = "Adaptive"
}

enum PerformanceViolation {
    case lowFPS(Double)
    case highMemory(UInt64)
    case highCPU(Double)
    case thermalThrottling(ProcessInfo.ThermalState)
    case networkTimeout
    case batteryDrainHigh
}

enum OptimizationRecommendation: String, CaseIterable {
    case reduceFidelity = "Reduce visual fidelity"
    case optimizeARProcessing = "Optimize AR processing"
    case reduceMemoryUsage = "Reduce memory usage"
    case optimizeAssetLoading = "Optimize asset loading"
    case reduceCPUUsage = "Reduce CPU usage"
    case optimizeMLInference = "Optimize ML inference"
    case reduceThermalLoad = "Reduce thermal load"
    case optimizeNetworking = "Optimize networking"
}

struct PerformanceThresholds {
    let minFPS: Double = 30.0
    let maxMemoryMB: UInt64 = 512
    let maxCPUPercentage: Double = 80.0
    let maxBatteryDrainPerHour: Double = 20.0 // 20% per hour
    let maxNetworkLatency: TimeInterval = 2.0
}

struct PerformanceReport {
    let currentMetrics: PerformanceMetrics
    let averageMetrics: PerformanceMetrics
    let violations: [PerformanceViolation]
    let recommendations: [OptimizationRecommendation]
}

// MARK: - Notification Extensions

extension Notification.Name {
    static let performanceViolation = Notification.Name("PerformanceViolation")
    static let thermalStateChanged = Notification.Name("ThermalStateChanged")
    static let performanceModeChanged = Notification.Name("PerformanceModeChanged")
}