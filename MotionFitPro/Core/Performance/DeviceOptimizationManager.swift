import Foundation
import UIKit
import ARKit
import CoreML

@MainActor
class DeviceOptimizationManager: ObservableObject {
    static let shared = DeviceOptimizationManager()
    
    @Published var currentDevice: DeviceProfile
    @Published var optimizationLevel: OptimizationLevel = .balanced
    @Published var qualitySettings: QualitySettings
    @Published var isAdaptiveMode = true
    
    // Device capabilities
    private let deviceCapabilities: DeviceCapabilities
    private let performanceProfiler = PerformanceProfiler.shared
    
    // Quality scaling
    private var thermalThrottleStartTime: Date?
    private var adaptiveQualityEnabled = true
    
    private init() {
        self.currentDevice = DeviceProfileDetector.detectCurrentDevice()
        self.deviceCapabilities = DeviceCapabilities(device: currentDevice)
        self.qualitySettings = QualitySettings.default(for: currentDevice)
        
        setupPerformanceMonitoring()
        setupThermalStateObserver()
    }
    
    // MARK: - Public Methods
    
    func optimizeForCurrentDevice() {
        Logger.shared.info("Optimizing for device: \(currentDevice.name)", category: .performance)
        
        // Set base quality settings
        qualitySettings = QualitySettings.optimized(for: currentDevice)
        
        // Apply device-specific optimizations
        applyDeviceSpecificOptimizations()
        
        // Configure AR session
        configureARSession()
        
        // Configure Core ML
        configureCoreML()
        
        Logger.shared.info("Device optimization completed", category: .performance)
    }
    
    func adaptToPerformance(metrics: PerformanceMetrics) {
        guard isAdaptiveMode else { return }
        
        let shouldDowngrade = shouldDowngradeQuality(metrics)
        let shouldUpgrade = shouldUpgradeQuality(metrics)
        
        if shouldDowngrade {
            downgradeQuality()
        } else if shouldUpgrade {
            upgradeQuality()
        }
        
        // Update optimization level
        updateOptimizationLevel(based: metrics)
    }
    
    func setOptimizationLevel(_ level: OptimizationLevel) {
        optimizationLevel = level
        applyOptimizationLevel(level)
        Logger.shared.info("Optimization level set to: \(level)", category: .performance)
    }
    
    func enableAdaptiveMode(_ enabled: Bool) {
        isAdaptiveMode = enabled
        if enabled {
            Logger.shared.info("Adaptive performance mode enabled", category: .performance)
        } else {
            Logger.shared.info("Adaptive performance mode disabled", category: .performance)
        }
    }
    
    func getRecommendedSettings(for workoutType: WorkoutType) -> QualitySettings {
        var settings = qualitySettings
        
        switch workoutType {
        case .highIntensity:
            // Reduce quality for demanding workouts
            settings.arProcessingQuality = max(settings.arProcessingQuality - 1, 1)
            settings.mlInferenceFrequency = max(settings.mlInferenceFrequency - 5, 10)
        case .precision:
            // Maintain high quality for form-focused workouts
            settings.arProcessingQuality = min(settings.arProcessingQuality + 1, 3)
            settings.mlInferenceFrequency = min(settings.mlInferenceFrequency + 5, 30)
        case .endurance:
            // Optimize for battery life
            settings.batteryOptimizationEnabled = true
            settings.backgroundRefreshRate = 0.5
        }
        
        return settings
    }
    
    // MARK: - Private Methods
    
    private func setupPerformanceMonitoring() {
        NotificationCenter.default.addObserver(
            forName: .performanceViolation,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            if let violation = notification.object as? PerformanceViolation {
                self?.handlePerformanceViolation(violation)
            }
        }
    }
    
    private func setupThermalStateObserver() {
        NotificationCenter.default.addObserver(
            forName: .thermalStateChanged,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            if let thermalState = notification.object as? ProcessInfo.ThermalState {
                self?.handleThermalStateChange(thermalState)
            }
        }
    }
    
    private func applyDeviceSpecificOptimizations() {
        switch currentDevice.tier {
        case .flagship:
            applyFlagshipOptimizations()
        case .highEnd:
            applyHighEndOptimizations()
        case .midRange:
            applyMidRangeOptimizations()
        case .lowEnd:
            applyLowEndOptimizations()
        }
    }
    
    private func applyFlagshipOptimizations() {
        qualitySettings = QualitySettings(
            arProcessingQuality: 3, // Highest
            mlInferenceFrequency: 30, // 30 FPS
            targetFrameRate: 60,
            visualEffectsEnabled: true,
            hdrEnabled: true,
            antiAliasingEnabled: true,
            shadowsEnabled: true,
            reflectionsEnabled: true,
            particleEffectsQuality: 3,
            audioProcessingQuality: 3,
            hapticFeedbackEnabled: true,
            backgroundRefreshRate: 1.0,
            batteryOptimizationEnabled: false,
            memoryOptimizationLevel: 1
        )
    }
    
    private func applyHighEndOptimizations() {
        qualitySettings = QualitySettings(
            arProcessingQuality: 3,
            mlInferenceFrequency: 25,
            targetFrameRate: 60,
            visualEffectsEnabled: true,
            hdrEnabled: true,
            antiAliasingEnabled: true,
            shadowsEnabled: true,
            reflectionsEnabled: false,
            particleEffectsQuality: 2,
            audioProcessingQuality: 3,
            hapticFeedbackEnabled: true,
            backgroundRefreshRate: 1.0,
            batteryOptimizationEnabled: false,
            memoryOptimizationLevel: 1
        )
    }
    
    private func applyMidRangeOptimizations() {
        qualitySettings = QualitySettings(
            arProcessingQuality: 2,
            mlInferenceFrequency: 20,
            targetFrameRate: 60,
            visualEffectsEnabled: true,
            hdrEnabled: false,
            antiAliasingEnabled: false,
            shadowsEnabled: false,
            reflectionsEnabled: false,
            particleEffectsQuality: 2,
            audioProcessingQuality: 2,
            hapticFeedbackEnabled: true,
            backgroundRefreshRate: 0.75,
            batteryOptimizationEnabled: true,
            memoryOptimizationLevel: 2
        )
    }
    
    private func applyLowEndOptimizations() {
        qualitySettings = QualitySettings(
            arProcessingQuality: 1,
            mlInferenceFrequency: 15,
            targetFrameRate: 30,
            visualEffectsEnabled: false,
            hdrEnabled: false,
            antiAliasingEnabled: false,
            shadowsEnabled: false,
            reflectionsEnabled: false,
            particleEffectsQuality: 1,
            audioProcessingQuality: 1,
            hapticFeedbackEnabled: false,
            backgroundRefreshRate: 0.5,
            batteryOptimizationEnabled: true,
            memoryOptimizationLevel: 3
        )
    }
    
    private func configureARSession() {
        // Configure AR session based on device capabilities
        let arConfig = ARWorldTrackingConfiguration()
        
        if deviceCapabilities.supportsBodyTracking {
            if let bodyTrackingConfig = ARBodyTrackingConfiguration.self as? ARConfiguration.Type {
                // Use body tracking if supported
            }
        }
        
        // Set frame rate
        arConfig.videoFormat = selectOptimalVideoFormat()
        
        // Configure plane detection based on quality settings
        if qualitySettings.arProcessingQuality >= 2 {
            arConfig.planeDetection = [.horizontal, .vertical]
        } else {
            arConfig.planeDetection = .horizontal
        }
        
        // Configure environment texturing
        if qualitySettings.arProcessingQuality >= 3 && deviceCapabilities.supportsEnvironmentTexturing {
            arConfig.environmentTexturing = .automatic
        } else {
            arConfig.environmentTexturing = .none
        }
    }
    
    private func selectOptimalVideoFormat() -> ARVideoFormat {
        let availableFormats = ARWorldTrackingConfiguration.supportedVideoFormats
        
        // Target resolution and frame rate based on device
        let targetWidth: Int
        let targetFrameRate: Int
        
        switch currentDevice.tier {
        case .flagship, .highEnd:
            targetWidth = 1920
            targetFrameRate = qualitySettings.targetFrameRate
        case .midRange:
            targetWidth = 1280
            targetFrameRate = qualitySettings.targetFrameRate
        case .lowEnd:
            targetWidth = 640
            targetFrameRate = 30
        }
        
        // Find best matching format
        let bestFormat = availableFormats.min { format1, format2 in
            let width1Diff = abs(format1.imageResolution.width - targetWidth)
            let width2Diff = abs(format2.imageResolution.width - targetWidth)
            
            let fps1Diff = abs(format1.framesPerSecond - targetFrameRate)
            let fps2Diff = abs(format2.framesPerSecond - targetFrameRate)
            
            return (width1Diff + fps1Diff) < (width2Diff + fps2Diff)
        }
        
        return bestFormat ?? availableFormats.first!
    }
    
    private func configureCoreML() {
        // Core ML configuration will be handled by MLOptimizationManager
        NotificationCenter.default.post(
            name: .coreMLConfigurationChanged,
            object: qualitySettings
        )
    }
    
    private func shouldDowngradeQuality(_ metrics: PerformanceMetrics) -> Bool {
        // Check multiple performance indicators
        let fpsBelow45 = metrics.fps < 45
        let cpuHigh = metrics.cpuUsage > 85
        let memoryHigh = metrics.memoryUsage.usagePercentage > 80
        let thermalCritical = metrics.thermalState == .critical || metrics.thermalState == .serious
        
        return fpsBelow45 || cpuHigh || memoryHigh || thermalCritical
    }
    
    private func shouldUpgradeQuality(_ metrics: PerformanceMetrics) -> Bool {
        // Only upgrade if performance is consistently good
        let fpsGood = metrics.fps > 55
        let cpuLow = metrics.cpuUsage < 60
        let memoryLow = metrics.memoryUsage.usagePercentage < 60
        let thermalNormal = metrics.thermalState == .nominal
        
        return fpsGood && cpuLow && memoryLow && thermalNormal
    }
    
    private func downgradeQuality() {
        guard qualitySettings.canDowngrade() else { return }
        
        Logger.shared.info("Downgrading quality settings due to performance", category: .performance)
        
        // Reduce quality step by step
        if qualitySettings.visualEffectsEnabled {
            qualitySettings.visualEffectsEnabled = false
        } else if qualitySettings.particleEffectsQuality > 1 {
            qualitySettings.particleEffectsQuality -= 1
        } else if qualitySettings.arProcessingQuality > 1 {
            qualitySettings.arProcessingQuality -= 1
        } else if qualitySettings.mlInferenceFrequency > 10 {
            qualitySettings.mlInferenceFrequency -= 5
        } else if qualitySettings.targetFrameRate > 30 {
            qualitySettings.targetFrameRate = 30
        }
        
        NotificationCenter.default.post(name: .qualitySettingsChanged, object: qualitySettings)
    }
    
    private func upgradeQuality() {
        guard qualitySettings.canUpgrade() else { return }
        
        Logger.shared.info("Upgrading quality settings due to good performance", category: .performance)
        
        // Increase quality step by step
        if qualitySettings.targetFrameRate < 60 {
            qualitySettings.targetFrameRate = 60
        } else if qualitySettings.mlInferenceFrequency < 25 {
            qualitySettings.mlInferenceFrequency += 5
        } else if qualitySettings.arProcessingQuality < 3 {
            qualitySettings.arProcessingQuality += 1
        } else if qualitySettings.particleEffectsQuality < 3 {
            qualitySettings.particleEffectsQuality += 1
        } else if !qualitySettings.visualEffectsEnabled && deviceCapabilities.supportsAdvancedEffects {
            qualitySettings.visualEffectsEnabled = true
        }
        
        NotificationCenter.default.post(name: .qualitySettingsChanged, object: qualitySettings)
    }
    
    private func updateOptimizationLevel(based metrics: PerformanceMetrics) {
        let newLevel: OptimizationLevel
        
        if metrics.thermalState == .critical || metrics.fps < 25 {
            newLevel = .emergency
        } else if metrics.thermalState == .serious || metrics.fps < 35 {
            newLevel = .efficient
        } else if metrics.fps < 50 || metrics.cpuUsage > 80 {
            newLevel = .balanced
        } else {
            newLevel = .maximum
        }
        
        if newLevel != optimizationLevel {
            optimizationLevel = newLevel
            applyOptimizationLevel(newLevel)
        }
    }
    
    private func applyOptimizationLevel(_ level: OptimizationLevel) {
        switch level {
        case .maximum:
            applyMaximumPerformanceSettings()
        case .balanced:
            applyBalancedSettings()
        case .efficient:
            applyEfficientSettings()
        case .emergency:
            applyEmergencySettings()
        }
        
        NotificationCenter.default.post(name: .optimizationLevelChanged, object: level)
    }
    
    private func applyMaximumPerformanceSettings() {
        qualitySettings.batteryOptimizationEnabled = false
        qualitySettings.backgroundRefreshRate = 1.0
    }
    
    private func applyBalancedSettings() {
        qualitySettings.batteryOptimizationEnabled = true
        qualitySettings.backgroundRefreshRate = 0.75
    }
    
    private func applyEfficientSettings() {
        qualitySettings.batteryOptimizationEnabled = true
        qualitySettings.backgroundRefreshRate = 0.5
        qualitySettings.mlInferenceFrequency = max(qualitySettings.mlInferenceFrequency - 5, 10)
    }
    
    private func applyEmergencySettings() {
        qualitySettings.batteryOptimizationEnabled = true
        qualitySettings.backgroundRefreshRate = 0.25
        qualitySettings.visualEffectsEnabled = false
        qualitySettings.targetFrameRate = 30
        qualitySettings.mlInferenceFrequency = 10
    }
    
    private func handlePerformanceViolation(_ violation: PerformanceViolation) {
        switch violation {
        case .lowFPS(let fps):
            if fps < 25 {
                setOptimizationLevel(.emergency)
            } else if fps < 35 {
                setOptimizationLevel(.efficient)
            }
        case .highMemory, .highCPU:
            downgradeQuality()
        case .thermalThrottling(let state):
            handleThermalStateChange(state)
        case .networkTimeout, .batteryDrainHigh:
            qualitySettings.batteryOptimizationEnabled = true
        }
    }
    
    private func handleThermalStateChange(_ state: ProcessInfo.ThermalState) {
        switch state {
        case .nominal:
            thermalThrottleStartTime = nil
            if isAdaptiveMode {
                setOptimizationLevel(.balanced)
            }
        case .fair:
            setOptimizationLevel(.balanced)
        case .serious:
            thermalThrottleStartTime = Date()
            setOptimizationLevel(.efficient)
        case .critical:
            thermalThrottleStartTime = Date()
            setOptimizationLevel(.emergency)
        @unknown default:
            setOptimizationLevel(.balanced)
        }
    }
}

// MARK: - Supporting Types

struct DeviceProfile {
    let name: String
    let tier: DeviceTier
    let chipset: String
    let memoryGB: Int
    let supportsBodyTracking: Bool
    let supportsLiDAR: Bool
    let neuralEngineVersion: Int
    let maxConcurrentMLModels: Int
    
    enum DeviceTier {
        case flagship    // iPhone 15 Pro, iPhone 14 Pro
        case highEnd     // iPhone 15, iPhone 14
        case midRange    // iPhone 13, iPhone 12
        case lowEnd      // iPhone SE, older models
    }
}

struct DeviceCapabilities {
    let supportsBodyTracking: Bool
    let supportsEnvironmentTexturing: Bool
    let supportsAdvancedEffects: Bool
    let supportsHighResolutionAR: Bool
    let supportsNeuralEngine: Bool
    let maxMLInferenceThreads: Int
    
    init(device: DeviceProfile) {
        self.supportsBodyTracking = device.supportsBodyTracking
        self.supportsEnvironmentTexturing = device.tier == .flagship || device.tier == .highEnd
        self.supportsAdvancedEffects = device.memoryGB >= 6
        self.supportsHighResolutionAR = device.tier == .flagship
        self.supportsNeuralEngine = device.neuralEngineVersion > 0
        self.maxMLInferenceThreads = device.maxConcurrentMLModels
    }
}

struct QualitySettings {
    var arProcessingQuality: Int // 1-3
    var mlInferenceFrequency: Int // Hz
    var targetFrameRate: Int
    var visualEffectsEnabled: Bool
    var hdrEnabled: Bool
    var antiAliasingEnabled: Bool
    var shadowsEnabled: Bool
    var reflectionsEnabled: Bool
    var particleEffectsQuality: Int // 1-3
    var audioProcessingQuality: Int // 1-3
    var hapticFeedbackEnabled: Bool
    var backgroundRefreshRate: Double // 0.0-1.0
    var batteryOptimizationEnabled: Bool
    var memoryOptimizationLevel: Int // 1-3
    
    static func `default`(for device: DeviceProfile) -> QualitySettings {
        switch device.tier {
        case .flagship:
            return QualitySettings(
                arProcessingQuality: 3,
                mlInferenceFrequency: 30,
                targetFrameRate: 60,
                visualEffectsEnabled: true,
                hdrEnabled: true,
                antiAliasingEnabled: true,
                shadowsEnabled: true,
                reflectionsEnabled: true,
                particleEffectsQuality: 3,
                audioProcessingQuality: 3,
                hapticFeedbackEnabled: true,
                backgroundRefreshRate: 1.0,
                batteryOptimizationEnabled: false,
                memoryOptimizationLevel: 1
            )
        case .highEnd:
            return QualitySettings(
                arProcessingQuality: 2,
                mlInferenceFrequency: 25,
                targetFrameRate: 60,
                visualEffectsEnabled: true,
                hdrEnabled: false,
                antiAliasingEnabled: true,
                shadowsEnabled: false,
                reflectionsEnabled: false,
                particleEffectsQuality: 2,
                audioProcessingQuality: 2,
                hapticFeedbackEnabled: true,
                backgroundRefreshRate: 0.75,
                batteryOptimizationEnabled: true,
                memoryOptimizationLevel: 2
            )
        case .midRange:
            return QualitySettings(
                arProcessingQuality: 2,
                mlInferenceFrequency: 20,
                targetFrameRate: 60,
                visualEffectsEnabled: false,
                hdrEnabled: false,
                antiAliasingEnabled: false,
                shadowsEnabled: false,
                reflectionsEnabled: false,
                particleEffectsQuality: 1,
                audioProcessingQuality: 2,
                hapticFeedbackEnabled: true,
                backgroundRefreshRate: 0.5,
                batteryOptimizationEnabled: true,
                memoryOptimizationLevel: 2
            )
        case .lowEnd:
            return QualitySettings(
                arProcessingQuality: 1,
                mlInferenceFrequency: 15,
                targetFrameRate: 30,
                visualEffectsEnabled: false,
                hdrEnabled: false,
                antiAliasingEnabled: false,
                shadowsEnabled: false,
                reflectionsEnabled: false,
                particleEffectsQuality: 1,
                audioProcessingQuality: 1,
                hapticFeedbackEnabled: false,
                backgroundRefreshRate: 0.25,
                batteryOptimizationEnabled: true,
                memoryOptimizationLevel: 3
            )
        }
    }
    
    static func optimized(for device: DeviceProfile) -> QualitySettings {
        var settings = QualitySettings.default(for: device)
        
        // Apply device-specific optimizations
        if device.memoryGB < 4 {
            settings.memoryOptimizationLevel = 3
            settings.arProcessingQuality = min(settings.arProcessingQuality, 2)
        }
        
        if device.neuralEngineVersion < 3 {
            settings.mlInferenceFrequency = min(settings.mlInferenceFrequency, 20)
        }
        
        return settings
    }
    
    func canDowngrade() -> Bool {
        return arProcessingQuality > 1 || 
               mlInferenceFrequency > 10 || 
               targetFrameRate > 30 || 
               visualEffectsEnabled || 
               particleEffectsQuality > 1
    }
    
    func canUpgrade() -> Bool {
        return arProcessingQuality < 3 || 
               mlInferenceFrequency < 30 || 
               targetFrameRate < 60 || 
               !visualEffectsEnabled || 
               particleEffectsQuality < 3
    }
}

enum OptimizationLevel: String, CaseIterable {
    case maximum = "Maximum Performance"
    case balanced = "Balanced"
    case efficient = "Efficient"
    case emergency = "Emergency"
}

enum WorkoutType {
    case highIntensity
    case precision
    case endurance
}

class DeviceProfileDetector {
    static func detectCurrentDevice() -> DeviceProfile {
        let device = UIDevice.current
        var systemInfo = utsname()
        uname(&systemInfo)
        
        let deviceIdentifier = withUnsafePointer(to: &systemInfo.machine) {
            $0.withMemoryRebound(to: CChar.self, capacity: 1) {
                String(validatingUTF8: $0) ?? "Unknown"
            }
        }
        
        return parseDeviceProfile(from: deviceIdentifier)
    }
    
    private static func parseDeviceProfile(from identifier: String) -> DeviceProfile {
        switch identifier {
        case let id where id.contains("iPhone16"):
            return DeviceProfile(
                name: "iPhone 15 Pro Max",
                tier: .flagship,
                chipset: "A17 Pro",
                memoryGB: 8,
                supportsBodyTracking: true,
                supportsLiDAR: true,
                neuralEngineVersion: 4,
                maxConcurrentMLModels: 4
            )
        case let id where id.contains("iPhone15"):
            return DeviceProfile(
                name: "iPhone 15 Pro",
                tier: .flagship,
                chipset: "A17 Pro",
                memoryGB: 8,
                supportsBodyTracking: true,
                supportsLiDAR: true,
                neuralEngineVersion: 4,
                maxConcurrentMLModels: 4
            )
        case let id where id.contains("iPhone14"):
            return DeviceProfile(
                name: "iPhone 14 Pro",
                tier: .highEnd,
                chipset: "A16 Bionic",
                memoryGB: 6,
                supportsBodyTracking: true,
                supportsLiDAR: true,
                neuralEngineVersion: 3,
                maxConcurrentMLModels: 3
            )
        case let id where id.contains("iPhone13"):
            return DeviceProfile(
                name: "iPhone 13",
                tier: .midRange,
                chipset: "A15 Bionic",
                memoryGB: 4,
                supportsBodyTracking: true,
                supportsLiDAR: false,
                neuralEngineVersion: 3,
                maxConcurrentMLModels: 2
            )
        default:
            return DeviceProfile(
                name: "iPhone (Generic)",
                tier: .midRange,
                chipset: "A14 Bionic",
                memoryGB: 4,
                supportsBodyTracking: false,
                supportsLiDAR: false,
                neuralEngineVersion: 2,
                maxConcurrentMLModels: 2
            )
        }
    }
}

// MARK: - Notification Extensions

extension Notification.Name {
    static let qualitySettingsChanged = Notification.Name("QualitySettingsChanged")
    static let optimizationLevelChanged = Notification.Name("OptimizationLevelChanged")
    static let coreMLConfigurationChanged = Notification.Name("CoreMLConfigurationChanged")
}