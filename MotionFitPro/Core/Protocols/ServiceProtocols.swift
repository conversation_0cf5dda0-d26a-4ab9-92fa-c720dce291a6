import Foundation
import Combine
import ARKit
import SwiftUI

// MARK: - Core Service Protocols

/// Protocol for logging service
protocol LoggerServiceProtocol {
    func log(_ message: String, level: LogLevel, category: Logger.Category)
    func debug(_ message: String, category: Logger.Category)
    func info(_ message: String, category: Logger.Category)
    func warning(_ message: String, category: Logger.Category)
    func error(_ message: String, category: Logger.Category)
}

/// Protocol for haptic feedback service
protocol HapticServiceProtocol {
    func trigger(_ type: HapticType)
    func triggerSuccess()
    func triggerWarning()
    func triggerError()
    func triggerSelection()
}

/// Protocol for audio service
protocol AudioServiceProtocol {
    func playSound(_ effect: SoundEffect)
    func speak(_ text: String, priority: SpeechPriority)
    func pauseSpeech()
    func resumeSpeech()
    func stopSpeech()
    var isSpeaking: Bool { get }
}

// MARK: - Data Repository Protocols

/// Protocol for user profile repository
protocol UserProfileRepositoryProtocol {
    func getCurrentProfile() async throws -> UserProfile?
    func saveProfile(_ profile: UserProfile) async throws
    func updateProfile(_ profile: UserProfile) async throws
    func deleteProfile(_ profileId: UUID) async throws
    func getAllProfiles() async throws -> [UserProfile]
}

/// Protocol for workout repository
protocol WorkoutRepositoryProtocol {
    func getWorkouts(for userId: UUID) async throws -> [WorkoutSession]
    func saveWorkout(_ workout: WorkoutSession) async throws
    func updateWorkout(_ workout: WorkoutSession) async throws
    func deleteWorkout(_ workoutId: UUID) async throws
    func getWorkoutHistory(for userId: UUID, limit: Int) async throws -> [WorkoutSession]
}

/// Protocol for exercise repository
protocol ExerciseRepositoryProtocol {
    func getAllExercises() async throws -> [ExerciseType]
    func getExercise(by type: ExerciseType) async throws -> ExerciseType?
    func getExercisesForMuscleGroup(_ muscleGroup: MuscleGroup) async throws -> [ExerciseType]
    func getExercisesForDifficulty(_ difficulty: DifficultyLevel) async throws -> [ExerciseType]
}

// MARK: - ML Service Protocols

/// Protocol for ML processing service
protocol MLProcessingServiceProtocol {
    func processBodyPose(_ poseData: BodyPoseData) async throws -> ExerciseAnalysis
    func classifyExercise(_ poseData: BodyPoseData) async throws -> ExerciseType
    func analyzeForm(_ poseData: BodyPoseData, for exercise: ExerciseType) async throws -> FormAnalysis
    func detectMovementPhase(_ poseData: BodyPoseData, for exercise: ExerciseType) async throws -> MovementPhase
    var isProcessing: Bool { get }
}

/// Protocol for form analysis service
protocol FormAnalysisServiceProtocol {
    func analyzeForm(_ poseData: BodyPoseData, for exercise: ExerciseType) async throws -> FormAnalysis
    func detectFormIssues(_ analysis: FormAnalysis) -> [FormIssue]
    func generateRecommendations(_ analysis: FormAnalysis) -> [String]
    func calculateOverallScore(_ scores: [FormCriteria: Float]) -> Float
}

// MARK: - AR Service Protocols

/// Protocol for AR tracking service
protocol ARTrackingServiceProtocol {
    func startTracking() async throws
    func stopTracking()
    func pauseTracking()
    func resumeTracking()
    var isTracking: Bool { get }
    var trackingQuality: TrackingQuality { get }
    var bodyPosePublisher: AnyPublisher<BodyPoseData, Never> { get }
}

/// Protocol for body pose service
protocol BodyPoseServiceProtocol {
    func processARFrame(_ frame: ARFrame) async -> BodyPoseData?
    func extractJoints(from bodyAnchor: ARBodyAnchor) -> [JointName: Joint3D]
    func calculateConfidence(for joints: [JointName: Joint3D]) -> Float
    func validatePoseData(_ poseData: BodyPoseData) -> Bool
}

// MARK: - Feedback Service Protocols

/// Protocol for feedback service
protocol FeedbackServiceProtocol {
    func generateFeedback(for analysis: ExerciseAnalysis, personality: CoachingPersonality) -> FormFeedback
    func deliverFeedback(_ feedback: FormFeedback) async
    func queueFeedback(_ feedback: FormFeedback)
    func clearFeedbackQueue()
    var pendingFeedback: [FormFeedback] { get }
}

/// Protocol for coaching service
protocol CoachingServiceProtocol {
    func generateCoachingMessage(for context: FeedbackContext, personality: CoachingPersonality) -> String
    func adaptToUserProgress(_ progress: UserProgress) -> CoachingPersonality
    func generateEncouragement(for achievement: Achievement) -> String
    func generateFormCorrection(for issue: FormIssue) -> String
}

// MARK: - Analytics Service Protocols

/// Protocol for analytics service
protocol AnalyticsServiceProtocol {
    func trackEvent(_ event: AnalyticsEvent)
    func trackWorkoutStart(_ workout: WorkoutSession)
    func trackWorkoutComplete(_ workout: WorkoutSession)
    func trackExercisePerformance(_ performance: ExercisePerformance)
    func trackUserProgress(_ progress: UserProgress)
    func generateInsights(for userId: UUID) async throws -> [WorkoutInsight]
}

// MARK: - Security Service Protocols

/// Protocol for security service
protocol SecurityServiceProtocol {
    func encryptData(_ data: Data) throws -> Data
    func decryptData(_ encryptedData: Data) throws -> Data
    func generateSecureToken() -> String
    func validateToken(_ token: String) -> Bool
    func secureStore(_ data: Data, for key: String) throws
    func secureRetrieve(for key: String) throws -> Data?
}

/// Protocol for privacy service
protocol PrivacyServiceProtocol {
    func requestPermission(for type: PermissionType) async -> Bool
    func checkPermission(for type: PermissionType) -> PermissionStatus
    func anonymizeData(_ data: Any) -> Any
    func handleDataDeletion(for userId: UUID) async throws
    func exportUserData(for userId: UUID) async throws -> Data
}

// MARK: - Supporting Types

enum LogLevel: String, CaseIterable {
    case debug = "DEBUG"
    case info = "INFO"
    case warning = "WARNING"
    case error = "ERROR"
    case critical = "CRITICAL"
}

enum PermissionType: String, CaseIterable {
    case camera = "camera"
    case microphone = "microphone"
    case motion = "motion"
    case healthKit = "healthKit"
    case notifications = "notifications"
}

enum PermissionStatus: String, CaseIterable {
    case notDetermined = "notDetermined"
    case denied = "denied"
    case authorized = "authorized"
    case restricted = "restricted"
}

struct AnalyticsEvent {
    let name: String
    let parameters: [String: Any]
    let timestamp: Date
    let userId: UUID?
    
    init(name: String, parameters: [String: Any] = [:], userId: UUID? = nil) {
        self.name = name
        self.parameters = parameters
        self.timestamp = Date()
        self.userId = userId
    }
}

struct UserProgress {
    let userId: UUID
    let totalWorkouts: Int
    let totalExercises: Int
    let averageFormScore: Double
    let improvementRate: Double
    let currentStreak: Int
    let achievements: [Achievement]
    let timestamp: Date
    
    init(
        userId: UUID,
        totalWorkouts: Int = 0,
        totalExercises: Int = 0,
        averageFormScore: Double = 0.0,
        improvementRate: Double = 0.0,
        currentStreak: Int = 0,
        achievements: [Achievement] = [],
        timestamp: Date = Date()
    ) {
        self.userId = userId
        self.totalWorkouts = totalWorkouts
        self.totalExercises = totalExercises
        self.averageFormScore = averageFormScore
        self.improvementRate = improvementRate
        self.currentStreak = currentStreak
        self.achievements = achievements
        self.timestamp = timestamp
    }
}

struct WorkoutInsight {
    let type: InsightType
    let title: String
    let description: String
    let actionable: Bool
    let priority: InsightPriority
    let timestamp: Date
    
    enum InsightType: String, CaseIterable {
        case improvement = "improvement"
        case recommendation = "recommendation"
        case achievement = "achievement"
        case warning = "warning"
    }
    
    enum InsightPriority: String, CaseIterable {
        case low = "low"
        case medium = "medium"
        case high = "high"
        case critical = "critical"
    }
}

// MARK: - Feedback Context Types

enum FeedbackContext {
    case safety(risk: SafetyRisk)
    case formCorrection(issue: FormIssue)
    case excellentForm
    case repCompletion(repNumber: Int, setNumber: Int, formScore: Double)
    case setCompletion(setNumber: Int, totalSets: Int, averageFormScore: Double)
    case general
    
    func isSimilar(to other: FeedbackContext) -> Bool {
        switch (self, other) {
        case (.safety, .safety):
            return true
        case (.formCorrection(let issue1), .formCorrection(let issue2)):
            return issue1.criteria == issue2.criteria
        case (.excellentForm, .excellentForm):
            return true
        case (.general, .general):
            return true
        default:
            return false
        }
    }
}
