import Foundation

enum MotionFitProError: LocalizedError, Equatable {
    
    // MARK: - AR Errors
    case arNotSupported
    case arSessionFailed(String)
    case bodyTrackingUnavailable
    case bodyTrackingLost
    case cameraPermissionDenied
    case arConfigurationFailed
    
    // MARK: - ML Errors
    case mlModelNotFound(String)
    case mlModelLoadFailed(String)
    case predictionFailed(String)
    case invalidInput
    case modelVersionMismatch
    
    // MARK: - Data Errors
    case dataCorruption
    case saveFailed(String)
    case loadFailed(String)
    case networkError(String)
    case cloudKitError(String)
    case cacheError(String)
    
    // MARK: - Workout Errors
    case workoutNotFound
    case exerciseNotSupported(String)
    case workoutAlreadyInProgress
    case invalidWorkoutConfiguration
    case workoutDataIncomplete
    
    // MARK: - Permission Errors
    case microphonePermissionDenied
    case notificationPermissionDenied
    case healthKitPermissionDenied
    
    // MARK: - System Errors
    case insufficientStorage
    case deviceNotSupported
    case lowBattery
    case overheating
    case backgroundModeRestricted
    
    // MARK: - Generic Errors
    case unknown(String)
    case customError(String)
    
    var errorDescription: String? {
        switch self {
        case .arNotSupported:
            return "AR is not supported on this device."
        case .arSessionFailed(let message):
            return "AR session failed: \(message)"
        case .bodyTrackingUnavailable:
            return "Body tracking is not available on this device."
        case .bodyTrackingLost:
            return "Body tracking was lost."
        case .cameraPermissionDenied:
            return "Camera permission is required."
        case .arConfigurationFailed:
            return "Failed to configure AR session."
        case .mlModelNotFound(let modelName):
            return "ML model '\(modelName)' was not found."
        case .mlModelLoadFailed(let message):
            return "Failed to load ML model: \(message)"
        case .predictionFailed(let message):
            return "Exercise prediction failed: \(message)"
        case .invalidInput:
            return "Invalid input data."
        case .modelVersionMismatch:
            return "Model version mismatch."
        case .dataCorruption:
            return "Data corruption detected."
        case .saveFailed(let message):
            return "Failed to save data: \(message)"
        case .loadFailed(let message):
            return "Failed to load data: \(message)"
        case .networkError(let message):
            return "Network error: \(message)"
        case .cloudKitError(let message):
            return "iCloud sync error: \(message)"
        case .cacheError(let message):
            return "Cache error: \(message)"
        case .workoutNotFound:
            return "Workout not found."
        case .exerciseNotSupported(let exercise):
            return "Exercise '\(exercise)' is not supported."
        case .workoutAlreadyInProgress:
            return "A workout is already in progress."
        case .invalidWorkoutConfiguration:
            return "Invalid workout configuration."
        case .workoutDataIncomplete:
            return "Workout data is incomplete."
        case .microphonePermissionDenied:
            return "Microphone permission required."
        case .notificationPermissionDenied:
            return "Notification permission required."
        case .healthKitPermissionDenied:
            return "HealthKit permission required."
        case .insufficientStorage:
            return "Insufficient storage space."
        case .deviceNotSupported:
            return "This device is not supported."
        case .lowBattery:
            return "Battery level is too low."
        case .overheating:
            return "Device is overheating."
        case .backgroundModeRestricted:
            return "Background app refresh is disabled."
        case .unknown(let message):
            return "Unknown error: \(message)"
        case .customError(let message):
            return message
        }
    }
}