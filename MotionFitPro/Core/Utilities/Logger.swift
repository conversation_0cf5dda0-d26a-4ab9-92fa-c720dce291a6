import Foundation
import os.log

final class Logger {
    nonisolated(unsafe) static let shared = Logger()
    
    private init() {}
    
    enum LogLevel: String {
        case debug = "DEBUG"
        case info = "INFO"
        case warning = "WARNING"
        case error = "ERROR"
        case critical = "CRITICAL"
    }
    
    enum Category: String {
        case app = "App"
        case arSession = "ARSession"
        case bodyTracking = "BodyTracking"
        case mlProcessing = "MLProcessing"
        case workout = "Workout"
        case audio = "Audio"
        case data = "Data"
        case network = "Network"
        case ui = "UI"
        case performance = "Performance"
    }
    
    func log(_ level: LogLevel, 
             category: Category, 
             message: String, 
             file: String = #file, 
             function: String = #function, 
             line: Int = #line) {
        print("[\(level.rawValue)] [\(category.rawValue)] \(message)")
    }
    
    func debug(_ message: String, category: Category = .app) {
        log(.debug, category: category, message: message)
    }
    
    func info(_ message: String, category: Category = .app) {
        log(.info, category: category, message: message)
    }
    
    func warning(_ message: String, category: Category = .app) {
        log(.warning, category: category, message: message)
    }
    
    func error(_ message: String, category: Category = .app) {
        log(.error, category: category, message: message)
    }
    
    func error(_ error: Error, category: Category = .app) {
        log(.error, category: category, message: "Error: \(error.localizedDescription)")
    }
}