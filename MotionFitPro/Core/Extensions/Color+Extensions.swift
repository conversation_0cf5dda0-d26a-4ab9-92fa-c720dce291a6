import SwiftUI

extension Color {
    
    // MARK: - App Theme Colors
    static let primaryBlue = Color(red: 0.0, green: 0.48, blue: 1.0)
    static let primaryPurple = Color(red: 0.55, green: 0.26, blue: 0.86)
    static let accentGreen = Color(red: 0.20, green: 0.78, blue: 0.35)
    static let warningOrange = Color(red: 1.0, green: 0.58, blue: 0.0)
    static let errorRed = Color(red: 1.0, green: 0.23, blue: 0.19)
    
    // MARK: - Background Colors
    static let backgroundPrimary = Color(UIColor.systemBackground)
    static let backgroundSecondary = Color(UIColor.secondarySystemBackground)
    static let backgroundTertiary = Color(UIColor.tertiarySystemBackground)
    
    // MARK: - Text Colors
    static let textPrimary = Color(UIColor.label)
    static let textSecondary = Color(UIColor.secondaryLabel)
    static let textTertiary = Color(UIColor.tertiaryLabel)
    
    // MARK: - Gradient Colors
    static let gradientStart = primaryBlue
    static let gradientEnd = primaryPurple
    
    // MARK: - AR Overlay Colors
    static let arOverlay = Color.black.opacity(0.3)
    static let jointColor = accentGreen
    static let boneColor = Color.white
    static let trackingGood = accentGreen
    static let trackingPoor = warningOrange
    static let trackingLost = errorRed
    
    // MARK: - Chart Colors
    static let chartPrimary = primaryBlue
    static let chartSecondary = primaryPurple
    static let chartBackground = backgroundSecondary
    
    // MARK: - Custom Initializers
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }
        
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
    
    // MARK: - Utility Methods
    func lighter(by percentage: Double = 30.0) -> Color {
        return self.adjust(by: abs(percentage))
    }
    
    func darker(by percentage: Double = 30.0) -> Color {
        return self.adjust(by: -1 * abs(percentage))
    }
    
    private func adjust(by percentage: Double = 30.0) -> Color {
        return Color(UIColor(self).adjust(by: percentage) ?? UIColor())
    }
}

extension UIColor {
    func adjust(by percentage: Double = 30.0) -> UIColor? {
        var red: CGFloat = 0, green: CGFloat = 0, blue: CGFloat = 0, alpha: CGFloat = 0
        if self.getRed(&red, green: &green, blue: &blue, alpha: &alpha) {
            return UIColor(red: min(red + percentage/100, 1.0),
                          green: min(green + percentage/100, 1.0),
                          blue: min(blue + percentage/100, 1.0),
                          alpha: alpha)
        } else {
            return nil
        }
    }
}