import SwiftUI
import Foundation

extension View {
    
    // MARK: - Card Style
    func cardStyle(backgroundColor: Color = .white, cornerRadius: CGFloat = 12) -> some View {
        self
            .background(backgroundColor)
            .cornerRadius(cornerRadius)
            .shadow(color: .gray.opacity(0.2), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Primary Button Style
    func primaryButtonStyle(backgroundColor: Color = .blue, foregroundColor: Color = .white) -> some View {
        self
            .frame(height: 50)
            .background(backgroundColor)
            .foregroundColor(foregroundColor)
            .cornerRadius(12)
            .shadow(color: backgroundColor.opacity(0.3), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Dependency Injection (disabled until DIContainer build issue resolved)
    // func withDependencyInjection() -> some View {
    //     self
    //         .environmentObject(DIContainer.shared)
    // }
    
    // MARK: - Error Handling
    func withErrorHandling() -> some View {
        self
            .alert("Error", isPresented: .constant(false)) {
                But<PERSON>("OK") { }
            } message: {
                Text("An error occurred")
            }
    }
}