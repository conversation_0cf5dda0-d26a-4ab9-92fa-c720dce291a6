//
//  TemporalAnalyzer.swift
//  MotionFitPro
//
//  Created by Cascade on 2025-07-14.
//

import Foundation
import simd

/// Analyzes a sequence of body poses over time to derive dynamic metrics.
class TemporalAnalyzer {
    
    /// A circular buffer to store the history of recent body poses.
    /// A buffer size of 180 corresponds to 3 seconds of data at 60 FPS.
    private var poseHistory: CircularBuffer<BodyPoseData>
    
    /// The maximum number of poses to store in the history.
    private let historySize: Int
    
    /// Initializes the analyzer with a specific history size.
    /// - Parameter historySize: The number of poses to keep for temporal analysis. Defaults to 180.
    init(historySize: Int = 180) {
        self.historySize = historySize
        self.poseHistory = CircularBuffer<BodyPoseData>(capacity: historySize)
    }
    
    /// Adds a new pose to the history for analysis.
    /// - Parameter pose: The `BodyPoseData` to add.
    func addPose(_ pose: BodyPoseData) {
        poseHistory.append(pose)
    }
    
    /// Calculates the velocity of a specific joint over the last two frames.
    /// - Parameter jointName: The name of the joint to analyze.
    /// - Returns: The velocity in meters per second, or `nil` if not enough data is available.
    func calculateVelocity(for jointName: ARKit.ARSkeleton.JointName) -> simd_float3? {
        let poses = poseHistory.allElements
        guard poses.count >= 2 else { return nil }
        
        let lastPose = poses[poses.count - 1]
        let previousPose = poses[poses.count - 2]
        
        guard
            let lastJointPosition = lastPose.joints[jointName.rawValue]?.position,
            let previousJointPosition = previousPose.joints[jointName.rawValue]?.position
        else {
            return nil
        }
        
        let deltaTime = lastPose.timestamp - previousPose.timestamp
        guard deltaTime > 0 else { return simd_float3(0, 0, 0) }
        
        let velocity = (lastJointPosition - previousJointPosition) / Float(deltaTime)
        return velocity
    }
    
    /// Applies a simple moving average filter to a specific joint's position.
    /// - Parameters:
    ///   - jointName: The name of the joint to smooth.
    ///   - windowSize: The number of recent frames to average over.
    /// - Returns: The smoothed position, or `nil` if not enough data is available.
    func getSmoothedPosition(for jointName: ARKit.ARSkeleton.JointName, windowSize: Int = 5) -> simd_float3? {
        let poses = poseHistory.allElements
        guard poses.count >= windowSize else { return nil }
        
        let recentPoses = poses.suffix(windowSize)
        
        let positions = recentPoses.compactMap { $0.joints[jointName.rawValue]?.position }
        guard !positions.isEmpty else { return nil }
        
        let sum = positions.reduce(simd_float3(0, 0, 0), +)
        let smoothedPosition = sum / Float(positions.count)
        
        return smoothedPosition
    }
    
    /// Provides the current buffer of poses.
    func getPoseHistory() -> [BodyPoseData] {
        return poseHistory.allElements
    }
}
