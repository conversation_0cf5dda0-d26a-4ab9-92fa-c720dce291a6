import Foundation
import simd

// MARK: - Exercise Detection Protocol

protocol ExerciseDetector {
    var exerciseType: ExerciseType { get }
    var currentPhase: MovementPhase { get }
    var repCount: Int { get }
    var formScore: Float { get }
    var feedback: [String] { get }
    
    func processPoseData(_ poseData: BodyPoseData) -> ExerciseAnalysis?
    func reset()
    func getDifficultySettings() -> DifficultySettings
    func setDifficulty(_ difficulty: ExerciseDifficulty)
}

// MARK: - Difficulty Settings

enum ExerciseDifficulty: String, CaseIterable {
    case beginner = "beginner"
    case intermediate = "intermediate"
    case advanced = "advanced"
    
    var displayName: String {
        switch self {
        case .beginner: return "Beginner"
        case .intermediate: return "Intermediate"
        case .advanced: return "Advanced"
        }
    }
}

struct DifficultySettings {
    let repGoal: Int
    let formThreshold: Float
    let timeGoal: TimeInterval?
    let holdTime: TimeInterval?
    let speedRequirement: Float?
    
    static func settings(for exercise: ExerciseType, difficulty: ExerciseDifficulty) -> DifficultySettings {
        switch (exercise, difficulty) {
        case (.squat, .beginner):
            return DifficultySettings(repGoal: 10, formThreshold: 0.6, timeGoal: nil, holdTime: nil, speedRequirement: nil)
        case (.squat, .intermediate):
            return DifficultySettings(repGoal: 15, formThreshold: 0.7, timeGoal: nil, holdTime: nil, speedRequirement: nil)
        case (.squat, .advanced):
            return DifficultySettings(repGoal: 25, formThreshold: 0.8, timeGoal: nil, holdTime: nil, speedRequirement: nil)
            
        case (.pushUp, .beginner):
            return DifficultySettings(repGoal: 8, formThreshold: 0.6, timeGoal: nil, holdTime: nil, speedRequirement: nil)
        case (.pushUp, .intermediate):
            return DifficultySettings(repGoal: 15, formThreshold: 0.7, timeGoal: nil, holdTime: nil, speedRequirement: nil)
        case (.pushUp, .advanced):
            return DifficultySettings(repGoal: 25, formThreshold: 0.8, timeGoal: nil, holdTime: nil, speedRequirement: nil)
            
        case (.lunge, .beginner):
            return DifficultySettings(repGoal: 8, formThreshold: 0.6, timeGoal: nil, holdTime: nil, speedRequirement: nil)
        case (.lunge, .intermediate):
            return DifficultySettings(repGoal: 12, formThreshold: 0.7, timeGoal: nil, holdTime: nil, speedRequirement: nil)
        case (.lunge, .advanced):
            return DifficultySettings(repGoal: 20, formThreshold: 0.8, timeGoal: nil, holdTime: nil, speedRequirement: nil)
            
        case (.plank, .beginner):
            return DifficultySettings(repGoal: 1, formThreshold: 0.7, timeGoal: 30, holdTime: 30, speedRequirement: nil)
        case (.plank, .intermediate):
            return DifficultySettings(repGoal: 1, formThreshold: 0.8, timeGoal: 60, holdTime: 60, speedRequirement: nil)
        case (.plank, .advanced):
            return DifficultySettings(repGoal: 1, formThreshold: 0.85, timeGoal: 120, holdTime: 120, speedRequirement: nil)
            
        case (.jumpingJacks, .beginner):
            return DifficultySettings(repGoal: 20, formThreshold: 0.6, timeGoal: nil, holdTime: nil, speedRequirement: 0.8)
        case (.jumpingJacks, .intermediate):
            return DifficultySettings(repGoal: 30, formThreshold: 0.7, timeGoal: nil, holdTime: nil, speedRequirement: 1.0)
        case (.jumpingJacks, .advanced):
            return DifficultySettings(repGoal: 50, formThreshold: 0.8, timeGoal: nil, holdTime: nil, speedRequirement: 1.2)
            
        case (.burpees, .beginner):
            return DifficultySettings(repGoal: 5, formThreshold: 0.6, timeGoal: nil, holdTime: nil, speedRequirement: nil)
        case (.burpees, .intermediate):
            return DifficultySettings(repGoal: 10, formThreshold: 0.7, timeGoal: nil, holdTime: nil, speedRequirement: nil)
        case (.burpees, .advanced):
            return DifficultySettings(repGoal: 15, formThreshold: 0.8, timeGoal: nil, holdTime: nil, speedRequirement: nil)
            
        case (.mountainClimbers, .beginner):
            return DifficultySettings(repGoal: 20, formThreshold: 0.6, timeGoal: nil, holdTime: nil, speedRequirement: 0.8)
        case (.mountainClimbers, .intermediate):
            return DifficultySettings(repGoal: 30, formThreshold: 0.7, timeGoal: nil, holdTime: nil, speedRequirement: 1.0)
        case (.mountainClimbers, .advanced):
            return DifficultySettings(repGoal: 50, formThreshold: 0.8, timeGoal: nil, holdTime: nil, speedRequirement: 1.2)
            
        default:
            return DifficultySettings(repGoal: 10, formThreshold: 0.6, timeGoal: nil, holdTime: nil, speedRequirement: nil)
        }
    }
}

// MARK: - Push-up Detector

class PushUpDetector: ExerciseDetector {
    let exerciseType: ExerciseType = .pushUp
    private(set) var currentPhase: MovementPhase = .ready
    private(set) var repCount: Int = 0
    private(set) var formScore: Float = 0.8
    private(set) var feedback: [String] = []
    
    private var difficulty: ExerciseDifficulty = .beginner
    private var phaseHistory: [MovementPhase] = []
    private var poseHistory: [BodyPoseData] = []
    private let maxHistorySize = 10
    
    // Push-up specific thresholds
    private struct Thresholds {
        static let elbowAngleTop: Float = 160     // Straight arms
        static let elbowAngleBottom: Float = 90   // Deep push-up
        static let bodyLineVariance: Float = 15   // Degrees from straight line
        static let chestHeight: Float = 0.15      // Meters from ground
    }
    
    func processPoseData(_ poseData: BodyPoseData) -> ExerciseAnalysis? {
        addToPoseHistory(poseData)
        
        let analysis = analyzePushUpForm(poseData)
        updatePhase(from: analysis)
        
        // Check for rep completion
        if detectRepCompletion() {
            repCount += 1
        }
        
        return analysis
    }
    
    private func analyzePushUpForm(_ poseData: BodyPoseData) -> ExerciseAnalysis {
        var issues: [FormIssue] = []
        var scores: [String: Float] = [:]
        
        // Calculate key angles
        let leftElbowAngle = calculateElbowAngle(side: .left, poseData: poseData)
        let rightElbowAngle = calculateElbowAngle(side: .right, poseData: poseData)
        let bodyLineScore = calculateBodyLineScore(poseData)
        let depthScore = calculateDepthScore(poseData)
        
        // Elbow symmetry
        if let leftAngle = leftElbowAngle, let rightAngle = rightElbowAngle {
            let symmetryDiff = abs(leftAngle - rightAngle)
            let symmetryScore = max(0.0, 1.0 - (symmetryDiff / 30.0))
            scores["symmetry"] = symmetryScore
            
            if symmetryDiff > 15 {
                issues.append(FormIssue(
                    criteria: "symmetry",
                    severity: .medium,
                    description: "Keep both arms moving equally",
                    correctionSuggestion: "Focus on even elbow bend on both sides"
                ))
            }
        }
        
        // Body alignment
        scores["bodyLine"] = bodyLineScore
        if bodyLineScore < 0.7 {
            issues.append(FormIssue(
                criteria: "bodyLine",
                severity: .high,
                description: "Maintain straight body line",
                correctionSuggestion: "Engage core and keep body straight from head to heels"
            ))
        }
        
        // Depth analysis
        scores["depth"] = depthScore
        if depthScore < 0.6 {
            issues.append(FormIssue(
                criteria: "depth",
                severity: .medium,
                description: "Go deeper in your push-up",
                correctionSuggestion: "Lower chest closer to the ground"
            ))
        }
        
        // Overall form score
        let overallScore = scores.values.reduce(0, +) / Float(scores.count)
        formScore = overallScore
        
        // Update feedback
        updateFeedback(from: issues, overallScore: overallScore)
        
        return ExerciseAnalysis(
            exercise: .pushUp,
            formScore: Double(overallScore),
            repPhase: currentPhase,
            poseData: poseData,
            detectedIssues: issues,
            confidence: 0.85
        )
    }
    
    private func calculateElbowAngle(side: JointSide, poseData: BodyPoseData) -> Float? {
        let shoulder: JointName
        let elbow: JointName
        let wrist: JointName
        
        switch side {
        case .left:
            shoulder = .leftShoulder
            elbow = .leftElbow
            wrist = .leftWrist
        case .right:
            shoulder = .rightShoulder
            elbow = .rightElbow
            wrist = .rightWrist
        }
        
        guard let shoulderPos = poseData.joints[shoulder]?.position,
              let elbowPos = poseData.joints[elbow]?.position,
              let wristPos = poseData.joints[wrist]?.position else {
            return nil
        }
        
        return JointAngleCalculator.calculateAngle(
            startJoint: shoulderPos,
            centerJoint: elbowPos,
            endJoint: wristPos
        )
    }
    
    private func calculateBodyLineScore(_ poseData: BodyPoseData) -> Float {
        guard let head = poseData.joints[.head]?.position,
              let shoulder = poseData.joints[.leftShoulder]?.position,
              let hip = poseData.joints[.leftHip]?.position,
              let ankle = poseData.joints[.leftFoot]?.position else {
            return 0.5
        }
        
        // Calculate the line from head to ankle
        let headToAnkle = normalize(ankle - head)
        let shoulderToHip = normalize(hip - shoulder)
        
        // Check how aligned the body segments are
        let alignment = dot(headToAnkle, shoulderToHip)
        return max(0.0, min(1.0, alignment))
    }
    
    private func calculateDepthScore(_ poseData: BodyPoseData) -> Float {
        guard let leftElbow = calculateElbowAngle(side: .left, poseData: poseData),
              let rightElbow = calculateElbowAngle(side: .right, poseData: poseData) else {
            return 0.5
        }
        
        let avgElbowAngle = (leftElbow + rightElbow) / 2.0
        
        if avgElbowAngle < 90 {
            return 1.0  // Very deep
        } else if avgElbowAngle < 110 {
            return 0.8  // Good depth
        } else if avgElbowAngle < 130 {
            return 0.6  // Moderate depth
        } else {
            return 0.3  // Shallow
        }
    }
    
    private func updatePhase(from analysis: ExerciseAnalysis) {
        guard let leftElbow = calculateElbowAngle(side: .left, poseData: analysis.poseData),
              let rightElbow = calculateElbowAngle(side: .right, poseData: analysis.poseData) else {
            return
        }
        
        let avgElbowAngle = (leftElbow + rightElbow) / 2.0
        
        switch currentPhase {
        case .ready:
            if avgElbowAngle < 150 {
                currentPhase = .eccentric
            }
        case .eccentric:
            if avgElbowAngle < 100 {
                currentPhase = .isometric
            }
        case .isometric:
            if avgElbowAngle > 120 {
                currentPhase = .concentric
            }
        case .concentric:
            if avgElbowAngle > 150 {
                currentPhase = .ready
            }
        default:
            break
        }
        
        phaseHistory.append(currentPhase)
        if phaseHistory.count > maxHistorySize {
            phaseHistory.removeFirst()
        }
    }
    
    private func detectRepCompletion() -> Bool {
        guard phaseHistory.count >= 4 else { return false }
        
        let recentPhases = Array(phaseHistory.suffix(4))
        return recentPhases.contains(.eccentric) && 
               recentPhases.contains(.concentric) &&
               currentPhase == .ready
    }
    
    private func updateFeedback(from issues: [FormIssue], overallScore: Float) {
        feedback.removeAll()
        
        // Add specific feedback based on issues
        for issue in issues {
            if let suggestion = issue.correctionSuggestion {
                feedback.append(suggestion)
            }
        }
        
        // Add encouragement for good form
        if overallScore > 0.8 {
            feedback.append("Excellent push-up form!")
        } else if overallScore > 0.6 {
            feedback.append("Good form, keep it up!")
        }
    }
    
    private func addToPoseHistory(_ poseData: BodyPoseData) {
        poseHistory.append(poseData)
        if poseHistory.count > maxHistorySize {
            poseHistory.removeFirst()
        }
    }
    
    func reset() {
        currentPhase = .ready
        repCount = 0
        formScore = 0.8
        feedback.removeAll()
        phaseHistory.removeAll()
        poseHistory.removeAll()
    }
    
    func getDifficultySettings() -> DifficultySettings {
        return DifficultySettings.settings(for: .pushUp, difficulty: difficulty)
    }
    
    func setDifficulty(_ difficulty: ExerciseDifficulty) {
        self.difficulty = difficulty
    }
}

// MARK: - Lunge Detector

class LungeDetector: ExerciseDetector {
    let exerciseType: ExerciseType = .lunge
    private(set) var currentPhase: MovementPhase = .ready
    private(set) var repCount: Int = 0
    private(set) var formScore: Float = 0.8
    private(set) var feedback: [String] = []
    
    private var difficulty: ExerciseDifficulty = .beginner
    private var phaseHistory: [MovementPhase] = []
    private var poseHistory: [BodyPoseData] = []
    private let maxHistorySize = 10
    
    // Lunge specific thresholds
    private struct Thresholds {
        static let frontKneeAngleDeep: Float = 90      // Deep lunge position
        static let frontKneeAngleShallow: Float = 120   // Shallow lunge
        static let backKneeAngleDeep: Float = 100      // Back knee bend
        static let hipDepthRatio: Float = 0.6          // Hip descent ratio
        static let balanceVariance: Float = 0.15       // Lateral stability threshold
    }
    
    func processPoseData(_ poseData: BodyPoseData) -> ExerciseAnalysis? {
        addToPoseHistory(poseData)
        
        let analysis = analyzeLungeForm(poseData)
        updatePhase(from: analysis)
        
        // Check for rep completion
        if detectRepCompletion() {
            repCount += 1
        }
        
        return analysis
    }
    
    private func analyzeLungeForm(_ poseData: BodyPoseData) -> ExerciseAnalysis {
        var issues: [FormIssue] = []
        var scores: [String: Float] = [:]
        
        // Calculate key angles
        let leftKneeAngle = calculateKneeAngle(side: .left, poseData: poseData)
        let rightKneeAngle = calculateKneeAngle(side: .right, poseData: poseData)
        let depthScore = calculateDepthScore(poseData)
        let balanceScore = calculateBalanceScore(poseData)
        let formScore = calculateFormScore(poseData)
        
        // Determine which leg is leading
        let leadingLeg = determineLungeLeg(poseData)
        
        // Knee angle analysis
        if let frontKnee = leadingLeg == .left ? leftKneeAngle : rightKneeAngle {
            let kneeScore = max(0.0, min(1.0, (180 - frontKnee) / 90))
            scores["kneeDepth"] = kneeScore
            
            if frontKnee > 130 {
                issues.append(FormIssue(
                    criteria: "depth",
                    severity: .medium,
                    description: "Lunge deeper for better muscle activation",
                    correctionSuggestion: "Lower your front thigh parallel to the ground"
                ))
            }
        }
        
        // Balance analysis
        scores["balance"] = balanceScore
        if balanceScore < 0.7 {
            issues.append(FormIssue(
                criteria: "balance",
                severity: .high,
                description: "Maintain better balance",
                correctionSuggestion: "Keep core engaged and step wider for stability"
            ))
        }
        
        // Form analysis
        scores["form"] = formScore
        if formScore < 0.6 {
            issues.append(FormIssue(
                criteria: "posture",
                severity: .medium,
                description: "Keep torso upright",
                correctionSuggestion: "Engage core and avoid leaning forward"
            ))
        }
        
        // Overall form score
        let overallScore = scores.values.reduce(0, +) / Float(scores.count)
        self.formScore = overallScore
        
        // Update feedback
        updateFeedback(from: issues, overallScore: overallScore)
        
        return ExerciseAnalysis(
            exercise: .lunge,
            formScore: Double(overallScore),
            repPhase: currentPhase,
            poseData: poseData,
            detectedIssues: issues,
            confidence: 0.8
        )
    }
    
    private func calculateKneeAngle(side: JointSide, poseData: BodyPoseData) -> Float? {
        let hip: JointName
        let knee: JointName
        let ankle: JointName
        
        switch side {
        case .left:
            hip = .leftHip
            knee = .leftLowerLeg
            ankle = .leftFoot
        case .right:
            hip = .rightHip
            knee = .rightLowerLeg
            ankle = .rightFoot
        }
        
        guard let hipPos = poseData.joints[hip]?.position,
              let kneePos = poseData.joints[knee]?.position,
              let anklePos = poseData.joints[ankle]?.position else {
            return nil
        }
        
        return JointAngleCalculator.calculateAngle(
            startJoint: hipPos,
            centerJoint: kneePos,
            endJoint: anklePos
        )
    }
    
    private func determineLungeLeg(_ poseData: BodyPoseData) -> JointSide {
        guard let leftFoot = poseData.joints[.leftFoot]?.position,
              let rightFoot = poseData.joints[.rightFoot]?.position else {
            return .left // Default
        }
        
        // The forward leg is the one with greater Z position (closer to camera)
        return leftFoot.z > rightFoot.z ? .left : .right
    }
    
    private func calculateDepthScore(_ poseData: BodyPoseData) -> Float {
        guard let leftKnee = calculateKneeAngle(side: .left, poseData: poseData),
              let rightKnee = calculateKneeAngle(side: .right, poseData: poseData) else {
            return 0.5
        }
        
        let frontKnee = min(leftKnee, rightKnee) // Front leg should bend more
        
        if frontKnee < 90 {
            return 1.0  // Excellent depth
        } else if frontKnee < 110 {
            return 0.8  // Good depth
        } else if frontKnee < 130 {
            return 0.6  // Moderate depth
        } else {
            return 0.3  // Shallow
        }
    }
    
    private func calculateBalanceScore(_ poseData: BodyPoseData) -> Float {
        guard let leftFoot = poseData.joints[.leftFoot]?.position,
              let rightFoot = poseData.joints[.rightFoot]?.position,
              let hip = poseData.joints[.leftHip]?.position else {
            return 0.5
        }
        
        // Calculate center of mass relative to base of support
        let footDistance = distance(leftFoot, rightFoot)
        let hipCenterX = hip.x
        let footCenterX = (leftFoot.x + rightFoot.x) / 2
        
        let lateralDeviation = abs(hipCenterX - footCenterX)
        let balanceRatio = max(0.0, 1.0 - (lateralDeviation / (footDistance / 2)))
        
        return balanceRatio
    }
    
    private func calculateFormScore(_ poseData: BodyPoseData) -> Float {
        guard let head = poseData.joints[.head]?.position,
              let spine = poseData.joints[.spine3]?.position,
              let hip = poseData.joints[.leftHip]?.position else {
            return 0.5
        }
        
        // Check torso alignment (should be relatively vertical)
        let torsoVector = normalize(head - hip)
        let verticalVector = simd_float3(0, 1, 0)
        let alignment = dot(torsoVector, verticalVector)
        
        return max(0.0, min(1.0, alignment))
    }
    
    private func updatePhase(from analysis: ExerciseAnalysis) {
        let depthScore = calculateDepthScore(analysis.poseData)
        
        switch currentPhase {
        case .ready:
            if depthScore < 0.8 { // Starting to descend
                currentPhase = .eccentric
            }
        case .eccentric:
            if depthScore > 0.8 { // Deep position
                currentPhase = .isometric
            }
        case .isometric:
            if depthScore < 0.6 { // Coming back up
                currentPhase = .concentric
            }
        case .concentric:
            if depthScore > 0.9 { // Back to standing
                currentPhase = .ready
            }
        default:
            break
        }
        
        phaseHistory.append(currentPhase)
        if phaseHistory.count > maxHistorySize {
            phaseHistory.removeFirst()
        }
    }
    
    private func detectRepCompletion() -> Bool {
        guard phaseHistory.count >= 4 else { return false }
        
        let recentPhases = Array(phaseHistory.suffix(4))
        return recentPhases.contains(.eccentric) && 
               recentPhases.contains(.concentric) &&
               currentPhase == .ready
    }
    
    private func updateFeedback(from issues: [FormIssue], overallScore: Float) {
        feedback.removeAll()
        
        // Add specific feedback based on issues
        for issue in issues {
            if let suggestion = issue.correctionSuggestion {
                feedback.append(suggestion)
            }
        }
        
        // Add encouragement for good form
        if overallScore > 0.8 {
            feedback.append("Perfect lunge form!")
        } else if overallScore > 0.6 {
            feedback.append("Good lunge, focus on depth!")
        }
    }
    
    private func addToPoseHistory(_ poseData: BodyPoseData) {
        poseHistory.append(poseData)
        if poseHistory.count > maxHistorySize {
            poseHistory.removeFirst()
        }
    }
    
    func reset() {
        currentPhase = .ready
        repCount = 0
        formScore = 0.8
        feedback.removeAll()
        phaseHistory.removeAll()
        poseHistory.removeAll()
    }
    
    func getDifficultySettings() -> DifficultySettings {
        return DifficultySettings.settings(for: .lunge, difficulty: difficulty)
    }
    
    func setDifficulty(_ difficulty: ExerciseDifficulty) {
        self.difficulty = difficulty
    }
}

// MARK: - Plank Detector

class PlankDetector: ExerciseDetector {
    let exerciseType: ExerciseType = .plank
    private(set) var currentPhase: MovementPhase = .ready
    private(set) var repCount: Int = 0
    private(set) var formScore: Float = 0.8
    private(set) var feedback: [String] = []
    
    private var difficulty: ExerciseDifficulty = .beginner
    private var holdStartTime: Date?
    private var totalHoldTime: TimeInterval = 0
    private var poseHistory: [BodyPoseData] = []
    private let maxHistorySize = 30
    
    // Plank specific thresholds
    private struct Thresholds {
        static let bodyLineVariance: Float = 10        // Degrees from straight line
        static let hipDropThreshold: Float = 0.1       // Meters hip drop allowed
        static let coreStabilityWindow: TimeInterval = 2.0  // Stability measurement window
    }
    
    func processPoseData(_ poseData: BodyPoseData) -> ExerciseAnalysis? {
        addToPoseHistory(poseData)
        
        let analysis = analyzePlankForm(poseData)
        updatePhase(from: analysis)
        
        // Handle hold time tracking
        trackHoldTime(analysis)
        
        return analysis
    }
    
    private func analyzePlankForm(_ poseData: BodyPoseData) -> ExerciseAnalysis {
        var issues: [FormIssue] = []
        var scores: [String: Float] = [:]
        
        // Calculate key metrics
        let bodyLineScore = calculateBodyLineScore(poseData)
        let coreStabilityScore = calculateCoreStabilityScore(poseData)
        let armPositionScore = calculateArmPositionScore(poseData)
        
        // Body line analysis
        scores["bodyLine"] = bodyLineScore
        if bodyLineScore < 0.7 {
            issues.append(FormIssue(
                criteria: "bodyLine",
                severity: .high,
                description: "Keep body in straight line",
                correctionSuggestion: "Align head, shoulders, hips, and ankles"
            ))
        }
        
        // Core stability analysis
        scores["coreStability"] = coreStabilityScore
        if coreStabilityScore < 0.6 {
            issues.append(FormIssue(
                criteria: "core",
                severity: .high,
                description: "Engage your core more",
                correctionSuggestion: "Tighten abs and prevent hip sagging"
            ))
        }
        
        // Arm position analysis
        scores["armPosition"] = armPositionScore
        if armPositionScore < 0.7 {
            issues.append(FormIssue(
                criteria: "arms",
                severity: .medium,
                description: "Check arm alignment",
                correctionSuggestion: "Keep forearms parallel and elbows under shoulders"
            ))
        }
        
        // Overall form score
        let overallScore = scores.values.reduce(0, +) / Float(scores.count)
        self.formScore = overallScore
        
        // Update feedback
        updateFeedback(from: issues, overallScore: overallScore)
        
        return ExerciseAnalysis(
            exercise: .plank,
            formScore: Double(overallScore),
            repPhase: currentPhase,
            poseData: poseData,
            detectedIssues: issues,
            confidence: 0.85
        )
    }
    
    private func calculateBodyLineScore(_ poseData: BodyPoseData) -> Float {
        guard let head = poseData.joints[.head]?.position,
              let shoulder = poseData.joints[.leftShoulder]?.position,
              let hip = poseData.joints[.leftHip]?.position,
              let ankle = poseData.joints[.leftFoot]?.position else {
            return 0.5
        }
        
        // Calculate the ideal line from head to ankle
        let headToAnkle = normalize(ankle - head)
        let shoulderToHip = normalize(hip - shoulder)
        
        // Check alignment
        let alignment = dot(headToAnkle, shoulderToHip)
        return max(0.0, min(1.0, alignment))
    }
    
    private func calculateCoreStabilityScore(_ poseData: BodyPoseData) -> Float {
        guard let leftHip = poseData.joints[.leftHip]?.position,
              let rightHip = poseData.joints[.rightHip]?.position,
              let spine = poseData.joints[.spine3]?.position else {
            return 0.5
        }
        
        // Check for hip drop/sag
        let hipCenter = (leftHip + rightHip) / 2
        let hipSpineDistance = distance(hipCenter, spine)
        
        // Score based on maintaining hip height
        let stabilityScore = min(1.0, hipSpineDistance / 0.3) // Normalize against ideal distance
        return max(0.0, stabilityScore)
    }
    
    private func calculateArmPositionScore(_ poseData: BodyPoseData) -> Float {
        guard let leftElbow = poseData.joints[.leftElbow]?.position,
              let rightElbow = poseData.joints[.rightElbow]?.position,
              let leftShoulder = poseData.joints[.leftShoulder]?.position,
              let rightShoulder = poseData.joints[.rightShoulder]?.position else {
            return 0.5
        }
        
        // Check elbow-shoulder alignment
        let leftAlignment = abs(leftElbow.x - leftShoulder.x) < 0.1
        let rightAlignment = abs(rightElbow.x - rightShoulder.x) < 0.1
        
        return (leftAlignment ? 0.5 : 0.0) + (rightAlignment ? 0.5 : 0.0)
    }
    
    private func updatePhase(from analysis: ExerciseAnalysis) {
        let isPlankPosition = analysis.formScore > 0.6
        
        switch currentPhase {
        case .ready:
            if isPlankPosition {
                currentPhase = .isometric
                holdStartTime = Date()
            }
        case .isometric:
            if !isPlankPosition {
                currentPhase = .ready
                if let startTime = holdStartTime {
                    totalHoldTime += Date().timeIntervalSince(startTime)
                }
                holdStartTime = nil
            }
        default:
            break
        }
    }
    
    private func trackHoldTime(_ analysis: ExerciseAnalysis) {
        // For planks, "reps" are successful hold periods
        let settings = getDifficultySettings()
        
        if let startTime = holdStartTime,
           currentPhase == .isometric {
            let currentHoldTime = Date().timeIntervalSince(startTime)
            if let targetHoldTime = settings.holdTime,
               currentHoldTime >= targetHoldTime {
                repCount += 1
                holdStartTime = Date() // Reset for next hold
            }
        }
    }
    
    private func updateFeedback(from issues: [FormIssue], overallScore: Float) {
        feedback.removeAll()
        
        // Add specific feedback based on issues
        for issue in issues {
            if let suggestion = issue.correctionSuggestion {
                feedback.append(suggestion)
            }
        }
        
        // Add hold time feedback
        if let startTime = holdStartTime, currentPhase == .isometric {
            let currentHoldTime = Date().timeIntervalSince(startTime)
            feedback.append("Hold time: \(Int(currentHoldTime))s")
        }
        
        // Add encouragement for good form
        if overallScore > 0.8 {
            feedback.append("Excellent plank form!")
        } else if overallScore > 0.6 {
            feedback.append("Good plank, maintain position!")
        }
    }
    
    private func addToPoseHistory(_ poseData: BodyPoseData) {
        poseHistory.append(poseData)
        if poseHistory.count > maxHistorySize {
            poseHistory.removeFirst()
        }
    }
    
    func reset() {
        currentPhase = .ready
        repCount = 0
        formScore = 0.8
        feedback.removeAll()
        holdStartTime = nil
        totalHoldTime = 0
        poseHistory.removeAll()
    }
    
    func getDifficultySettings() -> DifficultySettings {
        return DifficultySettings.settings(for: .plank, difficulty: difficulty)
    }
    
    func setDifficulty(_ difficulty: ExerciseDifficulty) {
        self.difficulty = difficulty
    }
}

// MARK: - Jumping Jacks Detector

class JumpingJacksDetector: ExerciseDetector {
    let exerciseType: ExerciseType = .jumpingJacks
    private(set) var currentPhase: MovementPhase = .ready
    private(set) var repCount: Int = 0
    private(set) var formScore: Float = 0.8
    private(set) var feedback: [String] = []
    
    private var difficulty: ExerciseDifficulty = .beginner
    private var phaseHistory: [MovementPhase] = []
    private var poseHistory: [BodyPoseData] = []
    private let maxHistorySize = 10
    
    // Jumping jacks specific thresholds
    private struct Thresholds {
        static let armAngleWide: Float = 160      // Arms overhead
        static let armAngleNarrow: Float = 20     // Arms at sides
        static let legSpreadWide: Float = 0.6     // Leg separation (meters)
        static let legSpreadNarrow: Float = 0.2   // Legs together
        static let rhythmTolerance: Float = 0.3   // Timing variance
    }
    
    func processPoseData(_ poseData: BodyPoseData) -> ExerciseAnalysis? {
        addToPoseHistory(poseData)
        
        let analysis = analyzeJumpingJackForm(poseData)
        updatePhase(from: analysis)
        
        // Check for rep completion
        if detectRepCompletion() {
            repCount += 1
        }
        
        return analysis
    }
    
    private func analyzeJumpingJackForm(_ poseData: BodyPoseData) -> ExerciseAnalysis {
        var issues: [FormIssue] = []
        var scores: [String: Float] = [:]
        
        // Calculate key metrics
        let armCoordinationScore = calculateArmCoordinationScore(poseData)
        let legCoordinationScore = calculateLegCoordinationScore(poseData)
        let rhythmScore = calculateRhythmScore(poseData)
        let symmetryScore = calculateSymmetryScore(poseData)
        
        // Arm coordination analysis
        scores["armCoordination"] = armCoordinationScore
        if armCoordinationScore < 0.6 {
            issues.append(FormIssue(
                criteria: "arms",
                severity: .medium,
                description: "Improve arm movement coordination",
                correctionSuggestion: "Swing arms fully overhead and back to sides"
            ))
        }
        
        // Leg coordination analysis
        scores["legCoordination"] = legCoordinationScore
        if legCoordinationScore < 0.6 {
            issues.append(FormIssue(
                criteria: "legs",
                severity: .medium,
                description: "Improve leg movement coordination",
                correctionSuggestion: "Jump feet wider apart and back together"
            ))
        }
        
        // Rhythm analysis
        scores["rhythm"] = rhythmScore
        if rhythmScore < 0.5 {
            issues.append(FormIssue(
                criteria: "timing",
                severity: .low,
                description: "Maintain steady rhythm",
                correctionSuggestion: "Keep consistent timing between movements"
            ))
        }
        
        // Symmetry analysis
        scores["symmetry"] = symmetryScore
        if symmetryScore < 0.7 {
            issues.append(FormIssue(
                criteria: "symmetry",
                severity: .medium,
                description: "Keep movements symmetrical",
                correctionSuggestion: "Move both arms and legs equally"
            ))
        }
        
        // Overall form score
        let overallScore = scores.values.reduce(0, +) / Float(scores.count)
        self.formScore = overallScore
        
        // Update feedback
        updateFeedback(from: issues, overallScore: overallScore)
        
        return ExerciseAnalysis(
            exercise: .jumpingJacks,
            formScore: Double(overallScore),
            repPhase: currentPhase,
            poseData: poseData,
            detectedIssues: issues,
            confidence: 0.75
        )
    }
    
    private func calculateArmCoordinationScore(_ poseData: BodyPoseData) -> Float {
        guard let leftShoulder = poseData.joints[.leftShoulder]?.position,
              let rightShoulder = poseData.joints[.rightShoulder]?.position,
              let leftHand = poseData.joints[.leftHand]?.position,
              let rightHand = poseData.joints[.rightHand]?.position else {
            return 0.5
        }
        
        // Calculate arm angles from horizontal
        let leftArmVector = normalize(leftHand - leftShoulder)
        let rightArmVector = normalize(rightHand - rightShoulder)
        let horizontalVector = simd_float3(1, 0, 0)
        
        let leftArmAngle = acos(dot(leftArmVector, horizontalVector)) * (180.0 / .pi)
        let rightArmAngle = acos(dot(rightArmVector, horizontalVector)) * (180.0 / .pi)
        
        // Score based on how high arms are raised
        let avgArmAngle = (leftArmAngle + rightArmAngle) / 2
        return max(0.0, min(1.0, avgArmAngle / 90.0)) // Normalize to 0-1
    }
    
    private func calculateLegCoordinationScore(_ poseData: BodyPoseData) -> Float {
        guard let leftFoot = poseData.joints[.leftFoot]?.position,
              let rightFoot = poseData.joints[.rightFoot]?.position else {
            return 0.5
        }
        
        let legSeparation = distance(leftFoot, rightFoot)
        
        // Score based on leg separation (wider is better for jumping jacks)
        if legSeparation > Thresholds.legSpreadWide {
            return 1.0  // Wide stance
        } else if legSeparation > 0.4 {
            return 0.7  // Moderate stance
        } else if legSeparation < Thresholds.legSpreadNarrow {
            return 1.0  // Feet together (other phase)
        } else {
            return 0.3  // Neither wide nor together
        }
    }
    
    private func calculateRhythmScore(_ poseData: BodyPoseData) -> Float {
        guard poseHistory.count >= 5 else { return 0.8 }
        
        // Analyze movement consistency over recent frames
        let recentPoses = Array(poseHistory.suffix(5))
        var armMovements: [Float] = []
        
        for i in 1..<recentPoses.count {
            if let prevLeftHand = recentPoses[i-1].joints[.leftHand]?.position,
               let currLeftHand = recentPoses[i].joints[.leftHand]?.position {
                let movement = distance(prevLeftHand, currLeftHand)
                armMovements.append(movement)
            }
        }
        
        guard !armMovements.isEmpty else { return 0.8 }
        
        // Calculate movement variance (lower is better)
        let avgMovement = armMovements.reduce(0, +) / Float(armMovements.count)
        let variance = armMovements.map { pow($0 - avgMovement, 2) }.reduce(0, +) / Float(armMovements.count)
        
        return max(0.0, 1.0 - variance)
    }
    
    private func calculateSymmetryScore(_ poseData: BodyPoseData) -> Float {
        guard let leftShoulder = poseData.joints[.leftShoulder]?.position,
              let rightShoulder = poseData.joints[.rightShoulder]?.position,
              let leftHand = poseData.joints[.leftHand]?.position,
              let rightHand = poseData.joints[.rightHand]?.position,
              let leftFoot = poseData.joints[.leftFoot]?.position,
              let rightFoot = poseData.joints[.rightFoot]?.position else {
            return 0.5
        }
        
        // Check arm symmetry
        let leftArmHeight = leftHand.y - leftShoulder.y
        let rightArmHeight = rightHand.y - rightShoulder.y
        let armSymmetry = 1.0 - min(1.0, abs(leftArmHeight - rightArmHeight) / 0.3)
        
        // Check leg symmetry (distance from center)
        let centerX = (leftFoot.x + rightFoot.x) / 2
        let leftDeviation = abs(leftFoot.x - centerX)
        let rightDeviation = abs(rightFoot.x - centerX)
        let legSymmetry = 1.0 - min(1.0, abs(leftDeviation - rightDeviation) / 0.2)
        
        return (armSymmetry + legSymmetry) / 2.0
    }
    
    private func updatePhase(from analysis: ExerciseAnalysis) {
        let armScore = calculateArmCoordinationScore(analysis.poseData)
        let legScore = calculateLegCoordinationScore(analysis.poseData)
        
        switch currentPhase {
        case .ready:
            if armScore > 0.3 || legScore > 0.7 { // Starting movement
                currentPhase = .concentric
            }
        case .concentric:
            if armScore > 0.8 && legScore > 0.8 { // Peak position
                currentPhase = .isometric
            }
        case .isometric:
            if armScore < 0.5 || legScore < 0.5 { // Coming back
                currentPhase = .eccentric
            }
        case .eccentric:
            if armScore < 0.3 && legScore < 0.3 { // Back to start
                currentPhase = .ready
            }
        default:
            break
        }
        
        phaseHistory.append(currentPhase)
        if phaseHistory.count > maxHistorySize {
            phaseHistory.removeFirst()
        }
    }
    
    private func detectRepCompletion() -> Bool {
        guard phaseHistory.count >= 4 else { return false }
        
        let recentPhases = Array(phaseHistory.suffix(4))
        return recentPhases.contains(.concentric) && 
               recentPhases.contains(.eccentric) &&
               currentPhase == .ready
    }
    
    private func updateFeedback(from issues: [FormIssue], overallScore: Float) {
        feedback.removeAll()
        
        // Add specific feedback based on issues
        for issue in issues {
            if let suggestion = issue.correctionSuggestion {
                feedback.append(suggestion)
            }
        }
        
        // Add encouragement for good form
        if overallScore > 0.8 {
            feedback.append("Great jumping jack coordination!")
        } else if overallScore > 0.6 {
            feedback.append("Good rhythm, keep it up!")
        }
    }
    
    private func addToPoseHistory(_ poseData: BodyPoseData) {
        poseHistory.append(poseData)
        if poseHistory.count > maxHistorySize {
            poseHistory.removeFirst()
        }
    }
    
    func reset() {
        currentPhase = .ready
        repCount = 0
        formScore = 0.8
        feedback.removeAll()
        phaseHistory.removeAll()
        poseHistory.removeAll()
    }
    
    func getDifficultySettings() -> DifficultySettings {
        return DifficultySettings.settings(for: .jumpingJacks, difficulty: difficulty)
    }
    
    func setDifficulty(_ difficulty: ExerciseDifficulty) {
        self.difficulty = difficulty
    }
}

// MARK: - Burpees Detector

class BurpeesDetector: ExerciseDetector {
    let exerciseType: ExerciseType = .burpees
    private(set) var currentPhase: MovementPhase = .ready
    private(set) var repCount: Int = 0
    private(set) var formScore: Float = 0.8
    private(set) var feedback: [String] = []
    
    private var difficulty: ExerciseDifficulty = .beginner
    private var phaseHistory: [MovementPhase] = []
    private var poseHistory: [BodyPoseData] = []
    private let maxHistorySize = 15
    
    // Burpee-specific phases
    private enum BurpeePhase: CaseIterable {
        case standing      // Starting position
        case squatDown     // Squatting down
        case plankPosition // Plank/push-up position
        case pushUp        // Optional push-up
        case squatUp       // Back to squat
        case jump          // Jump up
    }
    
    private var burpeePhase: BurpeePhase = .standing
    
    // Burpee specific thresholds
    private struct Thresholds {
        static let squatKneeAngle: Float = 90     // Deep squat position
        static let plankBodyLine: Float = 0.8     // Body line straightness
        static let jumpHeight: Float = 0.3        // Minimum jump height
        static let handPlacement: Float = 0.2     // Hand distance from feet
    }
    
    func processPoseData(_ poseData: BodyPoseData) -> ExerciseAnalysis? {
        addToPoseHistory(poseData)
        
        let analysis = analyzeBurpeeForm(poseData)
        updatePhase(from: analysis)
        
        // Check for rep completion
        if detectRepCompletion() {
            repCount += 1
        }
        
        return analysis
    }
    
    private func analyzeBurpeeForm(_ poseData: BodyPoseData) -> ExerciseAnalysis {
        var issues: [FormIssue] = []
        var scores: [String: Float] = [:]
        
        // Analyze based on current burpee phase
        switch burpeePhase {
        case .standing, .squatDown, .squatUp:
            let squatScore = analyzeSquatPhase(poseData)
            scores["squat"] = squatScore
            if squatScore < 0.6 {
                issues.append(FormIssue(
                    criteria: "squat",
                    severity: .medium,
                    description: "Improve squat form",
                    correctionSuggestion: "Keep knees aligned and go deeper"
                ))
            }
            
        case .plankPosition:
            let plankScore = analyzePlankPhase(poseData)
            scores["plank"] = plankScore
            if plankScore < 0.7 {
                issues.append(FormIssue(
                    criteria: "plank",
                    severity: .high,
                    description: "Maintain plank position",
                    correctionSuggestion: "Keep body straight from head to heels"
                ))
            }
            
        case .pushUp:
            let pushUpScore = analyzePushUpPhase(poseData)
            scores["pushUp"] = pushUpScore
            if pushUpScore < 0.6 {
                issues.append(FormIssue(
                    criteria: "pushUp",
                    severity: .medium,
                    description: "Complete the push-up",
                    correctionSuggestion: "Lower chest to ground and push back up"
                ))
            }
            
        case .jump:
            let jumpScore = analyzeJumpPhase(poseData)
            scores["jump"] = jumpScore
            if jumpScore < 0.5 {
                issues.append(FormIssue(
                    criteria: "jump",
                    severity: .low,
                    description: "Jump higher",
                    correctionSuggestion: "Explode upward with arms overhead"
                ))
            }
        }
        
        // Transition smoothness
        let transitionScore = calculateTransitionScore(poseData)
        scores["transition"] = transitionScore
        
        // Overall form score
        let overallScore = scores.values.reduce(0, +) / Float(scores.count)
        self.formScore = overallScore
        
        // Update feedback
        updateFeedback(from: issues, overallScore: overallScore)
        
        return ExerciseAnalysis(
            exercise: .burpees,
            formScore: Double(overallScore),
            repPhase: currentPhase,
            poseData: poseData,
            detectedIssues: issues,
            confidence: 0.8
        )
    }
    
    private func analyzeSquatPhase(_ poseData: BodyPoseData) -> Float {
        guard let leftKnee = poseData.joints[.leftLowerLeg]?.position,
              let leftHip = poseData.joints[.leftHip]?.position,
              let leftAnkle = poseData.joints[.leftFoot]?.position else {
            return 0.5
        }
        
        let kneeAngle = JointAngleCalculator.calculateAngle(
            startJoint: leftHip,
            centerJoint: leftKnee,
            endJoint: leftAnkle
        ) ?? 180
        
        // Score based on squat depth
        return max(0.0, min(1.0, (180 - kneeAngle) / 90))
    }
    
    private func analyzePlankPhase(_ poseData: BodyPoseData) -> Float {
        guard let head = poseData.joints[.head]?.position,
              let shoulder = poseData.joints[.leftShoulder]?.position,
              let hip = poseData.joints[.leftHip]?.position,
              let ankle = poseData.joints[.leftFoot]?.position else {
            return 0.5
        }
        
        // Check body line straightness
        let headToAnkle = normalize(ankle - head)
        let shoulderToHip = normalize(hip - shoulder)
        let alignment = dot(headToAnkle, shoulderToHip)
        
        return max(0.0, min(1.0, alignment))
    }
    
    private func analyzePushUpPhase(_ poseData: BodyPoseData) -> Float {
        guard let leftElbow = poseData.joints[.leftElbow]?.position,
              let leftShoulder = poseData.joints[.leftShoulder]?.position,
              let leftWrist = poseData.joints[.leftHand]?.position else {
            return 0.5
        }
        
        let elbowAngle = JointAngleCalculator.calculateAngle(
            startJoint: leftShoulder,
            centerJoint: leftElbow,
            endJoint: leftWrist
        ) ?? 180
        
        // Score based on elbow bend (lower angle = deeper push-up)
        return max(0.0, min(1.0, (180 - elbowAngle) / 90))
    }
    
    private func analyzeJumpPhase(_ poseData: BodyPoseData) -> Float {
        guard let leftFoot = poseData.joints[.leftFoot]?.position,
              let rightFoot = poseData.joints[.rightFoot]?.position else {
            return 0.5
        }
        
        // Estimate jump height based on foot position
        let avgFootHeight = (leftFoot.y + rightFoot.y) / 2
        
        // Score based on height (assumes ground level is around y=0)
        return max(0.0, min(1.0, avgFootHeight / Thresholds.jumpHeight))
    }
    
    private func calculateTransitionScore(_ poseData: BodyPoseData) -> Float {
        guard poseHistory.count >= 3 else { return 0.8 }
        
        // Analyze movement smoothness over recent frames
        let recentPoses = Array(poseHistory.suffix(3))
        var movements: [Float] = []
        
        for i in 1..<recentPoses.count {
            if let prevCenterMass = calculateCenterOfMass(recentPoses[i-1]),
               let currCenterMass = calculateCenterOfMass(recentPoses[i]) {
                let movement = distance(prevCenterMass, currCenterMass)
                movements.append(movement)
            }
        }
        
        guard !movements.isEmpty else { return 0.8 }
        
        // Score based on movement consistency
        let avgMovement = movements.reduce(0, +) / Float(movements.count)
        return max(0.0, min(1.0, avgMovement / 0.5)) // Normalize movement
    }
    
    private func calculateCenterOfMass(_ poseData: BodyPoseData) -> simd_float3? {
        let keyJoints: [JointName] = [.head, .leftShoulder, .rightShoulder, .leftHip, .rightHip]
        let positions = keyJoints.compactMap { poseData.joints[$0]?.position }
        
        guard !positions.isEmpty else { return nil }
        
        let sum = positions.reduce(simd_float3(0, 0, 0)) { $0 + $1 }
        return sum / Float(positions.count)
    }
    
    private func updatePhase(from analysis: ExerciseAnalysis) {
        // Simplified phase detection for burpees
        let bodyHeight = analysis.poseData.joints[.head]?.position.y ?? 0
        let handHeight = analysis.poseData.joints[.leftHand]?.position.y ?? 0
        
        switch burpeePhase {
        case .standing:
            if bodyHeight < 1.2 { // Starting to squat down
                burpeePhase = .squatDown
                currentPhase = .eccentric
            }
        case .squatDown:
            if handHeight < 0.5 { // Hands on ground
                burpeePhase = .plankPosition
                currentPhase = .isometric
            }
        case .plankPosition:
            if difficulty != .beginner { // Include push-up for intermediate/advanced
                burpeePhase = .pushUp
                currentPhase = .eccentric
            } else {
                burpeePhase = .squatUp
                currentPhase = .concentric
            }
        case .pushUp:
            burpeePhase = .squatUp
            currentPhase = .concentric
        case .squatUp:
            if bodyHeight > 1.5 { // Standing up
                burpeePhase = .jump
                currentPhase = .concentric
            }
        case .jump:
            if bodyHeight < 1.8 { // Landing
                burpeePhase = .standing
                currentPhase = .ready
            }
        }
        
        phaseHistory.append(currentPhase)
        if phaseHistory.count > maxHistorySize {
            phaseHistory.removeFirst()
        }
    }
    
    private func detectRepCompletion() -> Bool {
        return burpeePhase == .standing && currentPhase == .ready && 
               phaseHistory.contains(.eccentric) && phaseHistory.contains(.concentric)
    }
    
    private func updateFeedback(from issues: [FormIssue], overallScore: Float) {
        feedback.removeAll()
        
        // Add phase-specific feedback
        switch burpeePhase {
        case .standing:
            feedback.append("Ready for next burpee!")
        case .squatDown:
            feedback.append("Squat down, hands to ground")
        case .plankPosition:
            feedback.append("Hold plank position")
        case .pushUp:
            feedback.append("Complete the push-up")
        case .squatUp:
            feedback.append("Jump back to squat")
        case .jump:
            feedback.append("Jump up with arms overhead!")
        }
        
        // Add specific feedback based on issues
        for issue in issues {
            if let suggestion = issue.correctionSuggestion {
                feedback.append(suggestion)
            }
        }
        
        // Add encouragement for good form
        if overallScore > 0.8 {
            feedback.append("Excellent burpee form!")
        } else if overallScore > 0.6 {
            feedback.append("Good burpee, keep the rhythm!")
        }
    }
    
    private func addToPoseHistory(_ poseData: BodyPoseData) {
        poseHistory.append(poseData)
        if poseHistory.count > maxHistorySize {
            poseHistory.removeFirst()
        }
    }
    
    func reset() {
        currentPhase = .ready
        repCount = 0
        formScore = 0.8
        feedback.removeAll()
        phaseHistory.removeAll()
        poseHistory.removeAll()
        burpeePhase = .standing
    }
    
    func getDifficultySettings() -> DifficultySettings {
        return DifficultySettings.settings(for: .burpees, difficulty: difficulty)
    }
    
    func setDifficulty(_ difficulty: ExerciseDifficulty) {
        self.difficulty = difficulty
    }
}

// MARK: - Mountain Climbers Detector

class MountainClimbersDetector: ExerciseDetector {
    let exerciseType: ExerciseType = .mountainClimbers
    private(set) var currentPhase: MovementPhase = .ready
    private(set) var repCount: Int = 0
    private(set) var formScore: Float = 0.8
    private(set) var feedback: [String] = []
    
    private var difficulty: ExerciseDifficulty = .beginner
    private var phaseHistory: [MovementPhase] = []
    private var poseHistory: [BodyPoseData] = []
    private let maxHistorySize = 10
    
    // Track which leg is currently forward
    private var lastForwardLeg: JointSide = .left
    
    // Mountain climber specific thresholds
    private struct Thresholds {
        static let plankBodyLine: Float = 0.8     // Body line straightness
        static let kneeToChestDistance: Float = 0.3  // How close knee gets to chest
        static let legAlternationRate: Float = 2.0   // Seconds between leg switches
        static let coreStability: Float = 0.1     // Allowed hip movement
    }
    
    func processPoseData(_ poseData: BodyPoseData) -> ExerciseAnalysis? {
        addToPoseHistory(poseData)
        
        let analysis = analyzeMountainClimberForm(poseData)
        updatePhase(from: analysis)
        
        // Check for rep completion (one rep = both legs forward once)
        if detectRepCompletion() {
            repCount += 1
        }
        
        return analysis
    }
    
    private func analyzeMountainClimberForm(_ poseData: BodyPoseData) -> ExerciseAnalysis {
        var issues: [FormIssue] = []
        var scores: [String: Float] = [:]
        
        // Calculate key metrics
        let plankScore = calculatePlankScore(poseData)
        let legDriveScore = calculateLegDriveScore(poseData)
        let coreStabilityScore = calculateCoreStabilityScore(poseData)
        let speedScore = calculateSpeedScore(poseData)
        
        // Plank position analysis
        scores["plank"] = plankScore
        if plankScore < 0.7 {
            issues.append(FormIssue(
                criteria: "plank",
                severity: .high,
                description: "Maintain plank position",
                correctionSuggestion: "Keep body straight, hands under shoulders"
            ))
        }
        
        // Leg drive analysis
        scores["legDrive"] = legDriveScore
        if legDriveScore < 0.6 {
            issues.append(FormIssue(
                criteria: "legDrive",
                severity: .medium,
                description: "Drive knees toward chest",
                correctionSuggestion: "Bring knees closer to chest alternately"
            ))
        }
        
        // Core stability analysis
        scores["coreStability"] = coreStabilityScore
        if coreStabilityScore < 0.6 {
            issues.append(FormIssue(
                criteria: "core",
                severity: .high,
                description: "Stabilize your core",
                correctionSuggestion: "Keep hips level and core engaged"
            ))
        }
        
        // Speed analysis
        scores["speed"] = speedScore
        if speedScore < 0.5 {
            issues.append(FormIssue(
                criteria: "speed",
                severity: .low,
                description: "Increase movement speed",
                correctionSuggestion: "Move legs faster while maintaining form"
            ))
        }
        
        // Overall form score
        let overallScore = scores.values.reduce(0, +) / Float(scores.count)
        self.formScore = overallScore
        
        // Update feedback
        updateFeedback(from: issues, overallScore: overallScore)
        
        return ExerciseAnalysis(
            exercise: .mountainClimbers,
            formScore: Double(overallScore),
            repPhase: currentPhase,
            poseData: poseData,
            detectedIssues: issues,
            confidence: 0.8
        )
    }
    
    private func calculatePlankScore(_ poseData: BodyPoseData) -> Float {
        guard let head = poseData.joints[.head]?.position,
              let shoulder = poseData.joints[.leftShoulder]?.position,
              let hip = poseData.joints[.leftHip]?.position,
              let leftHand = poseData.joints[.leftHand]?.position,
              let rightHand = poseData.joints[.rightHand]?.position else {
            return 0.5
        }
        
        // Check body line straightness (head to hip)
        let bodyVector = normalize(hip - head)
        let horizontalVector = simd_float3(1, 0, 0)
        let alignment = abs(dot(bodyVector, horizontalVector))
        
        // Check hand position (should be under shoulders)
        let handCenterX = (leftHand.x + rightHand.x) / 2
        let shoulderCenterX = shoulder.x
        let handAlignment = max(0.0, 1.0 - abs(handCenterX - shoulderCenterX) / 0.2)
        
        return (alignment + handAlignment) / 2.0
    }
    
    private func calculateLegDriveScore(_ poseData: BodyPoseData) -> Float {
        guard let leftKnee = poseData.joints[.leftLowerLeg]?.position,
              let rightKnee = poseData.joints[.rightLowerLeg]?.position,
              let chest = poseData.joints[.spine3]?.position else {
            return 0.5
        }
        
        // Calculate distance of knees to chest
        let leftKneeDistance = distance(leftKnee, chest)
        let rightKneeDistance = distance(rightKnee, chest)
        
        // One knee should be close to chest, other extended back
        let closestKnee = min(leftKneeDistance, rightKneeDistance)
        let farthestKnee = max(leftKneeDistance, rightKneeDistance)
        
        // Score based on knee separation and drive
        let driveScore = max(0.0, 1.0 - (closestKnee / 0.5))
        let separationScore = max(0.0, min(1.0, (farthestKnee - closestKnee) / 0.8))
        
        return (driveScore + separationScore) / 2.0
    }
    
    private func calculateCoreStabilityScore(_ poseData: BodyPoseData) -> Float {
        guard let leftHip = poseData.joints[.leftHip]?.position,
              let rightHip = poseData.joints[.rightHip]?.position else {
            return 0.5
        }
        
        // Check for hip stability (should remain level)
        let hipLevelDifference = abs(leftHip.y - rightHip.y)
        return max(0.0, 1.0 - (hipLevelDifference / 0.1))
    }
    
    private func calculateSpeedScore(_ poseData: BodyPoseData) -> Float {
        guard poseHistory.count >= 5 else { return 0.8 }
        
        // Analyze leg movement speed over recent frames
        let recentPoses = Array(poseHistory.suffix(5))
        var legMovements: [Float] = []
        
        for i in 1..<recentPoses.count {
            if let prevLeftKnee = recentPoses[i-1].joints[.leftLowerLeg]?.position,
               let currLeftKnee = recentPoses[i].joints[.leftLowerLeg]?.position,
               let prevRightKnee = recentPoses[i-1].joints[.rightLowerLeg]?.position,
               let currRightKnee = recentPoses[i].joints[.rightLowerLeg]?.position {
                
                let leftMovement = distance(prevLeftKnee, currLeftKnee)
                let rightMovement = distance(prevRightKnee, currRightKnee)
                let totalMovement = leftMovement + rightMovement
                
                legMovements.append(totalMovement)
            }
        }
        
        guard !legMovements.isEmpty else { return 0.8 }
        
        // Score based on movement speed (higher movement = better)
        let avgMovement = legMovements.reduce(0, +) / Float(legMovements.count)
        return max(0.0, min(1.0, avgMovement / 0.3))
    }
    
    private func detectForwardLeg(_ poseData: BodyPoseData) -> JointSide {
        guard let leftKnee = poseData.joints[.leftLowerLeg]?.position,
              let rightKnee = poseData.joints[.rightLowerLeg]?.position,
              let chest = poseData.joints[.spine3]?.position else {
            return lastForwardLeg
        }
        
        let leftDistance = distance(leftKnee, chest)
        let rightDistance = distance(rightKnee, chest)
        
        return leftDistance < rightDistance ? .left : .right
    }
    
    private func updatePhase(from analysis: ExerciseAnalysis) {
        let currentForwardLeg = detectForwardLeg(analysis.poseData)
        
        // Detect leg alternation
        if currentForwardLeg != lastForwardLeg {
            switch currentPhase {
            case .ready:
                currentPhase = .concentric
            case .concentric:
                currentPhase = .eccentric
            case .eccentric:
                currentPhase = .concentric
            default:
                currentPhase = .concentric
            }
            lastForwardLeg = currentForwardLeg
        }
        
        phaseHistory.append(currentPhase)
        if phaseHistory.count > maxHistorySize {
            phaseHistory.removeFirst()
        }
    }
    
    private func detectRepCompletion() -> Bool {
        guard phaseHistory.count >= 4 else { return false }
        
        // One rep = both legs have been forward (alternation detected)
        let recentPhases = Array(phaseHistory.suffix(4))
        return recentPhases.contains(.concentric) && recentPhases.contains(.eccentric)
    }
    
    private func updateFeedback(from issues: [FormIssue], overallScore: Float) {
        feedback.removeAll()
        
        // Add specific feedback based on issues
        for issue in issues {
            if let suggestion = issue.correctionSuggestion {
                feedback.append(suggestion)
            }
        }
        
        // Add encouragement for good form
        if overallScore > 0.8 {
            feedback.append("Excellent mountain climber form!")
        } else if overallScore > 0.6 {
            feedback.append("Good pace, maintain the drive!")
        }
        
        // Add leg alternation feedback
        feedback.append("Forward leg: \(lastForwardLeg == .left ? "Left" : "Right")")
    }
    
    private func addToPoseHistory(_ poseData: BodyPoseData) {
        poseHistory.append(poseData)
        if poseHistory.count > maxHistorySize {
            poseHistory.removeFirst()
        }
    }
    
    func reset() {
        currentPhase = .ready
        repCount = 0
        formScore = 0.8
        feedback.removeAll()
        phaseHistory.removeAll()
        poseHistory.removeAll()
        lastForwardLeg = .left
    }
    
    func getDifficultySettings() -> DifficultySettings {
        return DifficultySettings.settings(for: .mountainClimbers, difficulty: difficulty)
    }
    
    func setDifficulty(_ difficulty: ExerciseDifficulty) {
        self.difficulty = difficulty
    }
}

// MARK: - Supporting Enums

enum JointSide {
    case left
    case right
}

// MARK: - Extensions to ExerciseType

extension ExerciseType {
    static let pushUp = ExerciseType.pushUp
    static let lunge = ExerciseType.lunge
    static let plank = ExerciseType.plank
    static let jumpingJacks = ExerciseType.jumpingJacks
    static let burpees = ExerciseType.burpees
    static let mountainClimbers = ExerciseType.mountainClimbers
}