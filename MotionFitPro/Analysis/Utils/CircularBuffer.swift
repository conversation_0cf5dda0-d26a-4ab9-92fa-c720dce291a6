//
//  CircularBuffer.swift
//  MotionFitPro
//
//  Created by Cascade on 2025-07-14.
//

import Foundation

/// A generic, thread-safe circular buffer implementation.
struct CircularBuffer<T> {
    private var buffer: [T?]
    private var head = 0
    private let lock = NSLock()

    /// The maximum number of elements the buffer can hold.
    let capacity: Int

    /// Initializes a circular buffer with a specified capacity.
    /// - Parameter capacity: The size of the buffer. Must be greater than 0.
    init(capacity: Int) {
        precondition(capacity > 0, "Capacity must be greater than 0")
        self.capacity = capacity
        self.buffer = [T?](repeating: nil, count: capacity)
    }

    /// Appends a new element to the buffer.
    /// If the buffer is full, the oldest element is overwritten.
    /// - Parameter element: The element to append.
    mutating func append(_ element: T) {
        lock.lock()
        defer { lock.unlock() }
        
        buffer[head] = element
        head = (head + 1) % capacity
    }

    /// Returns all the elements in the buffer, from oldest to newest.
    /// `nil` values are filtered out.
    var allElements: [T] {
        lock.lock()
        defer { lock.unlock() }
        
        // The elements are ordered from oldest to newest.
        // The `head` points to the next slot to be written, which is the oldest element.
        let elements = (0..<capacity).compactMap { i in
            buffer[(head + i) % capacity]
        }
        return elements
    }
    
    /// The number of non-nil elements currently in the buffer.
    var count: Int {
        return allElements.count
    }
    
    /// The most recently added element to the buffer.
    var last: T? {
        lock.lock()
        defer { lock.unlock() }
        
        let lastIndex = (head - 1 + capacity) % capacity
        return buffer[lastIndex]
    }
}
