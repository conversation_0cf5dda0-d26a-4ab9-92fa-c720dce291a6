//
//  MovementDetector.swift
//  MotionFitPro
//
//  Created by Cascade on 2025-07-14.
//

import Foundation
import ARKit

/// Analyzes pose data to detect high-level movements like exercise initiation and completion.
class MovementDetector {

    private let temporalAnalyzer: TemporalAnalyzer
    private(set) var state: MovementState = .atRest
    
    /// A threshold for the speed of the root joint to determine if the user is moving.
    /// This value may need tuning based on the specific exercise.
    private let movementSpeedThreshold: Float = 0.05 // in meters per second

    init(temporalAnalyzer: TemporalAnalyzer) {
        self.temporalAnalyzer = temporalAnalyzer
    }

    /// Processes a new body pose and updates the movement state.
    /// - Parameter pose: The latest `BodyPoseData` from the AR session.
    /// - Returns: An analysis result indicating the current state.
    @discardableResult
    func process(pose: BodyPoseData) -> MovementAnalysis {
        temporalAnalyzer.addPose(pose)

        let isCurrentlyMoving = checkIfMoving()
        let previousState = state
        
        // Update the state based on current movement
        state = isCurrentlyMoving ? .moving : .atRest
        
        // Detect transitions
        let didInitiateMovement = (previousState == .atRest && state == .moving)
        let didCompleteMovement = (previousState == .moving && state == .atRest)
        
        let confidence = calculateMovementConfidence()

        return MovementAnalysis(
            state: state,
            didInitiateMovement: didInitiateMovement,
            didCompleteMovement: didCompleteMovement,
            confidence: confidence
        )
    }

    /// Checks if the user is currently considered to be in a state of movement.
    /// - Returns: `true` if movement is detected, `false` otherwise.
    private func checkIfMoving() -> Bool {
        // Use the velocity of the root joint as a proxy for overall body movement.
        guard let rootVelocity = temporalAnalyzer.calculateVelocity(for: .root) else {
            return false
        }
        
        let speed = length(rootVelocity)
        return speed > movementSpeedThreshold
    }
    
    /// Calculates a confidence score for the current movement detection.
    /// This is a simple implementation and can be expanded.
    /// - Returns: A confidence score between 0.0 and 1.0.
    private func calculateMovementConfidence() -> Float {
        // For now, confidence is based on the number of poses in the history.
        // More poses lead to higher confidence.
        let poseCount = temporalAnalyzer.getPoseHistory().count
        let confidence = min(1.0, Float(poseCount) / 20.0) // Confidence builds over the first 20 frames
        return confidence
    }
}

// MARK: - Supporting Types

extension MovementDetector {
    /// Represents the current state of user movement.
    enum MovementState {
        case atRest
        case moving
    }

    /// A structure containing the results of the movement analysis for a single frame.
    struct MovementAnalysis {
        let state: MovementState
        let didInitiateMovement: Bool
        let didCompleteMovement: Bool
        let confidence: Float
    }
}
