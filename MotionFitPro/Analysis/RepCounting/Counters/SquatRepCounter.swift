import Foundation
import Combine

/// A class dedicated to counting and analyzing squat repetitions from a stream of body pose data.
class SquatRepCounter {
    /// The current state in the squat movement.
    private(set) var state: SquatState = .standing

    /// The total number of valid reps completed.
    @Published private(set) var repCount: Int = 0

    /// Publishes detailed analysis for each completed rep.
    let repAnalysisPublisher = PassthroughSubject<RepAnalysisData, Never>()

    // Thresholds for state transitions
    private let depthThreshold: Double = 100.0 // Knee angle in degrees for a valid squat depth
    private let standingThreshold: Double = 160.0 // Knee angle in degrees to be considered standing

    // Rep tracking variables
    private var repStartTime: Date? = nil
    private var minKneeAngle: Double = 180.0
    private var initialHipHeight: Double? = nil

    /// Processes a new body pose to update the squat state machine.
    /// - Parameter pose: The latest `BodyPoseData` from the AR session.
    func add(_ pose: BodyPoseData) {
        guard let leftKneeAngle = JointAngleCalculator.calculate(.leftKnee, for: pose).map(Double.init),
              let rightKneeAngle = JointAngleCalculator.calculate(.rightKnee, for: pose).map(Double.init),
              let hip = pose.joints[.root]?.position else {
            return
        }

        let avgKneeAngle = (leftKneeAngle + rightKneeAngle) / 2.0

        switch state {
        case .standing:
            // Transition to descending when the user starts bending their knees
            if avgKneeAngle < standingThreshold - 10 {
                state = .descending
                repStartTime = Date()
                minKneeAngle = avgKneeAngle
                initialHipHeight = Double(hip.y)
            }

        case .descending:
            minKneeAngle = min(minKneeAngle, avgKneeAngle)
            // Transition to bottom when the user reaches squat depth
            if avgKneeAngle <= depthThreshold {
                state = .bottom
            }
            // If user stands up before reaching depth, reset
            else if avgKneeAngle >= standingThreshold {
                resetRep()
            }

        case .bottom:
            minKneeAngle = min(minKneeAngle, avgKneeAngle)
            // Transition to ascending once the user starts to rise
            if avgKneeAngle > depthThreshold + 5 {
                state = .ascending
            }

        case .ascending:
            // Transition to standing when the user is fully upright
            if avgKneeAngle >= standingThreshold {
                guard let startTime = repStartTime else {
                    resetRep()
                    return
                }
                let duration = Date().timeIntervalSince(startTime)
                validateAndCountRep(duration: duration, depth: minKneeAngle)
                resetRep()
            }
        }
    }

    private func validateAndCountRep(duration: TimeInterval, depth: Double) {
        var suggestions: [String] = []
        var warnings: [String] = []
        var formScore = 100

        // Validate duration (1 to 10 seconds)
        guard (1.0...10.0).contains(duration) else {
            return // Rep was too fast or too slow
        }

        // Validate depth
        if depth > depthThreshold {
            suggestions.append("Go deeper to reach the full range of motion.")
            formScore -= 30
            // Do not count invalid reps
            return
        }

        // More form checks would go here (e.g., core stability, symmetry)

        // If all checks pass, count the rep
        repCount += 1

        let analysis = RepAnalysisData(
            formScore: formScore,
            duration: duration,
            velocityProfile: [], // To be implemented
            improvementSuggestions: suggestions,
            safetyWarnings: warnings
        )
        repAnalysisPublisher.send(analysis)
    }

    private func resetRep() {
        state = .standing
        repStartTime = nil
        minKneeAngle = 180.0
        initialHipHeight = nil
    }
}
