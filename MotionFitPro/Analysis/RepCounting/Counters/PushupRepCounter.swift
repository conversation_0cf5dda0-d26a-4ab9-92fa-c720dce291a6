import Foundation
import Combine

/// A class dedicated to counting and analyzing push-up repetitions from a stream of body pose data.
class PushupRepCounter {
    /// The current state in the push-up movement.
    private(set) var state: PushupState = .up

    /// The total number of valid reps completed.
    @Published private(set) var repCount: Int = 0

    /// Publishes detailed analysis for each completed rep.
    let repAnalysisPublisher = PassthroughSubject<RepAnalysisData, Never>()

    // Thresholds for state transitions
    private let downThreshold: Double = 100.0 // Elbow angle in degrees for a valid push-up depth
    private let upThreshold: Double = 160.0   // Elbow angle in degrees to be considered in the up position
    private let bodyAlignmentThreshold: Double = 25.0 // Max deviation in degrees from a straight line for body alignment

    // Rep tracking variables
    private var repStartTime: Date? = nil
    private var minElbowAngle: Double = 180.0
    private var maxAlignmentDeviation: Double = 0.0

    /// Processes a new body pose to update the push-up state machine.
    /// - Parameter pose: The latest `BodyPoseData` from the AR session.
    func add(_ pose: BodyPoseData) {
        guard let leftElbowAngle = JointAngleCalculator.calculate(.leftElbow, for: pose).map(Double.init),
              let rightElbowAngle = JointAngleCalculator.calculate(.rightElbow, for: pose).map(Double.init),
              let leftAlignment = JointAngleCalculator.calculate(.leftBodyAlignment, for: pose).map(Double.init),
              let rightAlignment = JointAngleCalculator.calculate(.rightBodyAlignment, for: pose).map(Double.init) else {
            return
        }

        let avgElbowAngle = (leftElbowAngle + rightElbowAngle) / 2.0
        let bodyAlignment = (leftAlignment + rightAlignment) / 2.0
        let alignmentDeviation = abs(180.0 - bodyAlignment)

        switch state {
        case .up:
            if avgElbowAngle < upThreshold - 10 {
                state = .descending
                repStartTime = Date()
                minElbowAngle = avgElbowAngle
                maxAlignmentDeviation = alignmentDeviation
            }

        case .descending:
            minElbowAngle = min(minElbowAngle, avgElbowAngle)
            maxAlignmentDeviation = max(maxAlignmentDeviation, alignmentDeviation)

            if avgElbowAngle <= downThreshold {
                state = .down
            } else if avgElbowAngle >= upThreshold {
                resetRep()
            }

        case .down:
            minElbowAngle = min(minElbowAngle, avgElbowAngle)
            maxAlignmentDeviation = max(maxAlignmentDeviation, alignmentDeviation)

            if avgElbowAngle > downThreshold + 5 {
                state = .ascending
            }

        case .ascending:
            maxAlignmentDeviation = max(maxAlignmentDeviation, alignmentDeviation)
            if avgElbowAngle >= upThreshold {
                guard let startTime = repStartTime else {
                    resetRep()
                    return
                }
                let duration = Date().timeIntervalSince(startTime)
                validateAndCountRep(duration: duration, depth: minElbowAngle, alignment: maxAlignmentDeviation)
                resetRep()
            }
        }
    }

    private func validateAndCountRep(duration: TimeInterval, depth: Double, alignment: Double) {
        var suggestions: [String] = []
        var warnings: [String] = []
        var formScore = 100

        guard (1.0...10.0).contains(duration) else { return }

        if depth > downThreshold {
            suggestions.append("Lower your chest closer to the ground.")
            formScore -= 30
            return // Rep didn't go deep enough
        }

        if alignment > bodyAlignmentThreshold {
            warnings.append("Keep your back straight. Avoid arching or piking your hips.")
            formScore -= 25
        }

        repCount += 1

        let analysis = RepAnalysisData(
            formScore: formScore,
            duration: duration,
            velocityProfile: [],
            improvementSuggestions: suggestions,
            safetyWarnings: warnings
        )
        repAnalysisPublisher.send(analysis)
    }

    private func resetRep() {
        state = .up
        repStartTime = nil
        minElbowAngle = 180.0
        maxAlignmentDeviation = 0.0
    }
}
