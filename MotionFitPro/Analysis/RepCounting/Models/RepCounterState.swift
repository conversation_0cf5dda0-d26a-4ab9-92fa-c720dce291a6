import Foundation

/// Represents the state of a squat repetition.
enum SquatState {
    case standing   // Starting position, upright.
    case descending // The downward phase of the squat.
    case bottom     // The lowest point of the squat.
    case ascending  // The upward phase of the squat.
}

/// Represents the state of a push-up repetition.
enum PushupState {
    case up         // Starting position, arms extended.
    case descending // The downward phase of the push-up.
    case down       // The lowest point of the push-up.
    case ascending  // The upward phase of the push-up.
}
