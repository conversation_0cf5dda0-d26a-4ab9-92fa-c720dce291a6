import Foundation

/// A detailed analysis of a single repetition of an exercise.
struct RepAnalysisData {
    /// A unique identifier for this specific repetition.
    let id = UUID()

    /// The overall quality of the rep, from 0 (poor) to 100 (perfect).
    let formScore: Int

    /// The total duration of the repetition in seconds.
    let duration: TimeInterval

    /// The velocity profile of the primary joint during the movement.
    let velocityProfile: [Double]

    /// Specific feedback messages identifying areas for improvement.
    /// Example: "Go deeper on your next squat."
    let improvementSuggestions: [String]

    /// Critical safety warnings if dangerous form is detected.
    /// Example: "Warning: Excessive forward lean detected."
    let safetyWarnings: [String]
}
