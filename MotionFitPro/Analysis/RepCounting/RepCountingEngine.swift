import Foundation
import Combine

/// The central engine for managing and coordinating all exercise rep counters.
class RepCountingEngine {
    /// The currently active exercise being tracked.
    private(set) var activeExercise: ExerciseType? {
        didSet {
            // When the exercise changes, reset all counters and subscriptions.
            resetCounters()
        }
    }

    // Publishers that aggregate results from the active counter
    @Published private(set) var repCount: Int = 0 {
        didSet {
            if repCount > oldValue {
                HapticManager.shared.triggerSuccess()
            }
        }
    }
    let repAnalysisPublisher = PassthroughSubject<RepAnalysisData, Never>()

    // Individual exercise counters
    private let squatCounter = SquatRepCounter()
    private let pushupCounter = PushupRepCounter()

    private var cancellables = Set<AnyCancellable>()

    /// Sets the active exercise to track.
    /// - Parameter exercise: The `ExerciseType` to start tracking.
    func setActiveExercise(_ exercise: ExerciseType) {
        guard exercise != self.activeExercise else { return }
        self.activeExercise = exercise
        setupSubscriptions()
    }

    /// Processes a new body pose, routing it to the appropriate counter.
    /// - Parameter pose: The latest `BodyPoseData` from the AR session.
    func add(_ pose: BodyPoseData) {
        switch activeExercise {
        case .squat:
            squatCounter.add(pose)
        case .pushUp:
            pushupCounter.add(pose)
        default:
            // No counter for the active exercise, or no exercise is active.
            break
        }
    }

    /// Resets all counters and clears existing subscriptions.
    private func resetCounters() {
        cancellables.forEach { $0.cancel() }
        cancellables.removeAll()
        repCount = 0
        // We can also reset the internal state of each counter if needed, but they reset on their own.
    }

    /// Sets up the Combine subscriptions to the active counter's publishers.
    private func setupSubscriptions() {
        guard let exercise = activeExercise else { return }

        switch exercise {
        case .squat:
            squatCounter.$repCount
                .assign(to: &$repCount)

            squatCounter.repAnalysisPublisher
                .subscribe(repAnalysisPublisher)
                .store(in: &cancellables)

        case .pushUp:
            pushupCounter.$repCount
                .assign(to: &$repCount)

            pushupCounter.repAnalysisPublisher
                .subscribe(repAnalysisPublisher)
                .store(in: &cancellables)

        default:
            break
        }
    }
}
