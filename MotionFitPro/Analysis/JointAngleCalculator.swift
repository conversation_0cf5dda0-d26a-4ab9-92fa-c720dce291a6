//
//  JointAngleCalculator.swift
//  MotionFitPro
//
//  Created by Cascade on 2025-07-14.
//

import Foundation
import ARKit
import simd

/// A utility for calculating angles between body joints.
struct JointAngleCalculator {

    /// Calculates the angle in degrees between three joints.
    ///
    /// The angle is calculated at the center joint, between the vectors formed by the start and end joints.
    /// For example, to calculate the knee angle, the joints would be hip (start), knee (center), and ankle (end).
    ///
    /// - Parameters:
    ///   - startJoint: The position of the first joint.
    ///   - centerJoint: The position of the joint where the angle is measured.
    ///   - endJoint: The position of the third joint.
    /// - Returns: The angle in degrees, or `nil` if the angle cannot be computed.
    static func calculateAngle(startJoint: simd_float3, centerJoint: simd_float3, endJoint: simd_float3) -> Float? {
        // Create vectors from the center joint to the start and end joints.
        let vector1 = normalize(startJoint - centerJoint)
        let vector2 = normalize(endJoint - centerJoint)

        // Calculate the dot product of the two vectors.
        let dotProduct = dot(vector1, vector2)

        // The dot product can sometimes be slightly outside the [-1.0, 1.0] range due to floating-point inaccuracies.
        // We clamp it to ensure it's a valid input for acos.
        let clampedDotProduct = max(-1.0, min(1.0, dotProduct))

        // Calculate the angle in radians using the arccosine of the dot product.
        let angleInRadians = acos(clampedDotProduct)

        // Convert the angle from radians to degrees.
        let angleInDegrees = angleInRadians * (180.0 / .pi)

        return angleInDegrees
    }
    
    /// A convenience method to calculate an angle directly from a `BodyPoseData` object.
    /// - Parameters:
    ///   - angle: The specific `JointAngle` to calculate.
    ///   - pose: The `BodyPoseData` containing the skeleton.
    /// - Returns: The calculated angle in degrees, or `nil` if any of the required joints are missing or invalid.
    static func calculate(_ angle: JointAngle, for pose: BodyPoseData) -> Float? {
        guard
            let startJointName = angle.jointNames.start,
            let centerJointName = angle.jointNames.center,
            let endJointName = angle.jointNames.end,
            let startJoint = pose.joints[startJointName]?.position,
            let centerJoint = pose.joints[centerJointName]?.position,
            let endJoint = pose.joints[endJointName]?.position
        else {
            return nil
        }
        
        return calculateAngle(startJoint: startJoint, centerJoint: centerJoint, endJoint: endJoint)
    }
}

/// An enum defining the joint triplets for various fitness-relevant angles.
enum JointAngle {
    case leftKnee
    case rightKnee
    case leftHip
    case rightHip
    case leftShoulder
    case rightShoulder
    case leftElbow
    case rightElbow
    case leftBodyAlignment
    case rightBodyAlignment

    /// The three joints required to calculate the angle.
    var jointNames: (start: JointName?, center: JointName?, end: JointName?) {
        switch self {
        case .leftKnee:
            return (.leftUpperLeg, .leftLowerLeg, .leftFoot)
        case .rightKnee:
            return (.rightUpperLeg, .rightLowerLeg, .rightFoot)
        case .leftHip:
            return (.leftShoulder, .leftUpperLeg, .leftLowerLeg)
        case .rightHip:
            return (.rightShoulder, .rightUpperLeg, .rightLowerLeg)
        case .leftShoulder:
            return (.leftUpperArm, .leftShoulder, .leftUpperLeg)
        case .rightShoulder:
            return (.rightUpperArm, .rightShoulder, .rightUpperLeg)
        case .leftElbow:
            return (.leftShoulder, .leftUpperArm, .leftLowerArm)
        case .rightElbow:
            return (.rightShoulder, .rightUpperArm, .rightLowerArm)
        case .leftBodyAlignment:
            return (.leftShoulder, .leftUpperLeg, .leftFoot)
        case .rightBodyAlignment:
            return (.rightShoulder, .rightUpperLeg, .rightFoot)
        }
    }
}
