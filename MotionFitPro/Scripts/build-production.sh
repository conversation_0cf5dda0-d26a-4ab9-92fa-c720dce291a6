#!/bin/bash

# MotionFit Pro - Production Build Script
# This script prepares and builds the app for App Store submission

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="MotionFitPro"
SCHEME="MotionFitPro"
CONFIGURATION="Release"
WORKSPACE="${PROJECT_NAME}.xcworkspace"
ARCHIVE_PATH="./build/${PROJECT_NAME}.xcarchive"
EXPORT_PATH="./build/export"
BUILD_DIR="./build"

# App Store Connect Configuration
BUNDLE_ID="com.motionfitpro.app"
TEAM_ID="YOUR_TEAM_ID"
PROVISIONING_PROFILE="MotionFitPro_AppStore"

echo -e "${BLUE}🚀 Starting MotionFit Pro Production Build${NC}"
echo "=================================================="

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check prerequisites
check_prerequisites() {
    echo -e "${BLUE}🔍 Checking Prerequisites${NC}"
    
    # Check if Xcode is installed
    if ! command -v xcodebuild &> /dev/null; then
        print_error "Xcode command line tools not found"
        exit 1
    fi
    
    # Check if workspace exists
    if [ ! -f "$WORKSPACE" ]; then
        print_error "Workspace $WORKSPACE not found"
        exit 1
    fi
    
    # Check if we're on the main branch
    CURRENT_BRANCH=$(git branch --show-current)
    if [ "$CURRENT_BRANCH" != "main" ] && [ "$CURRENT_BRANCH" != "release" ]; then
        print_warning "Not on main or release branch (current: $CURRENT_BRANCH)"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    # Check for uncommitted changes
    if [ -n "$(git status --porcelain)" ]; then
        print_warning "Uncommitted changes detected"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    print_status "Prerequisites check completed"
}

# Function to run production readiness check
run_production_check() {
    echo -e "${BLUE}🔍 Running Production Readiness Check${NC}"
    
    # This would run our ProductionReadinessChecker
    # For now, we'll simulate the check
    
    # Check for debug code
    if grep -r "print(" --include="*.swift" . | grep -v "Scripts" | grep -v "Tests" > /dev/null; then
        print_warning "Debug print statements found in code"
    fi
    
    # Check for TODO/FIXME
    TODO_COUNT=$(grep -r "TODO\|FIXME" --include="*.swift" . | grep -v "Scripts" | grep -v "Tests" | wc -l)
    if [ "$TODO_COUNT" -gt 0 ]; then
        print_warning "$TODO_COUNT TODO/FIXME comments found"
    fi
    
    # Check configuration
    if grep -q "isDebugMode = true" MotionFitPro/Configuration/ProductionConfiguration.swift; then
        print_error "Debug mode is enabled in production configuration"
        exit 1
    fi
    
    print_status "Production readiness check completed"
}

# Function to clean build directory
clean_build() {
    echo -e "${BLUE}🧹 Cleaning Build Directory${NC}"
    
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
    fi
    mkdir -p "$BUILD_DIR"
    
    # Clean Xcode build cache
    xcodebuild clean -workspace "$WORKSPACE" -scheme "$SCHEME" -configuration "$CONFIGURATION"
    
    print_status "Build directory cleaned"
}

# Function to update version and build number
update_version() {
    echo -e "${BLUE}📝 Updating Version Information${NC}"
    
    # Get current version from Info.plist
    CURRENT_VERSION=$(plutil -extract CFBundleShortVersionString raw MotionFitPro/Info.plist)
    CURRENT_BUILD=$(plutil -extract CFBundleVersion raw MotionFitPro/Info.plist)
    
    echo "Current version: $CURRENT_VERSION (build $CURRENT_BUILD)"
    
    # Auto-increment build number
    NEW_BUILD=$((CURRENT_BUILD + 1))
    
    # Update build number
    plutil -replace CFBundleVersion -string "$NEW_BUILD" MotionFitPro/Info.plist
    
    print_status "Updated build number to $NEW_BUILD"
}

# Function to run tests
run_tests() {
    echo -e "${BLUE}🧪 Running Tests${NC}"
    
    # Run unit tests
    xcodebuild test \
        -workspace "$WORKSPACE" \
        -scheme "$SCHEME" \
        -destination 'platform=iOS Simulator,name=iPhone 15 Pro' \
        -configuration "$CONFIGURATION" \
        -quiet
    
    print_status "All tests passed"
}

# Function to build archive
build_archive() {
    echo -e "${BLUE}🏗️  Building Archive${NC}"
    
    xcodebuild archive \
        -workspace "$WORKSPACE" \
        -scheme "$SCHEME" \
        -configuration "$CONFIGURATION" \
        -archivePath "$ARCHIVE_PATH" \
        -destination 'generic/platform=iOS' \
        DEVELOPMENT_TEAM="$TEAM_ID" \
        CODE_SIGN_STYLE=Manual \
        PROVISIONING_PROFILE_SPECIFIER="$PROVISIONING_PROFILE" \
        CODE_SIGN_IDENTITY="iPhone Distribution" \
        -quiet
    
    print_status "Archive created successfully"
}

# Function to export IPA
export_ipa() {
    echo -e "${BLUE}📦 Exporting IPA${NC}"
    
    # Create export options plist
    cat > "$BUILD_DIR/ExportOptions.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>method</key>
    <string>app-store</string>
    <key>teamID</key>
    <string>$TEAM_ID</string>
    <key>uploadBitcode</key>
    <false/>
    <key>uploadSymbols</key>
    <true/>
    <key>compileBitcode</key>
    <false/>
    <key>signingStyle</key>
    <string>manual</string>
    <key>provisioningProfiles</key>
    <dict>
        <key>$BUNDLE_ID</key>
        <string>$PROVISIONING_PROFILE</string>
    </dict>
</dict>
</plist>
EOF
    
    # Export archive
    xcodebuild -exportArchive \
        -archivePath "$ARCHIVE_PATH" \
        -exportPath "$EXPORT_PATH" \
        -exportOptionsPlist "$BUILD_DIR/ExportOptions.plist" \
        -quiet
    
    print_status "IPA exported successfully"
}

# Function to validate app
validate_app() {
    echo -e "${BLUE}✅ Validating App${NC}"
    
    IPA_PATH="$EXPORT_PATH/$PROJECT_NAME.ipa"
    
    if [ ! -f "$IPA_PATH" ]; then
        print_error "IPA file not found at $IPA_PATH"
        exit 1
    fi
    
    # Validate with App Store Connect
    xcrun altool --validate-app \
        -f "$IPA_PATH" \
        -t ios \
        --apiKey "YOUR_API_KEY" \
        --apiIssuer "YOUR_API_ISSUER"
    
    print_status "App validation completed"
}

# Function to generate build report
generate_report() {
    echo -e "${BLUE}📊 Generating Build Report${NC}"
    
    REPORT_FILE="$BUILD_DIR/build-report.txt"
    
    cat > "$REPORT_FILE" << EOF
MotionFit Pro - Production Build Report
======================================

Build Date: $(date)
Git Commit: $(git rev-parse HEAD)
Git Branch: $(git branch --show-current)
Xcode Version: $(xcodebuild -version | head -n 1)

App Information:
- Bundle ID: $BUNDLE_ID
- Version: $(plutil -extract CFBundleShortVersionString raw MotionFitPro/Info.plist)
- Build: $(plutil -extract CFBundleVersion raw MotionFitPro/Info.plist)

Build Configuration:
- Scheme: $SCHEME
- Configuration: $CONFIGURATION
- Archive Path: $ARCHIVE_PATH
- Export Path: $EXPORT_PATH

Files Generated:
- Archive: $(ls -lh "$ARCHIVE_PATH" 2>/dev/null || echo "Not found")
- IPA: $(ls -lh "$EXPORT_PATH/$PROJECT_NAME.ipa" 2>/dev/null || echo "Not found")

Build Status: SUCCESS ✅
EOF
    
    print_status "Build report generated: $REPORT_FILE"
}

# Function to upload to App Store Connect (optional)
upload_to_app_store() {
    echo -e "${BLUE}🚀 Uploading to App Store Connect${NC}"
    
    read -p "Upload to App Store Connect? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        IPA_PATH="$EXPORT_PATH/$PROJECT_NAME.ipa"
        
        xcrun altool --upload-app \
            -f "$IPA_PATH" \
            -t ios \
            --apiKey "YOUR_API_KEY" \
            --apiIssuer "YOUR_API_ISSUER"
        
        print_status "Upload to App Store Connect completed"
    else
        print_warning "Skipping App Store Connect upload"
    fi
}

# Main execution
main() {
    echo -e "${BLUE}Starting production build process...${NC}"
    
    check_prerequisites
    run_production_check
    clean_build
    update_version
    run_tests
    build_archive
    export_ipa
    validate_app
    generate_report
    upload_to_app_store
    
    echo
    echo -e "${GREEN}🎉 Production build completed successfully!${NC}"
    echo -e "${GREEN}📱 IPA file: $EXPORT_PATH/$PROJECT_NAME.ipa${NC}"
    echo -e "${GREEN}📊 Build report: $BUILD_DIR/build-report.txt${NC}"
    echo
    echo -e "${BLUE}Next steps:${NC}"
    echo "1. Test the IPA on physical devices"
    echo "2. Submit for App Store review"
    echo "3. Prepare release notes and marketing materials"
}

# Run main function
main "$@"
