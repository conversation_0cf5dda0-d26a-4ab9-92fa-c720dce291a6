import SwiftUI
import Combine

/// Manages advanced animations, transitions, and micro-interactions throughout the app
@MainActor
class AnimationManager: ObservableObject {
    
    // MARK: - Published Properties
    @Published var isReduceMotionEnabled: Bool = false
    @Published var animationSpeed: AnimationSpeed = .normal
    @Published var currentTheme: AnimationTheme = .energetic
    @Published var isAnimationEnabled: Bool = true
    
    // MARK: - Private Properties
    private let accessibilityManager = AccessibilityManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // Animation configurations
    private var animationConfigs: [AnimationType: AnimationConfig] = [:]
    
    // MARK: - Initialization
    init() {
        setupAnimationConfigs()
        setupAccessibilityMonitoring()
    }
    
    // MARK: - Public Interface
    
    /// Get animation for specific type
    func animation(for type: AnimationType) -> Animation {
        guard isAnimationEnabled && !isReduceMotionEnabled else {
            return .none
        }
        
        let config = animationConfigs[type] ?? AnimationConfig.default
        return createAnimation(from: config)
    }
    
    /// Get spring animation with custom parameters
    func springAnimation(response: Double = 0.5, dampingFraction: Double = 0.8, blendDuration: Double = 0) -> Animation {
        guard isAnimationEnabled && !isReduceMotionEnabled else {
            return .none
        }
        
        return .spring(response: response, dampingFraction: dampingFraction, blendDuration: blendDuration)
    }
    
    /// Get easing animation
    func easingAnimation(duration: Double = 0.3, curve: AnimationCurve = .easeInOut) -> Animation {
        guard isAnimationEnabled && !isReduceMotionEnabled else {
            return .none
        }
        
        switch curve {
        case .easeIn:
            return .easeIn(duration: duration)
        case .easeOut:
            return .easeOut(duration: duration)
        case .easeInOut:
            return .easeInOut(duration: duration)
        case .linear:
            return .linear(duration: duration)
        }
    }
    
    /// Get workout-specific animation
    func workoutAnimation(for event: WorkoutEvent) -> Animation {
        switch event {
        case .repCompleted:
            return animation(for: .repCompletion)
        case .setCompleted:
            return animation(for: .setCompletion)
        case .exerciseCompleted:
            return animation(for: .exerciseCompletion)
        case .workoutCompleted:
            return animation(for: .workoutCompletion)
        case .formCorrection:
            return animation(for: .formFeedback)
        case .achievement:
            return animation(for: .achievement)
        }
    }
    
    /// Create transition for view changes
    func transition(for type: TransitionType) -> AnyTransition {
        guard isAnimationEnabled && !isReduceMotionEnabled else {
            return .identity
        }
        
        switch type {
        case .slide:
            return .slide
        case .scale:
            return .scale
        case .opacity:
            return .opacity
        case .move:
            return .move(edge: .trailing)
        case .push:
            return .asymmetric(insertion: .move(edge: .trailing), removal: .move(edge: .leading))
        case .workout:
            return .asymmetric(
                insertion: .scale.combined(with: .opacity),
                removal: .scale.combined(with: .opacity)
            )
        }
    }
    
    /// Set animation theme
    func setAnimationTheme(_ theme: AnimationTheme) {
        currentTheme = theme
        updateAnimationConfigs(for: theme)
    }
    
    /// Set animation speed
    func setAnimationSpeed(_ speed: AnimationSpeed) {
        animationSpeed = speed
        updateAnimationSpeeds()
    }
    
    /// Enable/disable animations
    func setAnimationEnabled(_ enabled: Bool) {
        isAnimationEnabled = enabled
    }
    
    // MARK: - Micro-interactions
    
    /// Button press animation
    func buttonPressAnimation() -> Animation {
        return springAnimation(response: 0.3, dampingFraction: 0.6)
    }
    
    /// Card tap animation
    func cardTapAnimation() -> Animation {
        return springAnimation(response: 0.4, dampingFraction: 0.7)
    }
    
    /// Loading animation
    func loadingAnimation() -> Animation {
        return .linear(duration: 1.0).repeatForever(autoreverses: false)
    }
    
    /// Pulse animation for notifications
    func pulseAnimation() -> Animation {
        return .easeInOut(duration: 1.0).repeatForever(autoreverses: true)
    }
    
    /// Shake animation for errors
    func shakeAnimation() -> Animation {
        return .linear(duration: 0.1).repeatCount(6, autoreverses: true)
    }
    
    /// Success celebration animation
    func celebrationAnimation() -> Animation {
        return .spring(response: 0.6, dampingFraction: 0.5)
    }
    
    // MARK: - Custom Animations
    
    /// Create custom workout progress animation
    func workoutProgressAnimation(progress: Double) -> Animation {
        let duration = 0.5 * animationSpeed.multiplier
        return .easeOut(duration: duration)
    }
    
    /// Create form feedback animation
    func formFeedbackAnimation(severity: FeedbackSeverity) -> Animation {
        switch severity {
        case .low:
            return springAnimation(response: 0.4, dampingFraction: 0.8)
        case .medium:
            return springAnimation(response: 0.3, dampingFraction: 0.6)
        case .high:
            return springAnimation(response: 0.2, dampingFraction: 0.4)
        }
    }
    
    /// Create rep counter animation
    func repCounterAnimation() -> Animation {
        return .spring(response: 0.3, dampingFraction: 0.5)
    }
    
    /// Create achievement unlock animation
    func achievementUnlockAnimation() -> Animation {
        return .spring(response: 0.8, dampingFraction: 0.6)
    }
    
    // MARK: - Private Methods
    
    private func setupAnimationConfigs() {
        animationConfigs = [
            .buttonPress: AnimationConfig(
                type: .spring,
                duration: 0.3,
                springResponse: 0.3,
                springDamping: 0.6
            ),
            .cardTap: AnimationConfig(
                type: .spring,
                duration: 0.4,
                springResponse: 0.4,
                springDamping: 0.7
            ),
            .repCompletion: AnimationConfig(
                type: .spring,
                duration: 0.5,
                springResponse: 0.3,
                springDamping: 0.5
            ),
            .setCompletion: AnimationConfig(
                type: .spring,
                duration: 0.6,
                springResponse: 0.4,
                springDamping: 0.6
            ),
            .exerciseCompletion: AnimationConfig(
                type: .spring,
                duration: 0.8,
                springResponse: 0.5,
                springDamping: 0.7
            ),
            .workoutCompletion: AnimationConfig(
                type: .spring,
                duration: 1.0,
                springResponse: 0.8,
                springDamping: 0.6
            ),
            .formFeedback: AnimationConfig(
                type: .easing,
                duration: 0.3,
                curve: .easeInOut
            ),
            .achievement: AnimationConfig(
                type: .spring,
                duration: 1.2,
                springResponse: 0.8,
                springDamping: 0.5
            ),
            .loading: AnimationConfig(
                type: .linear,
                duration: 1.0,
                repeats: true
            ),
            .pulse: AnimationConfig(
                type: .easing,
                duration: 1.0,
                curve: .easeInOut,
                repeats: true,
                autoreverses: true
            )
        ]
    }
    
    private func setupAccessibilityMonitoring() {
        accessibilityManager.$isReduceMotionEnabled
            .receive(on: DispatchQueue.main)
            .assign(to: &$isReduceMotionEnabled)
    }
    
    private func createAnimation(from config: AnimationConfig) -> Animation {
        let duration = config.duration * animationSpeed.multiplier
        
        switch config.type {
        case .spring:
            var animation = Animation.spring(
                response: config.springResponse ?? 0.5,
                dampingFraction: config.springDamping ?? 0.8
            )
            
            if config.repeats {
                animation = animation.repeatForever(autoreverses: config.autoreverses)
            }
            
            return animation
            
        case .easing:
            var animation: Animation
            
            switch config.curve ?? .easeInOut {
            case .easeIn:
                animation = .easeIn(duration: duration)
            case .easeOut:
                animation = .easeOut(duration: duration)
            case .easeInOut:
                animation = .easeInOut(duration: duration)
            case .linear:
                animation = .linear(duration: duration)
            }
            
            if config.repeats {
                animation = animation.repeatForever(autoreverses: config.autoreverses)
            }
            
            return animation
            
        case .linear:
            var animation = Animation.linear(duration: duration)
            
            if config.repeats {
                animation = animation.repeatForever(autoreverses: config.autoreverses)
            }
            
            return animation
        }
    }
    
    private func updateAnimationConfigs(for theme: AnimationTheme) {
        switch theme {
        case .energetic:
            applyEnergeticTheme()
        case .calm:
            applyCalmTheme()
        case .professional:
            applyProfessionalTheme()
        case .playful:
            applyPlayfulTheme()
        }
    }
    
    private func applyEnergeticTheme() {
        // Faster, more dynamic animations
        for (type, config) in animationConfigs {
            animationConfigs[type] = config.withSpeedMultiplier(1.2)
        }
    }
    
    private func applyCalmTheme() {
        // Slower, more gentle animations
        for (type, config) in animationConfigs {
            animationConfigs[type] = config.withSpeedMultiplier(0.8)
        }
    }
    
    private func applyProfessionalTheme() {
        // Subtle, refined animations
        for (type, config) in animationConfigs {
            animationConfigs[type] = config.withDampingMultiplier(1.2)
        }
    }
    
    private func applyPlayfulTheme() {
        // Bouncy, expressive animations
        for (type, config) in animationConfigs {
            animationConfigs[type] = config.withDampingMultiplier(0.6)
        }
    }
    
    private func updateAnimationSpeeds() {
        // Animation speeds are applied when creating animations
        // This method can be used to update any cached animations if needed
    }
}

// MARK: - Supporting Types

enum AnimationType {
    case buttonPress
    case cardTap
    case repCompletion
    case setCompletion
    case exerciseCompletion
    case workoutCompletion
    case formFeedback
    case achievement
    case loading
    case pulse
}

enum TransitionType {
    case slide
    case scale
    case opacity
    case move
    case push
    case workout
}

enum AnimationCurve {
    case easeIn
    case easeOut
    case easeInOut
    case linear
}

enum AnimationSpeed: Double, CaseIterable {
    case slow = 0.5
    case normal = 1.0
    case fast = 1.5
    case veryFast = 2.0
    
    var multiplier: Double {
        return 1.0 / rawValue
    }
    
    var displayName: String {
        switch self {
        case .slow: return "Slow"
        case .normal: return "Normal"
        case .fast: return "Fast"
        case .veryFast: return "Very Fast"
        }
    }
}

enum AnimationTheme: String, CaseIterable {
    case energetic = "Energetic"
    case calm = "Calm"
    case professional = "Professional"
    case playful = "Playful"
    
    var displayName: String {
        return rawValue
    }
}

enum WorkoutEvent {
    case repCompleted
    case setCompleted
    case exerciseCompleted
    case workoutCompleted
    case formCorrection
    case achievement
}

struct AnimationConfig {
    let type: AnimationConfigType
    let duration: Double
    let springResponse: Double?
    let springDamping: Double?
    let curve: AnimationCurve?
    let repeats: Bool
    let autoreverses: Bool
    
    init(
        type: AnimationConfigType,
        duration: Double,
        springResponse: Double? = nil,
        springDamping: Double? = nil,
        curve: AnimationCurve? = nil,
        repeats: Bool = false,
        autoreverses: Bool = false
    ) {
        self.type = type
        self.duration = duration
        self.springResponse = springResponse
        self.springDamping = springDamping
        self.curve = curve
        self.repeats = repeats
        self.autoreverses = autoreverses
    }
    
    static let `default` = AnimationConfig(
        type: .easing,
        duration: 0.3,
        curve: .easeInOut
    )
    
    func withSpeedMultiplier(_ multiplier: Double) -> AnimationConfig {
        return AnimationConfig(
            type: type,
            duration: duration / multiplier,
            springResponse: springResponse.map { $0 / multiplier },
            springDamping: springDamping,
            curve: curve,
            repeats: repeats,
            autoreverses: autoreverses
        )
    }
    
    func withDampingMultiplier(_ multiplier: Double) -> AnimationConfig {
        return AnimationConfig(
            type: type,
            duration: duration,
            springResponse: springResponse,
            springDamping: springDamping.map { $0 * multiplier },
            curve: curve,
            repeats: repeats,
            autoreverses: autoreverses
        )
    }
}

enum AnimationConfigType {
    case spring
    case easing
    case linear
}

// MARK: - View Extensions

extension View {
    /// Apply button press animation
    func buttonPressAnimation() -> some View {
        self.scaleEffect(1.0)
            .animation(AnimationManager.shared.buttonPressAnimation(), value: UUID())
    }
    
    /// Apply card tap animation
    func cardTapAnimation() -> some View {
        self.scaleEffect(1.0)
            .animation(AnimationManager.shared.cardTapAnimation(), value: UUID())
    }
    
    /// Apply workout event animation
    func workoutEventAnimation(_ event: WorkoutEvent) -> some View {
        self.animation(AnimationManager.shared.workoutAnimation(for: event), value: UUID())
    }
    
    /// Apply conditional animation based on accessibility settings
    func conditionalAnimation<V: Equatable>(_ animation: Animation?, value: V) -> some View {
        self.animation(AnimationManager.shared.isAnimationEnabled ? animation : nil, value: value)
    }
}

// MARK: - Animation Modifiers

struct PulseModifier: ViewModifier {
    @State private var isPulsing = false
    private let animation: Animation
    
    init(animation: Animation = AnimationManager.shared.pulseAnimation()) {
        self.animation = animation
    }
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isPulsing ? 1.1 : 1.0)
            .opacity(isPulsing ? 0.8 : 1.0)
            .animation(animation, value: isPulsing)
            .onAppear {
                isPulsing = true
            }
    }
}

struct ShakeModifier: ViewModifier {
    @State private var shakeOffset: CGFloat = 0
    private let animation: Animation
    
    init(animation: Animation = AnimationManager.shared.shakeAnimation()) {
        self.animation = animation
    }
    
    func body(content: Content) -> some View {
        content
            .offset(x: shakeOffset)
            .animation(animation, value: shakeOffset)
    }
    
    func shake() {
        shakeOffset = 10
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            shakeOffset = -10
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            shakeOffset = 0
        }
    }
}

extension View {
    func pulse() -> some View {
        self.modifier(PulseModifier())
    }
    
    func shake() -> some View {
        self.modifier(ShakeModifier())
    }
}
