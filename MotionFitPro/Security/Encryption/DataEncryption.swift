import Foundation
import CryptoKit
import Security

// MARK: - Data Encryption Service

/// Comprehensive data encryption and security service
final class DataEncryption: SecurityServiceProtocol {
    
    // MARK: - Properties
    
    private let logger = Logger()
    private let keychain = KeychainManager()
    
    // Encryption keys
    private var masterKey: SymmetricKey?
    private var dataEncryptionKey: SymmetricKey?
    
    // Key identifiers
    private let masterKeyIdentifier = "com.motionfitpro.masterkey"
    private let dataKeyIdentifier = "com.motionfitpro.datakey"
    
    // MARK: - Initialization
    
    init() {
        setupEncryptionKeys()
    }
    
    // MARK: - Setup
    
    private func setupEncryptionKeys() {
        do {
            // Try to load existing keys
            if let existingMasterKey = try loadMasterKey() {
                masterKey = existingMasterKey
                logger.info("Master key loaded from keychain", category: .security)
            } else {
                // Generate new master key
                masterKey = generateMasterKey()
                try saveMasterKey(masterKey!)
                logger.info("New master key generated and saved", category: .security)
            }
            
            // Generate or load data encryption key
            dataEncryptionKey = try generateDataEncryptionKey()
            
        } catch {
            logger.error("Failed to setup encryption keys: \(error)", category: .security)
            // Fallback to in-memory keys (less secure but functional)
            masterKey = SymmetricKey(size: .bits256)
            dataEncryptionKey = SymmetricKey(size: .bits256)
        }
    }
    
    // MARK: - Key Management
    
    private func generateMasterKey() -> SymmetricKey {
        return SymmetricKey(size: .bits256)
    }
    
    private func generateDataEncryptionKey() throws -> SymmetricKey {
        guard let masterKey = masterKey else {
            throw EncryptionError.keyNotFound
        }
        
        // Derive data encryption key from master key
        let salt = "MotionFitPro-DataKey".data(using: .utf8)!
        let derivedKey = HKDF<SHA256>.deriveKey(
            inputKeyMaterial: masterKey,
            salt: salt,
            outputByteCount: 32
        )
        
        return derivedKey
    }
    
    private func loadMasterKey() throws -> SymmetricKey? {
        guard let keyData = try keychain.getData(for: masterKeyIdentifier) else {
            return nil
        }
        
        return SymmetricKey(data: keyData)
    }
    
    private func saveMasterKey(_ key: SymmetricKey) throws {
        let keyData = key.withUnsafeBytes { Data($0) }
        try keychain.save(keyData, for: masterKeyIdentifier)
    }
    
    // MARK: - SecurityServiceProtocol Implementation
    
    func encryptData(_ data: Data) throws -> Data {
        guard let encryptionKey = dataEncryptionKey else {
            throw EncryptionError.keyNotFound
        }
        
        do {
            let sealedBox = try AES.GCM.seal(data, using: encryptionKey)
            return sealedBox.combined!
        } catch {
            logger.error("Data encryption failed: \(error)", category: .security)
            throw EncryptionError.encryptionFailed(error)
        }
    }
    
    func decryptData(_ encryptedData: Data) throws -> Data {
        guard let encryptionKey = dataEncryptionKey else {
            throw EncryptionError.keyNotFound
        }
        
        do {
            let sealedBox = try AES.GCM.SealedBox(combined: encryptedData)
            return try AES.GCM.open(sealedBox, using: encryptionKey)
        } catch {
            logger.error("Data decryption failed: \(error)", category: .security)
            throw EncryptionError.decryptionFailed(error)
        }
    }
    
    func generateSecureToken() -> String {
        let tokenData = Data((0..<32).map { _ in UInt8.random(in: 0...255) })
        return tokenData.base64EncodedString()
    }
    
    func validateToken(_ token: String) -> Bool {
        // Basic validation - check if it's a valid base64 string of expected length
        guard let tokenData = Data(base64Encoded: token) else {
            return false
        }
        
        return tokenData.count == 32
    }
    
    func secureStore(_ data: Data, for key: String) throws {
        let encryptedData = try encryptData(data)
        try keychain.save(encryptedData, for: key)
        logger.debug("Data securely stored for key: \(key)", category: .security)
    }
    
    func secureRetrieve(for key: String) throws -> Data? {
        guard let encryptedData = try keychain.getData(for: key) else {
            return nil
        }
        
        let decryptedData = try decryptData(encryptedData)
        logger.debug("Data securely retrieved for key: \(key)", category: .security)
        return decryptedData
    }
    
    // MARK: - Advanced Encryption Methods
    
    func encryptSensitiveData(_ data: Data, with additionalData: Data? = nil) throws -> EncryptedData {
        guard let encryptionKey = dataEncryptionKey else {
            throw EncryptionError.keyNotFound
        }
        
        do {
            let nonce = AES.GCM.Nonce()
            let sealedBox = try AES.GCM.seal(
                data,
                using: encryptionKey,
                nonce: nonce,
                authenticating: additionalData
            )
            
            return EncryptedData(
                ciphertext: sealedBox.ciphertext,
                nonce: sealedBox.nonce,
                tag: sealedBox.tag,
                additionalData: additionalData
            )
        } catch {
            logger.error("Sensitive data encryption failed: \(error)", category: .security)
            throw EncryptionError.encryptionFailed(error)
        }
    }
    
    func decryptSensitiveData(_ encryptedData: EncryptedData) throws -> Data {
        guard let encryptionKey = dataEncryptionKey else {
            throw EncryptionError.keyNotFound
        }
        
        do {
            let sealedBox = try AES.GCM.SealedBox(
                nonce: encryptedData.nonce,
                ciphertext: encryptedData.ciphertext,
                tag: encryptedData.tag
            )
            
            return try AES.GCM.open(
                sealedBox,
                using: encryptionKey,
                authenticating: encryptedData.additionalData
            )
        } catch {
            logger.error("Sensitive data decryption failed: \(error)", category: .security)
            throw EncryptionError.decryptionFailed(error)
        }
    }
    
    // MARK: - Hashing and Verification
    
    func hashData(_ data: Data) -> Data {
        return Data(SHA256.hash(data: data))
    }
    
    func verifyDataIntegrity(_ data: Data, expectedHash: Data) -> Bool {
        let computedHash = hashData(data)
        return computedHash == expectedHash
    }
    
    func generateSalt() -> Data {
        return Data((0..<16).map { _ in UInt8.random(in: 0...255) })
    }
    
    func hashPassword(_ password: String, salt: Data) -> Data {
        let passwordData = password.data(using: .utf8)!
        let combinedData = passwordData + salt
        return hashData(combinedData)
    }
    
    // MARK: - Key Rotation
    
    func rotateEncryptionKeys() throws {
        logger.info("Starting encryption key rotation", category: .security)
        
        // Generate new master key
        let newMasterKey = generateMasterKey()
        
        // Save new master key
        try saveMasterKey(newMasterKey)
        
        // Update instance variables
        masterKey = newMasterKey
        dataEncryptionKey = try generateDataEncryptionKey()
        
        logger.info("Encryption key rotation completed", category: .security)
    }
    
    // MARK: - Utility Methods
    
    func clearKeys() {
        masterKey = nil
        dataEncryptionKey = nil
        
        // Remove keys from keychain
        try? keychain.delete(masterKeyIdentifier)
        
        logger.warning("Encryption keys cleared", category: .security)
    }
    
    func getEncryptionInfo() -> EncryptionInfo {
        return EncryptionInfo(
            algorithm: "AES-256-GCM",
            keySize: 256,
            hasValidKeys: masterKey != nil && dataEncryptionKey != nil,
            lastKeyRotation: Date() // In production, track actual rotation date
        )
    }
}

// MARK: - Supporting Types

struct EncryptedData {
    let ciphertext: Data
    let nonce: AES.GCM.Nonce
    let tag: Data
    let additionalData: Data?
    
    var combined: Data {
        var result = Data()
        result.append(nonce.withUnsafeBytes { Data($0) })
        result.append(ciphertext)
        result.append(tag)
        return result
    }
}

struct EncryptionInfo {
    let algorithm: String
    let keySize: Int
    let hasValidKeys: Bool
    let lastKeyRotation: Date
}

enum EncryptionError: LocalizedError {
    case keyNotFound
    case encryptionFailed(Error)
    case decryptionFailed(Error)
    case invalidData
    case keychainError(OSStatus)
    
    var errorDescription: String? {
        switch self {
        case .keyNotFound:
            return "Encryption key not found"
        case .encryptionFailed(let error):
            return "Encryption failed: \(error.localizedDescription)"
        case .decryptionFailed(let error):
            return "Decryption failed: \(error.localizedDescription)"
        case .invalidData:
            return "Invalid data for encryption/decryption"
        case .keychainError(let status):
            return "Keychain error: \(status)"
        }
    }
}
