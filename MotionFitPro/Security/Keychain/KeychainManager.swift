import Foundation
import Security

// MARK: - Keychain Manager

/// Secure keychain management for sensitive data storage
final class KeychainManager {
    
    // MARK: - Properties
    
    private let logger = Logger()
    private let serviceName = "com.motionfitpro.keychain"
    
    // MARK: - Public Methods
    
    func save(_ data: Data, for key: String) throws {
        // Delete existing item first
        try? delete(key)
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecAttrAccount as String: key,
            kSecValueData as String: data,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        ]
        
        let status = SecItemAdd(query as CFDictionary, nil)
        
        guard status == errSecSuccess else {
            logger.error("Failed to save to keychain: \(status)", category: .security)
            throw KeychainError.saveFailed(status)
        }
        
        logger.debug("Data saved to keychain for key: \(key)", category: .security)
    }
    
    func getData(for key: String) throws -> Data? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        switch status {
        case errSecSuccess:
            guard let data = result as? Data else {
                throw KeychainError.invalidData
            }
            logger.debug("Data retrieved from keychain for key: \(key)", category: .security)
            return data
            
        case errSecItemNotFound:
            return nil
            
        default:
            logger.error("Failed to retrieve from keychain: \(status)", category: .security)
            throw KeychainError.retrievalFailed(status)
        }
    }
    
    func delete(_ key: String) throws {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecAttrAccount as String: key
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        
        guard status == errSecSuccess || status == errSecItemNotFound else {
            logger.error("Failed to delete from keychain: \(status)", category: .security)
            throw KeychainError.deleteFailed(status)
        }
        
        logger.debug("Data deleted from keychain for key: \(key)", category: .security)
    }
    
    func update(_ data: Data, for key: String) throws {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecAttrAccount as String: key
        ]
        
        let attributes: [String: Any] = [
            kSecValueData as String: data
        ]
        
        let status = SecItemUpdate(query as CFDictionary, attributes as CFDictionary)
        
        guard status == errSecSuccess else {
            if status == errSecItemNotFound {
                // Item doesn't exist, create it
                try save(data, for: key)
                return
            }
            
            logger.error("Failed to update keychain: \(status)", category: .security)
            throw KeychainError.updateFailed(status)
        }
        
        logger.debug("Data updated in keychain for key: \(key)", category: .security)
    }
    
    func exists(_ key: String) -> Bool {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecAttrAccount as String: key,
            kSecReturnData as String: false
        ]
        
        let status = SecItemCopyMatching(query as CFDictionary, nil)
        return status == errSecSuccess
    }
    
    func getAllKeys() throws -> [String] {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecReturnAttributes as String: true,
            kSecMatchLimit as String: kSecMatchLimitAll
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        switch status {
        case errSecSuccess:
            guard let items = result as? [[String: Any]] else {
                return []
            }
            
            return items.compactMap { item in
                item[kSecAttrAccount as String] as? String
            }
            
        case errSecItemNotFound:
            return []
            
        default:
            logger.error("Failed to retrieve all keys from keychain: \(status)", category: .security)
            throw KeychainError.retrievalFailed(status)
        }
    }
    
    func clearAll() throws {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        
        guard status == errSecSuccess || status == errSecItemNotFound else {
            logger.error("Failed to clear keychain: \(status)", category: .security)
            throw KeychainError.deleteFailed(status)
        }
        
        logger.warning("All keychain data cleared", category: .security)
    }
    
    // MARK: - Convenience Methods
    
    func saveString(_ string: String, for key: String) throws {
        guard let data = string.data(using: .utf8) else {
            throw KeychainError.invalidData
        }
        try save(data, for: key)
    }
    
    func getString(for key: String) throws -> String? {
        guard let data = try getData(for: key) else {
            return nil
        }
        
        guard let string = String(data: data, encoding: .utf8) else {
            throw KeychainError.invalidData
        }
        
        return string
    }
    
    func saveCodable<T: Codable>(_ object: T, for key: String) throws {
        let encoder = JSONEncoder()
        let data = try encoder.encode(object)
        try save(data, for: key)
    }
    
    func getCodable<T: Codable>(_ type: T.Type, for key: String) throws -> T? {
        guard let data = try getData(for: key) else {
            return nil
        }
        
        let decoder = JSONDecoder()
        return try decoder.decode(type, from: data)
    }
    
    // MARK: - Biometric Authentication
    
    func saveWithBiometrics(_ data: Data, for key: String) throws {
        // Delete existing item first
        try? delete(key)
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecAttrAccount as String: key,
            kSecValueData as String: data,
            kSecAttrAccessControl as String: createBiometricAccessControl()
        ]
        
        let status = SecItemAdd(query as CFDictionary, nil)
        
        guard status == errSecSuccess else {
            logger.error("Failed to save to keychain with biometrics: \(status)", category: .security)
            throw KeychainError.saveFailed(status)
        }
        
        logger.debug("Data saved to keychain with biometric protection for key: \(key)", category: .security)
    }
    
    private func createBiometricAccessControl() -> SecAccessControl {
        var error: Unmanaged<CFError>?
        
        let accessControl = SecAccessControlCreateWithFlags(
            kCFAllocatorDefault,
            kSecAttrAccessibleWhenUnlockedThisDeviceOnly,
            .biometryAny,
            &error
        )
        
        if let error = error {
            logger.error("Failed to create biometric access control: \(error)", category: .security)
        }
        
        return accessControl!
    }
    
    // MARK: - Keychain Information
    
    func getKeychainInfo() -> KeychainInfo {
        do {
            let allKeys = try getAllKeys()
            return KeychainInfo(
                serviceName: serviceName,
                totalItems: allKeys.count,
                keys: allKeys
            )
        } catch {
            logger.error("Failed to get keychain info: \(error)", category: .security)
            return KeychainInfo(serviceName: serviceName, totalItems: 0, keys: [])
        }
    }
}

// MARK: - Supporting Types

struct KeychainInfo {
    let serviceName: String
    let totalItems: Int
    let keys: [String]
}

enum KeychainError: LocalizedError {
    case saveFailed(OSStatus)
    case retrievalFailed(OSStatus)
    case updateFailed(OSStatus)
    case deleteFailed(OSStatus)
    case invalidData
    case biometricNotAvailable
    
    var errorDescription: String? {
        switch self {
        case .saveFailed(let status):
            return "Failed to save to keychain: \(keychainErrorMessage(status))"
        case .retrievalFailed(let status):
            return "Failed to retrieve from keychain: \(keychainErrorMessage(status))"
        case .updateFailed(let status):
            return "Failed to update keychain: \(keychainErrorMessage(status))"
        case .deleteFailed(let status):
            return "Failed to delete from keychain: \(keychainErrorMessage(status))"
        case .invalidData:
            return "Invalid data for keychain operation"
        case .biometricNotAvailable:
            return "Biometric authentication not available"
        }
    }
    
    private func keychainErrorMessage(_ status: OSStatus) -> String {
        switch status {
        case errSecItemNotFound:
            return "Item not found"
        case errSecDuplicateItem:
            return "Duplicate item"
        case errSecAuthFailed:
            return "Authentication failed"
        case errSecUserCancel:
            return "User cancelled"
        case errSecNotAvailable:
            return "Service not available"
        default:
            return "Unknown error (\(status))"
        }
    }
}
