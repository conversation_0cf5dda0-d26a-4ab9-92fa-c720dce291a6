import Foundation
import CoreML
import simd
import Combine

// MARK: - Exercise Classification Model

/// Real-time exercise classification using Core ML
@MainActor
final class ExerciseClassificationModel: ObservableObject {
    
    // MARK: - Properties
    
    private var model: MLModel?
    private let logger = Logger()
    
    @Published var isModelLoaded = false
    @Published var lastPrediction: ExerciseClassificationResult?
    @Published var confidence: Float = 0.0
    
    // Model configuration
    private let modelName = "ExerciseClassifier"
    private let inputFeatureCount = 51 // 17 joints * 3 coordinates
    private let sequenceLength = 30 // 30 frames for temporal analysis
    private let confidenceThreshold: Float = 0.7
    
    // Feature processing
    private var featureBuffer: [[Float]] = []
    private let maxBufferSize = 30
    
    // MARK: - Initialization
    
    init() {
        Task {
            await loadModel()
        }
    }
    
    // MARK: - Model Loading
    
    private func loadModel() async {
        do {
            // Try to load the model from the app bundle
            if let modelURL = Bundle.main.url(forResource: modelName, withExtension: "mlmodelc") {
                model = try MLModel(contentsOf: modelURL)
                isModelLoaded = true
                logger.info("Exercise classification model loaded successfully", category: .mlProcessing)
            } else {
                // Fallback: Create a synthetic model for development
                model = try createSyntheticModel()
                isModelLoaded = true
                logger.warning("Using synthetic exercise classification model", category: .mlProcessing)
            }
        } catch {
            logger.error("Failed to load exercise classification model: \(error)", category: .mlProcessing)
            // Create a fallback rule-based classifier
            isModelLoaded = true
        }
    }
    
    private func createSyntheticModel() throws -> MLModel {
        // Create a simple synthetic model for development
        // In production, this would be replaced with a trained Core ML model
        
        let inputDescription = MLFeatureDescription(
            name: "pose_sequence",
            type: .multiArray(MLMultiArrayConstraint(
                shape: [NSNumber(value: sequenceLength), NSNumber(value: inputFeatureCount)],
                dataType: .float32
            ))
        )
        
        let outputDescription = MLFeatureDescription(
            name: "exercise_probabilities",
            type: .multiArray(MLMultiArrayConstraint(
                shape: [NSNumber(value: ExerciseType.allCases.count)],
                dataType: .float32
            ))
        )
        
        let modelDescription = MLModelDescription(
            inputDescriptions: [inputDescription],
            outputDescriptions: [outputDescription],
            predictedFeatureName: "exercise_probabilities",
            predictedProbabilitiesName: nil,
            metadata: [:]
        )
        
        // This is a placeholder - in reality, you'd load a trained model
        throw MLModelError.generic("Synthetic model creation not implemented")
    }
    
    // MARK: - Classification
    
    func classifyExercise(from poseData: BodyPoseData) async -> ExerciseClassificationResult {
        guard isModelLoaded else {
            return ExerciseClassificationResult(
                exercise: .unknown,
                confidence: 0.0,
                timestamp: Date(),
                allProbabilities: [:]
            )
        }
        
        // Extract features from pose data
        let features = extractFeatures(from: poseData)
        
        // Add to feature buffer for temporal analysis
        addToFeatureBuffer(features)
        
        // Perform classification if we have enough frames
        if featureBuffer.count >= sequenceLength {
            return await performClassification()
        } else {
            // Not enough data yet, return unknown
            return ExerciseClassificationResult(
                exercise: .unknown,
                confidence: 0.0,
                timestamp: Date(),
                allProbabilities: [:]
            )
        }
    }
    
    private func extractFeatures(from poseData: BodyPoseData) -> [Float] {
        var features: [Float] = []
        
        // Extract 3D coordinates for key joints
        let keyJoints: [JointName] = [
            .head, .neck,
            .leftShoulder, .rightShoulder,
            .leftElbow, .rightElbow,
            .leftWrist, .rightWrist,
            .spine6, .root,
            .leftHip, .rightHip,
            .leftKnee, .rightKnee,
            .leftAnkle, .rightAnkle,
            .leftToe
        ]
        
        for jointName in keyJoints {
            if let joint = poseData.joints[jointName] {
                features.append(joint.position.x)
                features.append(joint.position.y)
                features.append(joint.position.z)
            } else {
                // Missing joint - use zeros
                features.append(0.0)
                features.append(0.0)
                features.append(0.0)
            }
        }
        
        // Normalize features
        return normalizeFeatures(features)
    }
    
    private func normalizeFeatures(_ features: [Float]) -> [Float] {
        // Simple normalization - in production, use training statistics
        return features.map { $0 / 2.0 } // Assuming pose coordinates are roughly in [-2, 2] range
    }
    
    private func addToFeatureBuffer(_ features: [Float]) {
        featureBuffer.append(features)
        
        // Maintain buffer size
        if featureBuffer.count > maxBufferSize {
            featureBuffer.removeFirst()
        }
    }
    
    private func performClassification() async -> ExerciseClassificationResult {
        guard let model = model else {
            return fallbackClassification()
        }
        
        do {
            // Prepare input for Core ML model
            let inputArray = try MLMultiArray(
                shape: [NSNumber(value: sequenceLength), NSNumber(value: inputFeatureCount)],
                dataType: .float32
            )
            
            // Fill the input array with recent features
            let recentFeatures = Array(featureBuffer.suffix(sequenceLength))
            for (frameIndex, frameFeatures) in recentFeatures.enumerated() {
                for (featureIndex, feature) in frameFeatures.enumerated() {
                    let index = frameIndex * inputFeatureCount + featureIndex
                    inputArray[index] = NSNumber(value: feature)
                }
            }
            
            // Create input feature provider
            let input = try MLDictionaryFeatureProvider(dictionary: [
                "pose_sequence": MLFeatureValue(multiArray: inputArray)
            ])
            
            // Perform prediction
            let output = try model.prediction(from: input)
            
            // Parse results
            return parseModelOutput(output)
            
        } catch {
            logger.error("ML model prediction failed: \(error)", category: .mlProcessing)
            return fallbackClassification()
        }
    }
    
    private func parseModelOutput(_ output: MLFeatureProvider) -> ExerciseClassificationResult {
        guard let probabilitiesValue = output.featureValue(for: "exercise_probabilities"),
              let probabilities = probabilitiesValue.multiArrayValue else {
            return fallbackClassification()
        }
        
        // Convert probabilities to dictionary
        var exerciseProbabilities: [ExerciseType: Float] = [:]
        let exercises = ExerciseType.allCases.filter { $0 != .unknown }
        
        for (index, exercise) in exercises.enumerated() {
            if index < probabilities.count {
                exerciseProbabilities[exercise] = probabilities[index].floatValue
            }
        }
        
        // Find the exercise with highest probability
        let bestMatch = exerciseProbabilities.max { $0.value < $1.value }
        let predictedExercise = bestMatch?.key ?? .unknown
        let confidence = bestMatch?.value ?? 0.0
        
        let result = ExerciseClassificationResult(
            exercise: confidence > confidenceThreshold ? predictedExercise : .unknown,
            confidence: confidence,
            timestamp: Date(),
            allProbabilities: exerciseProbabilities
        )
        
        lastPrediction = result
        self.confidence = confidence
        
        return result
    }
    
    private func fallbackClassification() -> ExerciseClassificationResult {
        // Rule-based fallback classification
        guard let latestFeatures = featureBuffer.last else {
            return ExerciseClassificationResult(
                exercise: .unknown,
                confidence: 0.0,
                timestamp: Date(),
                allProbabilities: [:]
            )
        }
        
        // Simple heuristic-based classification
        let exercise = classifyUsingHeuristics(latestFeatures)
        
        return ExerciseClassificationResult(
            exercise: exercise,
            confidence: 0.6, // Lower confidence for heuristic classification
            timestamp: Date(),
            allProbabilities: [exercise: 0.6]
        )
    }
    
    private func classifyUsingHeuristics(_ features: [Float]) -> ExerciseType {
        // Simple heuristic rules based on pose features
        // This is a simplified example - real heuristics would be more sophisticated
        
        // Check for squat-like pose (knees bent, hips low)
        if features.count >= 42 { // Ensure we have enough features
            let leftKneeY = features[39] // Approximate index for left knee Y
            let rightKneeY = features[42] // Approximate index for right knee Y
            let hipY = features[30] // Approximate index for hip Y
            
            if leftKneeY < hipY && rightKneeY < hipY {
                return .squat
            }
        }
        
        // Default to unknown
        return .unknown
    }
    
    // MARK: - Utility Methods
    
    func resetBuffer() {
        featureBuffer.removeAll()
    }
    
    func getModelInfo() -> String {
        guard let model = model else {
            return "Model not loaded"
        }
        
        return """
        Model: \(modelName)
        Input Features: \(inputFeatureCount)
        Sequence Length: \(sequenceLength)
        Confidence Threshold: \(confidenceThreshold)
        Buffer Size: \(featureBuffer.count)/\(maxBufferSize)
        """
    }
}

// MARK: - Classification Result

struct ExerciseClassificationResult: Sendable {
    let exercise: ExerciseType
    let confidence: Float
    let timestamp: Date
    let allProbabilities: [ExerciseType: Float]
    
    var isConfident: Bool {
        return confidence > 0.7
    }
    
    var description: String {
        return "\(exercise.displayName) (\(Int(confidence * 100))%)"
    }
}
