import Foundation
import CoreML
import simd
import Combine

// MARK: - Form Analysis Model

/// Real-time form analysis using biomechanical principles and ML
@MainActor
final class FormAnalysisModel: ObservableObject {
    
    // MARK: - Properties
    
    private var model: MLModel?
    private let logger = Logger()
    
    @Published var isModelLoaded = false
    @Published var lastAnalysis: FormAnalysis?
    
    // Model configuration
    private let modelName = "FormAnalyzer"
    private let inputFeatureCount = 75 // Extended features including angles and velocities
    
    // Biomechanical analysis
    private let biomechanicsAnalyzer = BiomechanicsAnalyzer()
    private var previousPose: BodyPoseData?
    
    // MARK: - Initialization
    
    init() {
        Task {
            await loadModel()
        }
    }
    
    // MARK: - Model Loading
    
    private func loadModel() async {
        do {
            if let modelURL = Bundle.main.url(forResource: modelName, withExtension: "mlmodelc") {
                model = try MLModel(contentsOf: modelURL)
                isModelLoaded = true
                logger.info("Form analysis model loaded successfully", category: .mlProcessing)
            } else {
                // Use biomechanics-based analysis as fallback
                isModelLoaded = true
                logger.warning("Using biomechanics-based form analysis", category: .mlProcessing)
            }
        } catch {
            logger.error("Failed to load form analysis model: \(error)", category: .mlProcessing)
            isModelLoaded = true // Still functional with biomechanics analysis
        }
    }
    
    // MARK: - Form Analysis
    
    func analyzeForm(_ poseData: BodyPoseData, for exercise: ExerciseType) async -> FormAnalysis {
        // Perform biomechanical analysis
        let biomechanicalAnalysis = biomechanicsAnalyzer.analyze(poseData, for: exercise, previousPose: previousPose)
        
        // If ML model is available, enhance with ML predictions
        var finalAnalysis = biomechanicalAnalysis
        
        if let model = model {
            do {
                let mlAnalysis = try await performMLAnalysis(poseData, exercise: exercise)
                finalAnalysis = combineAnalyses(biomechanicalAnalysis, mlAnalysis)
            } catch {
                logger.warning("ML analysis failed, using biomechanical analysis: \(error)", category: .mlProcessing)
            }
        }
        
        // Store for next frame comparison
        previousPose = poseData
        lastAnalysis = finalAnalysis
        
        return finalAnalysis
    }
    
    private func performMLAnalysis(_ poseData: BodyPoseData, exercise: ExerciseType) async throws -> FormAnalysis {
        guard let model = model else {
            throw MLModelError.generic("Model not available")
        }
        
        // Extract enhanced features for ML model
        let features = extractEnhancedFeatures(from: poseData, exercise: exercise)
        
        // Prepare input
        let inputArray = try MLMultiArray(shape: [NSNumber(value: inputFeatureCount)], dataType: .float32)
        for (index, feature) in features.enumerated() {
            if index < inputArray.count {
                inputArray[index] = NSNumber(value: feature)
            }
        }
        
        let input = try MLDictionaryFeatureProvider(dictionary: [
            "pose_features": MLFeatureValue(multiArray: inputArray)
        ])
        
        // Perform prediction
        let output = try model.prediction(from: input)
        
        // Parse ML output
        return parseMLOutput(output, exercise: exercise)
    }
    
    private func extractEnhancedFeatures(from poseData: BodyPoseData, exercise: ExerciseType) -> [Float] {
        var features: [Float] = []
        
        // Basic joint positions (51 features)
        let basicFeatures = extractBasicFeatures(from: poseData)
        features.append(contentsOf: basicFeatures)
        
        // Joint angles (12 features)
        let angles = calculateJointAngles(from: poseData)
        features.append(contentsOf: angles)
        
        // Velocities if previous pose available (12 features)
        if let previousPose = previousPose {
            let velocities = calculateVelocities(current: poseData, previous: previousPose)
            features.append(contentsOf: velocities)
        } else {
            features.append(contentsOf: Array(repeating: 0.0, count: 12))
        }
        
        return features
    }
    
    private func extractBasicFeatures(from poseData: BodyPoseData) -> [Float] {
        var features: [Float] = []
        
        let keyJoints: [JointName] = [
            .head, .neck,
            .leftShoulder, .rightShoulder,
            .leftElbow, .rightElbow,
            .leftWrist, .rightWrist,
            .spine6, .root,
            .leftHip, .rightHip,
            .leftKnee, .rightKnee,
            .leftAnkle, .rightAnkle,
            .leftToe
        ]
        
        for jointName in keyJoints {
            if let joint = poseData.joints[jointName] {
                features.append(joint.position.x)
                features.append(joint.position.y)
                features.append(joint.position.z)
            } else {
                features.append(0.0)
                features.append(0.0)
                features.append(0.0)
            }
        }
        
        return features
    }
    
    private func calculateJointAngles(from poseData: BodyPoseData) -> [Float] {
        var angles: [Float] = []
        
        // Calculate key joint angles for form analysis
        
        // Knee angles
        if let leftKneeAngle = biomechanicsAnalyzer.calculateKneeAngle(poseData, side: .left) {
            angles.append(leftKneeAngle)
        } else {
            angles.append(0.0)
        }
        
        if let rightKneeAngle = biomechanicsAnalyzer.calculateKneeAngle(poseData, side: .right) {
            angles.append(rightKneeAngle)
        } else {
            angles.append(0.0)
        }
        
        // Hip angles
        if let leftHipAngle = biomechanicsAnalyzer.calculateHipAngle(poseData, side: .left) {
            angles.append(leftHipAngle)
        } else {
            angles.append(0.0)
        }
        
        if let rightHipAngle = biomechanicsAnalyzer.calculateHipAngle(poseData, side: .right) {
            angles.append(rightHipAngle)
        } else {
            angles.append(0.0)
        }
        
        // Shoulder angles
        if let leftShoulderAngle = biomechanicsAnalyzer.calculateShoulderAngle(poseData, side: .left) {
            angles.append(leftShoulderAngle)
        } else {
            angles.append(0.0)
        }
        
        if let rightShoulderAngle = biomechanicsAnalyzer.calculateShoulderAngle(poseData, side: .right) {
            angles.append(rightShoulderAngle)
        } else {
            angles.append(0.0)
        }
        
        // Elbow angles
        if let leftElbowAngle = biomechanicsAnalyzer.calculateElbowAngle(poseData, side: .left) {
            angles.append(leftElbowAngle)
        } else {
            angles.append(0.0)
        }
        
        if let rightElbowAngle = biomechanicsAnalyzer.calculateElbowAngle(poseData, side: .right) {
            angles.append(rightElbowAngle)
        } else {
            angles.append(0.0)
        }
        
        // Spine angle
        if let spineAngle = biomechanicsAnalyzer.calculateSpineAngle(poseData) {
            angles.append(spineAngle)
        } else {
            angles.append(0.0)
        }
        
        // Torso lean
        if let torsoLean = biomechanicsAnalyzer.calculateTorsoLean(poseData) {
            angles.append(torsoLean)
        } else {
            angles.append(0.0)
        }
        
        // Balance metrics
        if let balance = biomechanicsAnalyzer.calculateBalance(poseData) {
            angles.append(balance.x)
            angles.append(balance.y)
        } else {
            angles.append(0.0)
            angles.append(0.0)
        }
        
        return angles
    }
    
    private func calculateVelocities(current: BodyPoseData, previous: BodyPoseData) -> [Float] {
        var velocities: [Float] = []
        
        let timeDelta = Float(current.timestamp.timeIntervalSince(previous.timestamp))
        guard timeDelta > 0 else {
            return Array(repeating: 0.0, count: 12)
        }
        
        let keyJoints: [JointName] = [
            .leftWrist, .rightWrist,
            .leftElbow, .rightElbow,
            .leftKnee, .rightKnee,
            .leftAnkle, .rightAnkle
        ]
        
        for jointName in keyJoints {
            if let currentJoint = current.joints[jointName],
               let previousJoint = previous.joints[jointName] {
                let velocity = (currentJoint.position - previousJoint.position) / timeDelta
                let speed = length(velocity)
                velocities.append(speed)
            } else {
                velocities.append(0.0)
            }
        }
        
        // Pad to 12 features if needed
        while velocities.count < 12 {
            velocities.append(0.0)
        }
        
        return Array(velocities.prefix(12))
    }
    
    private func parseMLOutput(_ output: MLFeatureProvider, exercise: ExerciseType) -> FormAnalysis {
        // Parse ML model output to create FormAnalysis
        // This would depend on the specific ML model output format
        
        // For now, create a basic analysis
        return FormAnalysis(
            overallScore: 0.8,
            scores: [
                .jointAlignment: 0.8,
                .rangeOfMotion: 0.7,
                .tempo: 0.9,
                .stability: 0.8
            ],
            issues: [],
            recommendations: ["ML-enhanced analysis"],
            exerciseType: exercise
        )
    }
    
    private func combineAnalyses(_ biomechanical: FormAnalysis, _ ml: FormAnalysis) -> FormAnalysis {
        // Combine biomechanical and ML analyses
        var combinedScores: [FormCriteria: Float] = [:]
        
        // Weight biomechanical analysis more heavily for safety
        let biomechanicalWeight: Float = 0.7
        let mlWeight: Float = 0.3
        
        for criteria in FormCriteria.allCases {
            let bioScore = biomechanical.scores[criteria] ?? 0.0
            let mlScore = ml.scores[criteria] ?? 0.0
            combinedScores[criteria] = bioScore * biomechanicalWeight + mlScore * mlWeight
        }
        
        let overallScore = combinedScores.values.reduce(0, +) / Float(combinedScores.count)
        
        // Combine issues and recommendations
        var combinedIssues = biomechanical.issues
        combinedIssues.append(contentsOf: ml.issues)
        
        var combinedRecommendations = biomechanical.recommendations
        combinedRecommendations.append(contentsOf: ml.recommendations)
        
        return FormAnalysis(
            overallScore: overallScore,
            scores: combinedScores,
            issues: combinedIssues,
            recommendations: combinedRecommendations,
            exerciseType: biomechanical.exerciseType
        )
    }
}

// MARK: - Supporting Types

enum BodySide {
    case left
    case right
}

// MARK: - ML Model Error

enum MLModelError: LocalizedError {
    case generic(String)
    
    var errorDescription: String? {
        switch self {
        case .generic(let message):
            return message
        }
    }
}
