//
//  MockExerciseClassifierModel.swift
//  MotionFitPro
//
//  Created by Cascade on 2025-07-14.
//

import Foundation
import CoreML

/// A mock MLModel for development and testing of the exercise classification pipeline.
/// This class simulates the output of a real Core ML model.
class MockExerciseClassifierModel {

    /// The feature description for the model's input.
    /// A real model would have this defined in its .mlmodel file.
    var modelDescription: MLModelDescription = {
        let input = try! MLFeatureDescription(name: "features", type: .multiArray, multiArrayConstraint: MLMultiArrayConstraint(shape: [1, 100], dataType: .double))
        let outputLabel = try! MLFeatureDescription(name: "label", type: .string)
        let outputProbs = try! MLFeatureDescription(name: "labelProbability", type: .dictionary, dictionaryConstraint: MLDictionaryConstraint(keyType: .string))
        
        return MLModelDescription(inputDescriptionsByName: ["features": input], outputDescriptionsByName: ["label": outputLabel, "labelProbability": outputProbs], predictedFeatureName: "label", predictedProbabilitiesName: "labelProbability")
    }()

    /// Simulates a prediction from the model.
    /// - Parameter input: An `MLFeatureProvider` containing the input features.
    /// - Returns: An `MLFeatureProvider` with the simulated prediction results.
    /// - Throws: An error if the prediction fails (for API compatibility).
    func prediction(from input: MLFeatureProvider) throws -> MLFeatureProvider {
        // In this mock implementation, we ignore the input and return a fixed result.
        // This allows us to test the pipeline's response to a specific classification.
        let outputLabel = MLFeatureValue(string: ExerciseType.squat.rawValue)
        let outputProbs = MLFeatureValue(dictionary: [
            ExerciseType.squat.rawValue: 0.95,
            ExerciseType.pushUp.rawValue: 0.02,
            ExerciseType.lunge.rawValue: 0.01,
            ExerciseType.jumpingJack.rawValue: 0.01,
            ExerciseType.plank.rawValue: 0.01
        ])

        let outputFeatures: [String: MLFeatureValue] = [
            "label": outputLabel,
            "labelProbability": outputProbs
        ]

        return try MLDictionaryFeatureProvider(dictionary: outputFeatures)
    }
}
