import Foundation
import CoreML
import Vision
import Accelerate
import simd

@MainActor
class MLOptimizationManager: ObservableObject {
    static let shared = MLOptimizationManager()
    
    @Published var currentConfiguration: MLConfiguration = .balanced
    @Published var modelCacheStatus: ModelCacheStatus = .idle
    @Published var inferenceMetrics: InferenceMetrics = InferenceMetrics()
    @Published var isNeuralEngineOptimized = false
    
    // Model management
    private let modelCache = MLModelCache()
    private let batchProcessor = MLBatchProcessor()
    private let quantizationManager = ModelQuantizationManager()
    private let neuralEngineOptimizer = NeuralEngineOptimizer()
    
    // Performance monitoring
    private var inferenceTimer = InferenceTimer()
    private var thermalMonitor = ThermalMonitor()
    
    // Configuration
    private var deviceCapabilities: MLDeviceCapabilities
    private var optimizationStrategy: OptimizationStrategy = .adaptive
    
    private init() {
        self.deviceCapabilities = MLDeviceCapabilities.detect()
        setupThermalMonitoring()
        setupPerformanceMonitoring()
        optimizeForDevice()
    }
    
    // MARK: - Public Methods
    
    func optimizeForInference(modelType: MLModelType, inputSize: CGSize) async {
        Logger.shared.info("Optimizing ML for inference: \(modelType)", category: .mlProcessing)
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        await withTaskGroup(of: Void.self) { group in
            // Preload and optimize models
            group.addTask {
                await self.preloadModels(for: modelType)
            }
            
            // Optimize for Neural Engine
            group.addTask {
                await self.optimizeForNeuralEngine(modelType: modelType)
            }
            
            // Configure batch processing
            group.addTask {
                await self.configureBatchProcessing(for: modelType, inputSize: inputSize)
            }
            
            // Setup input/output buffers
            group.addTask {
                await self.preallocateBuffers(for: modelType, inputSize: inputSize)
            }
        }
        
        let optimizationTime = CFAbsoluteTimeGetCurrent() - startTime
        Logger.shared.info("ML optimization completed in \(optimizationTime * 1000)ms", category: .mlProcessing)
    }
    
    func performInference<T>(
        model: MLModel,
        input: MLFeatureProvider,
        priority: InferencePriority = .normal
    ) async throws -> T {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        // Select optimal compute unit
        let computeUnit = selectOptimalComputeUnit(for: model, priority: priority)
        
        // Perform inference with optimization
        let result = try await performOptimizedInference(
            model: model,
            input: input,
            computeUnit: computeUnit
        )
        
        let inferenceTime = CFAbsoluteTimeGetCurrent() - startTime
        updateInferenceMetrics(inferenceTime: inferenceTime)
        
        guard let typedResult = result as? T else {
            throw MLOptimizationError.invalidOutputType
        }
        
        return typedResult
    }
    
    func performBatchInference<T>(
        model: MLModel,
        inputs: [MLFeatureProvider],
        batchSize: Int? = nil
    ) async throws -> [T] {
        let optimalBatchSize = batchSize ?? calculateOptimalBatchSize(for: model, inputCount: inputs.count)
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        let results = try await batchProcessor.processBatch(
            model: model,
            inputs: inputs,
            batchSize: optimalBatchSize
        )
        
        let totalTime = CFAbsoluteTimeGetCurrent() - startTime
        Logger.shared.debug("Batch inference completed: \(inputs.count) inputs in \(totalTime * 1000)ms", category: .mlProcessing)
        
        return results.compactMap { $0 as? T }
    }
    
    func setConfiguration(_ configuration: MLConfiguration) {
        Logger.shared.info("Setting ML configuration: \(configuration)", category: .mlProcessing)
        
        currentConfiguration = configuration
        applyConfiguration(configuration)
        
        NotificationCenter.default.post(name: .mlConfigurationChanged, object: configuration)
    }
    
    func enableNeuralEngineOptimization(_ enabled: Bool) {
        isNeuralEngineOptimized = enabled
        
        if enabled && deviceCapabilities.hasNeuralEngine {
            neuralEngineOptimizer.enableOptimizations()
        } else {
            neuralEngineOptimizer.disableOptimizations()
        }
        
        Logger.shared.info("Neural Engine optimization: \(enabled ? "enabled" : "disabled")", category: .mlProcessing)
    }
    
    func quantizeModel(_ model: MLModel, targetSize: ModelSize) async throws -> MLModel {
        Logger.shared.info("Quantizing model to \(targetSize)", category: .mlProcessing)
        
        let quantizedModel = try await quantizationManager.quantizeModel(
            model: model,
            targetSize: targetSize,
            preserveAccuracy: currentConfiguration.preserveAccuracy
        )
        
        // Cache the quantized model
        await modelCache.store(quantizedModel, key: "\(model.modelDescription.metadata[MLModelMetadataKey.description] ?? "unknown")_\(targetSize)")
        
        return quantizedModel
    }
    
    func warmupModels(for workoutType: WorkoutType) async {
        Logger.shared.info("Warming up ML models for workout: \(workoutType)", category: .mlProcessing)
        
        let modelsToWarmup = getRequiredModels(for: workoutType)
        
        await withTaskGroup(of: Void.self) { group in
            for modelType in modelsToWarmup {
                group.addTask {
                    await self.warmupModel(modelType)
                }
            }
        }
    }
    
    func getPerformanceReport() -> MLPerformanceReport {
        return MLPerformanceReport(
            configuration: currentConfiguration,
            inferenceMetrics: inferenceMetrics,
            cacheStatus: modelCacheStatus,
            deviceCapabilities: deviceCapabilities,
            neuralEngineStatus: isNeuralEngineOptimized,
            recommendations: generateOptimizationRecommendations()
        )
    }
    
    // MARK: - Private Methods
    
    private func setupThermalMonitoring() {
        thermalMonitor.onThermalStateChanged = { [weak self] state in
            Task { @MainActor in
                self?.handleThermalStateChange(state)
            }
        }
    }
    
    private func setupPerformanceMonitoring() {
        NotificationCenter.default.addObserver(
            forName: .qualitySettingsChanged,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            if let settings = notification.object as? QualitySettings {
                self?.adaptToQualitySettings(settings)
            }
        }
    }
    
    private func optimizeForDevice() {
        let config: MLConfiguration
        
        switch deviceCapabilities.tier {
        case .flagship:
            config = .maximum
        case .highPerformance:
            config = .balanced
        case .standard:
            config = .efficient
        case .lowEnd:
            config = .minimal
        }
        
        setConfiguration(config)
        
        // Enable Neural Engine if available
        if deviceCapabilities.hasNeuralEngine {
            enableNeuralEngineOptimization(true)
        }
    }
    
    private func preloadModels(for modelType: MLModelType) async {
        modelCacheStatus = .loading
        
        do {
            let models = try await loadModelsForType(modelType)
            
            for model in models {
                await modelCache.preload(model)
            }
            
            modelCacheStatus = .ready
            Logger.shared.info("Models preloaded for type: \(modelType)", category: .mlProcessing)
        } catch {
            modelCacheStatus = .error
            Logger.shared.error("Failed to preload models: \(error)", category: .mlProcessing)
        }
    }
    
    private func optimizeForNeuralEngine(modelType: MLModelType) async {
        guard deviceCapabilities.hasNeuralEngine else { return }
        
        do {
            let optimizedModels = try await neuralEngineOptimizer.optimizeModels(for: modelType)
            
            for model in optimizedModels {
                await modelCache.store(model, key: "\(modelType)_neural_engine")
            }
            
            Logger.shared.info("Models optimized for Neural Engine", category: .mlProcessing)
        } catch {
            Logger.shared.error("Neural Engine optimization failed: \(error)", category: .mlProcessing)
        }
    }
    
    private func configureBatchProcessing(for modelType: MLModelType, inputSize: CGSize) async {
        let optimalBatchSize = calculateOptimalBatchSize(for: modelType, inputSize: inputSize)
        let maxConcurrentOperations = deviceCapabilities.maxConcurrentInferences
        
        await batchProcessor.configure(
            batchSize: optimalBatchSize,
            maxConcurrentOperations: maxConcurrentOperations,
            modelType: modelType
        )
        
        Logger.shared.debug("Batch processing configured: batch size \(optimalBatchSize), max concurrent \(maxConcurrentOperations)", category: .mlProcessing)
    }
    
    private func preallocateBuffers(for modelType: MLModelType, inputSize: CGSize) async {
        let bufferSizes = calculateBufferSizes(for: modelType, inputSize: inputSize)
        
        await withTaskGroup(of: Void.self) { group in
            group.addTask {
                await self.preallocateInputBuffers(sizes: bufferSizes.input)
            }
            
            group.addTask {
                await self.preallocateOutputBuffers(sizes: bufferSizes.output)
            }
            
            group.addTask {
                await self.preallocateIntermediateBuffers(sizes: bufferSizes.intermediate)
            }
        }
    }
    
    private func performOptimizedInference(
        model: MLModel,
        input: MLFeatureProvider,
        computeUnit: MLComputeUnits
    ) async throws -> MLFeatureProvider {
        let configuration = MLModelConfiguration()
        configuration.computeUnits = computeUnit
        configuration.allowLowPrecisionAccumulationOnGPU = currentConfiguration.allowLowPrecision
        
        // Use cached model if available
        if let cachedModel = await modelCache.getCachedModel(for: model, configuration: configuration) {
            return try await cachedModel.prediction(from: input)
        }
        
        // Fallback to original model
        return try await model.prediction(from: input)
    }
    
    private func selectOptimalComputeUnit(for model: MLModel, priority: InferencePriority) -> MLComputeUnits {
        switch priority {
        case .realtime:
            return deviceCapabilities.hasNeuralEngine ? .cpuAndNeuralEngine : .cpuAndGPU
        case .normal:
            return .all
        case .background:
            return .cpuOnly
        case .lowPower:
            return .cpuOnly
        }
    }
    
    private func calculateOptimalBatchSize(for modelType: MLModelType, inputCount: Int) -> Int {
        let baseBatchSize: Int
        
        switch modelType {
        case .poseEstimation:
            baseBatchSize = 4
        case .exerciseClassification:
            baseBatchSize = 8
        case .formAnalysis:
            baseBatchSize = 2
        }
        
        // Adjust based on device capabilities
        let deviceMultiplier = deviceCapabilities.tier.batchSizeMultiplier
        let optimalBatchSize = baseBatchSize * deviceMultiplier
        
        return min(optimalBatchSize, inputCount)
    }
    
    private func calculateOptimalBatchSize(for modelType: MLModelType, inputSize: CGSize) -> Int {
        let memoryRequirement = estimateMemoryRequirement(for: modelType, inputSize: inputSize)
        let availableMemory = deviceCapabilities.availableMemoryMB
        
        let maxBatchSize = Int(availableMemory / memoryRequirement)
        let optimalBatchSize = calculateOptimalBatchSize(for: modelType, inputCount: maxBatchSize)
        
        return max(1, min(optimalBatchSize, maxBatchSize))
    }
    
    private func estimateMemoryRequirement(for modelType: MLModelType, inputSize: CGSize) -> Double {
        let pixelCount = inputSize.width * inputSize.height
        let baseMemoryMB: Double
        
        switch modelType {
        case .poseEstimation:
            baseMemoryMB = pixelCount * 4 / (1024 * 1024) // 4 bytes per pixel
        case .exerciseClassification:
            baseMemoryMB = pixelCount * 3 / (1024 * 1024) // 3 bytes per pixel
        case .formAnalysis:
            baseMemoryMB = pixelCount * 8 / (1024 * 1024) // 8 bytes per pixel (high precision)
        }
        
        return baseMemoryMB * 2 // Double for intermediate buffers
    }
    
    private func calculateBufferSizes(for modelType: MLModelType, inputSize: CGSize) -> BufferSizes {
        let inputBufferSize = Int(inputSize.width * inputSize.height * 4) // RGBA
        let outputBufferSize: Int
        let intermediateBufferSize: Int
        
        switch modelType {
        case .poseEstimation:
            outputBufferSize = 17 * 3 * 4 // 17 joints, xyz coordinates, 4 bytes each
            intermediateBufferSize = inputBufferSize / 2
        case .exerciseClassification:
            outputBufferSize = 100 * 4 // 100 classes, 4 bytes each
            intermediateBufferSize = inputBufferSize / 4
        case .formAnalysis:
            outputBufferSize = 50 * 4 // 50 form metrics, 4 bytes each
            intermediateBufferSize = inputBufferSize
        }
        
        return BufferSizes(
            input: inputBufferSize,
            output: outputBufferSize,
            intermediate: intermediateBufferSize
        )
    }
    
    private func preallocateInputBuffers(sizes: Int) async {
        // Pre-allocate input buffers to avoid allocation overhead
        // Implementation would create and pool CVPixelBuffers
    }
    
    private func preallocateOutputBuffers(sizes: Int) async {
        // Pre-allocate output buffers
        // Implementation would create and pool MLMultiArrays
    }
    
    private func preallocateIntermediateBuffers(sizes: Int) async {
        // Pre-allocate intermediate processing buffers
    }
    
    private func updateInferenceMetrics(inferenceTime: TimeInterval) {
        inferenceMetrics.lastInferenceTime = inferenceTime
        inferenceMetrics.averageInferenceTime = (inferenceMetrics.averageInferenceTime * 0.9) + (inferenceTime * 0.1)
        inferenceMetrics.totalInferences += 1
        
        // Check performance targets
        let targetLatency = currentConfiguration.targetLatency
        if inferenceTime > targetLatency {
            inferenceMetrics.violationCount += 1
            Logger.shared.warning("Inference latency violation: \(inferenceTime * 1000)ms > \(targetLatency * 1000)ms", category: .mlProcessing)
        }
    }
    
    private func loadModelsForType(_ modelType: MLModelType) async throws -> [MLModel] {
        // Load models based on type
        var models: [MLModel] = []
        
        switch modelType {
        case .poseEstimation:
            let poseModel = try await loadModel(named: "PoseEstimationModel")
            models.append(poseModel)
        case .exerciseClassification:
            let classifierModel = try await loadModel(named: "ExerciseClassificationModel")
            models.append(classifierModel)
        case .formAnalysis:
            let formModel = try await loadModel(named: "FormAnalysisModel")
            models.append(formModel)
        }
        
        return models
    }
    
    private func loadModel(named name: String) async throws -> MLModel {
        guard let modelURL = Bundle.main.url(forResource: name, withExtension: "mlmodelc") else {
            throw MLOptimizationError.modelNotFound(name)
        }
        
        let configuration = MLModelConfiguration()
        configuration.computeUnits = isNeuralEngineOptimized ? .cpuAndNeuralEngine : .all
        
        return try await MLModel(contentsOf: modelURL, configuration: configuration)
    }
    
    private func warmupModel(_ modelType: MLModelType) async {
        do {
            let models = try await loadModelsForType(modelType)
            
            for model in models {
                // Perform dummy inference to warm up the model
                let dummyInput = createDummyInput(for: model)
                _ = try await model.prediction(from: dummyInput)
            }
            
            Logger.shared.debug("Model warmed up: \(modelType)", category: .mlProcessing)
        } catch {
            Logger.shared.error("Failed to warm up model \(modelType): \(error)", category: .mlProcessing)
        }
    }
    
    private func createDummyInput(for model: MLModel) -> MLFeatureProvider {
        // Create dummy input based on model's input description
        let inputDescription = model.modelDescription.inputDescriptionsByName
        var features: [String: MLFeatureValue] = [:]
        
        for (name, description) in inputDescription {
            switch description.type {
            case .image:
                if let imageConstraint = description.imageConstraint {
                    let dummyImage = createDummyImage(
                        width: imageConstraint.pixelsWide,
                        height: imageConstraint.pixelsHigh
                    )
                    features[name] = MLFeatureValue(cgImage: dummyImage)
                }
            case .multiArray:
                if let arrayConstraint = description.multiArrayConstraint {
                    let dummyArray = try? MLMultiArray(shape: arrayConstraint.shape, dataType: arrayConstraint.dataType)
                    if let array = dummyArray {
                        features[name] = MLFeatureValue(multiArray: array)
                    }
                }
            default:
                break
            }
        }
        
        return try! MLDictionaryFeatureProvider(dictionary: features)
    }
    
    private func createDummyImage(width: Int, height: Int) -> CGImage {
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let context = CGContext(
            data: nil,
            width: width,
            height: height,
            bitsPerComponent: 8,
            bytesPerRow: width * 4,
            space: colorSpace,
            bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue
        )!
        
        return context.makeImage()!
    }
    
    private func getRequiredModels(for workoutType: WorkoutType) -> [MLModelType] {
        switch workoutType {
        case .highIntensity:
            return [.poseEstimation, .exerciseClassification]
        case .precision:
            return [.poseEstimation, .formAnalysis]
        case .endurance:
            return [.poseEstimation, .exerciseClassification]
        }
    }
    
    private func applyConfiguration(_ configuration: MLConfiguration) {
        switch configuration {
        case .maximum:
            applyMaximumConfiguration()
        case .balanced:
            applyBalancedConfiguration()
        case .efficient:
            applyEfficientConfiguration()
        case .minimal:
            applyMinimalConfiguration()
        }
    }
    
    private func applyMaximumConfiguration() {
        batchProcessor.setMaxConcurrentOperations(deviceCapabilities.maxConcurrentInferences)
        neuralEngineOptimizer.enableAllOptimizations()
    }
    
    private func applyBalancedConfiguration() {
        batchProcessor.setMaxConcurrentOperations(max(1, deviceCapabilities.maxConcurrentInferences / 2))
        neuralEngineOptimizer.enableBalancedOptimizations()
    }
    
    private func applyEfficientConfiguration() {
        batchProcessor.setMaxConcurrentOperations(1)
        neuralEngineOptimizer.enableEfficientOptimizations()
    }
    
    private func applyMinimalConfiguration() {
        batchProcessor.setMaxConcurrentOperations(1)
        neuralEngineOptimizer.disableOptimizations()
    }
    
    private func handleThermalStateChange(_ state: ProcessInfo.ThermalState) {
        switch state {
        case .nominal:
            if optimizationStrategy == .adaptive {
                setConfiguration(.balanced)
            }
        case .fair:
            setConfiguration(.efficient)
        case .serious:
            setConfiguration(.minimal)
        case .critical:
            setConfiguration(.minimal)
            batchProcessor.pauseProcessing()
        @unknown default:
            setConfiguration(.balanced)
        }
    }
    
    private func adaptToQualitySettings(_ settings: QualitySettings) {
        let inferenceFrequency = settings.mlInferenceFrequency
        
        // Adjust batch size based on inference frequency
        let targetBatchSize = inferenceFrequency > 25 ? 2 : 4
        batchProcessor.setTargetBatchSize(targetBatchSize)
        
        // Adjust precision based on quality
        let allowLowPrecision = settings.batteryOptimizationEnabled
        currentConfiguration = currentConfiguration.withLowPrecision(allowLowPrecision)
    }
    
    private func generateOptimizationRecommendations() -> [MLOptimizationRecommendation] {
        var recommendations: [MLOptimizationRecommendation] = []
        
        if inferenceMetrics.averageInferenceTime > currentConfiguration.targetLatency {
            recommendations.append(.reduceModelComplexity)
            recommendations.append(.enableQuantization)
        }
        
        if inferenceMetrics.violationCount > 10 {
            recommendations.append(.optimizeBatchSize)
            recommendations.append(.enableNeuralEngine)
        }
        
        if !isNeuralEngineOptimized && deviceCapabilities.hasNeuralEngine {
            recommendations.append(.enableNeuralEngine)
        }
        
        return recommendations
    }
}

// MARK: - Supporting Classes

class MLModelCache {
    private var cache: [String: MLModel] = [:]
    private let queue = DispatchQueue(label: "ml-model-cache", qos: .userInitiated)
    
    func store(_ model: MLModel, key: String) async {
        await withCheckedContinuation { continuation in
            queue.async {
                self.cache[key] = model
                continuation.resume()
            }
        }
    }
    
    func preload(_ model: MLModel) async {
        let key = model.modelDescription.metadata[MLModelMetadataKey.description] ?? "unknown"
        await store(model, key: key)
    }
    
    func getCachedModel(for model: MLModel, configuration: MLModelConfiguration) async -> MLModel? {
        let key = "\(model.modelDescription.metadata[MLModelMetadataKey.description] ?? "unknown")_\(configuration.computeUnits.rawValue)"
        
        return await withCheckedContinuation { continuation in
            queue.async {
                continuation.resume(returning: self.cache[key])
            }
        }
    }
}

class MLBatchProcessor {
    private var batchSize = 4
    private var maxConcurrentOperations = 2
    private var targetBatchSize = 4
    private var isPaused = false
    
    func configure(batchSize: Int, maxConcurrentOperations: Int, modelType: MLModelType) async {
        self.batchSize = batchSize
        self.maxConcurrentOperations = maxConcurrentOperations
        self.targetBatchSize = batchSize
    }
    
    func processBatch<T>(
        model: MLModel,
        inputs: [MLFeatureProvider],
        batchSize: Int
    ) async throws -> [T] {
        guard !isPaused else { 
            throw MLOptimizationError.processingPaused
        }
        
        var results: [T] = []
        
        for chunk in inputs.chunked(into: batchSize) {
            let chunkResults = try await processBatchChunk(model: model, inputs: chunk)
            results.append(contentsOf: chunkResults)
        }
        
        return results
    }
    
    func setMaxConcurrentOperations(_ count: Int) {
        maxConcurrentOperations = count
    }
    
    func setTargetBatchSize(_ size: Int) {
        targetBatchSize = size
    }
    
    func pauseProcessing() {
        isPaused = true
    }
    
    func resumeProcessing() {
        isPaused = false
    }
    
    private func processBatchChunk<T>(model: MLModel, inputs: [MLFeatureProvider]) async throws -> [T] {
        return try await withThrowingTaskGroup(of: T.self) { group in
            for input in inputs {
                group.addTask {
                    let output = try await model.prediction(from: input)
                    return output as! T
                }
            }
            
            var results: [T] = []
            for try await result in group {
                results.append(result)
            }
            return results
        }
    }
}

class ModelQuantizationManager {
    func quantizeModel(
        model: MLModel,
        targetSize: ModelSize,
        preserveAccuracy: Bool
    ) async throws -> MLModel {
        // Simplified quantization - in production, this would use Core ML Tools
        Logger.shared.info("Quantizing model to \(targetSize)", category: .mlProcessing)
        
        // Simulate quantization delay
        await Task.sleep(nanoseconds: 100_000_000) // 100ms
        
        return model // Return original model for now
    }
}

class NeuralEngineOptimizer {
    private var optimizationsEnabled = false
    
    func enableOptimizations() {
        optimizationsEnabled = true
    }
    
    func disableOptimizations() {
        optimizationsEnabled = false
    }
    
    func enableAllOptimizations() {
        optimizationsEnabled = true
    }
    
    func enableBalancedOptimizations() {
        optimizationsEnabled = true
    }
    
    func enableEfficientOptimizations() {
        optimizationsEnabled = true
    }
    
    func optimizeModels(for modelType: MLModelType) async throws -> [MLModel] {
        guard optimizationsEnabled else { return [] }
        
        // Simulate Neural Engine optimization
        await Task.sleep(nanoseconds: 200_000_000) // 200ms
        
        return [] // Return optimized models
    }
}

class InferenceTimer {
    private var startTime: CFAbsoluteTime = 0
    
    func start() {
        startTime = CFAbsoluteTimeGetCurrent()
    }
    
    func stop() -> TimeInterval {
        return CFAbsoluteTimeGetCurrent() - startTime
    }
}

class ThermalMonitor {
    var onThermalStateChanged: ((ProcessInfo.ThermalState) -> Void)?
    
    init() {
        NotificationCenter.default.addObserver(
            forName: ProcessInfo.thermalStateDidChangeNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.onThermalStateChanged?(ProcessInfo.processInfo.thermalState)
        }
    }
}

// MARK: - Supporting Types

enum MLModelType: String, CaseIterable {
    case poseEstimation = "pose_estimation"
    case exerciseClassification = "exercise_classification"
    case formAnalysis = "form_analysis"
}

enum InferencePriority {
    case realtime
    case normal
    case background
    case lowPower
}

enum ModelSize: String, CaseIterable {
    case full = "Full"
    case half = "Half"
    case quarter = "Quarter"
}

enum MLConfiguration: String, CaseIterable {
    case maximum = "Maximum"
    case balanced = "Balanced"
    case efficient = "Efficient"
    case minimal = "Minimal"
    
    var targetLatency: TimeInterval {
        switch self {
        case .maximum: return 0.033 // 30 FPS
        case .balanced: return 0.050 // 20 FPS
        case .efficient: return 0.100 // 10 FPS
        case .minimal: return 0.200 // 5 FPS
        }
    }
    
    var allowLowPrecision: Bool {
        switch self {
        case .maximum: return false
        case .balanced: return false
        case .efficient: return true
        case .minimal: return true
        }
    }
    
    var preserveAccuracy: Bool {
        switch self {
        case .maximum: return true
        case .balanced: return true
        case .efficient: return false
        case .minimal: return false
        }
    }
    
    func withLowPrecision(_ enabled: Bool) -> MLConfiguration {
        return self // Simplified - would create modified configuration
    }
}

enum OptimizationStrategy {
    case aggressive
    case balanced
    case conservative
    case adaptive
}

enum ModelCacheStatus {
    case idle
    case loading
    case ready
    case error
}

enum MLOptimizationError: Error {
    case modelNotFound(String)
    case invalidOutputType
    case processingPaused
    case quantizationFailed
    case neuralEngineNotAvailable
}

enum MLOptimizationRecommendation: String, CaseIterable {
    case reduceModelComplexity = "Reduce model complexity"
    case enableQuantization = "Enable model quantization"
    case optimizeBatchSize = "Optimize batch size"
    case enableNeuralEngine = "Enable Neural Engine optimization"
    case reduceInferenceFrequency = "Reduce inference frequency"
    case useBackgroundPriority = "Use background priority"
}

struct MLDeviceCapabilities {
    let tier: DeviceTier
    let hasNeuralEngine: Bool
    let neuralEngineVersion: Int
    let maxConcurrentInferences: Int
    let availableMemoryMB: Double
    
    enum DeviceTier {
        case flagship
        case highPerformance
        case standard
        case lowEnd
        
        var batchSizeMultiplier: Int {
            switch self {
            case .flagship: return 4
            case .highPerformance: return 2
            case .standard: return 1
            case .lowEnd: return 1
            }
        }
    }
    
    static func detect() -> MLDeviceCapabilities {
        let device = UIDevice.current
        let processInfo = ProcessInfo.processInfo
        
        // Simplified device detection
        let tier: DeviceTier = .standard
        let hasNeuralEngine = true
        let neuralEngineVersion = 3
        let maxConcurrentInferences = 2
        let availableMemoryMB = Double(processInfo.physicalMemory) / (1024 * 1024) * 0.5 // 50% of physical memory
        
        return MLDeviceCapabilities(
            tier: tier,
            hasNeuralEngine: hasNeuralEngine,
            neuralEngineVersion: neuralEngineVersion,
            maxConcurrentInferences: maxConcurrentInferences,
            availableMemoryMB: availableMemoryMB
        )
    }
}

struct InferenceMetrics {
    var lastInferenceTime: TimeInterval = 0
    var averageInferenceTime: TimeInterval = 0
    var totalInferences: Int = 0
    var violationCount: Int = 0
}

struct BufferSizes {
    let input: Int
    let output: Int
    let intermediate: Int
}

struct MLPerformanceReport {
    let configuration: MLConfiguration
    let inferenceMetrics: InferenceMetrics
    let cacheStatus: ModelCacheStatus
    let deviceCapabilities: MLDeviceCapabilities
    let neuralEngineStatus: Bool
    let recommendations: [MLOptimizationRecommendation]
}

// MARK: - Extensions

extension Array {
    func chunked(into size: Int) -> [[Element]] {
        return stride(from: 0, to: count, by: size).map {
            Array(self[$0..<Swift.min($0 + size, count)])
        }
    }
}

extension Notification.Name {
    static let mlConfigurationChanged = Notification.Name("MLConfigurationChanged")
}