import Foundation
import Combine
import CoreML

// MARK: - Real ML Processing Service

/// Production ML processing service with real models and feature extraction
@MainActor
final class RealMLProcessingService: MLProcessingServiceProtocol, ObservableObject {
    
    // MARK: - Properties
    
    @Published var isProcessing = false
    @Published var processingStats = ProcessingStats()
    
    private let logger = Logger()
    
    // ML Models
    private let exerciseClassifier = ExerciseClassificationModel()
    private let formAnalyzer = FormAnalysisModel()
    
    // Feature extraction
    private let featureExtractor = FeatureExtractor()
    
    // Processing pipeline
    private let processingQueue = DispatchQueue(label: "ml.processing", qos: .userInitiated)
    private var processingTask: Task<Void, Never>?
    
    // Performance monitoring
    private var processingTimes: [TimeInterval] = []
    private let maxProcessingHistory = 100
    
    // MARK: - Initialization
    
    init() {
        setupProcessingPipeline()
    }
    
    deinit {
        processingTask?.cancel()
    }
    
    // MARK: - Setup
    
    private func setupProcessingPipeline() {
        logger.info("Setting up ML processing pipeline", category: .mlProcessing)
        
        // Initialize models asynchronously
        Task {
            await exerciseClassifier.objectWillChange.sink { [weak self] in
                self?.objectWillChange.send()
            }.store(in: &cancellables)
            
            await formAnalyzer.objectWillChange.sink { [weak self] in
                self?.objectWillChange.send()
            }.store(in: &cancellables)
        }
    }
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Main Processing Methods
    
    func processBodyPose(_ poseData: BodyPoseData) async throws -> ExerciseAnalysis {
        let startTime = Date()
        isProcessing = true
        
        defer {
            isProcessing = false
            updateProcessingStats(startTime: startTime)
        }
        
        do {
            // Extract features
            let features = featureExtractor.extractFeatures(from: poseData)
            
            guard features.isValid else {
                throw MLProcessingError.invalidFeatures("Insufficient feature quality")
            }
            
            // Classify exercise (if we have enough temporal data)
            let exerciseType: ExerciseType
            if featureExtractor.isReady() {
                let classification = await exerciseClassifier.classifyExercise(from: poseData)
                exerciseType = classification.isConfident ? classification.exercise : .unknown
            } else {
                exerciseType = .unknown
            }
            
            // Analyze form
            let formAnalysis = await formAnalyzer.analyzeForm(poseData, for: exerciseType)
            
            // Detect movement phase
            let movementPhase = detectMovementPhase(poseData, for: exerciseType, formAnalysis: formAnalysis)
            
            // Create comprehensive analysis
            let analysis = ExerciseAnalysis(
                exercise: exerciseType,
                timestamp: poseData.timestamp,
                formScore: Double(formAnalysis.overallScore),
                repPhase: movementPhase,
                poseData: poseData,
                detectedIssues: formAnalysis.issues,
                safetyRisk: assessSafetyRisk(formAnalysis),
                confidence: features.confidence,
                formAnalysis: formAnalysis
            )
            
            logger.debug("Processed pose data: \(exerciseType.displayName), score: \(formAnalysis.overallScore)", category: .mlProcessing)
            
            return analysis
            
        } catch {
            logger.error("ML processing failed: \(error)", category: .mlProcessing)
            throw MLProcessingError.processingFailed(error.localizedDescription)
        }
    }
    
    func classifyExercise(_ poseData: BodyPoseData) async throws -> ExerciseType {
        guard featureExtractor.isReady() else {
            return .unknown
        }
        
        let classification = await exerciseClassifier.classifyExercise(from: poseData)
        return classification.exercise
    }
    
    func analyzeForm(_ poseData: BodyPoseData, for exercise: ExerciseType) async throws -> FormAnalysis {
        return await formAnalyzer.analyzeForm(poseData, for: exercise)
    }
    
    func detectMovementPhase(_ poseData: BodyPoseData, for exercise: ExerciseType) async throws -> MovementPhase {
        return detectMovementPhase(poseData, for: exercise, formAnalysis: nil)
    }
    
    // MARK: - Movement Phase Detection
    
    private func detectMovementPhase(_ poseData: BodyPoseData, for exercise: ExerciseType, formAnalysis: FormAnalysis?) -> MovementPhase {
        switch exercise {
        case .squat:
            return detectSquatPhase(poseData)
        case .pushUp:
            return detectPushUpPhase(poseData)
        case .lunge:
            return detectLungePhase(poseData)
        case .plank:
            return .middle // Plank is isometric
        default:
            return .middle
        }
    }
    
    private func detectSquatPhase(_ poseData: BodyPoseData) -> MovementPhase {
        // Analyze knee and hip angles to determine squat phase
        let biomechanicsAnalyzer = BiomechanicsAnalyzer()
        
        guard let leftKneeAngle = biomechanicsAnalyzer.calculateKneeAngle(poseData, side: .left),
              let rightKneeAngle = biomechanicsAnalyzer.calculateKneeAngle(poseData, side: .right) else {
            return .middle
        }
        
        let avgKneeAngle = (leftKneeAngle + rightKneeAngle) / 2.0
        
        // Determine phase based on knee angle
        switch avgKneeAngle {
        case 160...180:
            return .start // Standing position
        case 120..<160:
            return .eccentric // Lowering
        case 90..<120:
            return .bottom // Bottom position
        case 60..<90:
            return .concentric // Rising
        default:
            return .middle
        }
    }
    
    private func detectPushUpPhase(_ poseData: BodyPoseData) -> MovementPhase {
        // Analyze elbow angles and body height
        let biomechanicsAnalyzer = BiomechanicsAnalyzer()
        
        guard let leftElbowAngle = biomechanicsAnalyzer.calculateElbowAngle(poseData, side: .left),
              let rightElbowAngle = biomechanicsAnalyzer.calculateElbowAngle(poseData, side: .right) else {
            return .middle
        }
        
        let avgElbowAngle = (leftElbowAngle + rightElbowAngle) / 2.0
        
        switch avgElbowAngle {
        case 160...180:
            return .top // Arms extended
        case 120..<160:
            return .eccentric // Lowering
        case 60..<120:
            return .bottom // Bottom position
        case 90..<160:
            return .concentric // Pushing up
        default:
            return .middle
        }
    }
    
    private func detectLungePhase(_ poseData: BodyPoseData) -> MovementPhase {
        // Analyze front leg knee angle and body position
        let biomechanicsAnalyzer = BiomechanicsAnalyzer()
        
        // Determine which leg is forward (simplified)
        guard let leftKneeAngle = biomechanicsAnalyzer.calculateKneeAngle(poseData, side: .left),
              let rightKneeAngle = biomechanicsAnalyzer.calculateKneeAngle(poseData, side: .right) else {
            return .middle
        }
        
        let frontKneeAngle = min(leftKneeAngle, rightKneeAngle) // Front leg typically has smaller angle
        
        switch frontKneeAngle {
        case 140...180:
            return .start // Standing
        case 100..<140:
            return .eccentric // Lowering
        case 70..<100:
            return .bottom // Bottom position
        case 90..<140:
            return .concentric // Rising
        default:
            return .middle
        }
    }
    
    // MARK: - Safety Assessment
    
    private func assessSafetyRisk(_ formAnalysis: FormAnalysis) -> SafetyRisk {
        // Assess safety risk based on form issues
        let highRiskIssues = formAnalysis.issues.filter { $0.severity == .high || $0.severity == .critical }
        let mediumRiskIssues = formAnalysis.issues.filter { $0.severity == .medium }
        
        if !highRiskIssues.isEmpty {
            return .high
        } else if mediumRiskIssues.count >= 3 {
            return .medium
        } else if !mediumRiskIssues.isEmpty {
            return .low
        } else {
            return .none
        }
    }
    
    // MARK: - Performance Monitoring
    
    private func updateProcessingStats(startTime: Date) {
        let processingTime = Date().timeIntervalSince(startTime)
        processingTimes.append(processingTime)
        
        // Maintain history size
        if processingTimes.count > maxProcessingHistory {
            processingTimes.removeFirst()
        }
        
        // Update stats
        processingStats = ProcessingStats(
            averageProcessingTime: processingTimes.reduce(0, +) / Double(processingTimes.count),
            maxProcessingTime: processingTimes.max() ?? 0,
            minProcessingTime: processingTimes.min() ?? 0,
            totalProcessedFrames: processingTimes.count,
            currentFPS: 1.0 / processingTime
        )
    }
    
    // MARK: - Utility Methods
    
    func resetProcessing() {
        featureExtractor.reset()
        processingTimes.removeAll()
        processingStats = ProcessingStats()
        
        logger.info("ML processing pipeline reset", category: .mlProcessing)
    }
    
    func getModelInfo() -> String {
        return """
        Exercise Classifier: \(exerciseClassifier.getModelInfo())
        Form Analyzer: Biomechanics + ML
        Feature Extractor: \(featureExtractor.getHistoryCount()) frames
        Processing Stats: \(processingStats.description)
        """
    }
}

// MARK: - Processing Statistics

struct ProcessingStats {
    let averageProcessingTime: TimeInterval
    let maxProcessingTime: TimeInterval
    let minProcessingTime: TimeInterval
    let totalProcessedFrames: Int
    let currentFPS: Double
    
    init() {
        self.averageProcessingTime = 0
        self.maxProcessingTime = 0
        self.minProcessingTime = 0
        self.totalProcessedFrames = 0
        self.currentFPS = 0
    }
    
    init(averageProcessingTime: TimeInterval, maxProcessingTime: TimeInterval, minProcessingTime: TimeInterval, totalProcessedFrames: Int, currentFPS: Double) {
        self.averageProcessingTime = averageProcessingTime
        self.maxProcessingTime = maxProcessingTime
        self.minProcessingTime = minProcessingTime
        self.totalProcessedFrames = totalProcessedFrames
        self.currentFPS = currentFPS
    }
    
    var description: String {
        return """
        Avg: \(String(format: "%.2f", averageProcessingTime * 1000))ms
        FPS: \(String(format: "%.1f", currentFPS))
        Frames: \(totalProcessedFrames)
        """
    }
}

// MARK: - ML Processing Errors

enum MLProcessingError: LocalizedError {
    case invalidFeatures(String)
    case processingFailed(String)
    case modelNotLoaded(String)
    case insufficientData
    
    var errorDescription: String? {
        switch self {
        case .invalidFeatures(let details):
            return "Invalid features: \(details)"
        case .processingFailed(let details):
            return "Processing failed: \(details)"
        case .modelNotLoaded(let details):
            return "Model not loaded: \(details)"
        case .insufficientData:
            return "Insufficient data for processing"
        }
    }
}
