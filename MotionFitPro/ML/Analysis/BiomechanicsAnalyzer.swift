import Foundation
import simd

// MARK: - Biomechanics Analyzer

/// Analyzes exercise form using biomechanical principles
final class BiomechanicsAnalyzer {
    
    // MARK: - Constants
    
    private let logger = Logger()
    
    // Angle thresholds for different exercises (in degrees)
    private let squatThresholds = SquatThresholds()
    private let pushUpThresholds = PushUpThresholds()
    private let lungeThresholds = LungeThresholds()
    
    // MARK: - Main Analysis
    
    func analyze(_ poseData: BodyPoseData, for exercise: ExerciseType, previousPose: BodyPoseData?) -> FormAnalysis {
        var scores: [FormCriteria: Float] = [:]
        var issues: [FormIssue] = []
        var recommendations: [String] = []
        
        switch exercise {
        case .squat:
            let analysis = analyzeSquat(poseData)
            scores = analysis.scores
            issues = analysis.issues
            recommendations = analysis.recommendations
            
        case .pushUp:
            let analysis = analyzePushUp(poseData)
            scores = analysis.scores
            issues = analysis.issues
            recommendations = analysis.recommendations
            
        case .lunge:
            let analysis = analyzeLunge(poseData)
            scores = analysis.scores
            issues = analysis.issues
            recommendations = analysis.recommendations
            
        case .plank:
            let analysis = analyzePlank(poseData)
            scores = analysis.scores
            issues = analysis.issues
            recommendations = analysis.recommendations
            
        default:
            // Generic analysis for other exercises
            let analysis = analyzeGeneric(poseData)
            scores = analysis.scores
            issues = analysis.issues
            recommendations = analysis.recommendations
        }
        
        // Calculate overall score
        let overallScore = scores.values.reduce(0, +) / Float(scores.count)
        
        return FormAnalysis(
            overallScore: overallScore,
            scores: scores,
            issues: issues,
            recommendations: recommendations,
            exerciseType: exercise,
            confidence: calculateConfidence(poseData)
        )
    }
    
    // MARK: - Exercise-Specific Analysis
    
    private func analyzeSquat(_ poseData: BodyPoseData) -> (scores: [FormCriteria: Float], issues: [FormIssue], recommendations: [String]) {
        var scores: [FormCriteria: Float] = [:]
        var issues: [FormIssue] = []
        var recommendations: [String] = []
        
        // Joint Alignment Analysis
        let alignmentScore = analyzeSquatAlignment(poseData, issues: &issues, recommendations: &recommendations)
        scores[.jointAlignment] = alignmentScore
        
        // Range of Motion Analysis
        let romScore = analyzeSquatRangeOfMotion(poseData, issues: &issues, recommendations: &recommendations)
        scores[.rangeOfMotion] = romScore
        
        // Stability Analysis
        let stabilityScore = analyzeStability(poseData, issues: &issues, recommendations: &recommendations)
        scores[.stability] = stabilityScore
        
        // Posture Analysis
        let postureScore = analyzeSquatPosture(poseData, issues: &issues, recommendations: &recommendations)
        scores[.posture] = postureScore
        
        return (scores, issues, recommendations)
    }
    
    private func analyzeSquatAlignment(_ poseData: BodyPoseData, issues: inout [FormIssue], recommendations: inout [String]) -> Float {
        var score: Float = 1.0
        
        // Check knee alignment
        if let leftKneeAngle = calculateKneeAngle(poseData, side: .left),
           let rightKneeAngle = calculateKneeAngle(poseData, side: .right) {
            
            // Knees should be aligned and not cave inward
            let kneeSymmetry = abs(leftKneeAngle - rightKneeAngle)
            if kneeSymmetry > squatThresholds.maxKneeAsymmetry {
                score -= 0.2
                issues.append(FormIssue(
                    criteria: .jointAlignment,
                    severity: .medium,
                    description: "Knee alignment asymmetry detected",
                    jointInvolved: .leftKnee
                ))
                recommendations.append("Keep knees aligned and tracking over toes")
            }
            
            // Check for knee valgus (knees caving in)
            if leftKneeAngle < squatThresholds.minKneeAngle || rightKneeAngle < squatThresholds.minKneeAngle {
                score -= 0.3
                issues.append(FormIssue(
                    criteria: .jointAlignment,
                    severity: .high,
                    description: "Knee valgus detected - knees caving inward",
                    jointInvolved: .leftKnee
                ))
                recommendations.append("Push knees outward, strengthen glutes")
            }
        }
        
        // Check hip alignment
        if let balance = calculateBalance(poseData) {
            if abs(balance.x) > 0.1 { // 10cm lateral shift
                score -= 0.15
                issues.append(FormIssue(
                    criteria: .jointAlignment,
                    severity: .medium,
                    description: "Lateral weight shift detected",
                    jointInvolved: .root
                ))
                recommendations.append("Keep weight centered between both feet")
            }
        }
        
        return max(score, 0.0)
    }
    
    private func analyzeSquatRangeOfMotion(_ poseData: BodyPoseData, issues: inout [FormIssue], recommendations: inout [String]) -> Float {
        var score: Float = 1.0
        
        // Check squat depth
        if let hipAngle = calculateHipAngle(poseData, side: .left) {
            if hipAngle > squatThresholds.minHipAngleForDepth {
                score -= 0.3
                issues.append(FormIssue(
                    criteria: .rangeOfMotion,
                    severity: .medium,
                    description: "Insufficient squat depth",
                    jointInvolved: .leftHip
                ))
                recommendations.append("Squat deeper - aim for thighs parallel to ground")
            }
        }
        
        // Check ankle mobility
        if let leftAnkleAngle = calculateAnkleAngle(poseData, side: .left),
           let rightAnkleAngle = calculateAnkleAngle(poseData, side: .right) {
            
            if leftAnkleAngle < squatThresholds.minAnkleFlexion || rightAnkleAngle < squatThresholds.minAnkleFlexion {
                score -= 0.2
                issues.append(FormIssue(
                    criteria: .rangeOfMotion,
                    severity: .low,
                    description: "Limited ankle mobility",
                    jointInvolved: .leftAnkle
                ))
                recommendations.append("Work on ankle mobility and calf flexibility")
            }
        }
        
        return max(score, 0.0)
    }
    
    private func analyzeSquatPosture(_ poseData: BodyPoseData, issues: inout [FormIssue], recommendations: inout [String]) -> Float {
        var score: Float = 1.0
        
        // Check spine alignment
        if let spineAngle = calculateSpineAngle(poseData) {
            if abs(spineAngle) > squatThresholds.maxSpineDeviation {
                score -= 0.25
                issues.append(FormIssue(
                    criteria: .posture,
                    severity: .medium,
                    description: "Excessive spine curvature",
                    jointInvolved: .spine6
                ))
                recommendations.append("Maintain neutral spine throughout movement")
            }
        }
        
        // Check torso lean
        if let torsoLean = calculateTorsoLean(poseData) {
            if torsoLean > squatThresholds.maxTorsoLean {
                score -= 0.2
                issues.append(FormIssue(
                    criteria: .posture,
                    severity: .medium,
                    description: "Excessive forward lean",
                    jointInvolved: .spine6
                ))
                recommendations.append("Keep chest up and avoid excessive forward lean")
            }
        }
        
        return max(score, 0.0)
    }
    
    private func analyzePushUp(_ poseData: BodyPoseData) -> (scores: [FormCriteria: Float], issues: [FormIssue], recommendations: [String]) {
        var scores: [FormCriteria: Float] = [:]
        var issues: [FormIssue] = []
        var recommendations: [String] = []
        
        // Body alignment (plank position)
        let alignmentScore = analyzePushUpAlignment(poseData, issues: &issues, recommendations: &recommendations)
        scores[.jointAlignment] = alignmentScore
        
        // Range of motion
        let romScore = analyzePushUpRangeOfMotion(poseData, issues: &issues, recommendations: &recommendations)
        scores[.rangeOfMotion] = romScore
        
        // Stability
        let stabilityScore = analyzeStability(poseData, issues: &issues, recommendations: &recommendations)
        scores[.stability] = stabilityScore
        
        return (scores, issues, recommendations)
    }
    
    private func analyzePushUpAlignment(_ poseData: BodyPoseData, issues: inout [FormIssue], recommendations: inout [String]) -> Float {
        var score: Float = 1.0
        
        // Check body line (head, shoulders, hips, ankles should be aligned)
        if let head = poseData.joints[.head],
           let shoulders = poseData.joints[.spine6],
           let hips = poseData.joints[.root],
           let ankles = poseData.joints[.leftAnkle] {
            
            let bodyLineDeviation = calculateBodyLineDeviation(head: head, shoulders: shoulders, hips: hips, ankles: ankles)
            
            if bodyLineDeviation > pushUpThresholds.maxBodyLineDeviation {
                score -= 0.3
                issues.append(FormIssue(
                    criteria: .jointAlignment,
                    severity: .medium,
                    description: "Body not in straight line",
                    jointInvolved: .spine6
                ))
                recommendations.append("Maintain straight line from head to heels")
            }
        }
        
        return max(score, 0.0)
    }
    
    private func analyzePushUpRangeOfMotion(_ poseData: BodyPoseData, issues: inout [FormIssue], recommendations: inout [String]) -> Float {
        var score: Float = 1.0
        
        // Check elbow angle for proper depth
        if let leftElbowAngle = calculateElbowAngle(poseData, side: .left),
           let rightElbowAngle = calculateElbowAngle(poseData, side: .right) {
            
            let avgElbowAngle = (leftElbowAngle + rightElbowAngle) / 2
            
            if avgElbowAngle > pushUpThresholds.minElbowAngleForDepth {
                score -= 0.25
                issues.append(FormIssue(
                    criteria: .rangeOfMotion,
                    severity: .medium,
                    description: "Insufficient push-up depth",
                    jointInvolved: .leftElbow
                ))
                recommendations.append("Lower chest closer to ground")
            }
        }
        
        return max(score, 0.0)
    }
    
    private func analyzeLunge(_ poseData: BodyPoseData) -> (scores: [FormCriteria: Float], issues: [FormIssue], recommendations: [String]) {
        // Similar structure to squat analysis but with lunge-specific checks
        var scores: [FormCriteria: Float] = [:]
        var issues: [FormIssue] = []
        var recommendations: [String] = []
        
        // Placeholder implementation
        scores[.jointAlignment] = 0.8
        scores[.rangeOfMotion] = 0.7
        scores[.stability] = 0.8
        
        return (scores, issues, recommendations)
    }
    
    private func analyzePlank(_ poseData: BodyPoseData) -> (scores: [FormCriteria: Float], issues: [FormIssue], recommendations: [String]) {
        // Similar to push-up alignment analysis
        var scores: [FormCriteria: Float] = [:]
        var issues: [FormIssue] = []
        var recommendations: [String] = []
        
        // Placeholder implementation
        scores[.jointAlignment] = 0.8
        scores[.stability] = 0.7
        scores[.posture] = 0.8
        
        return (scores, issues, recommendations)
    }
    
    private func analyzeGeneric(_ poseData: BodyPoseData) -> (scores: [FormCriteria: Float], issues: [FormIssue], recommendations: [String]) {
        // Generic analysis for unknown exercises
        var scores: [FormCriteria: Float] = [:]
        var issues: [FormIssue] = []
        var recommendations: [String] = []
        
        // Basic stability and posture checks
        scores[.stability] = analyzeStability(poseData, issues: &issues, recommendations: &recommendations)
        scores[.posture] = 0.8 // Default score
        
        return (scores, issues, recommendations)
    }
    
    private func analyzeStability(_ poseData: BodyPoseData, issues: inout [FormIssue], recommendations: inout [String]) -> Float {
        var score: Float = 1.0
        
        // Check balance and center of mass
        if let balance = calculateBalance(poseData) {
            let totalDeviation = sqrt(balance.x * balance.x + balance.y * balance.y)
            
            if totalDeviation > 0.15 { // 15cm deviation
                score -= 0.2
                issues.append(FormIssue(
                    criteria: .stability,
                    severity: .medium,
                    description: "Balance instability detected",
                    jointInvolved: .root
                ))
                recommendations.append("Focus on core engagement and balance")
            }
        }
        
        return max(score, 0.0)
    }
    
    private func calculateConfidence(_ poseData: BodyPoseData) -> Float {
        // Calculate confidence based on joint visibility and tracking quality
        let visibleJoints = poseData.joints.values.filter { $0.confidence > 0.5 }
        let confidenceScore = Float(visibleJoints.count) / Float(poseData.joints.count)

        return min(confidenceScore * poseData.confidence, 1.0)
    }

    // MARK: - Angle Calculations

    func calculateKneeAngle(_ poseData: BodyPoseData, side: BodySide) -> Float? {
        let hipJoint: JointName = side == .left ? .leftHip : .rightHip
        let kneeJoint: JointName = side == .left ? .leftKnee : .rightKnee
        let ankleJoint: JointName = side == .left ? .leftAnkle : .rightAnkle

        guard let hip = poseData.joints[hipJoint],
              let knee = poseData.joints[kneeJoint],
              let ankle = poseData.joints[ankleJoint] else {
            return nil
        }

        return calculateAngle(point1: hip.position, vertex: knee.position, point2: ankle.position)
    }

    func calculateHipAngle(_ poseData: BodyPoseData, side: BodySide) -> Float? {
        let shoulderJoint: JointName = side == .left ? .leftShoulder : .rightShoulder
        let hipJoint: JointName = side == .left ? .leftHip : .rightHip
        let kneeJoint: JointName = side == .left ? .leftKnee : .rightKnee

        guard let shoulder = poseData.joints[shoulderJoint],
              let hip = poseData.joints[hipJoint],
              let knee = poseData.joints[kneeJoint] else {
            return nil
        }

        return calculateAngle(point1: shoulder.position, vertex: hip.position, point2: knee.position)
    }

    func calculateShoulderAngle(_ poseData: BodyPoseData, side: BodySide) -> Float? {
        let neckJoint: JointName = .neck
        let shoulderJoint: JointName = side == .left ? .leftShoulder : .rightShoulder
        let elbowJoint: JointName = side == .left ? .leftElbow : .rightElbow

        guard let neck = poseData.joints[neckJoint],
              let shoulder = poseData.joints[shoulderJoint],
              let elbow = poseData.joints[elbowJoint] else {
            return nil
        }

        return calculateAngle(point1: neck.position, vertex: shoulder.position, point2: elbow.position)
    }

    func calculateElbowAngle(_ poseData: BodyPoseData, side: BodySide) -> Float? {
        let shoulderJoint: JointName = side == .left ? .leftShoulder : .rightShoulder
        let elbowJoint: JointName = side == .left ? .leftElbow : .rightElbow
        let wristJoint: JointName = side == .left ? .leftWrist : .rightWrist

        guard let shoulder = poseData.joints[shoulderJoint],
              let elbow = poseData.joints[elbowJoint],
              let wrist = poseData.joints[wristJoint] else {
            return nil
        }

        return calculateAngle(point1: shoulder.position, vertex: elbow.position, point2: wrist.position)
    }

    func calculateAnkleAngle(_ poseData: BodyPoseData, side: BodySide) -> Float? {
        let kneeJoint: JointName = side == .left ? .leftKnee : .rightKnee
        let ankleJoint: JointName = side == .left ? .leftAnkle : .rightAnkle
        let toeJoint: JointName = side == .left ? .leftToe : .rightToe

        guard let knee = poseData.joints[kneeJoint],
              let ankle = poseData.joints[ankleJoint],
              let toe = poseData.joints[toeJoint] else {
            return nil
        }

        return calculateAngle(point1: knee.position, vertex: ankle.position, point2: toe.position)
    }

    func calculateSpineAngle(_ poseData: BodyPoseData) -> Float? {
        guard let head = poseData.joints[.head],
              let spine = poseData.joints[.spine6],
              let root = poseData.joints[.root] else {
            return nil
        }

        // Calculate deviation from vertical
        let spineVector = spine.position - root.position
        let verticalVector = simd_float3(0, 1, 0)

        let dotProduct = dot(normalize(spineVector), verticalVector)
        let angle = acos(clamp(dotProduct, -1.0, 1.0))

        return angle * 180.0 / Float.pi
    }

    func calculateTorsoLean(_ poseData: BodyPoseData) -> Float? {
        guard let shoulders = poseData.joints[.spine6],
              let hips = poseData.joints[.root] else {
            return nil
        }

        let torsoVector = shoulders.position - hips.position
        let verticalVector = simd_float3(0, 1, 0)

        let dotProduct = dot(normalize(torsoVector), verticalVector)
        let angle = acos(clamp(dotProduct, -1.0, 1.0))

        return angle * 180.0 / Float.pi
    }

    func calculateBalance(_ poseData: BodyPoseData) -> simd_float2? {
        guard let leftFoot = poseData.joints[.leftAnkle],
              let rightFoot = poseData.joints[.rightAnkle],
              let centerOfMass = poseData.joints[.root] else {
            return nil
        }

        // Calculate center of support (midpoint between feet)
        let centerOfSupport = (leftFoot.position + rightFoot.position) / 2.0

        // Calculate deviation from center of support
        let deviation = centerOfMass.position - centerOfSupport

        return simd_float2(deviation.x, deviation.z)
    }

    private func calculateAngle(point1: simd_float3, vertex: simd_float3, point2: simd_float3) -> Float {
        let vector1 = normalize(point1 - vertex)
        let vector2 = normalize(point2 - vertex)

        let dotProduct = dot(vector1, vector2)
        let angle = acos(clamp(dotProduct, -1.0, 1.0))

        return angle * 180.0 / Float.pi
    }

    private func calculateBodyLineDeviation(head: Joint3D, shoulders: Joint3D, hips: Joint3D, ankles: Joint3D) -> Float {
        // Calculate how much the body deviates from a straight line
        let points = [head.position, shoulders.position, hips.position, ankles.position]

        // Fit a line through the points and calculate average deviation
        var totalDeviation: Float = 0.0

        for i in 1..<points.count {
            let segment = points[i] - points[i-1]
            let segmentLength = length(segment)
            totalDeviation += segmentLength
        }

        return totalDeviation / Float(points.count - 1)
    }
}

// MARK: - Exercise Thresholds

struct SquatThresholds {
    let maxKneeAsymmetry: Float = 15.0 // degrees
    let minKneeAngle: Float = 70.0 // degrees (to prevent knee valgus)
    let minHipAngleForDepth: Float = 90.0 // degrees
    let minAnkleFlexion: Float = 15.0 // degrees
    let maxSpineDeviation: Float = 20.0 // degrees from neutral
    let maxTorsoLean: Float = 45.0 // degrees from vertical
}

struct PushUpThresholds {
    let maxBodyLineDeviation: Float = 0.1 // meters
    let minElbowAngleForDepth: Float = 90.0 // degrees
    let maxElbowFlare: Float = 45.0 // degrees from body
}

struct LungeThresholds {
    let minFrontKneeAngle: Float = 90.0 // degrees
    let maxKneeForwardTravel: Float = 0.05 // meters past toes
    let minBackKneeAngle: Float = 90.0 // degrees
    let maxTorsoLean: Float = 15.0 // degrees from vertical
}
