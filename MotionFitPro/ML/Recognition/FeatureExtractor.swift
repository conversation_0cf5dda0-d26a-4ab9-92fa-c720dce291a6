//
//  FeatureExtractor.swift
//  MotionFitPro
//
//  Created by Cascade on 2025-07-14.
//

import Foundation
import Accelerate

/// Extracts a feature vector from a sequence of body poses for use in an ML model.
class FeatureExtractor {
    
    private let poseHistory: [BodyPoseData]
    
    /// Initializes the feature extractor with a history of poses.
    /// - Parameter poseHistory: An array of `BodyPoseData` to be analyzed.
    init(poseHistory: [BodyPoseData]) {
        self.poseHistory = poseHistory
    }
    
    /// Generates a comprehensive feature vector from the pose history.
    /// - Returns: A feature vector as an array of `Double`s, or `nil` if extraction is not possible.
    func extractFeatures() -> [Double]? {
        guard !poseHistory.isEmpty else { return nil }
        
        var features: [Double] = []
        
        // 1. Joint Angle Statistics
        features += extractJointAngleFeatures()
        
        // 2. Velocity Profile Characteristics
        features += extractVelocityFeatures()
        
        // 3. Trajectory and Symmetry Features
        features += extractTrajectoryFeatures()
        
        // The final feature vector should be padded or truncated to a fixed size
        // that the ML model expects. For now, we return the raw features.
        return features
    }
    
    // MARK: - Feature Calculation Methods
    
    private func extractJointAngleFeatures() -> [Double] {
        var angleFeatures: [Double] = []
        let anglesToAnalyze: [JointAngle] = [.leftKnee, .rightKnee, .leftHip, .rightHip, .leftElbow, .rightElbow]
        
        for angleType in anglesToAnalyze {
            let angleSequence = poseHistory.compactMap { JointAngleCalculator.calculate(angleType, for: $0) }.map { Double($0) }
            
            if !angleSequence.isEmpty {
                angleFeatures.append(vDSP.mean(angleSequence))
                angleFeatures.append(vDSP.standardDeviation(angleSequence))
                angleFeatures.append(vDSP.maximum(angleSequence) - vDSP.minimum(angleSequence)) // Range
            } else {
                angleFeatures.append(contentsOf: [0, 0, 0]) // Append zeros if no data
            }
        }
        return angleFeatures
    }
    
    private func extractVelocityFeatures() -> [Double] {
        var velocityFeatures: [Double] = []
        guard poseHistory.count >= 2 else { return Array(repeating: 0, count: 6) } // Expected feature count
        
        let keyJoints: [ARKit.ARSkeleton.JointName] = [.leftWrist, .rightWrist, .leftAnkle, .rightAnkle]
        
        for jointName in keyJoints {
            var speeds: [Double] = []
            for i in 1..<poseHistory.count {
                let p1 = poseHistory[i-1]
                let p2 = poseHistory[i]
                
                if let pos1 = p1.joints[jointName.rawValue]?.position, let pos2 = p2.joints[jointName.rawValue]?.position {
                    let distance = simd_distance(pos1, pos2)
                    let time = p2.timestamp - p1.timestamp
                    if time > 0 {
                        speeds.append(Double(distance) / time)
                    }
                }
            }
            
            if !speeds.isEmpty {
                velocityFeatures.append(vDSP.mean(speeds))
                velocityFeatures.append(vDSP.standardDeviation(speeds))
            } else {
                velocityFeatures.append(contentsOf: [0, 0])
            }
        }
        return velocityFeatures
    }
    
    private func extractTrajectoryFeatures() -> [Double] {
        // This is a placeholder for more complex trajectory analysis.
        // For now, we'll calculate the total distance traveled by the root.
        guard poseHistory.count >= 2 else { return [0.0] }
        
        var totalDistance: Double = 0.0
        for i in 1..<poseHistory.count {
            if let root1 = poseHistory[i-1].joints[ARKit.ARSkeleton.JointName.root.rawValue]?.position,
               let root2 = poseHistory[i].joints[ARKit.ARSkeleton.JointName.root.rawValue]?.position {
                totalDistance += Double(simd_distance(root1, root2))
            }
        }
        return [totalDistance]
    }
}
