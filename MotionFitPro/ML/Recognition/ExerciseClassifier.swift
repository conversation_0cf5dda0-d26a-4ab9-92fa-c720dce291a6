//
//  ExerciseClassifier.swift
//  MotionFitPro
//
//  Created by Cascade on 2025-07-14.
//

import Foundation
import CoreML
import Combine
import os.log

/// Classifies exercises in real-time using a Core ML model.
class ExerciseClassifier {
    
    // MARK: - Published Properties
    
    /// A publisher that emits the most likely exercise classification.
    let classificationPublisher = PassthroughSubject<ClassificationResult, Never>()
    
    // MARK: - Private Properties
    
    private let model: MockExerciseClassifierModel
    private var poseHistory: CircularBuffer<BodyPoseData>
    private let logger = Logger(subsystem: "com.yourcompany.MotionFitPro", category: "ExerciseClassifier")

    /// The number of poses required before attempting a classification.
    private let classificationWindowSize = 120 // e.g., 2 seconds at 60 FPS
    /// The confidence threshold required to accept a classification.
    private let confidenceThreshold: Double = 0.80
    
    init() {
        self.model = MockExerciseClassifierModel()
        self.poseHistory = CircularBuffer<BodyPoseData>(capacity: classificationWindowSize)
    }
    
    /// Processes a new body pose and triggers a classification if the window is full.
    /// - Parameter pose: The latest `BodyPoseData` from the AR session.
    func process(pose: BodyPoseData) {
        poseHistory.append(pose)
        
        // Trigger classification only when the buffer is full to ensure enough data.
        if poseHistory.count == classificationWindowSize {
            classify()
        }
    }
    
    private func classify() {
        let history = poseHistory.allElements
        guard !history.isEmpty else { return }
        
        // 1. Extract features
        let featureExtractor = FeatureExtractor(poseHistory: history)
        guard let featureVector = featureExtractor.extractFeatures() else {
            logger.warning("Failed to extract features from pose history.")
            return
        }
        
        // 2. Prepare model input
        guard let multiArray = try? MLMultiArray(shape: [1, NSNumber(value: featureVector.count)], dataType: .double) else {
            logger.error("Failed to create MLMultiArray.")
            return
        }
        
        for (index, element) in featureVector.enumerated() {
            multiArray[index] = NSNumber(value: element)
        }
        
        let input = MLFeatureValue(multiArray: multiArray)
        let dataPoint = try! MLDictionaryFeatureProvider(dictionary: ["features": input])
        
        // 3. Make prediction
        do {
            let prediction = try model.prediction(from: dataPoint)
            
            guard
                let label = prediction.featureValue(for: "label")?.stringValue,
                let probabilities = prediction.featureValue(for: "labelProbability")?.dictionaryValue,
                let confidence = probabilities[label] as? Double
            else {
                logger.error("Failed to parse prediction output.")
                return
            }
            
            logger.log("Raw classification: \(label) with confidence \(confidence, format: .percent(2))")
            
            // 4. Apply confidence threshold
            let exercise = ExerciseType(rawValue: label) ?? .unknown
            let result = ClassificationResult(
                exercise: confidence >= confidenceThreshold ? exercise : .unknown,
                confidence: confidence
            )
            
            classificationPublisher.send(result)
            
        } catch {
            logger.error("Prediction failed with error: \(error.localizedDescription)")
        }
    }
}

// MARK: - Supporting Types

extension ExerciseClassifier {
    /// A structure to hold the result of an exercise classification.
    struct ClassificationResult {
        let exercise: ExerciseType
        let confidence: Double
    }
}
