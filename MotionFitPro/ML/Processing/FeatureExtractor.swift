import Foundation
import simd
import Accelerate

// MARK: - Feature Extraction Pipeline

/// Extracts and processes features from pose data for ML inference
final class FeatureExtractor {
    
    // MARK: - Properties
    
    private let logger = Logger()
    
    // Feature configuration
    private let windowSize = 30 // frames
    private let featureCount = 75 // total features per frame
    private let samplingRate: Float = 30.0 // fps
    
    // Feature buffers
    private var poseHistory: [BodyPoseData] = []
    private var featureHistory: [[Float]] = []
    
    // Normalization parameters (would be learned from training data)
    private let normalizationStats = NormalizationStats()
    
    // MARK: - Feature Extraction
    
    func extractFeatures(from poseData: BodyPoseData) -> FeatureVector {
        // Add to history
        addToHistory(poseData)
        
        // Extract current frame features
        let currentFeatures = extractFrameFeatures(poseData)
        
        // Extract temporal features if we have enough history
        let temporalFeatures = extractTemporalFeatures()
        
        // Extract biomechanical features
        let biomechanicalFeatures = extractBiomechanicalFeatures(poseData)
        
        // Combine all features
        var allFeatures = currentFeatures
        allFeatures.append(contentsOf: temporalFeatures)
        allFeatures.append(contentsOf: biomechanicalFeatures)
        
        // Normalize features
        let normalizedFeatures = normalizeFeatures(allFeatures)
        
        return FeatureVector(
            features: normalizedFeatures,
            timestamp: poseData.timestamp,
            confidence: poseData.confidence,
            frameCount: poseHistory.count
        )
    }
    
    // MARK: - Frame Features
    
    private func extractFrameFeatures(_ poseData: BodyPoseData) -> [Float] {
        var features: [Float] = []
        
        // Joint positions (51 features: 17 joints × 3 coordinates)
        let jointFeatures = extractJointPositions(poseData)
        features.append(contentsOf: jointFeatures)
        
        // Joint confidences (17 features)
        let confidenceFeatures = extractJointConfidences(poseData)
        features.append(contentsOf: confidenceFeatures)
        
        // Relative positions (12 features: key joint relationships)
        let relativeFeatures = extractRelativePositions(poseData)
        features.append(contentsOf: relativeFeatures)
        
        return features
    }
    
    private func extractJointPositions(_ poseData: BodyPoseData) -> [Float] {
        var positions: [Float] = []
        
        let orderedJoints: [JointName] = [
            .head, .neck,
            .leftShoulder, .rightShoulder,
            .leftElbow, .rightElbow,
            .leftWrist, .rightWrist,
            .spine6, .root,
            .leftHip, .rightHip,
            .leftKnee, .rightKnee,
            .leftAnkle, .rightAnkle,
            .leftToe
        ]
        
        for jointName in orderedJoints {
            if let joint = poseData.joints[jointName] {
                positions.append(joint.position.x)
                positions.append(joint.position.y)
                positions.append(joint.position.z)
            } else {
                // Missing joint - use interpolated or default values
                positions.append(0.0)
                positions.append(0.0)
                positions.append(0.0)
            }
        }
        
        return positions
    }
    
    private func extractJointConfidences(_ poseData: BodyPoseData) -> [Float] {
        var confidences: [Float] = []
        
        let orderedJoints: [JointName] = [
            .head, .neck,
            .leftShoulder, .rightShoulder,
            .leftElbow, .rightElbow,
            .leftWrist, .rightWrist,
            .spine6, .root,
            .leftHip, .rightHip,
            .leftKnee, .rightKnee,
            .leftAnkle, .rightAnkle,
            .leftToe
        ]
        
        for jointName in orderedJoints {
            if let joint = poseData.joints[jointName] {
                confidences.append(joint.confidence)
            } else {
                confidences.append(0.0)
            }
        }
        
        return confidences
    }
    
    private func extractRelativePositions(_ poseData: BodyPoseData) -> [Float] {
        var relatives: [Float] = []
        
        // Key relative positions for exercise classification
        if let root = poseData.joints[.root] {
            // Head relative to root
            if let head = poseData.joints[.head] {
                let relative = head.position - root.position
                relatives.append(contentsOf: [relative.x, relative.y, relative.z])
            } else {
                relatives.append(contentsOf: [0.0, 0.0, 0.0])
            }
            
            // Hands relative to root
            if let leftWrist = poseData.joints[.leftWrist] {
                let relative = leftWrist.position - root.position
                relatives.append(contentsOf: [relative.x, relative.y, relative.z])
            } else {
                relatives.append(contentsOf: [0.0, 0.0, 0.0])
            }
            
            if let rightWrist = poseData.joints[.rightWrist] {
                let relative = rightWrist.position - root.position
                relatives.append(contentsOf: [relative.x, relative.y, relative.z])
            } else {
                relatives.append(contentsOf: [0.0, 0.0, 0.0])
            }
            
            // Feet relative to root
            if let leftAnkle = poseData.joints[.leftAnkle] {
                let relative = leftAnkle.position - root.position
                relatives.append(contentsOf: [relative.x, relative.y, relative.z])
            } else {
                relatives.append(contentsOf: [0.0, 0.0, 0.0])
            }
        } else {
            // No root joint - fill with zeros
            relatives.append(contentsOf: Array(repeating: 0.0, count: 12))
        }
        
        return relatives
    }
    
    // MARK: - Temporal Features
    
    private func extractTemporalFeatures() -> [Float] {
        guard poseHistory.count >= 2 else {
            return Array(repeating: 0.0, count: 24) // 8 joints × 3 velocity components
        }
        
        var temporalFeatures: [Float] = []
        
        // Calculate velocities for key joints
        let current = poseHistory.last!
        let previous = poseHistory[poseHistory.count - 2]
        let timeDelta = Float(current.timestamp.timeIntervalSince(previous.timestamp))
        
        guard timeDelta > 0 else {
            return Array(repeating: 0.0, count: 24)
        }
        
        let keyJoints: [JointName] = [
            .head, .leftWrist, .rightWrist,
            .leftElbow, .rightElbow,
            .leftKnee, .rightKnee,
            .root
        ]
        
        for jointName in keyJoints {
            if let currentJoint = current.joints[jointName],
               let previousJoint = previous.joints[jointName] {
                let velocity = (currentJoint.position - previousJoint.position) / timeDelta
                temporalFeatures.append(contentsOf: [velocity.x, velocity.y, velocity.z])
            } else {
                temporalFeatures.append(contentsOf: [0.0, 0.0, 0.0])
            }
        }
        
        return temporalFeatures
    }
    
    // MARK: - Biomechanical Features
    
    private func extractBiomechanicalFeatures(_ poseData: BodyPoseData) -> [Float] {
        var bioFeatures: [Float] = []
        
        // Joint angles (10 features)
        let angles = calculateKeyAngles(poseData)
        bioFeatures.append(contentsOf: angles)
        
        // Body proportions (6 features)
        let proportions = calculateBodyProportions(poseData)
        bioFeatures.append(contentsOf: proportions)
        
        // Balance metrics (3 features)
        let balance = calculateBalanceMetrics(poseData)
        bioFeatures.append(contentsOf: balance)
        
        return bioFeatures
    }
    
    private func calculateKeyAngles(_ poseData: BodyPoseData) -> [Float] {
        var angles: [Float] = []
        
        let biomechanicsAnalyzer = BiomechanicsAnalyzer()
        
        // Knee angles
        angles.append(biomechanicsAnalyzer.calculateKneeAngle(poseData, side: .left) ?? 0.0)
        angles.append(biomechanicsAnalyzer.calculateKneeAngle(poseData, side: .right) ?? 0.0)
        
        // Hip angles
        angles.append(biomechanicsAnalyzer.calculateHipAngle(poseData, side: .left) ?? 0.0)
        angles.append(biomechanicsAnalyzer.calculateHipAngle(poseData, side: .right) ?? 0.0)
        
        // Elbow angles
        angles.append(biomechanicsAnalyzer.calculateElbowAngle(poseData, side: .left) ?? 0.0)
        angles.append(biomechanicsAnalyzer.calculateElbowAngle(poseData, side: .right) ?? 0.0)
        
        // Shoulder angles
        angles.append(biomechanicsAnalyzer.calculateShoulderAngle(poseData, side: .left) ?? 0.0)
        angles.append(biomechanicsAnalyzer.calculateShoulderAngle(poseData, side: .right) ?? 0.0)
        
        // Spine angle
        angles.append(biomechanicsAnalyzer.calculateSpineAngle(poseData) ?? 0.0)
        
        // Torso lean
        angles.append(biomechanicsAnalyzer.calculateTorsoLean(poseData) ?? 0.0)
        
        return angles
    }
    
    private func calculateBodyProportions(_ poseData: BodyPoseData) -> [Float] {
        var proportions: [Float] = []
        
        // Calculate key body segment lengths
        if let head = poseData.joints[.head],
           let neck = poseData.joints[.neck],
           let spine = poseData.joints[.spine6],
           let root = poseData.joints[.root],
           let leftShoulder = poseData.joints[.leftShoulder],
           let rightShoulder = poseData.joints[.rightShoulder] {
            
            // Torso length
            let torsoLength = distance(spine.position, root.position)
            proportions.append(torsoLength)
            
            // Shoulder width
            let shoulderWidth = distance(leftShoulder.position, rightShoulder.position)
            proportions.append(shoulderWidth)
            
            // Head-neck length
            let headNeckLength = distance(head.position, neck.position)
            proportions.append(headNeckLength)
            
            // Spine-shoulder length
            let spineShoulderLength = distance(spine.position, leftShoulder.position)
            proportions.append(spineShoulderLength)
            
            // Aspect ratios
            proportions.append(shoulderWidth / torsoLength) // Width-to-height ratio
            proportions.append(headNeckLength / torsoLength) // Head proportion
        } else {
            proportions.append(contentsOf: Array(repeating: 0.0, count: 6))
        }
        
        return proportions
    }
    
    private func calculateBalanceMetrics(_ poseData: BodyPoseData) -> [Float] {
        var balance: [Float] = []
        
        let biomechanicsAnalyzer = BiomechanicsAnalyzer()
        
        if let balanceVector = biomechanicsAnalyzer.calculateBalance(poseData) {
            balance.append(balanceVector.x)
            balance.append(balanceVector.y)
            balance.append(sqrt(balanceVector.x * balanceVector.x + balanceVector.y * balanceVector.y)) // magnitude
        } else {
            balance.append(contentsOf: [0.0, 0.0, 0.0])
        }
        
        return balance
    }
    
    // MARK: - Normalization
    
    private func normalizeFeatures(_ features: [Float]) -> [Float] {
        return features.enumerated().map { index, value in
            let mean = normalizationStats.means[min(index, normalizationStats.means.count - 1)]
            let std = normalizationStats.stds[min(index, normalizationStats.stds.count - 1)]
            return std > 0 ? (value - mean) / std : value
        }
    }
    
    // MARK: - History Management
    
    private func addToHistory(_ poseData: BodyPoseData) {
        poseHistory.append(poseData)
        
        // Maintain window size
        if poseHistory.count > windowSize {
            poseHistory.removeFirst()
        }
    }
    
    // MARK: - Utility Methods
    
    func reset() {
        poseHistory.removeAll()
        featureHistory.removeAll()
    }
    
    func getHistoryCount() -> Int {
        return poseHistory.count
    }
    
    func isReady() -> Bool {
        return poseHistory.count >= 2 // Need at least 2 frames for temporal features
    }
}

// MARK: - Supporting Types

struct FeatureVector {
    let features: [Float]
    let timestamp: Date
    let confidence: Float
    let frameCount: Int
    
    var isValid: Bool {
        return !features.isEmpty && confidence > 0.3
    }
}

struct NormalizationStats {
    // These would be computed from training data
    // For now, using reasonable defaults
    let means: [Float] = Array(repeating: 0.0, count: 100)
    let stds: [Float] = Array(repeating: 1.0, count: 100)
}
