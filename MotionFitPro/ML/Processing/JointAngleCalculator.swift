import Foundation
import simd

/// Utility class for calculating angles between joints using simd mathematics
final class JointAngleCalculator: Sendable {
    
    /// Calculate the angle between three joints where the center joint is the vertex
    /// - Parameters:
    ///   - startJoint: First joint position
    ///   - centerJoint: Vertex joint position (where angle is measured)
    ///   - endJoint: Third joint position
    /// - Returns: Angle in degrees, or nil if calculation is invalid
    static func calculateAngle(startJoint: simd_float3, centerJoint: simd_float3, endJoint: simd_float3) -> Float? {
        // Create vectors from center to start and center to end
        let vector1 = startJoint - centerJoint
        let vector2 = endJoint - centerJoint
        
        // Calculate magnitudes
        let magnitude1 = simd_length(vector1)
        let magnitude2 = simd_length(vector2)
        
        // Check for zero-length vectors
        guard magnitude1 > 0 && magnitude2 > 0 else {
            return nil
        }
        
        // Normalize vectors
        let normalizedVector1 = vector1 / magnitude1
        let normalizedVector2 = vector2 / magnitude2
        
        // Calculate dot product
        let dotProduct = simd_dot(normalizedVector1, normalizedVector2)
        
        // Clamp the dot product to avoid numerical issues with acos
        let clampedDotProduct = max(-1.0, min(1.0, dotProduct))
        
        // Calculate angle in radians and convert to degrees
        let angleRadians = acos(clampedDotProduct)
        let angleDegrees = angleRadians * 180.0 / Float.pi
        
        return angleDegrees
    }
    
    /// Calculate angle between joints using BodyPoseData joint names
    /// - Parameters:
    ///   - poseData: The body pose data containing joint positions
    ///   - startJoint: Name of the first joint
    ///   - centerJoint: Name of the vertex joint
    ///   - endJoint: Name of the third joint
    /// - Returns: Angle in degrees, or nil if joints are not found or calculation is invalid
    static func calculateAngle(from poseData: BodyPoseData, startJoint: String, centerJoint: String, endJoint: String) -> Float? {
        guard let start = poseData.joints[startJoint],
              let center = poseData.joints[centerJoint],
              let end = poseData.joints[endJoint] else {
            return nil
        }
        
        return calculateAngle(startJoint: start.position, centerJoint: center.position, endJoint: end.position)
    }
    
    /// Calculate multiple common body angles for exercise analysis
    /// - Parameter poseData: The body pose data
    /// - Returns: Dictionary of angle names to degrees
    static func calculateCommonAngles(from poseData: BodyPoseData) -> [String: Float] {
        var angles: [String: Float] = [:]
        
        // Knee angles
        if let leftKneeAngle = calculateAngle(from: poseData, startJoint: "left_upLeg", centerJoint: "left_leg", endJoint: "left_foot") {
            angles["leftKnee"] = leftKneeAngle
        }
        
        if let rightKneeAngle = calculateAngle(from: poseData, startJoint: "right_upLeg", centerJoint: "right_leg", endJoint: "right_foot") {
            angles["rightKnee"] = rightKneeAngle
        }
        
        // Elbow angles
        if let leftElbowAngle = calculateAngle(from: poseData, startJoint: "left_shoulder", centerJoint: "left_arm", endJoint: "left_hand") {
            angles["leftElbow"] = leftElbowAngle
        }
        
        if let rightElbowAngle = calculateAngle(from: poseData, startJoint: "right_shoulder", centerJoint: "right_arm", endJoint: "right_hand") {
            angles["rightElbow"] = rightElbowAngle
        }
        
        // Hip angles (thigh to torso)
        if let leftHipAngle = calculateAngle(from: poseData, startJoint: "spine_7", centerJoint: "root", endJoint: "left_upLeg") {
            angles["leftHip"] = leftHipAngle
        }
        
        if let rightHipAngle = calculateAngle(from: poseData, startJoint: "spine_7", centerJoint: "root", endJoint: "right_upLeg") {
            angles["rightHip"] = rightHipAngle
        }
        
        // Shoulder angles
        if let leftShoulderAngle = calculateAngle(from: poseData, startJoint: "spine_7", centerJoint: "left_shoulder", endJoint: "left_arm") {
            angles["leftShoulder"] = leftShoulderAngle
        }
        
        if let rightShoulderAngle = calculateAngle(from: poseData, startJoint: "spine_7", centerJoint: "right_shoulder", endJoint: "right_arm") {
            angles["rightShoulder"] = rightShoulderAngle
        }
        
        return angles
    }
    
    /// Calculate the angle between a joint and the vertical axis (useful for posture analysis)
    /// - Parameters:
    ///   - joint1: First joint position
    ///   - joint2: Second joint position (forms line to analyze)
    /// - Returns: Angle from vertical in degrees
    static func angleFromVertical(joint1: simd_float3, joint2: simd_float3) -> Float {
        let bodyVector = joint2 - joint1
        let verticalVector = simd_float3(0, 1, 0) // Y-axis up
        
        let magnitude = simd_length(bodyVector)
        guard magnitude > 0 else { return 0 }
        
        let normalizedBodyVector = bodyVector / magnitude
        let dotProduct = simd_dot(normalizedBodyVector, verticalVector)
        let clampedDotProduct = max(-1.0, min(1.0, dotProduct))
        
        let angleRadians = acos(clampedDotProduct)
        return angleRadians * 180.0 / Float.pi
    }
    
    /// Check if an angle is within a target range (useful for form validation)
    /// - Parameters:
    ///   - angle: The measured angle
    ///   - target: Target angle
    ///   - tolerance: Acceptable deviation from target
    /// - Returns: True if angle is within acceptable range
    static func isAngleInRange(_ angle: Float, target: Float, tolerance: Float) -> Bool {
        return abs(angle - target) <= tolerance
    }
}

// MARK: - Exercise-Specific Angle Analysis

extension JointAngleCalculator {
    
    /// Analyze squat form based on key joint angles
    /// - Parameter poseData: Body pose data
    /// - Returns: Dictionary of analysis results
    static func analyzeSquatForm(from poseData: BodyPoseData) -> [String: Any] {
        var analysis: [String: Any] = [:]
        
        let angles = calculateCommonAngles(from: poseData)
        
        // Knee angle analysis (should be between 70-120 degrees for good squat depth)
        if let leftKnee = angles["leftKnee"], let rightKnee = angles["rightKnee"] {
            let avgKneeAngle = (leftKnee + rightKnee) / 2
            analysis["averageKneeAngle"] = avgKneeAngle
            analysis["goodSquatDepth"] = avgKneeAngle >= 70 && avgKneeAngle <= 120
        }
        
        // Hip angle analysis
        if let leftHip = angles["leftHip"], let rightHip = angles["rightHip"] {
            let avgHipAngle = (leftHip + rightHip) / 2
            analysis["averageHipAngle"] = avgHipAngle
        }
        
        // Torso verticality (angle from vertical should be minimal)
        if let spine = poseData.joints["spine_7"], let root = poseData.joints["root"] {
            let torsoAngle = angleFromVertical(joint1: root.position, joint2: spine.position)
            analysis["torsoAngleFromVertical"] = torsoAngle
            analysis["goodPosture"] = torsoAngle <= 30 // Within 30 degrees of vertical
        }
        
        return analysis
    }
    
    /// Analyze push-up form
    /// - Parameter poseData: Body pose data
    /// - Returns: Dictionary of analysis results
    static func analyzePushUpForm(from poseData: BodyPoseData) -> [String: Any] {
        var analysis: [String: Any] = [:]
        
        let angles = calculateCommonAngles(from: poseData)
        
        // Elbow angle analysis
        if let leftElbow = angles["leftElbow"], let rightElbow = angles["rightElbow"] {
            let avgElbowAngle = (leftElbow + rightElbow) / 2
            analysis["averageElbowAngle"] = avgElbowAngle
            analysis["goodPushUpDepth"] = avgElbowAngle >= 45 && avgElbowAngle <= 90
        }
        
        // Body alignment (head-spine-hip should be relatively straight)
        if let head = poseData.joints["head"],
           let spine = poseData.joints["spine_7"],
           let root = poseData.joints["root"] {
            
            let headSpineAngle = angleFromVertical(joint1: spine.position, joint2: head.position)
            let spineHipAngle = angleFromVertical(joint1: root.position, joint2: spine.position)
            
            analysis["headAlignment"] = headSpineAngle
            analysis["spineAlignment"] = spineHipAngle
            analysis["goodAlignment"] = headSpineAngle <= 20 && spineHipAngle <= 20
        }
        
        return analysis
    }
}