import Foundation
import simd

/// Analyzes pose data over time using circular buffers for efficient memory management
@MainActor
final class TemporalAnalyzer: ObservableObject, Sendable {
    
    // MARK: - Configuration
    
    /// Maximum number of poses to keep in history
    private let maxHistorySize: Int
    
    /// Time window for movement analysis (in seconds)
    private let analysisTimeWindow: TimeInterval
    
    // MARK: - Circular Buffer for Pose History
    
    private var poseHistory: [BodyPoseData] = []
    private var currentIndex: Int = 0
    private var bufferFull: Bool = false
    
    // MARK: - Movement Analysis
    
    /// Current movement velocity for key joints
    @Published var jointVelocities: [String: simd_float3] = [:]
    
    /// Movement smoothness score (0-1, higher is smoother)
    @Published var movementSmoothness: Float = 0.0
    
    /// Detected movement patterns
    @Published var detectedPatterns: [MovementPattern] = []
    
    // MARK: - Initialization
    
    nonisolated init(maxHistorySize: Int = 300, analysisTimeWindow: TimeInterval = 10.0) {
        self.maxHistorySize = maxHistorySize
        self.analysisTimeWindow = analysisTimeWindow
    }
    
    // MARK: - Pose History Management
    
    /// Add a new pose to the temporal analysis
    /// - Parameter poseData: New pose data to analyze
    func addPose(_ poseData: BodyPoseData) {
        // Add to circular buffer
        if poseHistory.count < maxHistorySize {
            poseHistory.append(poseData)
        } else {
            poseHistory[currentIndex] = poseData
            currentIndex = (currentIndex + 1) % maxHistorySize
            bufferFull = true
        }
        
        // Update movement analysis
        updateMovementAnalysis()
    }
    
    /// Get poses within the specified time window
    /// - Parameter timeWindow: Time window in seconds (defaults to analysisTimeWindow)
    /// - Returns: Array of poses within the time window
    func getRecentPoses(within timeWindow: TimeInterval? = nil) -> [BodyPoseData] {
        let window = timeWindow ?? analysisTimeWindow
        let cutoffTime = Date().timeIntervalSince1970 - window
        
        return getOrderedPoseHistory().filter { $0.timestamp >= cutoffTime }
    }
    
    /// Get all poses in chronological order
    /// - Returns: Ordered array of all poses in history
    func getOrderedPoseHistory() -> [BodyPoseData] {
        guard bufferFull else {
            return poseHistory
        }
        
        let recentPoses = Array(poseHistory[currentIndex...])
        let olderPoses = Array(poseHistory[0..<currentIndex])
        return recentPoses + olderPoses
    }
    
    // MARK: - Movement Analysis
    
    private func updateMovementAnalysis() {
        calculateJointVelocities()
        calculateMovementSmoothness()
        detectMovementPatterns()
    }
    
    /// Calculate velocities for key joints
    private func calculateJointVelocities() {
        let recentPoses = getRecentPoses(within: 1.0) // Last 1 second
        guard recentPoses.count >= 2 else { return }
        
        let latestPose = recentPoses.last!
        let previousPose = recentPoses[recentPoses.count - 2]
        
        let timeDelta = Float(latestPose.timestamp - previousPose.timestamp)
        guard timeDelta > 0 else { return }
        
        var velocities: [String: simd_float3] = [:]
        
        for (jointName, latestJoint) in latestPose.joints {
            if let previousJoint = previousPose.joints[jointName] {
                let positionDelta = latestJoint.position - previousJoint.position
                let velocity = positionDelta / timeDelta
                velocities[jointName] = velocity
            }
        }
        
        jointVelocities = velocities
    }
    
    /// Calculate movement smoothness based on acceleration consistency
    private func calculateMovementSmoothness() {
        let recentPoses = getRecentPoses(within: 2.0)
        guard recentPoses.count >= 3 else { return }
        
        var smoothnessScores: [Float] = []
        
        // Analyze key joints for smoothness
        let keyJoints = ["head", "spine_7", "root", "left_hand", "right_hand"]
        
        for jointName in keyJoints {
            let jointPositions = recentPoses.compactMap { $0.joints[jointName]?.position }
            guard jointPositions.count >= 3 else { continue }
            
            let smoothness = calculateJointSmoothness(positions: jointPositions)
            smoothnessScores.append(smoothness)
        }
        
        movementSmoothness = smoothnessScores.isEmpty ? 0.0 : smoothnessScores.reduce(0, +) / Float(smoothnessScores.count)
    }
    
    /// Calculate smoothness for a single joint based on acceleration changes
    private func calculateJointSmoothness(positions: [simd_float3]) -> Float {
        guard positions.count >= 3 else { return 0.0 }
        
        var accelerationChanges: [Float] = []
        
        for i in 2..<positions.count {
            let velocity1 = positions[i-1] - positions[i-2]
            let velocity2 = positions[i] - positions[i-1]
            let acceleration = velocity2 - velocity1
            let accelerationMagnitude = simd_length(acceleration)
            accelerationChanges.append(accelerationMagnitude)
        }
        
        guard !accelerationChanges.isEmpty else { return 0.0 }
        
        // Lower acceleration variance indicates smoother movement
        let meanAcceleration = accelerationChanges.reduce(0, +) / Float(accelerationChanges.count)
        let variance = accelerationChanges.map { pow($0 - meanAcceleration, 2) }.reduce(0, +) / Float(accelerationChanges.count)
        
        // Convert variance to smoothness score (0-1, higher is smoother)
        let smoothness = max(0, 1.0 - sqrt(variance))
        return smoothness
    }
    
    // MARK: - Pattern Detection
    
    /// Detect movement patterns in recent pose history
    private func detectMovementPatterns() {
        let recentPoses = getRecentPoses(within: 5.0)
        guard recentPoses.count >= 10 else { return }
        
        var patterns: [MovementPattern] = []
        
        // Detect repetitive movements
        if let repetitivePattern = detectRepetitiveMovement(in: recentPoses) {
            patterns.append(repetitivePattern)
        }
        
        // Detect directional trends
        if let directionalPattern = detectDirectionalTrend(in: recentPoses) {
            patterns.append(directionalPattern)
        }
        
        // Detect stability periods
        if let stabilityPattern = detectStabilityPeriod(in: recentPoses) {
            patterns.append(stabilityPattern)
        }
        
        detectedPatterns = patterns
    }
    
    /// Detect repetitive movement patterns
    private func detectRepetitiveMovement(in poses: [BodyPoseData]) -> MovementPattern? {
        // Simplified pattern detection - look for cyclic movements in key joints
        let keyJoint = "root" // Focus on center of mass
        let positions = poses.compactMap { $0.joints[keyJoint]?.position }
        guard positions.count >= 10 else { return nil }
        
        // Check for vertical oscillation (common in squats, jumping jacks)
        let yPositions = positions.map { $0.y }
        let maxY = yPositions.max() ?? 0
        let minY = yPositions.min() ?? 0
        let range = maxY - minY
        
        if range > 0.1 { // Significant vertical movement
            let avgY = yPositions.reduce(0, +) / Float(yPositions.count)
            let oscillationCount = countOscillations(values: yPositions, threshold: avgY)
            
            if oscillationCount >= 2 {
                return MovementPattern(
                    type: .repetitive,
                    confidence: min(1.0, Float(oscillationCount) / 5.0),
                    description: "Repetitive vertical movement detected",
                    startTime: poses.first?.timestamp ?? 0,
                    endTime: poses.last?.timestamp ?? 0
                )
            }
        }
        
        return nil
    }
    
    /// Count oscillations around a threshold value
    private func countOscillations(values: [Float], threshold: Float) -> Int {
        var oscillations = 0
        var lastAbove = values.first ?? 0 > threshold
        
        for value in values.dropFirst() {
            let currentAbove = value > threshold
            if currentAbove != lastAbove {
                oscillations += 1
                lastAbove = currentAbove
            }
        }
        
        return oscillations / 2 // Count complete oscillations
    }
    
    /// Detect directional movement trends
    private func detectDirectionalTrend(in poses: [BodyPoseData]) -> MovementPattern? {
        guard let firstPose = poses.first, let lastPose = poses.last else { return nil }
        
        // Check for overall displacement
        if let firstRoot = firstPose.joints["root"], let lastRoot = lastPose.joints["root"] {
            let displacement = lastRoot.position - firstRoot.position
            let distance = simd_length(displacement)
            
            if distance > 0.5 { // Significant displacement
                let direction = displacement / distance
                let confidence = min(1.0, distance / 2.0) // Scale confidence based on distance
                
                return MovementPattern(
                    type: .directional,
                    confidence: confidence,
                    description: "Directional movement: \(formatDirection(direction))",
                    startTime: firstPose.timestamp,
                    endTime: lastPose.timestamp
                )
            }
        }
        
        return nil
    }
    
    /// Detect periods of stability (minimal movement)
    private func detectStabilityPeriod(in poses: [BodyPoseData]) -> MovementPattern? {
        let keyJoint = "root"
        let positions = poses.compactMap { $0.joints[keyJoint]?.position }
        guard positions.count >= 5 else { return nil }
        
        // Calculate movement variance
        let avgPosition = positions.reduce(simd_float3(0, 0, 0)) { $0 + $1 } / Float(positions.count)
        let distances = positions.map { simd_length($0 - avgPosition) }
        let maxDistance = distances.max() ?? 0
        
        if maxDistance < 0.1 { // Very stable
            let confidence = max(0, 1.0 - maxDistance * 10)
            
            return MovementPattern(
                type: .stable,
                confidence: confidence,
                description: "Stable position maintained",
                startTime: poses.first?.timestamp ?? 0,
                endTime: poses.last?.timestamp ?? 0
            )
        }
        
        return nil
    }
    
    /// Format direction vector as readable string
    private func formatDirection(_ direction: simd_float3) -> String {
        let x = direction.x
        let y = direction.y
        let z = direction.z
        
        var components: [String] = []
        
        if abs(x) > 0.3 {
            components.append(x > 0 ? "right" : "left")
        }
        if abs(y) > 0.3 {
            components.append(y > 0 ? "up" : "down")
        }
        if abs(z) > 0.3 {
            components.append(z > 0 ? "forward" : "backward")
        }
        
        return components.isEmpty ? "stationary" : components.joined(separator: ", ")
    }
    
    // MARK: - Data Export
    
    /// Export pose history for analysis
    /// - Parameter timeWindow: Time window to export (nil for all history)
    /// - Returns: Array of pose data within the specified window
    func exportPoseHistory(within timeWindow: TimeInterval? = nil) -> [BodyPoseData] {
        if let window = timeWindow {
            return getRecentPoses(within: window)
        } else {
            return getOrderedPoseHistory()
        }
    }
    
    /// Clear all pose history
    func clearHistory() {
        poseHistory.removeAll()
        currentIndex = 0
        bufferFull = false
        jointVelocities.removeAll()
        movementSmoothness = 0.0
        detectedPatterns.removeAll()
    }
}

// MARK: - Supporting Data Structures

/// Represents a detected movement pattern
struct MovementPattern: Sendable, Identifiable {
    let id = UUID()
    let type: PatternType
    let confidence: Float // 0-1
    let description: String
    let startTime: TimeInterval
    let endTime: TimeInterval
    
    var duration: TimeInterval {
        return endTime - startTime
    }
}

/// Types of movement patterns that can be detected
enum PatternType: String, CaseIterable, Sendable {
    case repetitive = "repetitive"
    case directional = "directional"
    case stable = "stable"
    case erratic = "erratic"
    
    var displayName: String {
        switch self {
        case .repetitive: return "Repetitive Movement"
        case .directional: return "Directional Movement"
        case .stable: return "Stable Position"
        case .erratic: return "Erratic Movement"
        }
    }
}