import Foundation
import simd

/// Detects exercise states and movement phases from body pose data
@MainActor
final class MovementDetector: ObservableObject, Sendable {
    
    // MARK: - Published Properties
    
    /// Current exercise state
    @Published var currentState: ExerciseState = .ready
    
    /// Current exercise being performed
    @Published var currentExercise: ExerciseType?
    
    /// Current rep count
    @Published var repCount: Int = 0
    
    /// Current set count
    @Published var setCount: Int = 0
    
    /// Movement quality score (0-1)
    @Published var movementQuality: Float = 0.0
    
    /// Form feedback messages
    @Published var formFeedback: [FormFeedback] = []
    
    // MARK: - Private Properties
    
    private var stateHistory: [ExerciseState] = []
    private var lastStateChangeTime: TimeInterval = 0
    private var movementBuffer: [BodyPoseData] = []
    private let bufferSize = 30 // ~1 second at 30 FPS
    
    // Exercise-specific thresholds
    private var exerciseThresholds: [ExerciseType: ExerciseThresholds] = [:]
    
    // MARK: - Initialization
    
    nonisolated init() {
        setupExerciseThresholds()
    }
    
    private func setupExerciseThresholds() {
        exerciseThresholds = [
            .squat: ExerciseThresholds(
                startAngle: 160,
                endAngle: 90,
                minDuration: 0.5,
                maxDuration: 3.0,
                qualityThreshold: 0.7
            ),
            .pushUp: ExerciseThresholds(
                startAngle: 170,
                endAngle: 45,
                minDuration: 0.8,
                maxDuration: 4.0,
                qualityThreshold: 0.7
            ),
            .plank: ExerciseThresholds(
                startAngle: 180,
                endAngle: 180,
                minDuration: 5.0,
                maxDuration: 300.0,
                qualityThreshold: 0.8
            )
        ]
    }
    
    // MARK: - Movement Detection
    
    /// Process new pose data and update exercise state
    /// - Parameter poseData: Latest body pose data
    func processMovement(_ poseData: BodyPoseData) {
        // Add to movement buffer
        movementBuffer.append(poseData)
        if movementBuffer.count > bufferSize {
            movementBuffer.removeFirst()
        }
        
        // Update movement quality
        updateMovementQuality(poseData)
        
        // Detect current exercise if not set
        if currentExercise == nil {
            detectExerciseType(from: poseData)
        }
        
        // Update exercise state
        if let exercise = currentExercise {
            updateExerciseState(for: exercise, with: poseData)
        }
        
        // Generate form feedback
        generateFormFeedback(for: poseData)
    }
    
    /// Detect the type of exercise being performed
    private func detectExerciseType(from poseData: BodyPoseData) {
        guard movementBuffer.count >= 10 else { return }
        
        // Analyze movement patterns to determine exercise
        let exercises: [ExerciseType] = [.squat, .pushUp, .plank]
        var scores: [ExerciseType: Float] = [:]
        
        for exercise in exercises {
            scores[exercise] = calculateExerciseScore(for: exercise, with: poseData)
        }
        
        // Find the exercise with highest confidence score
        if let bestMatch = scores.max(by: { $0.value < $1.value }),
           bestMatch.value > 0.6 {
            currentExercise = bestMatch.key
            currentState = .ready
        }
    }
    
    /// Calculate confidence score for a specific exercise type
    private func calculateExerciseScore(for exercise: ExerciseType, with poseData: BodyPoseData) -> Float {
        switch exercise {
        case .squat:
            return calculateSquatScore(poseData)
        case .pushUp:
            return calculatePushUpScore(poseData)
        case .plank:
            return calculatePlankScore(poseData)
        case .burpee:
            return calculateBurpeeScore(poseData)
        case .mountainClimber:
            return calculateMountainClimberScore(poseData)
        }
    }
    
    /// Calculate squat detection score
    private func calculateSquatScore(_ poseData: BodyPoseData) -> Float {
        guard poseData.hasRequiredJoints(for: .squat) else { return 0.0 }
        
        var score: Float = 0.0
        
        // Check if person is upright
        if let spine = poseData.joints["spine_7"], let root = poseData.joints["root"] {
            let torsoAngle = JointAngleCalculator.angleFromVertical(joint1: root.position, joint2: spine.position)
            if torsoAngle < 45 { // Relatively upright
                score += 0.4
            }
        }
        
        // Check knee angles
        let angles = JointAngleCalculator.calculateCommonAngles(from: poseData)
        if let leftKnee = angles["leftKnee"], let rightKnee = angles["rightKnee"] {
            let avgKneeAngle = (leftKnee + rightKnee) / 2
            if avgKneeAngle > 120 && avgKneeAngle < 180 { // Standing or partial squat
                score += 0.6
            }
        }
        
        return score
    }
    
    /// Calculate push-up detection score
    private func calculatePushUpScore(_ poseData: BodyPoseData) -> Float {
        guard poseData.hasRequiredJoints(for: .pushUp) else { return 0.0 }
        
        var score: Float = 0.0
        
        // Check if person is horizontal (prone position)
        if let head = poseData.joints["head"], let root = poseData.joints["root"] {
            let bodyAngle = JointAngleCalculator.angleFromVertical(joint1: root.position, joint2: head.position)
            if bodyAngle > 60 && bodyAngle < 120 { // Horizontal-ish
                score += 0.5
            }
        }
        
        // Check if hands are supporting body weight (relatively low Y position)
        if let leftHand = poseData.joints["left_hand"], let rightHand = poseData.joints["right_hand"] {
            let avgHandHeight = (leftHand.position.y + rightHand.position.y) / 2
            if avgHandHeight < 0.5 { // Hands are low (supporting body)
                score += 0.5
            }
        }
        
        return score
    }
    
    /// Calculate plank detection score
    private func calculatePlankScore(_ poseData: BodyPoseData) -> Float {
        guard poseData.hasRequiredJoints(for: .plank) else { return 0.0 }
        
        var score: Float = 0.0
        
        // Check horizontal body alignment
        if let head = poseData.joints["head"],
           let spine = poseData.joints["spine_7"],
           let root = poseData.joints["root"] {
            
            let headSpineAngle = JointAngleCalculator.angleFromVertical(joint1: spine.position, joint2: head.position)
            let spineRootAngle = JointAngleCalculator.angleFromVertical(joint1: root.position, joint2: spine.position)
            
            if headSpineAngle > 70 && headSpineAngle < 110 && spineRootAngle > 70 && spineRootAngle < 110 {
                score += 0.7
            }
        }
        
        // Check if on hands and toes
        if let leftHand = poseData.joints["left_hand"],
           let rightHand = poseData.joints["right_hand"],
           let leftFoot = poseData.joints["left_foot"],
           let rightFoot = poseData.joints["right_foot"] {
            
            let avgHandHeight = (leftHand.position.y + rightHand.position.y) / 2
            let avgFootHeight = (leftFoot.position.y + rightFoot.position.y) / 2
            
            if avgHandHeight < 0.3 && avgFootHeight < 0.1 {
                score += 0.3
            }
        }
        
        return score
    }
    
    /// Calculate burpee detection score
    private func calculateBurpeeScore(_ poseData: BodyPoseData) -> Float {
        // Burpees involve multiple positions, so this is more complex
        // For now, return lower confidence as it requires temporal analysis
        return 0.3
    }
    
    /// Calculate mountain climber detection score
    private func calculateMountainClimberScore(_ poseData: BodyPoseData) -> Float {
        // Mountain climbers also require temporal analysis
        // Look for plank-like position with alternating leg movement
        let plankScore = calculatePlankScore(poseData)
        return plankScore * 0.7 // Lower confidence than pure plank
    }
    
    // MARK: - Exercise State Management
    
    /// Update exercise state based on current pose and exercise type
    private func updateExerciseState(for exercise: ExerciseType, with poseData: BodyPoseData) {
        guard let thresholds = exerciseThresholds[exercise] else { return }
        
        let currentTime = poseData.timestamp
        let keyAngle = getKeyAngle(for: exercise, from: poseData)
        
        switch currentState {
        case .ready:
            if isInStartPosition(for: exercise, angle: keyAngle, thresholds: thresholds) {
                transitionToState(.starting, at: currentTime)
            }
            
        case .starting:
            if isMovingToEndPosition(for: exercise, angle: keyAngle, thresholds: thresholds) {
                transitionToState(.descending, at: currentTime)
            }
            
        case .descending:
            if isInEndPosition(for: exercise, angle: keyAngle, thresholds: thresholds) {
                transitionToState(.bottom, at: currentTime)
            }
            
        case .bottom:
            if isMovingToStartPosition(for: exercise, angle: keyAngle, thresholds: thresholds) {
                transitionToState(.ascending, at: currentTime)
            } else if currentTime - lastStateChangeTime > thresholds.maxDuration {
                // Timeout - return to ready
                transitionToState(.ready, at: currentTime)
            }
            
        case .ascending:
            if isInStartPosition(for: exercise, angle: keyAngle, thresholds: thresholds) {
                // Rep completed
                repCount += 1
                transitionToState(.completed, at: currentTime)
                
                // Transition back to ready for next rep
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    self.transitionToState(.ready, at: currentTime + 0.5)
                }
            }
            
        case .completed:
            // Handled by delayed transition above
            break
        }
    }
    
    /// Get the key angle for exercise analysis
    private func getKeyAngle(for exercise: ExerciseType, from poseData: BodyPoseData) -> Float? {
        let angles = JointAngleCalculator.calculateCommonAngles(from: poseData)
        
        switch exercise {
        case .squat:
            if let leftKnee = angles["leftKnee"], let rightKnee = angles["rightKnee"] {
                return (leftKnee + rightKnee) / 2
            }
        case .pushUp:
            if let leftElbow = angles["leftElbow"], let rightElbow = angles["rightElbow"] {
                return (leftElbow + rightElbow) / 2
            }
        case .plank:
            // For plank, we care about body alignment rather than joint angles
            if let head = poseData.joints["head"], let root = poseData.joints["root"] {
                return JointAngleCalculator.angleFromVertical(joint1: root.position, joint2: head.position)
            }
        case .burpee, .mountainClimber:
            // Complex exercises - return a composite score
            return 0
        }
        
        return nil
    }
    
    /// Check if in starting position
    private func isInStartPosition(for exercise: ExerciseType, angle: Float?, thresholds: ExerciseThresholds) -> Bool {
        guard let angle = angle else { return false }
        return abs(angle - thresholds.startAngle) <= 20
    }
    
    /// Check if moving toward end position
    private func isMovingToEndPosition(for exercise: ExerciseType, angle: Float?, thresholds: ExerciseThresholds) -> Bool {
        guard let angle = angle else { return false }
        return angle < thresholds.startAngle && angle > thresholds.endAngle
    }
    
    /// Check if in end position
    private func isInEndPosition(for exercise: ExerciseType, angle: Float?, thresholds: ExerciseThresholds) -> Bool {
        guard let angle = angle else { return false }
        return abs(angle - thresholds.endAngle) <= 20
    }
    
    /// Check if moving toward start position
    private func isMovingToStartPosition(for exercise: ExerciseType, angle: Float?, thresholds: ExerciseThresholds) -> Bool {
        guard let angle = angle else { return false }
        return angle > thresholds.endAngle && angle < thresholds.startAngle
    }
    
    /// Transition to new exercise state
    private func transitionToState(_ newState: ExerciseState, at time: TimeInterval) {
        currentState = newState
        lastStateChangeTime = time
        stateHistory.append(newState)
        
        // Keep state history manageable
        if stateHistory.count > 100 {
            stateHistory.removeFirst(50)
        }
    }
    
    // MARK: - Movement Quality Analysis
    
    /// Update movement quality score
    private func updateMovementQuality(_ poseData: BodyPoseData) {
        guard let exercise = currentExercise else {
            movementQuality = 0.0
            return
        }
        
        var qualityScores: [Float] = []
        
        // Joint confidence
        let avgConfidence = poseData.joints.values.map { $0.confidence }.reduce(0, +) / Float(poseData.joints.count)
        qualityScores.append(avgConfidence)
        
        // Exercise-specific form analysis
        switch exercise {
        case .squat:
            let squatAnalysis = JointAngleCalculator.analyzeSquatForm(from: poseData)
            if let goodPosture = squatAnalysis["goodPosture"] as? Bool {
                qualityScores.append(goodPosture ? 1.0 : 0.3)
            }
            
        case .pushUp:
            let pushUpAnalysis = JointAngleCalculator.analyzePushUpForm(from: poseData)
            if let goodAlignment = pushUpAnalysis["goodAlignment"] as? Bool {
                qualityScores.append(goodAlignment ? 1.0 : 0.3)
            }
            
        case .plank:
            // For plank, consistent positioning is key
            qualityScores.append(currentState == .bottom ? 1.0 : 0.5)
            
        default:
            qualityScores.append(0.5) // Default for unimplemented exercises
        }
        
        movementQuality = qualityScores.reduce(0, +) / Float(qualityScores.count)
    }
    
    // MARK: - Form Feedback
    
    /// Generate real-time form feedback
    private func generateFormFeedback(for poseData: BodyPoseData) {
        guard let exercise = currentExercise else { return }
        
        var feedback: [FormFeedback] = []
        
        switch exercise {
        case .squat:
            feedback.append(contentsOf: generateSquatFeedback(poseData))
        case .pushUp:
            feedback.append(contentsOf: generatePushUpFeedback(poseData))
        case .plank:
            feedback.append(contentsOf: generatePlankFeedback(poseData))
        default:
            break
        }
        
        formFeedback = feedback
    }
    
    /// Generate squat-specific feedback
    private func generateSquatFeedback(_ poseData: BodyPoseData) -> [FormFeedback] {
        var feedback: [FormFeedback] = []
        let analysis = JointAngleCalculator.analyzeSquatForm(from: poseData)
        
        if let avgKneeAngle = analysis["averageKneeAngle"] as? Float {
            if avgKneeAngle < 70 {
                feedback.append(FormFeedback(type: .correction, message: "Squat deeper - aim for 90° knee angle"))
            } else if avgKneeAngle > 150 && currentState == .descending {
                feedback.append(FormFeedback(type: .encouragement, message: "Good depth! Now stand up"))
            }
        }
        
        if let goodPosture = analysis["goodPosture"] as? Bool, !goodPosture {
            feedback.append(FormFeedback(type: .correction, message: "Keep your chest up and back straight"))
        }
        
        return feedback
    }
    
    /// Generate push-up specific feedback
    private func generatePushUpFeedback(_ poseData: BodyPoseData) -> [FormFeedback] {
        var feedback: [FormFeedback] = []
        let analysis = JointAngleCalculator.analyzePushUpForm(from: poseData)
        
        if let goodAlignment = analysis["goodAlignment"] as? Bool, !goodAlignment {
            feedback.append(FormFeedback(type: .correction, message: "Keep your body in a straight line"))
        }
        
        if let avgElbowAngle = analysis["averageElbowAngle"] as? Float {
            if avgElbowAngle > 120 && currentState == .descending {
                feedback.append(FormFeedback(type: .correction, message: "Lower your chest closer to the ground"))
            }
        }
        
        return feedback
    }
    
    /// Generate plank-specific feedback
    private func generatePlankFeedback(_ poseData: BodyPoseData) -> [FormFeedback] {
        var feedback: [FormFeedback] = []
        
        if movementQuality < 0.7 {
            feedback.append(FormFeedback(type: .correction, message: "Maintain straight body alignment"))
        } else if currentState == .bottom {
            feedback.append(FormFeedback(type: .encouragement, message: "Great form! Hold that position"))
        }
        
        return feedback
    }
    
    // MARK: - Public Interface
    
    /// Reset all tracking data
    func reset() {
        currentState = .ready
        currentExercise = nil
        repCount = 0
        setCount = 0
        movementQuality = 0.0
        formFeedback.removeAll()
        stateHistory.removeAll()
        movementBuffer.removeAll()
        lastStateChangeTime = 0
    }
    
    /// Start a new set
    func startNewSet() {
        setCount += 1
        repCount = 0
        currentState = .ready
    }
    
    /// Get exercise statistics
    func getExerciseStats() -> ExerciseStats {
        return ExerciseStats(
            exercise: currentExercise,
            reps: repCount,
            sets: setCount,
            averageQuality: movementQuality,
            currentState: currentState
        )
    }
}

// MARK: - Supporting Data Structures

/// Exercise state enumeration
enum ExerciseState: String, CaseIterable, Sendable {
    case ready = "ready"
    case starting = "starting"
    case descending = "descending"
    case bottom = "bottom"
    case ascending = "ascending"
    case completed = "completed"
    
    var displayName: String {
        switch self {
        case .ready: return "Ready"
        case .starting: return "Starting"
        case .descending: return "Descending"
        case .bottom: return "Bottom Position"
        case .ascending: return "Ascending"
        case .completed: return "Completed"
        }
    }
}

/// Exercise-specific thresholds
struct ExerciseThresholds: Sendable {
    let startAngle: Float      // Angle at start position
    let endAngle: Float        // Angle at end position
    let minDuration: TimeInterval   // Minimum time to complete movement
    let maxDuration: TimeInterval   // Maximum time before timeout
    let qualityThreshold: Float     // Minimum quality score
}

/// Form feedback message
struct FormFeedback: Sendable, Identifiable {
    let id = UUID()
    let type: FeedbackType
    let message: String
    let timestamp: Date = Date()
}

/// Types of form feedback
enum FeedbackType: String, CaseIterable, Sendable {
    case correction = "correction"
    case encouragement = "encouragement"
    case warning = "warning"
    case celebration = "celebration"
    
    var displayName: String {
        switch self {
        case .correction: return "Correction"
        case .encouragement: return "Encouragement"
        case .warning: return "Warning"
        case .celebration: return "Celebration"
        }
    }
}

/// Exercise statistics
struct ExerciseStats: Sendable {
    let exercise: ExerciseType?
    let reps: Int
    let sets: Int
    let averageQuality: Float
    let currentState: ExerciseState
    
    var qualityGrade: String {
        switch averageQuality {
        case 0.9...1.0: return "A"
        case 0.8..<0.9: return "B"
        case 0.7..<0.8: return "C"
        case 0.6..<0.7: return "D"
        default: return "F"
        }
    }
}