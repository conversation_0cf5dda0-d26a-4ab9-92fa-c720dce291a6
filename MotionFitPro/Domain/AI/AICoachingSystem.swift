//
//  AICoachingSystem.swift
//  MotionFitPro
//
//  Created by <PERSON> on 2025-07-15.
//

import Foundation
import Combine

// MARK: - AI Coaching System

@MainActor
class AICoachingSystem: ObservableObject {
    @Published var currentCoach: CoachPersonality = .motivational
    @Published var adaptiveDifficulty: AdaptiveDifficulty = .analyzing
    @Published var currentFeedback: AIFeedback?
    @Published var injuryWarnings: [InjuryWarning] = []
    @Published var performanceInsights: PerformanceInsights?
    
    private var performanceHistory: [PerformanceData] = []
    private var sessionAnalytics: SessionAnalytics = SessionAnalytics()
    private let maxHistorySize = 100
    
    // MARK: - Coaching Personalities
    
    func setCoachPersonality(_ personality: CoachPersonality) {
        currentCoach = personality
    }
    
    func generateMotivationalMessage(for context: MotivationalContext) -> String {
        return currentCoach.generateMessage(for: context)
    }
    
    // MARK: - Real-time Form Correction
    
    func analyzeFormAndProvideCoaching(
        analysis: ExerciseAnalysis,
        userPerformance: UserPerformanceState
    ) -> AIFeedback {
        // Update performance history
        let performanceData = PerformanceData(
            timestamp: Date(),
            exercise: analysis.exercise,
            formScore: Float(analysis.formScore),
            repPhase: analysis.repPhase,
            detectedIssues: analysis.detectedIssues,
            confidence: Float(analysis.confidence)
        )
        addToPerformanceHistory(performanceData)
        
        // Generate specific coaching advice
        let coaching = generateSpecificCoaching(from: analysis)
        
        // Check for injury risks
        let injuryWarnings = detectInjuryRisks(from: analysis, history: performanceHistory)
        
        // Update adaptive difficulty
        updateAdaptiveDifficulty(based: userPerformance)
        
        let feedback = AIFeedback(
            primaryMessage: coaching.primaryMessage,
            secondaryTips: coaching.secondaryTips,
            encouragement: currentCoach.generateEncouragement(for: Float(analysis.formScore)),
            correctionPriority: coaching.priority,
            adaptiveSuggestion: generateAdaptiveSuggestion(),
            injuryWarnings: injuryWarnings
        )
        
        currentFeedback = feedback
        self.injuryWarnings = injuryWarnings
        
        return feedback
    }
    
    // MARK: - Adaptive Difficulty
    
    private func updateAdaptiveDifficulty(based performance: UserPerformanceState) {
        let recentPerformance = Array(performanceHistory.suffix(10))
        
        guard !recentPerformance.isEmpty else {
            adaptiveDifficulty = .analyzing
            return
        }
        
        let avgFormScore = recentPerformance.map { $0.formScore }.reduce(0, +) / Float(recentPerformance.count)
        let avgConfidence = recentPerformance.map { $0.confidence }.reduce(0, +) / Float(recentPerformance.count)
        let consistencyScore = calculateConsistencyScore(recentPerformance)
        
        let overallScore = (avgFormScore + avgConfidence + consistencyScore) / 3.0
        
        switch overallScore {
        case 0.9...1.0:
            adaptiveDifficulty = .tooEasy(suggestion: "Ready for advanced challenges!")
        case 0.8..<0.9:
            adaptiveDifficulty = .optimal(message: "Perfect challenge level")
        case 0.6..<0.8:
            adaptiveDifficulty = .moderate(advice: "Building strength steadily")
        case 0.4..<0.6:
            adaptiveDifficulty = .challenging(support: "Push through - you've got this!")
        default:
            adaptiveDifficulty = .tooHard(recommendation: "Let's adjust the difficulty")
        }
    }
    
    private func generateAdaptiveSuggestion() -> String? {
        switch adaptiveDifficulty {
        case .tooEasy(let suggestion):
            return suggestion
        case .optimal(let message):
            return message
        case .moderate(let advice):
            return advice
        case .challenging(let support):
            return support
        case .tooHard(let recommendation):
            return recommendation
        case .analyzing:
            return nil
        }
    }
    
    // MARK: - Injury Prevention
    
    private func detectInjuryRisks(
        from analysis: ExerciseAnalysis,
        history: [PerformanceData]
    ) -> [InjuryWarning] {
        var warnings: [InjuryWarning] = []
        
        // Check for rapid form degradation
        if let formDegradation = detectFormDegradation(history: history) {
            warnings.append(formDegradation)
        }
        
        // Check for exercise-specific injury risks
        let exerciseWarnings = detectExerciseSpecificRisks(analysis: analysis)
        warnings.append(contentsOf: exerciseWarnings)
        
        // Check for fatigue patterns
        if let fatigueWarning = detectFatiguePatterns(history: history) {
            warnings.append(fatigueWarning)
        }
        
        return warnings
    }
    
    private func detectFormDegradation(history: [PerformanceData]) -> InjuryWarning? {
        guard history.count >= 5 else { return nil }
        
        let recentScores = Array(history.suffix(5)).map { $0.formScore }
        let trend = calculateTrend(scores: recentScores)
        
        if trend < -0.3 { // Significant downward trend
            return InjuryWarning(
                type: .formDegradation,
                severity: .high,
                message: "Form is declining rapidly - consider taking a break",
                recommendation: "Focus on technique over speed"
            )
        }
        
        return nil
    }
    
    private func detectExerciseSpecificRisks(analysis: ExerciseAnalysis) -> [InjuryWarning] {
        var warnings: [InjuryWarning] = []
        
        for issue in analysis.detectedIssues {
            switch (analysis.exercise, issue.criteria) {
            case (.squat, "knee_alignment"):
                warnings.append(InjuryWarning(
                    type: .kneeStress,
                    severity: .medium,
                    message: "Knee alignment issue detected",
                    recommendation: "Keep knees tracking over toes"
                ))
            case (.pushUp, "wrist_position"):
                warnings.append(InjuryWarning(
                    type: .wristStrain,
                    severity: .low,
                    message: "Wrist strain risk",
                    recommendation: "Adjust hand position or use push-up handles"
                ))
            case (.plank, "lower_back"):
                warnings.append(InjuryWarning(
                    type: .backStrain,
                    severity: .high,
                    message: "Lower back compensation detected",
                    recommendation: "Engage core more, avoid sagging hips"
                ))
            default:
                break
            }
        }
        
        return warnings
    }
    
    private func detectFatiguePatterns(history: [PerformanceData]) -> InjuryWarning? {
        guard history.count >= 10 else { return nil }
        
        let recentSession = Array(history.suffix(10))
        let avgFormScore = recentSession.map { $0.formScore }.reduce(0, +) / Float(recentSession.count)
        
        if avgFormScore < 0.5 {
            return InjuryWarning(
                type: .fatigue,
                severity: .medium,
                message: "Fatigue affecting form quality",
                recommendation: "Consider taking a rest or reducing intensity"
            )
        }
        
        return nil
    }
    
    // MARK: - Coaching Message Generation
    
    private func generateSpecificCoaching(from analysis: ExerciseAnalysis) -> CoachingAdvice {
        let primaryIssue = analysis.detectedIssues.first
        
        let primaryMessage: String
        let priority: CorrectionPriority
        
        if let issue = primaryIssue {
            primaryMessage = generateIssueSpecificAdvice(issue: issue, exercise: analysis.exercise)
            priority = mapSeverityToPriority(issue.severity)
        } else {
            primaryMessage = currentCoach.generatePositiveReinforcement(for: Float(analysis.formScore))
            priority = .low
        }
        
        let secondaryTips = generateSecondaryTips(analysis: analysis)
        
        return CoachingAdvice(
            primaryMessage: primaryMessage,
            secondaryTips: secondaryTips,
            priority: priority
        )
    }
    
    private func generateIssueSpecificAdvice(issue: FormIssue, exercise: ExerciseType) -> String {
        let baseAdvice = issue.correctionSuggestion ?? "Focus on proper form"
        return currentCoach.personalizeAdvice(baseAdvice, for: exercise)
    }
    
    private func generateSecondaryTips(analysis: ExerciseAnalysis) -> [String] {
        var tips: [String] = []
        
        // Add exercise-specific tips based on phase
        switch (analysis.exercise, analysis.repPhase) {
        case (.squat, .eccentric):
            tips.append("Control the descent")
        case (.pushUp, .isometric):
            tips.append("Hold that bottom position")
        case (.plank, .isometric):
            tips.append("Breathe steadily")
        default:
            break
        }
        
        // Add form score based tips
        if analysis.formScore > 0.8 {
            tips.append(currentCoach.generateExcellenceReinforcement())
        } else if analysis.formScore < 0.6 {
            tips.append("Focus on quality over quantity")
        }
        
        return tips
    }
    
    // MARK: - Performance Analytics
    
    func generatePerformanceInsights() -> PerformanceInsights {
        let insights = PerformanceInsights(
            formTrend: calculateFormTrend(),
            consistencyScore: calculateOverallConsistency(),
            improvementAreas: identifyImprovementAreas(),
            achievements: identifyRecentAchievements(),
            recommendations: generatePersonalizedRecommendations()
        )
        
        performanceInsights = insights
        return insights
    }
    
    // MARK: - Helper Methods
    
    private func addToPerformanceHistory(_ data: PerformanceData) {
        performanceHistory.append(data)
        if performanceHistory.count > maxHistorySize {
            performanceHistory.removeFirst()
        }
    }
    
    private func calculateConsistencyScore(_ performances: [PerformanceData]) -> Float {
        guard performances.count >= 2 else { return 0.5 }
        
        let scores = performances.map { $0.formScore }
        let average = scores.reduce(0, +) / Float(scores.count)
        let variance = scores.map { pow($0 - average, 2) }.reduce(0, +) / Float(scores.count)
        
        return max(0.0, 1.0 - variance)
    }
    
    private func calculateTrend(scores: [Float]) -> Float {
        guard scores.count >= 2 else { return 0.0 }
        
        let first = Array(scores.prefix(scores.count / 2)).reduce(0, +) / Float(scores.count / 2)
        let last = Array(scores.suffix(scores.count / 2)).reduce(0, +) / Float(scores.count / 2)
        
        return last - first
    }
    
    private func calculateFormTrend() -> TrendDirection {
        guard performanceHistory.count >= 10 else { return .stable }
        
        let recentScores = Array(performanceHistory.suffix(10)).map { $0.formScore }
        let trend = calculateTrend(scores: recentScores)
        
        if trend > 0.1 {
            return .improving
        } else if trend < -0.1 {
            return .declining
        } else {
            return .stable
        }
    }
    
    private func calculateOverallConsistency() -> Float {
        return calculateConsistencyScore(performanceHistory)
    }
    
    private func identifyImprovementAreas() -> [String] {
        // Analyze common issues across recent performances
        let recentIssues = performanceHistory.suffix(20).flatMap { $0.detectedIssues }
        let issueCounts = Dictionary(grouping: recentIssues, by: { $0.criteria })
            .mapValues { $0.count }
            .sorted { $0.value > $1.value }
        
        return Array(issueCounts.prefix(3).map { $0.key })
    }
    
    private func identifyRecentAchievements() -> [String] {
        var achievements: [String] = []
        
        if let best = performanceHistory.max(by: { $0.formScore < $1.formScore }) {
            if best.formScore > 0.9 {
                achievements.append("Perfect Form Master")
            }
        }
        
        return achievements
    }
    
    private func generatePersonalizedRecommendations() -> [String] {
        var recommendations: [String] = []
        
        let formTrend = calculateFormTrend()
        switch formTrend {
        case .improving:
            recommendations.append("Great progress! Consider increasing difficulty")
        case .declining:
            recommendations.append("Focus on form fundamentals")
        case .stable:
            recommendations.append("Add variety to challenge yourself")
        }
        
        return recommendations
    }
    
    private func mapSeverityToPriority(_ severity: FormIssueSeverity) -> CorrectionPriority {
        switch severity {
        case .low: return .low
        case .medium: return .medium
        case .high: return .high
        }
    }
}

// MARK: - Supporting Models

struct PerformanceData {
    let timestamp: Date
    let exercise: ExerciseType
    let formScore: Float
    let repPhase: MovementPhase
    let detectedIssues: [FormIssue]
    let confidence: Float
}

struct UserPerformanceState {
    let currentRepCount: Int
    let sessionDuration: TimeInterval
    let averageFormScore: Float
    let fatigueLevel: Float
    let heartRateZone: Int?
}

struct AIFeedback {
    let primaryMessage: String
    let secondaryTips: [String]
    let encouragement: String
    let correctionPriority: CorrectionPriority
    let adaptiveSuggestion: String?
    let injuryWarnings: [InjuryWarning]
}

struct CoachingAdvice {
    let primaryMessage: String
    let secondaryTips: [String]
    let priority: CorrectionPriority
}

enum CorrectionPriority: Int, CaseIterable {
    case low = 1
    case medium = 2
    case high = 3
    
    var color: String {
        switch self {
        case .low: return "#2ECC71"
        case .medium: return "#F39C12"
        case .high: return "#E74C3C"
        }
    }
}

enum AdaptiveDifficulty {
    case analyzing
    case tooEasy(suggestion: String)
    case optimal(message: String)
    case moderate(advice: String)
    case challenging(support: String)
    case tooHard(recommendation: String)
    
    var description: String {
        switch self {
        case .analyzing: return "Analyzing performance..."
        case .tooEasy(let suggestion): return suggestion
        case .optimal(let message): return message
        case .moderate(let advice): return advice
        case .challenging(let support): return support
        case .tooHard(let recommendation): return recommendation
        }
    }
}

struct InjuryWarning {
    let type: InjuryType
    let severity: WarningSeverity
    let message: String
    let recommendation: String
}

enum InjuryType {
    case formDegradation
    case kneeStress
    case wristStrain
    case backStrain
    case shoulderImpingement
    case fatigue
}

enum WarningSeverity {
    case low, medium, high
    
    var color: String {
        switch self {
        case .low: return "#F39C12"
        case .medium: return "#E67E22"
        case .high: return "#E74C3C"
        }
    }
}

struct PerformanceInsights {
    let formTrend: TrendDirection
    let consistencyScore: Float
    let improvementAreas: [String]
    let achievements: [String]
    let recommendations: [String]
}

enum TrendDirection {
    case improving, stable, declining
    
    var description: String {
        switch self {
        case .improving: return "Improving"
        case .stable: return "Stable"
        case .declining: return "Needs Attention"
        }
    }
    
    var color: String {
        switch self {
        case .improving: return "#2ECC71"
        case .stable: return "#3498DB"
        case .declining: return "#E74C3C"
        }
    }
}

struct SessionAnalytics {
    var totalReps: Int = 0
    var perfectFormReps: Int = 0
    var averageFormScore: Float = 0.0
    var exerciseDuration: TimeInterval = 0
    var caloriesBurned: Int = 0
}