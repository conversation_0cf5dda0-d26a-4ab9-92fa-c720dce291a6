//
//  CoachPersonalities.swift
//  MotionFitPro
//
//  Created by <PERSON> on 2025-07-15.
//

import Foundation

// MARK: - Coach Personalities

enum CoachPersonality: String, CaseIterable, Identifiable {
    case motivational = "motivational"
    case technical = "technical"
    case supportive = "supportive"
    case competitive = "competitive"
    case zen = "zen"
    
    var id: String { rawValue }
    
    var displayName: String {
        switch self {
        case .motivational: return "The Motivator"
        case .technical: return "The Technician"
        case .supportive: return "The Supporter"
        case .competitive: return "The Competitor"
        case .zen: return "The Zen Master"
        }
    }
    
    var description: String {
        switch self {
        case .motivational:
            return "High-energy coaching that pushes you to achieve your best"
        case .technical:
            return "Precise, analytical feedback focused on perfect technique"
        case .supportive:
            return "Encouraging and patient guidance for steady progress"
        case .competitive:
            return "Challenge-driven coaching that brings out your fighting spirit"
        case .zen:
            return "Mindful, balanced approach focusing on mind-body connection"
        }
    }
    
    var emoji: String {
        switch self {
        case .motivational: return "🔥"
        case .technical: return "🎯"
        case .supportive: return "💚"
        case .competitive: return "⚡"
        case .zen: return "🧘‍♂️"
        }
    }
    
    var primaryColor: String {
        switch self {
        case .motivational: return "#E74C3C"
        case .technical: return "#3498DB"
        case .supportive: return "#2ECC71"
        case .competitive: return "#F39C12"
        case .zen: return "#9B59B6"
        }
    }
    
    // MARK: - Message Generation
    
    func generateMessage(for context: MotivationalContext) -> String {
        switch (self, context) {
        case (.motivational, .workoutStart):
            return motivationalStartMessages.randomElement() ?? "Let's dominate this workout!"
        case (.motivational, .repCompleted):
            return motivationalRepMessages.randomElement() ?? "Beast mode activated!"
        case (.motivational, .struggle):
            return motivationalStruggleMessages.randomElement() ?? "This is where champions are made!"
        case (.motivational, .workoutComplete):
            return motivationalCompleteMessages.randomElement() ?? "You absolutely crushed that!"
            
        case (.technical, .workoutStart):
            return technicalStartMessages.randomElement() ?? "Focus on precision and control."
        case (.technical, .repCompleted):
            return technicalRepMessages.randomElement() ?? "Excellent form execution."
        case (.technical, .struggle):
            return technicalStruggleMessages.randomElement() ?? "Maintain technique under fatigue."
        case (.technical, .workoutComplete):
            return technicalCompleteMessages.randomElement() ?? "Technical mastery demonstrated."
            
        case (.supportive, .workoutStart):
            return supportiveStartMessages.randomElement() ?? "You've got this, one step at a time."
        case (.supportive, .repCompleted):
            return supportiveRepMessages.randomElement() ?? "Beautiful work, keep it up!"
        case (.supportive, .struggle):
            return supportiveStruggleMessages.randomElement() ?? "It's okay, progress takes time."
        case (.supportive, .workoutComplete):
            return supportiveCompleteMessages.randomElement() ?? "So proud of your effort today!"
            
        case (.competitive, .workoutStart):
            return competitiveStartMessages.randomElement() ?? "Time to show what you're made of!"
        case (.competitive, .repCompleted):
            return competitiveRepMessages.randomElement() ?? "That's how winners move!"
        case (.competitive, .struggle):
            return competitiveStruggleMessages.randomElement() ?? "Champions push through the pain!"
        case (.competitive, .workoutComplete):
            return competitiveCompleteMessages.randomElement() ?? "Victory is yours!"
            
        case (.zen, .workoutStart):
            return zenStartMessages.randomElement() ?? "Center yourself and begin."
        case (.zen, .repCompleted):
            return zenRepMessages.randomElement() ?? "Harmony in motion."
        case (.zen, .struggle):
            return zenStruggleMessages.randomElement() ?? "Breathe through the challenge."
        case (.zen, .workoutComplete):
            return zenCompleteMessages.randomElement() ?? "Peace through movement achieved."
        }
    }
    
    func generateEncouragement(for formScore: Float) -> String {
        switch self {
        case .motivational:
            if formScore > 0.8 {
                return "UNSTOPPABLE! That form is fire! 🔥"
            } else if formScore > 0.6 {
                return "You're building momentum! Keep pushing! 💪"
            } else {
                return "Every rep makes you stronger! Don't stop now! ⚡"
            }
            
        case .technical:
            if formScore > 0.8 {
                return "Textbook execution. Biomechanically perfect. 🎯"
            } else if formScore > 0.6 {
                return "Good mechanics. Minor adjustments needed. 📐"
            } else {
                return "Focus on movement quality over quantity. 🔧"
            }
            
        case .supportive:
            if formScore > 0.8 {
                return "You're doing amazing! I'm so proud! 🌟"
            } else if formScore > 0.6 {
                return "Great effort! You're improving with each rep. 💚"
            } else {
                return "Every attempt is progress. Keep going! 🤗"
            }
            
        case .competitive:
            if formScore > 0.8 {
                return "DOMINATING! That's championship level! 🏆"
            } else if formScore > 0.6 {
                return "You're in the fight! Push for excellence! ⚔️"
            } else {
                return "Winners never quit! Show me what you've got! 🥊"
            }
            
        case .zen:
            if formScore > 0.8 {
                return "Perfect balance achieved. Mind and body unite. 🧘‍♂️"
            } else if formScore > 0.6 {
                return "Finding your center. Stay present. 🌸"
            } else {
                return "Accept where you are. Growth comes from within. 🌱"
            }
        }
    }
    
    func generatePositiveReinforcement(for formScore: Float) -> String {
        switch self {
        case .motivational:
            return formScore > 0.8 ? "PHENOMENAL FORM!" : "You're getting stronger!"
        case .technical:
            return formScore > 0.8 ? "Biomechanically optimal" : "Technique developing well"
        case .supportive:
            return formScore > 0.8 ? "You're incredible!" : "Such beautiful progress!"
        case .competitive:
            return formScore > 0.8 ? "ELITE PERFORMANCE!" : "Fighting like a champion!"
        case .zen:
            return formScore > 0.8 ? "Perfect harmony" : "Peaceful strength flows"
        }
    }
    
    func generateExcellenceReinforcement() -> String {
        switch self {
        case .motivational:
            return "You're on FIRE! This is your moment!"
        case .technical:
            return "Maintaining perfect form under load"
        case .supportive:
            return "Your dedication is truly inspiring!"
        case .competitive:
            return "This is what separates winners from the rest!"
        case .zen:
            return "Effortless strength through mindful practice"
        }
    }
    
    func personalizeAdvice(_ advice: String, for exercise: ExerciseType) -> String {
        let exerciseName = exercise.displayName.lowercased()
        
        switch self {
        case .motivational:
            return "🔥 \(advice) - OWN that \(exerciseName)!"
        case .technical:
            return "⚙️ \(advice) - Optimize your \(exerciseName) mechanics"
        case .supportive:
            return "💚 \(advice) - You're doing great with this \(exerciseName)"
        case .competitive:
            return "⚡ \(advice) - Dominate this \(exerciseName) like a champion!"
        case .zen:
            return "🧘‍♂️ \(advice) - Find peace in your \(exerciseName) practice"
        }
    }
}

// MARK: - Motivational Context

enum MotivationalContext {
    case workoutStart
    case repCompleted
    case struggle
    case workoutComplete
}

// MARK: - Message Banks

private extension CoachPersonality {
    // MARK: Motivational Messages
    var motivationalStartMessages: [String] {
        [
            "Time to UNLEASH the beast within! 🔥",
            "Let's DOMINATE this workout like never before!",
            "Your body is ready, your mind is strong - LET'S GO!",
            "Today we rewrite what's possible! BRING THE HEAT!",
            "Champions are made in moments like this! ATTACK!"
        ]
    }
    
    var motivationalRepMessages: [String] {
        [
            "BEAST MODE: ACTIVATED! 💪",
            "That's FIRE! Keep that energy blazing!",
            "UNSTOPPABLE FORCE in motion!",
            "You're CRUSHING IT! Feel that power!",
            "EXPLOSIVE! That's how legends train!"
        ]
    }
    
    var motivationalStruggleMessages: [String] {
        [
            "This is where CHAMPIONS are forged! PUSH!",
            "The burn means it's WORKING! Don't stop!",
            "LEGENDS are made in these moments! FIGHT!",
            "Your future self is CHEERING you on!",
            "This struggle builds UNBREAKABLE strength!"
        ]
    }
    
    var motivationalCompleteMessages: [String] {
        [
            "ABSOLUTELY DEMOLISHED that workout! 🏆",
            "You just REDEFINED what's possible!",
            "WARRIOR! You conquered every challenge!",
            "That was PURE DOMINANCE! Incredible!",
            "CHAMPION performance! You're unstoppable!"
        ]
    }
    
    // MARK: Technical Messages
    var technicalStartMessages: [String] {
        [
            "Focus on precision and biomechanical efficiency.",
            "Establish proper movement patterns from rep one.",
            "Quality execution creates lasting adaptation.",
            "Activate target muscles with intentional control.",
            "Perfect practice creates perfect performance."
        ]
    }
    
    var technicalRepMessages: [String] {
        [
            "Excellent kinetic chain activation.",
            "Optimal joint alignment maintained.",
            "Perfect force vector application.",
            "Textbook movement mechanics.",
            "Precise muscle recruitment pattern."
        ]
    }
    
    var technicalStruggleMessages: [String] {
        [
            "Maintain technique under metabolic stress.",
            "Quality over quantity - form is paramount.",
            "Focus on eccentric control and stability.",
            "Breathe through the challenge, stay technical.",
            "Fatigue tests true movement mastery."
        ]
    }
    
    var technicalCompleteMessages: [String] {
        [
            "Exceptional movement quality demonstrated.",
            "Technical mastery under progressive overload.",
            "Biomechanically sound session completed.",
            "Precision maintained throughout the protocol.",
            "Optimal training stimulus achieved."
        ]
    }
    
    // MARK: Supportive Messages
    var supportiveStartMessages: [String] {
        [
            "You've got this! One step at a time. 💚",
            "Believe in yourself - I believe in you!",
            "Every journey starts with courage to begin.",
            "You're stronger than you think. Let's start!",
            "Progress, not perfection. You're amazing!"
        ]
    }
    
    var supportiveRepMessages: [String] {
        [
            "Beautiful work! You're doing wonderfully!",
            "So proud of your effort and dedication!",
            "Each rep is a victory worth celebrating!",
            "You're making incredible progress!",
            "Keep going! You're absolutely amazing!"
        ]
    }
    
    var supportiveStruggleMessages: [String] {
        [
            "It's okay, you're doing your best! 🤗",
            "Progress takes time - be kind to yourself.",
            "Every challenge makes you stronger inside.",
            "I'm here with you. We'll get through this.",
            "Small steps lead to big victories!"
        ]
    }
    
    var supportiveCompleteMessages: [String] {
        [
            "So incredibly proud of your effort today! 🌟",
            "You showed up and gave your all - amazing!",
            "What a beautiful display of determination!",
            "You should feel so proud of yourself!",
            "Your commitment inspires me every day!"
        ]
    }
    
    // MARK: Competitive Messages
    var competitiveStartMessages: [String] {
        [
            "Time to show what you're MADE of! ⚡",
            "WARRIORS step up when it matters most!",
            "This is YOUR arena - DOMINATE it!",
            "Champions rise when the stakes are high!",
            "GAME TIME! Show them what ELITE looks like!"
        ]
    }
    
    var competitiveRepMessages: [String] {
        [
            "That's WINNER mentality in action! 🏆",
            "ELITE athletes move like THAT!",
            "CRUSHING the competition with every rep!",
            "CHAMPION-level execution right there!",
            "That's how LEGENDS separate themselves!"
        ]
    }
    
    var competitiveStruggleMessages: [String] {
        [
            "Champions THRIVE under pressure! FIGHT!",
            "This is where WINNERS separate from the rest!",
            "GLADIATORS push through the storm!",
            "Your competition is watching - SHOW THEM!",
            "CHAMPIONS are forged in fire like this!"
        ]
    }
    
    var competitiveCompleteMessages: [String] {
        [
            "TOTAL DOMINATION! That's CHAMPION work! 👑",
            "You just CRUSHED your competition!",
            "VICTORY belongs to the RELENTLESS!",
            "ELITE performance! You're UNSTOPPABLE!",
            "That's how CHAMPIONS finish strong!"
        ]
    }
    
    // MARK: Zen Messages
    var zenStartMessages: [String] {
        [
            "Center yourself and find your flow. 🧘‍♂️",
            "Begin with intention, move with purpose.",
            "Breathe deep, connect mind and body.",
            "Find stillness within movement.",
            "Let go of tension, embrace the journey."
        ]
    }
    
    var zenRepMessages: [String] {
        [
            "Harmony flows through purposeful movement.",
            "Beautiful balance of effort and ease.",
            "Mindful strength in perfect motion.",
            "Peaceful power expressed through form.",
            "Present moment awareness in action."
        ]
    }
    
    var zenStruggleMessages: [String] {
        [
            "Breathe through the challenge with grace.",
            "Accept what is, flow with what comes.",
            "Find calm within the storm of effort.",
            "Let struggle teach you about strength.",
            "Peace exists even in difficulty."
        ]
    }
    
    var zenCompleteMessages: [String] {
        [
            "Perfect harmony of effort and rest achieved. 🌸",
            "Peaceful strength cultivated through practice.",
            "Mind and body united in purposeful action.",
            "Serenity found through mindful movement.",
            "Balance restored through conscious effort."
        ]
    }
}