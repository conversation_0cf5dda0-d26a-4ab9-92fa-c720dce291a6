//
//  MultiplayerMatchmakingSystem.swift
//  MotionFitPro
//
//  Created by <PERSON> on 2025-07-15.
//

import Foundation
import Combine

// MARK: - Multiplayer Matchmaking System

@MainActor
class MultiplayerMatchmakingSystem: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var isSearching: Bool = false
    @Published var matchFound: LiveBattle?
    @Published var estimatedWaitTime: TimeInterval = 0
    @Published var currentQueue: MatchmakingQueue?
    @Published var queuePosition: Int = 0
    @Published var availableQuickMatches: [QuickMatchOption] = []
    
    // MARK: - Private Properties
    
    private let networkManager = MultiplayerNetworkManager()
    private let rankingSystem = RankingSystem()
    private var searchTimer: Timer?
    private var queueUpdateTimer: Timer?
    private var cancellables = Set<AnyCancellable>()
    
    // ELO Matchmaking Configuration
    private struct ELOConfig {
        static let baseK: Int = 32
        static let placementK: Int = 50
        static let highRatingK: Int = 16
        static let ratingThreshold: Int = 2000
        
        static let initialSearchRange: Int = 50
        static let maxSearchRange: Int = 300
        static let searchExpansionRate: Int = 25
        static let searchExpansionInterval: TimeInterval = 15.0
        
        static let quickMatchTimeLimit: TimeInterval = 60.0
        static let rankedMatchTimeLimit: TimeInterval = 180.0
    }
    
    init() {
        setupNetworkSubscriptions()
        loadQuickMatchOptions()
    }
    
    // MARK: - Quick Match System
    
    func startQuickMatch(format: BattleFormat, exercise: ExerciseType) async throws {
        guard !isSearching else {
            throw MatchmakingError.alreadySearching
        }
        
        let userProfile = try await getCurrentUserProfile()
        
        // Quick match prioritizes speed over perfect rating match
        let searchCriteria = QuickMatchCriteria(
            userProfile: userProfile,
            format: format,
            exercise: exercise,
            maxRatingDifference: ELOConfig.maxSearchRange,
            timeLimit: ELOConfig.quickMatchTimeLimit
        )
        
        isSearching = true
        
        do {
            // First try to find existing battle needing one more player
            if let existingBattle = try await findExistingQuickMatch(criteria: searchCriteria) {
                try await joinBattle(existingBattle)
                return
            }
            
            // Create new quick match battle
            let battle = try await createQuickMatchBattle(criteria: searchCriteria)
            
            // Start progressive search for opponents
            try await startProgressiveQuickMatchSearch(battle: battle, criteria: searchCriteria)
            
        } catch {
            isSearching = false
            throw error
        }
    }
    
    func startRankedMatch(format: BattleFormat, exercise: ExerciseType) async throws {
        guard !isSearching else {
            throw MatchmakingError.alreadySearching
        }
        
        let userProfile = try await getCurrentUserProfile()
        
        // Ranked match prioritizes fair rating match
        let searchCriteria = RankedMatchCriteria(
            userProfile: userProfile,
            format: format,
            exercise: exercise,
            initialRatingRange: ELOConfig.initialSearchRange,
            maxRatingRange: ELOConfig.maxSearchRange,
            timeLimit: ELOConfig.rankedMatchTimeLimit
        )
        
        isSearching = true
        currentQueue = MatchmakingQueue(type: .ranked, format: format, exercise: exercise)
        
        do {
            try await enterRankedQueue(criteria: searchCriteria)
        } catch {
            isSearching = false
            currentQueue = nil
            throw error
        }
    }
    
    func cancelMatchmaking() {
        isSearching = false
        searchTimer?.invalidate()
        queueUpdateTimer?.invalidate()
        currentQueue = nil
        queuePosition = 0
        
        Task {
            try? await networkManager.leaveMatchmakingQueue()
        }
    }
    
    // MARK: - Custom Challenges
    
    func createCustomChallenge(
        to friendUserId: String,
        format: BattleFormat,
        exercise: ExerciseType,
        message: String? = nil
    ) async throws -> CustomChallenge {
        
        let userProfile = try await getCurrentUserProfile()
        
        let challenge = CustomChallenge(
            id: UUID(),
            fromUserId: userProfile.userId,
            fromUsername: userProfile.username,
            toUserId: friendUserId,
            format: format,
            exercise: exercise,
            message: message,
            createdAt: Date(),
            expiresAt: Date().addingTimeInterval(24 * 3600) // 24 hours
        )
        
        try await networkManager.sendCustomChallenge(challenge)
        return challenge
    }
    
    func acceptCustomChallenge(_ challengeId: UUID) async throws -> LiveBattle {
        let challenge = try await networkManager.getCustomChallenge(challengeId)
        let battle = try await networkManager.acceptCustomChallenge(challenge)
        
        matchFound = battle
        return battle
    }
    
    func declineCustomChallenge(_ challengeId: UUID) async throws {
        try await networkManager.declineCustomChallenge(challengeId)
    }
    
    // MARK: - Progressive Search Implementation
    
    private func startProgressiveQuickMatchSearch(
        battle: LiveBattle,
        criteria: QuickMatchCriteria
    ) async throws {
        
        var currentRange = ELOConfig.initialSearchRange
        let startTime = Date()
        
        startSearchTimer()
        
        while isSearching {
            // Update search criteria with expanded range
            let expandedCriteria = criteria.withExpandedRange(currentRange)
            
            // Search for opponents
            if let opponent = try await findOpponentForQuickMatch(
                battle: battle,
                criteria: expandedCriteria
            ) {
                // Match found!
                try await finalizeQuickMatch(battle: battle, opponent: opponent)
                return
            }
            
            // Check timeout
            if Date().timeIntervalSince(startTime) > criteria.timeLimit {
                throw MatchmakingError.searchTimeout
            }
            
            // Expand search range
            currentRange = min(ELOConfig.maxSearchRange, currentRange + ELOConfig.searchExpansionRate)
            
            // Wait before next search iteration
            try await Task.sleep(nanoseconds: UInt64(ELOConfig.searchExpansionInterval * 1_000_000_000))
        }
        
        throw MatchmakingError.searchCancelled
    }
    
    private func enterRankedQueue(criteria: RankedMatchCriteria) async throws {
        // Join ranked matchmaking queue on server
        let queueEntry = try await networkManager.joinRankedQueue(criteria)
        queuePosition = queueEntry.position
        
        startQueueUpdateTimer()
        
        // Wait for match to be found by server
        // Server will notify us when a suitable match is found
    }
    
    // MARK: - Match Finding Logic
    
    private func findExistingQuickMatch(criteria: QuickMatchCriteria) async throws -> LiveBattle? {
        let availableBattles = try await networkManager.getAvailableQuickMatches(
            format: criteria.format,
            exercise: criteria.exercise
        )
        
        return selectBestQuickMatch(from: availableBattles, criteria: criteria)
    }
    
    private func selectBestQuickMatch(
        from battles: [LiveBattle],
        criteria: QuickMatchCriteria
    ) -> LiveBattle? {
        
        let userRating = criteria.userProfile.skillRating.currentRating
        
        let suitableBattles = battles.filter { battle in
            // Check if battle has space for one more player
            guard battle.participants.count < battle.format.maxParticipants else { return false }
            
            // Check rating compatibility
            let opponentRatings = battle.participants.map { $0.rating }
            let averageOpponentRating = opponentRatings.reduce(0, +) / opponentRatings.count
            let ratingDifference = abs(userRating - averageOpponentRating)
            
            return ratingDifference <= criteria.maxRatingDifference
        }
        
        // Select battle with closest rating match
        return suitableBattles.min { battle1, battle2 in
            let rating1 = battle1.participants.map { $0.rating }.reduce(0, +) / battle1.participants.count
            let rating2 = battle2.participants.map { $0.rating }.reduce(0, +) / battle2.participants.count
            
            let diff1 = abs(userRating - rating1)
            let diff2 = abs(userRating - rating2)
            
            return diff1 < diff2
        }
    }
    
    private func findOpponentForQuickMatch(
        battle: LiveBattle,
        criteria: QuickMatchCriteria
    ) async throws -> BattleParticipant? {
        
        let searchParams = OpponentSearchRequest(
            battleId: battle.id,
            format: criteria.format,
            exercise: criteria.exercise,
            minRating: criteria.userProfile.skillRating.currentRating - criteria.maxRatingDifference,
            maxRating: criteria.userProfile.skillRating.currentRating + criteria.maxRatingDifference,
            excludeUserIds: [criteria.userProfile.userId]
        )
        
        let candidates = try await networkManager.searchForOpponents(searchParams)
        
        // Select best opponent using ELO-based scoring
        return selectBestOpponent(from: candidates, userRating: criteria.userProfile.skillRating.currentRating)
    }
    
    private func selectBestOpponent(
        from candidates: [BattleParticipant],
        userRating: Int
    ) -> BattleParticipant? {
        
        return candidates.min { candidate1, candidate2 in
            let diff1 = abs(userRating - candidate1.rating)
            let diff2 = abs(userRating - candidate2.rating)
            return diff1 < diff2
        }
    }
    
    // MARK: - Battle Creation and Finalization
    
    private func createQuickMatchBattle(criteria: QuickMatchCriteria) async throws -> LiveBattle {
        let battle = LiveBattle(
            id: UUID(),
            format: criteria.format,
            exercise: criteria.exercise,
            battleType: .quickMatch,
            state: .waitingForOpponents,
            createdAt: Date(),
            participants: [
                BattleParticipant(
                    userId: criteria.userProfile.userId,
                    username: criteria.userProfile.username,
                    rating: criteria.userProfile.skillRating.currentRating,
                    league: rankingSystem.getLeague(for: criteria.userProfile.skillRating.currentRating)
                )
            ]
        )
        
        try await networkManager.createBattle(battle)
        return battle
    }
    
    private func finalizeQuickMatch(battle: LiveBattle, opponent: BattleParticipant) async throws {
        var finalBattle = battle
        finalBattle.participants.append(opponent)
        finalBattle.state = .ready
        
        try await networkManager.updateBattle(finalBattle)
        
        isSearching = false
        matchFound = finalBattle
        
        // Notify both players
        NotificationCenter.default.post(
            name: .quickMatchFound,
            object: finalBattle
        )
    }
    
    private func joinBattle(_ battle: LiveBattle) async throws {
        let userProfile = try await getCurrentUserProfile()
        
        let participant = BattleParticipant(
            userId: userProfile.userId,
            username: userProfile.username,
            rating: userProfile.skillRating.currentRating,
            league: rankingSystem.getLeague(for: userProfile.skillRating.currentRating)
        )
        
        try await networkManager.joinBattle(battle.id, participant: participant)
        
        var joinedBattle = battle
        joinedBattle.participants.append(participant)
        joinedBattle.state = .ready
        
        isSearching = false
        matchFound = joinedBattle
    }
    
    // MARK: - Timer Management
    
    private func startSearchTimer() {
        searchTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateSearchProgress()
            }
        }
    }
    
    private func startQueueUpdateTimer() {
        queueUpdateTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.updateQueueStatus()
            }
        }
    }
    
    @MainActor
    private func updateSearchProgress() {
        // Update estimated wait time based on current queue status
        if let queue = currentQueue {
            estimatedWaitTime = calculateEstimatedWaitTime(for: queue)
        }
    }
    
    private func updateQueueStatus() async {
        guard let queue = currentQueue else { return }
        
        do {
            let status = try await networkManager.getQueueStatus(queue.id)
            queuePosition = status.position
            estimatedWaitTime = status.estimatedWaitTime
            
            // Check if match was found
            if let matchId = status.matchId {
                let battle = try await networkManager.getBattle(matchId)
                matchFound = battle
                isSearching = false
                currentQueue = nil
            }
        } catch {
            print("Failed to update queue status: \(error)")
        }
    }
    
    // MARK: - Helper Functions
    
    private func calculateEstimatedWaitTime(for queue: MatchmakingQueue) -> TimeInterval {
        // Base calculation on queue position and historical data
        let baseWaitPerPosition: TimeInterval = 30.0 // 30 seconds per position
        let queueMultiplier: Double = queue.type == .ranked ? 1.5 : 1.0
        
        return Double(queuePosition) * baseWaitPerPosition * queueMultiplier
    }
    
    private func loadQuickMatchOptions() {
        availableQuickMatches = [
            QuickMatchOption(
                format: .speedDemon,
                exercise: .squat,
                playerCount: 2,
                estimatedWaitTime: 15
            ),
            QuickMatchOption(
                format: .formMaster,
                exercise: .pushUp,
                playerCount: 2,
                estimatedWaitTime: 25
            ),
            QuickMatchOption(
                format: .ironWill,
                exercise: .plank,
                playerCount: 4,
                estimatedWaitTime: 45
            ),
            QuickMatchOption(
                format: .mixedMartialFitness,
                exercise: .squat, // Starting exercise
                playerCount: 8,
                estimatedWaitTime: 60
            )
        ]
    }
    
    private func getCurrentUserProfile() async throws -> CompetitiveUserProfile {
        // This would get the current user's profile
        return CompetitiveUserProfile.createDefault()
    }
    
    // MARK: - Network Subscriptions
    
    private func setupNetworkSubscriptions() {
        networkManager.$incomingChallenges
            .receive(on: DispatchQueue.main)
            .sink { [weak self] challenges in
                // Handle incoming custom challenges
                self?.handleIncomingChallenges(challenges)
            }
            .store(in: &cancellables)
        
        networkManager.$matchmakingUpdates
            .receive(on: DispatchQueue.main)
            .sink { [weak self] update in
                self?.handleMatchmakingUpdate(update)
            }
            .store(in: &cancellables)
    }
    
    private func handleIncomingChallenges(_ challenges: [CustomChallenge]) {
        // Post notification for new challenges
        for challenge in challenges where !challenge.isProcessed {
            NotificationCenter.default.post(
                name: .customChallengeReceived,
                object: challenge
            )
        }
    }
    
    private func handleMatchmakingUpdate(_ update: MatchmakingUpdate) {
        switch update.type {
        case .matchFound:
            if let battle = update.battle {
                matchFound = battle
                isSearching = false
                currentQueue = nil
            }
        case .queuePositionChanged:
            queuePosition = update.newPosition ?? 0
        case .searchCancelled:
            cancelMatchmaking()
        }
    }
}

// MARK: - Supporting Data Structures

struct QuickMatchCriteria {
    let userProfile: CompetitiveUserProfile
    let format: BattleFormat
    let exercise: ExerciseType
    let maxRatingDifference: Int
    let timeLimit: TimeInterval
    
    func withExpandedRange(_ newRange: Int) -> QuickMatchCriteria {
        return QuickMatchCriteria(
            userProfile: userProfile,
            format: format,
            exercise: exercise,
            maxRatingDifference: newRange,
            timeLimit: timeLimit
        )
    }
}

struct RankedMatchCriteria {
    let userProfile: CompetitiveUserProfile
    let format: BattleFormat
    let exercise: ExerciseType
    let initialRatingRange: Int
    let maxRatingRange: Int
    let timeLimit: TimeInterval
}

struct MatchmakingQueue: Identifiable {
    let id = UUID()
    let type: QueueType
    let format: BattleFormat
    let exercise: ExerciseType
    let joinedAt = Date()
    
    enum QueueType {
        case quickMatch
        case ranked
        case tournament
    }
}

struct QuickMatchOption: Identifiable {
    let id = UUID()
    let format: BattleFormat
    let exercise: ExerciseType
    let playerCount: Int
    let estimatedWaitTime: TimeInterval
    
    var displayName: String {
        return "\(format.displayName) - \(exercise.displayName)"
    }
}

struct CustomChallenge: Identifiable, Codable {
    let id: UUID
    let fromUserId: String
    let fromUsername: String
    let toUserId: String
    let format: BattleFormat
    let exercise: ExerciseType
    let message: String?
    let createdAt: Date
    let expiresAt: Date
    var status: ChallengeStatus = .pending
    var isProcessed: Bool = false
    
    enum ChallengeStatus: String, Codable {
        case pending = "pending"
        case accepted = "accepted"
        case declined = "declined"
        case expired = "expired"
        case cancelled = "cancelled"
    }
    
    var isExpired: Bool {
        return Date() > expiresAt
    }
}

struct OpponentSearchRequest {
    let battleId: UUID
    let format: BattleFormat
    let exercise: ExerciseType
    let minRating: Int
    let maxRating: Int
    let excludeUserIds: [String]
}

struct QueueStatus {
    let queueId: UUID
    let position: Int
    let estimatedWaitTime: TimeInterval
    let matchId: UUID?
}

struct MatchmakingUpdate {
    let type: UpdateType
    let battle: LiveBattle?
    let newPosition: Int?
    
    enum UpdateType {
        case matchFound
        case queuePositionChanged
        case searchCancelled
    }
}

// MARK: - Network Manager Placeholder

class MultiplayerNetworkManager: ObservableObject {
    @Published var incomingChallenges: [CustomChallenge] = []
    @Published var matchmakingUpdates: MatchmakingUpdate?
    
    func getAvailableQuickMatches(format: BattleFormat, exercise: ExerciseType) async throws -> [LiveBattle] {
        // Implementation would fetch from server
        return []
    }
    
    func searchForOpponents(_ request: OpponentSearchRequest) async throws -> [BattleParticipant] {
        // Implementation would search server for suitable opponents
        return []
    }
    
    func createBattle(_ battle: LiveBattle) async throws {
        // Implementation would create battle on server
    }
    
    func updateBattle(_ battle: LiveBattle) async throws {
        // Implementation would update battle on server
    }
    
    func joinBattle(_ battleId: UUID, participant: BattleParticipant) async throws {
        // Implementation would join battle on server
    }
    
    func getBattle(_ battleId: UUID) async throws -> LiveBattle {
        // Implementation would fetch battle from server
        return LiveBattle(
            id: battleId,
            format: .speedDemon,
            exercise: .squat,
            battleType: .quickMatch,
            state: .ready,
            createdAt: Date(),
            participants: []
        )
    }
    
    func joinRankedQueue(_ criteria: RankedMatchCriteria) async throws -> QueueEntry {
        // Implementation would join ranked queue on server
        return QueueEntry(queueId: UUID(), position: 1)
    }
    
    func leaveMatchmakingQueue() async throws {
        // Implementation would leave queue on server
    }
    
    func getQueueStatus(_ queueId: UUID) async throws -> QueueStatus {
        // Implementation would get queue status from server
        return QueueStatus(queueId: queueId, position: 1, estimatedWaitTime: 30, matchId: nil)
    }
    
    func sendCustomChallenge(_ challenge: CustomChallenge) async throws {
        // Implementation would send challenge to server
    }
    
    func getCustomChallenge(_ challengeId: UUID) async throws -> CustomChallenge {
        // Implementation would fetch challenge from server
        return CustomChallenge(
            id: challengeId,
            fromUserId: "user1",
            fromUsername: "Player1",
            toUserId: "user2",
            format: .speedDemon,
            exercise: .squat,
            message: "Let's battle!"
        )
    }
    
    func acceptCustomChallenge(_ challenge: CustomChallenge) async throws -> LiveBattle {
        // Implementation would accept challenge and create battle
        return LiveBattle(
            id: UUID(),
            format: challenge.format,
            exercise: challenge.exercise,
            battleType: .customChallenge,
            state: .ready,
            createdAt: Date(),
            participants: []
        )
    }
    
    func declineCustomChallenge(_ challengeId: UUID) async throws {
        // Implementation would decline challenge on server
    }
}

struct QueueEntry {
    let queueId: UUID
    let position: Int
}

// MARK: - Errors

enum MatchmakingError: LocalizedError {
    case alreadySearching
    case searchTimeout
    case searchCancelled
    case noOpponentsFound
    case networkError(String)
    case invalidChallenge
    
    var errorDescription: String? {
        switch self {
        case .alreadySearching:
            return "Already searching for a match"
        case .searchTimeout:
            return "Search timed out"
        case .searchCancelled:
            return "Search was cancelled"
        case .noOpponentsFound:
            return "No suitable opponents found"
        case .networkError(let message):
            return "Network error: \(message)"
        case .invalidChallenge:
            return "Invalid challenge"
        }
    }
}

// MARK: - Notifications

extension Notification.Name {
    static let quickMatchFound = Notification.Name("quickMatchFound")
    static let customChallengeReceived = Notification.Name("customChallengeReceived")
    static let rankedMatchFound = Notification.Name("rankedMatchFound")
}