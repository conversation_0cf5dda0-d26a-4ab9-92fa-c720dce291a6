import Foundation
import Combine
import simd

@MainActor
class AICoachingService: ObservableObject {
    @Published var currentCoachingFeedback: CoachingFeedback?
    @Published var isAnalyzing = false
    @Published var coachingSettings = CoachingSettings()
    
    // Coaching state tracking
    private var exerciseHistory: [ExerciseAnalysis] = []
    private var currentExerciseData: ExerciseAnalysis?
    private var lastFeedbackTime: Date = Date()
    private var feedbackCooldown: TimeInterval = 3.0
    private var userSkillLevel: SkillLevel = .beginner
    private var workoutIntensity: Double = 0.5
    
    // Dependencies
    private let speechManager: SpeechSynthesisManager
    private let hapticManager: HapticFeedbackManager
    private let feedbackGenerator: FeedbackGenerator
    private let logger = Logger.shared
    
    private var cancellables = Set<AnyCancellable>()
    
    init(speechManager: SpeechSynthesisManager, hapticManager: HapticFeedbackManager) {
        self.speechManager = speechManager
        self.hapticManager = hapticManager
        self.feedbackGenerator = FeedbackGenerator()
        
        setupBindings()
        loadUserPreferences()
    }
    
    // MARK: - Public Methods
    
    /// Analyzes pose data and provides real-time coaching feedback
    func analyzePose(_ poseData: BodyPoseData, for exercise: ExerciseType, repPhase: RepPhase) {
        guard coachingSettings.isEnabled else { return }
        
        isAnalyzing = true
        
        Task {
            defer { await MainActor.run { isAnalyzing = false } }
            
            let analysis = await performFormAnalysis(poseData, exercise: exercise, repPhase: repPhase)
            
            await MainActor.run {
                processAnalysisResults(analysis, exercise: exercise, repPhase: repPhase)
            }
        }
    }
    
    /// Called when a repetition is completed
    func onRepetitionCompleted(analysis: ExerciseAnalysis, repNumber: Int, setNumber: Int) {
        logger.info("Rep completed: \(repNumber), Set: \(setNumber)", category: .workout)
        
        // Store analysis for learning
        exerciseHistory.append(analysis)
        
        // Generate celebration or improvement feedback
        let feedback = feedbackGenerator.generateRepCompletionFeedback(
            analysis: analysis,
            repNumber: repNumber,
            setNumber: setNumber,
            personality: coachingSettings.personality,
            skillLevel: userSkillLevel
        )
        
        deliverFeedback(feedback)
        
        // Update user skill level based on performance
        updateSkillLevel(from: analysis)
    }
    
    /// Called when a set is completed
    func onSetCompleted(setAnalysis: SetAnalysis, setNumber: Int, totalSets: Int) {
        logger.info("Set completed: \(setNumber)/\(totalSets)", category: .workout)
        
        let feedback = feedbackGenerator.generateSetCompletionFeedback(
            setAnalysis: setAnalysis,
            setNumber: setNumber,
            totalSets: totalSets,
            personality: coachingSettings.personality
        )
        
        deliverFeedback(feedback)
        
        // Provide rest guidance
        if setNumber < totalSets {
            provideRestGuidance(based: setAnalysis)
        }
    }
    
    /// Called when user starts resting between sets
    func onRestStarted(recommendedDuration: TimeInterval) {
        let feedback = feedbackGenerator.generateRestStartFeedback(
            duration: recommendedDuration,
            personality: coachingSettings.personality,
            workoutIntensity: workoutIntensity
        )
        
        deliverFeedback(feedback)
    }
    
    /// Called during rest periods to provide guidance
    func onRestProgress(timeRemaining: TimeInterval, totalRestTime: TimeInterval) {
        let progress = 1.0 - (timeRemaining / totalRestTime)
        
        // Provide guidance at specific intervals
        if progress > 0.5 && progress < 0.6 {
            let feedback = feedbackGenerator.generateRestProgressFeedback(
                timeRemaining: timeRemaining,
                personality: coachingSettings.personality
            )
            deliverFeedback(feedback)
        }
    }
    
    /// Updates coaching settings
    func updateSettings(_ newSettings: CoachingSettings) {
        coachingSettings = newSettings
        speechManager.updateSettings(newSettings.speechSettings)
        saveUserPreferences()
        logger.info("Coaching settings updated", category: .ui)
    }
    
    /// Provides safety warning for dangerous movement patterns
    func provideSafetyWarning(_ warning: SafetyWarning) {
        let feedback = CoachingFeedback(
            message: warning.message,
            type: .safety,
            priority: .immediate,
            personality: coachingSettings.personality,
            deliveryMethod: [.speech, .haptic, .visual]
        )
        
        // Safety warnings bypass cooldown
        deliverFeedbackImmediately(feedback)
        logger.warning("Safety warning delivered: \(warning.message)", category: .workout)
    }
    
    // MARK: - Private Methods
    
    private func setupBindings() {
        // Listen for speech synthesis completion to manage feedback queue
        speechManager.$isSpeaking
            .sink { [weak self] isSpeaking in
                if !isSpeaking {
                    self?.processNextFeedback()
                }
            }
            .store(in: &cancellables)
    }
    
    private func performFormAnalysis(_ poseData: BodyPoseData, exercise: ExerciseType, repPhase: RepPhase) async -> ExerciseAnalysis {
        // Simulate form analysis - in production, this would use ML models
        await Task.sleep(nanoseconds: 50_000_000) // 50ms processing time
        
        let formScore = calculateFormScore(poseData, exercise: exercise, repPhase: repPhase)
        let issues = identifyFormIssues(poseData, exercise: exercise, repPhase: repPhase)
        let safetyRisk = assessSafetyRisk(poseData, exercise: exercise)
        
        return ExerciseAnalysis(
            exercise: exercise,
            timestamp: Date(),
            formScore: formScore,
            repPhase: repPhase,
            poseData: poseData,
            detectedIssues: issues,
            safetyRisk: safetyRisk,
            confidence: poseData.confidence
        )
    }
    
    private func calculateFormScore(_ poseData: BodyPoseData, exercise: ExerciseType, repPhase: RepPhase) -> Double {
        // Simplified form scoring - would be more sophisticated in production
        let baseScore = Double(poseData.confidence * 100)
        
        switch exercise {
        case .squat:
            return calculateSquatFormScore(poseData, repPhase: repPhase, baseScore: baseScore)
        case .pushUp:
            return calculatePushUpFormScore(poseData, repPhase: repPhase, baseScore: baseScore)
        case .plank:
            return calculatePlankFormScore(poseData, baseScore: baseScore)
        default:
            return baseScore
        }
    }
    
    private func calculateSquatFormScore(_ poseData: BodyPoseData, repPhase: RepPhase, baseScore: Double) -> Double {
        var score = baseScore
        
        // Check knee alignment
        if let leftKnee = poseData.joints["left_leg"],
           let rightKnee = poseData.joints["right_leg"],
           let leftAnkle = poseData.joints["left_foot"],
           let rightAnkle = poseData.joints["right_foot"] {
            
            // Simplified knee tracking check
            let kneeAlignment = abs(leftKnee.position.x - rightKnee.position.x)
            if kneeAlignment > 0.3 { // Knees too far apart
                score *= 0.9
            }
        }
        
        // Check depth for squat bottom phase
        if repPhase == .bottom {
            if let hip = poseData.joints["root"],
               let knee = poseData.joints["left_leg"] {
                let depth = hip.position.y - knee.position.y
                if depth < 0.2 { // Not deep enough
                    score *= 0.8
                }
            }
        }
        
        return max(score, 60.0) // Minimum score of 60
    }
    
    private func calculatePushUpFormScore(_ poseData: BodyPoseData, repPhase: RepPhase, baseScore: Double) -> Double {
        var score = baseScore
        
        // Check body alignment
        if let head = poseData.joints["head"],
           let spine = poseData.joints["spine_7"],
           let hip = poseData.joints["root"] {
            
            // Check if body is in straight line
            let headToSpine = head.position.y - spine.position.y
            let spineToHip = spine.position.y - hip.position.y
            
            if abs(headToSpine - spineToHip) > 0.2 {
                score *= 0.85
            }
        }
        
        return max(score, 65.0)
    }
    
    private func calculatePlankFormScore(_ poseData: BodyPoseData, baseScore: Double) -> Double {
        var score = baseScore
        
        // Check planking form
        if let shoulders = poseData.joints["spine_7"],
           let hip = poseData.joints["root"],
           let knee = poseData.joints["left_leg"] {
            
            // Check if body is straight
            let shoulderToHip = shoulders.position.y - hip.position.y
            let hipToKnee = hip.position.y - knee.position.y
            
            if abs(shoulderToHip) > 0.15 || abs(hipToKnee) > 0.15 {
                score *= 0.8
            }
        }
        
        return max(score, 70.0)
    }
    
    private func identifyFormIssues(_ poseData: BodyPoseData, exercise: ExerciseType, repPhase: RepPhase) -> [FormIssue] {
        var issues: [FormIssue] = []
        
        switch exercise {
        case .squat:
            issues.append(contentsOf: identifySquatIssues(poseData, repPhase: repPhase))
        case .pushUp:
            issues.append(contentsOf: identifyPushUpIssues(poseData, repPhase: repPhase))
        case .plank:
            issues.append(contentsOf: identifyPlankIssues(poseData))
        default:
            break
        }
        
        return issues
    }
    
    private func identifySquatIssues(_ poseData: BodyPoseData, repPhase: RepPhase) -> [FormIssue] {
        var issues: [FormIssue] = []
        
        // Check for knee cave
        if let leftKnee = poseData.joints["left_leg"],
           let rightKnee = poseData.joints["right_leg"] {
            let kneeDistance = abs(leftKnee.position.x - rightKnee.position.x)
            if kneeDistance < 0.3 {
                issues.append(FormIssue(
                    type: .kneeAlignment,
                    severity: .moderate,
                    description: "Knees are caving inward",
                    correction: "Push your knees out over your toes"
                ))
            }
        }
        
        // Check squat depth
        if repPhase == .bottom {
            if let hip = poseData.joints["root"],
               let knee = poseData.joints["left_leg"] {
                let depth = hip.position.y - knee.position.y
                if depth < 0.15 {
                    issues.append(FormIssue(
                        type: .rangeOfMotion,
                        severity: .minor,
                        description: "Not reaching full squat depth",
                        correction: "Lower down until your thighs are parallel to the ground"
                    ))
                }
            }
        }
        
        return issues
    }
    
    private func identifyPushUpIssues(_ poseData: BodyPoseData, repPhase: RepPhase) -> [FormIssue] {
        var issues: [FormIssue] = []
        
        // Check for sagging hips
        if let spine = poseData.joints["spine_7"],
           let hip = poseData.joints["root"] {
            if hip.position.y < spine.position.y - 0.2 {
                issues.append(FormIssue(
                    type: .coreEngagement,
                    severity: .moderate,
                    description: "Hips are sagging",
                    correction: "Engage your core and keep your body in a straight line"
                ))
            }
        }
        
        return issues
    }
    
    private func identifyPlankIssues(_ poseData: BodyPoseData) -> [FormIssue] {
        var issues: [FormIssue] = []
        
        // Check for hip position
        if let shoulders = poseData.joints["spine_7"],
           let hip = poseData.joints["root"] {
            if hip.position.y > shoulders.position.y + 0.15 {
                issues.append(FormIssue(
                    type: .alignment,
                    severity: .minor,
                    description: "Hips are too high",
                    correction: "Lower your hips to create a straight line from head to heels"
                ))
            }
        }
        
        return issues
    }
    
    private func assessSafetyRisk(_ poseData: BodyPoseData, exercise: ExerciseType) -> SafetyRisk {
        // Check for dangerous movement patterns
        var riskLevel: SafetyRisk = .none
        
        // Check joint confidence levels
        let lowConfidenceJoints = poseData.joints.values.filter { $0.confidence < 0.5 }
        if lowConfidenceJoints.count > 3 {
            riskLevel = .low
        }
        
        // Exercise-specific safety checks
        switch exercise {
        case .squat:
            riskLevel = max(riskLevel, assessSquatSafety(poseData))
        case .pushUp:
            riskLevel = max(riskLevel, assessPushUpSafety(poseData))
        default:
            break
        }
        
        return riskLevel
    }
    
    private func assessSquatSafety(_ poseData: BodyPoseData) -> SafetyRisk {
        // Check for extreme knee position
        if let leftKnee = poseData.joints["left_leg"],
           let rightKnee = poseData.joints["right_leg"] {
            let kneeDistance = abs(leftKnee.position.x - rightKnee.position.x)
            if kneeDistance > 0.8 { // Knees too far apart
                return .high
            }
        }
        
        return .none
    }
    
    private func assessPushUpSafety(_ poseData: BodyPoseData) -> SafetyRisk {
        // Check for extreme spine curvature
        if let head = poseData.joints["head"],
           let spine = poseData.joints["spine_7"],
           let hip = poseData.joints["root"] {
            
            let spineAngle = atan2(head.position.y - spine.position.y, head.position.x - spine.position.x)
            let hipAngle = atan2(spine.position.y - hip.position.y, spine.position.x - hip.position.x)
            
            if abs(spineAngle - hipAngle) > 0.5 { // Significant misalignment
                return .moderate
            }
        }
        
        return .none
    }
    
    private func processAnalysisResults(_ analysis: ExerciseAnalysis, exercise: ExerciseType, repPhase: RepPhase) {
        currentExerciseData = analysis
        
        // Check if feedback is needed
        guard shouldProvideFeedback(analysis) else { return }
        
        // Generate contextual feedback
        let feedback = feedbackGenerator.generateFormFeedback(
            analysis: analysis,
            personality: coachingSettings.personality,
            skillLevel: userSkillLevel,
            previousFeedback: currentCoachingFeedback
        )
        
        if let feedback = feedback {
            deliverFeedback(feedback)
        }
        
        // Handle safety warnings
        if analysis.safetyRisk == .high {
            let warning = SafetyWarning(
                level: .high,
                message: "Stop the exercise! Check your form to prevent injury.",
                exercise: exercise
            )
            provideSafetyWarning(warning)
        }
    }
    
    private func shouldProvideFeedback(_ analysis: ExerciseAnalysis) -> Bool {
        // Don't provide feedback too frequently
        let timeSinceLastFeedback = Date().timeIntervalSince(lastFeedbackTime)
        guard timeSinceLastFeedback >= feedbackCooldown else { return false }
        
        // Provide feedback for significant form issues
        if analysis.formScore < 75 && !analysis.detectedIssues.isEmpty {
            return true
        }
        
        // Provide encouragement for good form
        if analysis.formScore > 90 && timeSinceLastFeedback > 10 {
            return true
        }
        
        return false
    }
    
    private func deliverFeedback(_ feedback: CoachingFeedback) {
        guard Date().timeIntervalSince(lastFeedbackTime) >= feedbackCooldown else { return }
        
        deliverFeedbackImmediately(feedback)
    }
    
    private func deliverFeedbackImmediately(_ feedback: CoachingFeedback) {
        currentCoachingFeedback = feedback
        lastFeedbackTime = Date()
        
        // Deliver through selected channels
        if feedback.deliveryMethod.contains(.speech) && coachingSettings.speechSettings.isEnabled {
            speechManager.speak(feedback.message, priority: feedback.priority)
        }
        
        if feedback.deliveryMethod.contains(.haptic) && coachingSettings.hapticEnabled {
            hapticManager.deliverFeedback(for: feedback.type)
        }
        
        if feedback.deliveryMethod.contains(.visual) {
            // Visual feedback is handled by the UI through published property
        }
        
        logger.info("Coaching feedback delivered: \(feedback.message)", category: .workout)
    }
    
    private func processNextFeedback() {
        // Implementation for feedback queue management
        // This would handle queued feedback when speech synthesis completes
    }
    
    private func provideRestGuidance(based setAnalysis: SetAnalysis) {
        let restRecommendation = calculateRestRecommendation(setAnalysis)
        
        let feedback = CoachingFeedback(
            message: "Take \(Int(restRecommendation)) seconds to recover. \(getRestTip())",
            type: .encouragement,
            priority: .normal,
            personality: coachingSettings.personality,
            deliveryMethod: [.speech]
        )
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.deliverFeedback(feedback)
        }
    }
    
    private func calculateRestRecommendation(_ setAnalysis: SetAnalysis) -> TimeInterval {
        // Base rest time on set performance
        let baseRest: TimeInterval = 60
        let intensityMultiplier = setAnalysis.averageFormScore < 80 ? 1.2 : 1.0
        
        return baseRest * intensityMultiplier
    }
    
    private func getRestTip() -> String {
        let tips = [
            "Focus on your breathing.",
            "Hydrate and prepare for the next set.",
            "Visualize perfect form for your next set.",
            "Shake out your muscles and stay loose."
        ]
        return tips.randomElement() ?? ""
    }
    
    private func updateSkillLevel(from analysis: ExerciseAnalysis) {
        // Update skill level based on consistent performance
        exerciseHistory.append(analysis)
        
        // Keep only recent history
        let recentCount = 20
        if exerciseHistory.count > recentCount {
            exerciseHistory = Array(exerciseHistory.suffix(recentCount))
        }
        
        // Calculate average performance over recent history
        let recentAverage = exerciseHistory.reduce(0) { $0 + $1.formScore } / Double(exerciseHistory.count)
        
        // Update skill level based on performance
        if recentAverage > 85 && userSkillLevel == .beginner {
            userSkillLevel = .intermediate
        } else if recentAverage > 90 && userSkillLevel == .intermediate {
            userSkillLevel = .advanced
        } else if recentAverage < 70 && userSkillLevel == .advanced {
            userSkillLevel = .intermediate
        } else if recentAverage < 60 && userSkillLevel == .intermediate {
            userSkillLevel = .beginner
        }
    }
    
    private func loadUserPreferences() {
        // Load coaching preferences from UserDefaults
        if let data = UserDefaults.standard.data(forKey: "CoachingSettings"),
           let settings = try? JSONDecoder().decode(CoachingSettings.self, data: data) {
            coachingSettings = settings
        }
        
        if let skillData = UserDefaults.standard.data(forKey: "UserSkillLevel"),
           let skill = try? JSONDecoder().decode(SkillLevel.self, data: skill) {
            userSkillLevel = skill
        }
    }
    
    private func saveUserPreferences() {
        if let data = try? JSONEncoder().encode(coachingSettings) {
            UserDefaults.standard.set(data, forKey: "CoachingSettings")
        }
        
        if let skillData = try? JSONEncoder().encode(userSkillLevel) {
            UserDefaults.standard.set(skillData, forKey: "UserSkillLevel")
        }
    }
}

// MARK: - Supporting Types

enum RepPhase {
    case preparation
    case eccentric // Lowering phase
    case bottom
    case concentric // Lifting phase
    case completion
}

enum SkillLevel: Codable {
    case beginner
    case intermediate
    case advanced
}

enum SafetyRisk: Comparable {
    case none
    case low
    case moderate
    case high
}

struct ExerciseAnalysis {
    let exercise: ExerciseType
    let timestamp: Date
    let formScore: Double
    let repPhase: RepPhase
    let poseData: BodyPoseData
    let detectedIssues: [FormIssue]
    let safetyRisk: SafetyRisk
    let confidence: Float
}

struct SetAnalysis {
    let reps: [ExerciseAnalysis]
    let averageFormScore: Double
    let totalDuration: TimeInterval
    let detectedPatterns: [String]
    
    init(reps: [ExerciseAnalysis]) {
        self.reps = reps
        self.averageFormScore = reps.isEmpty ? 0 : reps.reduce(0) { $0 + $1.formScore } / Double(reps.count)
        self.totalDuration = reps.last?.timestamp.timeIntervalSince(reps.first?.timestamp ?? Date()) ?? 0
        self.detectedPatterns = [] // Would analyze patterns in production
    }
}

struct FormIssue {
    let type: FormIssueType
    let severity: IssueSeverity
    let description: String
    let correction: String
    
    enum FormIssueType {
        case kneeAlignment
        case rangeOfMotion
        case coreEngagement
        case alignment
        case tempo
        case balance
    }
    
    enum IssueSeverity {
        case minor
        case moderate
        case major
    }
}

struct SafetyWarning {
    let level: SafetyLevel
    let message: String
    let exercise: ExerciseType
    
    enum SafetyLevel {
        case low
        case moderate
        case high
        case critical
    }
}