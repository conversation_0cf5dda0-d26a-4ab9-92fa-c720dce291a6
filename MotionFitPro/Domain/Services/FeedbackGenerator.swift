import Foundation

/// Generates context-aware coaching feedback based on exercise analysis and user preferences
class FeedbackGenerator {
    
    private var lastFeedbackContext: FeedbackContext?
    private var feedbackHistory: [TimestampedFeedback] = []
    private let maxHistoryLength = 10
    
    // MARK: - Public Methods
    
    /// Generates real-time form feedback during exercise execution
    func generateFormFeedback(
        analysis: ExerciseAnalysis,
        personality: CoachingPersonality,
        skillLevel: SkillLevel,
        previousFeedback: CoachingFeedback?
    ) -> CoachingFeedback? {
        
        // Determine if feedback is needed
        guard shouldProvideFeedback(analysis: analysis, previousFeedback: previousFeedback) else {
            return nil
        }
        
        let context = determineFeedbackContext(analysis: analysis)
        
        // Avoid repetitive feedback
        if isRecentlyProvidedFeedback(context: context) {
            return nil
        }
        
        let feedback = createFormFeedback(
            analysis: analysis,
            context: context,
            personality: personality,
            skillLevel: skillLevel
        )
        
        recordFeedback(feedback, context: context)
        return feedback
    }
    
    /// Generates feedback when a repetition is completed
    func generateRepCompletionFeedback(
        analysis: ExerciseAnalysis,
        repNumber: Int,
        setNumber: Int,
        personality: CoachingPersonality,
        skillLevel: SkillLevel
    ) -> CoachingFeedback {
        
        let context = FeedbackContext.repCompletion(
            repNumber: repNumber,
            setNumber: setNumber,
            formScore: analysis.formScore
        )
        
        let message = generateRepCompletionMessage(
            analysis: analysis,
            repNumber: repNumber,
            setNumber: setNumber,
            personality: personality,
            skillLevel: skillLevel
        )
        
        let deliveryMethods = determineDeliveryMethods(
            for: .encouragement,
            personality: personality,
            urgency: .normal
        )
        
        let feedback = CoachingFeedback(
            message: message,
            type: .encouragement,
            priority: .normal,
            personality: personality,
            deliveryMethod: deliveryMethods
        )
        
        recordFeedback(feedback, context: context)
        return feedback
    }
    
    /// Generates feedback when a set is completed
    func generateSetCompletionFeedback(
        setAnalysis: SetAnalysis,
        setNumber: Int,
        totalSets: Int,
        personality: CoachingPersonality
    ) -> CoachingFeedback {
        
        let context = FeedbackContext.setCompletion(
            setNumber: setNumber,
            totalSets: totalSets,
            averageFormScore: setAnalysis.averageFormScore
        )
        
        let message = generateSetCompletionMessage(
            setAnalysis: setAnalysis,
            setNumber: setNumber,
            totalSets: totalSets,
            personality: personality
        )
        
        let deliveryMethods = determineDeliveryMethods(
            for: .celebration,
            personality: personality,
            urgency: .normal
        )
        
        let feedback = CoachingFeedback(
            message: message,
            type: .celebration,
            priority: .normal,
            personality: personality,
            deliveryMethod: deliveryMethods
        )
        
        recordFeedback(feedback, context: context)
        return feedback
    }
    
    /// Generates feedback when rest period starts
    func generateRestStartFeedback(
        duration: TimeInterval,
        personality: CoachingPersonality,
        workoutIntensity: Double
    ) -> CoachingFeedback {
        
        let restContext: RestContext = workoutIntensity > 0.8 ? .recovery : .betweenSets
        
        let message = CoachingPersonalityManager.getRestGuidanceMessage(
            personality: personality,
            duration: duration,
            context: restContext
        )
        
        let deliveryMethods = determineDeliveryMethods(
            for: .rest,
            personality: personality,
            urgency: .low
        )
        
        return CoachingFeedback(
            message: message,
            type: .rest,
            priority: .low,
            personality: personality,
            deliveryMethod: deliveryMethods
        )
    }
    
    /// Generates feedback during rest periods
    func generateRestProgressFeedback(
        timeRemaining: TimeInterval,
        personality: CoachingPersonality
    ) -> CoachingFeedback {
        
        let message = generateRestProgressMessage(
            timeRemaining: timeRemaining,
            personality: personality
        )
        
        let deliveryMethods = determineDeliveryMethods(
            for: .information,
            personality: personality,
            urgency: .low
        )
        
        return CoachingFeedback(
            message: message,
            type: .information,
            priority: .low,
            personality: personality,
            deliveryMethod: deliveryMethods
        )
    }
    
    /// Generates motivational feedback based on workout progress
    func generateMotivationalFeedback(
        workoutProgress: Double,
        currentEnergy: EnergyLevel,
        personality: CoachingPersonality,
        skillLevel: SkillLevel
    ) -> CoachingFeedback? {
        
        guard currentEnergy == .low && workoutProgress > 0.5 else {
            return nil
        }
        
        let message = generateMotivationalMessage(
            workoutProgress: workoutProgress,
            personality: personality,
            skillLevel: skillLevel
        )
        
        let deliveryMethods = determineDeliveryMethods(
            for: .encouragement,
            personality: personality,
            urgency: .high
        )
        
        return CoachingFeedback(
            message: message,
            type: .encouragement,
            priority: .high,
            personality: personality,
            deliveryMethod: deliveryMethods
        )
    }
    
    /// Generates adaptive difficulty suggestions
    func generateDifficultySuggestion(
        currentPerformance: Double,
        historicalPerformance: [Double],
        personality: CoachingPersonality,
        skillLevel: SkillLevel
    ) -> CoachingFeedback? {
        
        let suggestion = analyzeDifficultyAdjustment(
            current: currentPerformance,
            historical: historicalPerformance,
            skillLevel: skillLevel
        )
        
        guard let suggestion = suggestion else { return nil }
        
        let message = formatDifficultySuggestion(
            suggestion: suggestion,
            personality: personality
        )
        
        let deliveryMethods = determineDeliveryMethods(
            for: .information,
            personality: personality,
            urgency: .normal
        )
        
        return CoachingFeedback(
            message: message,
            type: .information,
            priority: .normal,
            personality: personality,
            deliveryMethod: deliveryMethods
        )
    }
    
    // MARK: - Private Methods
    
    private func shouldProvideFeedback(
        analysis: ExerciseAnalysis,
        previousFeedback: CoachingFeedback?
    ) -> Bool {
        
        // Always provide feedback for safety issues
        if analysis.safetyRisk != .none {
            return true
        }
        
        // Provide feedback for significant form issues
        if analysis.formScore < 70 && !analysis.detectedIssues.isEmpty {
            return true
        }
        
        // Provide encouragement for excellent form (but not too frequently)
        if analysis.formScore > 90 {
            let timeSinceLastFeedback = previousFeedback?.timestamp.timeIntervalSinceNow ?? -10
            return abs(timeSinceLastFeedback) > 8
        }
        
        // Provide feedback for moderate issues occasionally
        if analysis.formScore < 80 && !analysis.detectedIssues.isEmpty {
            return Double.random(in: 0...1) > 0.7 // 30% chance
        }
        
        return false
    }
    
    private func determineFeedbackContext(analysis: ExerciseAnalysis) -> FeedbackContext {
        if analysis.safetyRisk != .none {
            return .safety(risk: analysis.safetyRisk)
        }
        
        if analysis.formScore > 90 {
            return .excellentForm
        }
        
        if !analysis.detectedIssues.isEmpty {
            let primaryIssue = analysis.detectedIssues.first!
            return .formCorrection(issue: primaryIssue)
        }
        
        return .general
    }
    
    private func isRecentlyProvidedFeedback(context: FeedbackContext) -> Bool {
        return feedbackHistory.contains { feedback in
            feedback.timestamp.timeIntervalSinceNow > -5.0 && // Within last 5 seconds
            feedback.context.isSimilar(to: context)
        }
    }
    
    private func createFormFeedback(
        analysis: ExerciseAnalysis,
        context: FeedbackContext,
        personality: CoachingPersonality,
        skillLevel: SkillLevel
    ) -> CoachingFeedback {
        
        let message: String
        let feedbackType: CoachingFeedback.FeedbackType
        let priority: CoachingPriority
        
        switch context {
        case .safety(let risk):
            message = generateSafetyMessage(risk: risk, analysis: analysis, personality: personality)
            feedbackType = .safety
            priority = .immediate
            
        case .formCorrection(let issue):
            message = CoachingPersonalityManager.getFormCorrectionMessage(
                personality: personality,
                issue: issue,
                skillLevel: skillLevel
            )
            feedbackType = .formCorrection
            priority = .high
            
        case .excellentForm:
            message = CoachingPersonalityManager.getEncouragementMessage(
                personality: personality,
                context: .goodForm,
                skillLevel: skillLevel
            )
            feedbackType = .encouragement
            priority = .normal
            
        case .general:
            message = generateGeneralEncouragement(personality: personality, skillLevel: skillLevel)
            feedbackType = .encouragement
            priority = .low
            
        default:
            message = "Keep it up!"
            feedbackType = .encouragement
            priority = .low
        }
        
        let deliveryMethods = determineDeliveryMethods(
            for: feedbackType,
            personality: personality,
            urgency: priority
        )
        
        return CoachingFeedback(
            message: message,
            type: feedbackType,
            priority: priority,
            personality: personality,
            deliveryMethod: deliveryMethods
        )
    }
    
    private func generateRepCompletionMessage(
        analysis: ExerciseAnalysis,
        repNumber: Int,
        setNumber: Int,
        personality: CoachingPersonality,
        skillLevel: SkillLevel
    ) -> String {
        
        // Milestone reps get special messages
        if repNumber % 5 == 0 || repNumber >= 10 {
            return CoachingPersonalityManager.getEncouragementMessage(
                personality: personality,
                context: .improvement,
                skillLevel: skillLevel
            )
        }
        
        // Good form gets encouragement
        if analysis.formScore > 85 {
            return CoachingPersonalityManager.getEncouragementMessage(
                personality: personality,
                context: .goodForm,
                skillLevel: skillLevel
            )
        }
        
        // Standard rep completion
        return CoachingPersonalityManager.getEncouragementMessage(
            personality: personality,
            context: .repCompletion,
            skillLevel: skillLevel
        )
    }
    
    private func generateSetCompletionMessage(
        setAnalysis: SetAnalysis,
        setNumber: Int,
        totalSets: Int,
        personality: CoachingPersonality
    ) -> String {
        
        let baseMessage = CoachingPersonalityManager.getEncouragementMessage(
            personality: personality,
            context: .setCompletion,
            skillLevel: .intermediate // Default for set completion
        )
        
        // Add specific feedback about set performance
        if setAnalysis.averageFormScore > 90 {
            let perfectFormAddition = " Your form was outstanding throughout that entire set!"
            return baseMessage + perfectFormAddition
        }
        
        if setNumber == totalSets {
            let finalSetAddition = " You've completed your final set!"
            return baseMessage + finalSetAddition
        }
        
        return baseMessage
    }
    
    private func generateRestProgressMessage(
        timeRemaining: TimeInterval,
        personality: CoachingPersonality
    ) -> String {
        
        let seconds = Int(timeRemaining)
        
        switch personality {
        case .supportive:
            return "Just \(seconds) more seconds of rest. You're doing wonderfully!"
        case .technical:
            return "\(seconds) seconds remaining for optimal recovery."
        case .motivational:
            return "\(seconds) seconds left! Get ready to DOMINATE the next set!"
        case .safetyFirst:
            return "\(seconds) seconds of rest remaining. Take the time you need."
        }
    }
    
    private func generateMotivationalMessage(
        workoutProgress: Double,
        personality: CoachingPersonality,
        skillLevel: SkillLevel
    ) -> String {
        
        let progressPercent = Int(workoutProgress * 100)
        
        switch personality {
        case .supportive:
            return "You're \(progressPercent)% through! I believe in you! You can do this!"
        case .technical:
            return "Workout progress: \(progressPercent)%. Maintain focus for optimal results."
        case .motivational:
            return "You're \(progressPercent)% DONE! Push through! Victory is close!"
        case .safetyFirst:
            return "You're \(progressPercent)% complete. Listen to your body and keep going safely."
        }
    }
    
    private func generateSafetyMessage(
        risk: SafetyRisk,
        analysis: ExerciseAnalysis,
        personality: CoachingPersonality
    ) -> String {
        
        let warning = SafetyWarning(
            level: .high,
            message: "Please check your form to prevent injury",
            exercise: analysis.exercise
        )
        
        return CoachingPersonalityManager.getSafetyWarningMessage(
            personality: personality,
            warning: warning
        )
    }
    
    private func generateGeneralEncouragement(
        personality: CoachingPersonality,
        skillLevel: SkillLevel
    ) -> String {
        
        return CoachingPersonalityManager.getEncouragementMessage(
            personality: personality,
            context: .repCompletion,
            skillLevel: skillLevel
        )
    }
    
    private func determineDeliveryMethods(
        for type: CoachingFeedback.FeedbackType,
        personality: CoachingPersonality,
        urgency: CoachingPriority
    ) -> Set<CoachingFeedback.DeliveryMethod> {
        
        var methods: Set<CoachingFeedback.DeliveryMethod> = []
        
        // Safety feedback always uses all channels
        if type == .safety {
            return [.speech, .haptic, .visual]
        }
        
        // High priority feedback uses speech + haptic
        if urgency == .immediate || urgency == .high {
            methods.insert(.speech)
            methods.insert(.haptic)
        }
        
        // Normal feedback uses speech
        if urgency == .normal {
            methods.insert(.speech)
        }
        
        // Visual feedback for form corrections
        if type == .formCorrection {
            methods.insert(.visual)
        }
        
        // Celebrations use all methods for motivational personality
        if type == .celebration && personality == .motivational {
            methods.insert(.haptic)
        }
        
        // Always include visual as backup
        methods.insert(.visual)
        
        return methods
    }
    
    private func analyzeDifficultyAdjustment(
        current: Double,
        historical: [Double],
        skillLevel: SkillLevel
    ) -> DifficultyAdjustment? {
        
        guard historical.count >= 3 else { return nil }
        
        let recentAverage = historical.suffix(3).reduce(0, +) / 3.0
        let overallAverage = historical.reduce(0, +) / Double(historical.count)
        
        // Suggest increase if consistently performing well
        if current > 90 && recentAverage > 88 && overallAverage > 85 {
            return .increase
        }
        
        // Suggest decrease if struggling
        if current < 70 && recentAverage < 75 {
            return .decrease
        }
        
        // Suggest maintaining current level
        if current > 80 && recentAverage > 78 {
            return .maintain
        }
        
        return nil
    }
    
    private func formatDifficultySuggestion(
        suggestion: DifficultyAdjustment,
        personality: CoachingPersonality
    ) -> String {
        
        switch (suggestion, personality) {
        case (.increase, .supportive):
            return "You're doing so well! Maybe it's time to challenge yourself a bit more?"
        case (.increase, .technical):
            return "Performance data suggests you're ready for increased difficulty."
        case (.increase, .motivational):
            return "You're CRUSHING this! Time to level up the challenge!"
        case (.increase, .safetyFirst):
            return "Your form is consistent. Consider a small increase when you feel ready."
            
        case (.decrease, .supportive):
            return "It's okay to dial it back a bit. Progress isn't always linear."
        case (.decrease, .technical):
            return "Consider reducing difficulty to maintain optimal movement patterns."
        case (.decrease, .motivational):
            return "Let's master this level first, then we'll come back stronger!"
        case (.decrease, .safetyFirst):
            return "Let's reduce the difficulty to ensure safe, proper form."
            
        case (.maintain, _):
            return "You're at the perfect level right now. Keep up the great work!"
        }
    }
    
    private func recordFeedback(_ feedback: CoachingFeedback, context: FeedbackContext) {
        let timestampedFeedback = TimestampedFeedback(
            feedback: feedback,
            context: context,
            timestamp: Date()
        )
        
        feedbackHistory.append(timestampedFeedback)
        
        // Trim history to maintain performance
        if feedbackHistory.count > maxHistoryLength {
            feedbackHistory.removeFirst()
        }
        
        lastFeedbackContext = context
    }
}

// MARK: - Supporting Types

enum FeedbackContext {
    case safety(risk: SafetyRisk)
    case formCorrection(issue: FormIssue)
    case excellentForm
    case repCompletion(repNumber: Int, setNumber: Int, formScore: Double)
    case setCompletion(setNumber: Int, totalSets: Int, averageFormScore: Double)
    case general
    
    func isSimilar(to other: FeedbackContext) -> Bool {
        switch (self, other) {
        case (.safety, .safety):
            return true
        case (.formCorrection(let issue1), .formCorrection(let issue2)):
            return issue1.type == issue2.type
        case (.excellentForm, .excellentForm):
            return true
        case (.general, .general):
            return true
        default:
            return false
        }
    }
}

enum EnergyLevel {
    case high
    case medium
    case low
}

enum DifficultyAdjustment {
    case increase
    case decrease
    case maintain
}

struct TimestampedFeedback {
    let feedback: CoachingFeedback
    let context: FeedbackContext
    let timestamp: Date
}