//
//  WorkoutProgram.swift
//  MotionFitPro
//
//  Created by <PERSON> on 2025-07-15.
//

import Foundation
import SwiftUI

// MARK: - Workout Program Models

struct WorkoutProgram: Identifiable, Codable {
    let id = UUID()
    let name: String
    let description: String
    let duration: TimeInterval
    let difficulty: WorkoutDifficulty
    let type: WorkoutType
    let exercises: [ProgramExercise]
    let icon: String
    let color: WorkoutColor
    let benefits: [String]
    let targetAudience: String
    let estimatedCalories: Int
    
    var formattedDuration: String {
        let minutes = Int(duration) / 60
        return "\(minutes) min"
    }
    
    var difficultyStars: Int {
        switch difficulty {
        case .beginner: return 1
        case .intermediate: return 3
        case .advanced: return 5
        }
    }
}

struct ProgramExercise: Identifiable, Codable {
    let id = UUID()
    let exerciseType: ExerciseType
    let duration: TimeInterval?        // For time-based exercises
    let targetReps: Int?              // For rep-based exercises
    let sets: Int
    let restDuration: TimeInterval
    let instructions: [String]
    let tips: [String]
    
    var formattedDuration: String? {
        guard let duration = duration else { return nil }
        return "\(Int(duration))s"
    }
    
    var formattedRest: String {
        return "\(Int(restDuration))s rest"
    }
}

enum WorkoutType: String, CaseIterable, Codable {
    case circuit = "circuit"
    case hiit = "hiit"
    case strength = "strength"
    case endurance = "endurance"
    case flexibility = "flexibility"
    
    var displayName: String {
        switch self {
        case .circuit: return "Circuit Training"
        case .hiit: return "HIIT"
        case .strength: return "Strength"
        case .endurance: return "Endurance"
        case .flexibility: return "Flexibility"
        }
    }
    
    var description: String {
        switch self {
        case .circuit: return "Mixed exercises in sequence"
        case .hiit: return "High-intensity intervals"
        case .strength: return "Build muscle and power"
        case .endurance: return "Cardiovascular conditioning"
        case .flexibility: return "Mobility and recovery"
        }
    }
}

enum WorkoutDifficulty: String, CaseIterable, Codable {
    case beginner = "beginner"
    case intermediate = "intermediate"
    case advanced = "advanced"
    
    var displayName: String {
        switch self {
        case .beginner: return "Beginner"
        case .intermediate: return "Intermediate"
        case .advanced: return "Advanced"
        }
    }
    
    var color: Color {
        switch self {
        case .beginner: return .green
        case .intermediate: return .orange
        case .advanced: return .red
        }
    }
}

struct WorkoutColor: Codable {
    let primary: String
    let secondary: String
    let accent: String
    
    var primaryColor: Color {
        Color(hex: primary)
    }
    
    var secondaryColor: Color {
        Color(hex: secondary)
    }
    
    var accentColor: Color {
        Color(hex: accent)
    }
    
    var gradient: LinearGradient {
        LinearGradient(
            colors: [primaryColor, secondaryColor],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
}

// MARK: - Premium Workout Programs

extension WorkoutProgram {
    static let morningBlast = WorkoutProgram(
        name: "5-Minute Morning Blast",
        description: "Energize your day with this quick, full-body circuit that gets your blood pumping and metabolism firing.",
        duration: 300, // 5 minutes
        difficulty: .beginner,
        type: .circuit,
        exercises: [
            ProgramExercise(
                exerciseType: .jumpingJacks,
                duration: 30,
                targetReps: nil,
                sets: 1,
                restDuration: 10,
                instructions: ["Stand with feet together", "Jump feet apart while raising arms overhead", "Jump back to starting position"],
                tips: ["Keep core engaged", "Land softly on balls of feet", "Maintain steady rhythm"]
            ),
            ProgramExercise(
                exerciseType: .squat,
                duration: 45,
                targetReps: nil,
                sets: 1,
                restDuration: 15,
                instructions: ["Stand with feet shoulder-width apart", "Lower hips back and down", "Return to standing"],
                tips: ["Keep chest up", "Weight in heels", "Knees track over toes"]
            ),
            ProgramExercise(
                exerciseType: .pushUp,
                duration: 30,
                targetReps: nil,
                sets: 1,
                restDuration: 10,
                instructions: ["Start in plank position", "Lower chest toward ground", "Push back up"],
                tips: ["Keep body straight", "Modify on knees if needed", "Control the movement"]
            ),
            ProgramExercise(
                exerciseType: .mountainClimbers,
                duration: 30,
                targetReps: nil,
                sets: 1,
                restDuration: 15,
                instructions: ["Start in plank position", "Alternate bringing knees to chest", "Maintain fast pace"],
                tips: ["Keep hips level", "Drive knees forward", "Breathe steadily"]
            ),
            ProgramExercise(
                exerciseType: .plank,
                duration: 30,
                targetReps: nil,
                sets: 1,
                restDuration: 0,
                instructions: ["Hold straight body position", "Engage core muscles", "Breathe normally"],
                tips: ["Don't let hips sag", "Keep shoulders over wrists", "Engage entire core"]
            )
        ],
        icon: "sunrise.fill",
        color: WorkoutColor(primary: "#FF6B35", secondary: "#F7931E", accent: "#FFD23F"),
        benefits: ["Boosts metabolism", "Increases energy", "Improves mood", "Quick and effective"],
        targetAudience: "Perfect for busy mornings and fitness beginners",
        estimatedCalories: 50
    )
    
    static let hiitChampionship = WorkoutProgram(
        name: "HIIT Championship",
        description: "Push your limits with this intense interval training designed for maximum fat burn and athletic performance.",
        duration: 900, // 15 minutes
        difficulty: .advanced,
        type: .hiit,
        exercises: [
            ProgramExercise(
                exerciseType: .burpees,
                duration: 45,
                targetReps: nil,
                sets: 3,
                restDuration: 15,
                instructions: ["Squat down, hands to floor", "Jump feet back to plank", "Add push-up (optional)", "Jump feet forward", "Explosive jump up"],
                tips: ["Maintain form under fatigue", "Scale intensity as needed", "Focus on smooth transitions"]
            ),
            ProgramExercise(
                exerciseType: .mountainClimbers,
                duration: 45,
                targetReps: nil,
                sets: 3,
                restDuration: 15,
                instructions: ["High-intensity knee drives", "Maximum speed with control", "Keep core tight"],
                tips: ["Faster pace than normal", "Maintain plank position", "Short, quick breaths"]
            ),
            ProgramExercise(
                exerciseType: .jumpingJacks,
                duration: 45,
                targetReps: nil,
                sets: 3,
                restDuration: 15,
                instructions: ["Explosive movements", "Full range of motion", "Maintain high intensity"],
                tips: ["Jump as high as possible", "Coordinate arms and legs", "Push through fatigue"]
            ),
            ProgramExercise(
                exerciseType: .squat,
                duration: 45,
                targetReps: nil,
                sets: 3,
                restDuration: 15,
                instructions: ["Explosive squat jumps", "Maximum depth and height", "Control landing"],
                tips: ["Add jump for intensity", "Deep squat position", "Explosive power"]
            )
        ],
        icon: "flame.fill",
        color: WorkoutColor(primary: "#E74C3C", secondary: "#C0392B", accent: "#F39C12"),
        benefits: ["Maximum calorie burn", "Improves VO2 max", "Builds explosive power", "Time efficient"],
        targetAudience: "For experienced athletes seeking peak performance",
        estimatedCalories: 180
    )
    
    static let formMaster = WorkoutProgram(
        name: "Form Master Challenge",
        description: "Perfect your technique with this precision-focused program that emphasizes quality over quantity.",
        duration: 720, // 12 minutes
        difficulty: .intermediate,
        type: .strength,
        exercises: [
            ProgramExercise(
                exerciseType: .squat,
                duration: nil,
                targetReps: 12,
                sets: 3,
                restDuration: 45,
                instructions: ["Focus on perfect form", "Slow, controlled movement", "Full range of motion"],
                tips: ["2-second descent", "1-second pause at bottom", "Quality over speed"]
            ),
            ProgramExercise(
                exerciseType: .pushUp,
                duration: nil,
                targetReps: 10,
                sets: 3,
                restDuration: 45,
                instructions: ["Controlled tempo", "Full chest-to-ground contact", "Perfect body alignment"],
                tips: ["3-second descent", "Pause at bottom", "Maintain perfect plank"]
            ),
            ProgramExercise(
                exerciseType: .lunge,
                duration: nil,
                targetReps: 8,
                sets: 3,
                restDuration: 45,
                instructions: ["Alternating legs", "Deep lunge position", "Controlled balance"],
                tips: ["Step forward deliberately", "90-degree knee angles", "Maintain upright torso"]
            ),
            ProgramExercise(
                exerciseType: .plank,
                duration: 60,
                targetReps: nil,
                sets: 2,
                restDuration: 60,
                instructions: ["Hold perfect position", "Focus on alignment", "Steady breathing"],
                tips: ["Engage entire core", "Straight line head-to-heels", "No sagging or piking"]
            )
        ],
        icon: "target",
        color: WorkoutColor(primary: "#3498DB", secondary: "#2980B9", accent: "#1ABC9C"),
        benefits: ["Perfect technique", "Injury prevention", "Mind-muscle connection", "Foundation building"],
        targetAudience: "Ideal for perfecting movement patterns and form",
        estimatedCalories: 120
    )
    
    static let enduranceTrial = WorkoutProgram(
        name: "Endurance Trial",
        description: "Test your mental and physical endurance with this sustained-effort challenge that builds lasting stamina.",
        duration: 1200, // 20 minutes
        difficulty: .intermediate,
        type: .endurance,
        exercises: [
            ProgramExercise(
                exerciseType: .squat,
                duration: 120,
                targetReps: nil,
                sets: 1,
                restDuration: 30,
                instructions: ["Steady pace for 2 minutes", "Maintain consistent form", "Control breathing"],
                tips: ["Find sustainable rhythm", "Don't rush", "Focus on endurance"]
            ),
            ProgramExercise(
                exerciseType: .pushUp,
                duration: 90,
                targetReps: nil,
                sets: 1,
                restDuration: 30,
                instructions: ["90 seconds continuous", "Modify as needed", "Keep moving"],
                tips: ["Drop to knees when needed", "Maintain form priority", "Mental toughness"]
            ),
            ProgramExercise(
                exerciseType: .plank,
                duration: 90,
                targetReps: nil,
                sets: 1,
                restDuration: 30,
                instructions: ["Hold for 90 seconds", "Static endurance challenge", "Mental focus"],
                tips: ["Break into smaller holds if needed", "Breathe steadily", "Stay strong"]
            ),
            ProgramExercise(
                exerciseType: .mountainClimbers,
                duration: 120,
                targetReps: nil,
                sets: 1,
                restDuration: 30,
                instructions: ["2-minute continuous", "Moderate pace", "Steady rhythm"],
                tips: ["Pace yourself", "Consistent breathing", "Mental endurance"]
            ),
            ProgramExercise(
                exerciseType: .jumpingJacks,
                duration: 180,
                targetReps: nil,
                sets: 1,
                restDuration: 0,
                instructions: ["3-minute finale", "Steady state cardio", "Push through fatigue"],
                tips: ["Find your rhythm", "This is mental", "Finish strong"]
            )
        ],
        icon: "timer",
        color: WorkoutColor(primary: "#9B59B6", secondary: "#8E44AD", accent: "#E67E22"),
        benefits: ["Cardiovascular endurance", "Mental toughness", "Stamina building", "Consistent pacing"],
        targetAudience: "For building long-term endurance and mental resilience",
        estimatedCalories: 200
    )
    
    static let allPrograms: [WorkoutProgram] = [
        morningBlast,
        hiitChampionship,
        formMaster,
        enduranceTrial
    ]
}

// MARK: - Color Extension

extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}