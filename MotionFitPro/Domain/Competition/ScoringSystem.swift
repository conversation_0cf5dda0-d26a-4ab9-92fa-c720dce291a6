//
//  ScoringSystem.swift
//  MotionFitPro
//
//  Created by <PERSON> on 2025-07-15.
//

import Foundation

// MARK: - Standardized Scoring System

class StandardizedScoringSystem {
    
    // MARK: - Core Scoring Configuration
    
    private struct ScoringWeights {
        // Universal weights across all exercises
        static let formQuality: Float = 0.6      // 60% - Form is most important
        static let completion: Float = 0.25      // 25% - Actually finishing reps
        static let consistency: Float = 0.15     // 15% - Consistent performance
        
        // Competition format modifiers
        struct FormatModifiers {
            static let formFocus: Float = 0.8    // Increase form weight to 80%
            static let timeAttack: Float = 0.3   // Reduce form weight, increase speed
            static let endurance: Float = 0.5    // Balanced approach
            static let bestOfThree: Float = 0.6  // Standard weights
        }
    }
    
    private struct PerformanceThresholds {
        static let minimumFormScore: Float = 0.3    // Below this = invalid rep
        static let excellentFormScore: Float = 0.85  // Above this = bonus points
        static let consistencyWindow: Int = 5        // Reps to analyze for consistency
        static let maxRepTime: TimeInterval = 10     // Max time per rep (anti-stalling)
    }
    
    // MARK: - Main Scoring Function
    
    func calculateScore(
        performanceData: CompetitionPerformanceData,
        format: CompetitionFormat
    ) -> CompetitionScore {
        let weights = getWeightsForFormat(format)
        let exerciseMetrics = calculateExerciseMetrics(performanceData)
        let consistencyMetrics = calculateConsistencyMetrics(performanceData)
        
        // Core component scores
        let formScore = calculateFormScore(exerciseMetrics, weights: weights)
        let completionScore = calculateCompletionScore(exerciseMetrics, format: format)
        let consistencyScore = calculateConsistencyScore(consistencyMetrics)
        
        // Format-specific bonuses and penalties
        let formatScore = calculateFormatSpecificScore(performanceData, format: format)
        let penalties = calculatePenalties(performanceData, format: format)
        
        // Combine scores
        let rawScore = (formScore * weights.form) + 
                      (completionScore * weights.completion) + 
                      (consistencyScore * weights.consistency)
        
        let finalScore = max(0, rawScore + formatScore - penalties)
        
        return CompetitionScore(
            totalScore: finalScore,
            formScore: formScore,
            completionScore: completionScore,
            consistencyScore: consistencyScore,
            formatBonusScore: formatScore,
            penalties: penalties,
            breakdown: ScoreBreakdown(
                validReps: exerciseMetrics.validReps,
                invalidReps: exerciseMetrics.invalidReps,
                averageFormScore: exerciseMetrics.averageFormScore,
                peakFormScore: exerciseMetrics.peakFormScore,
                formConsistency: consistencyMetrics.formConsistency,
                timingConsistency: consistencyMetrics.timingConsistency,
                totalDuration: performanceData.totalDuration
            )
        )
    }
    
    // MARK: - Scoring Components
    
    private func calculateFormScore(_ metrics: ExerciseMetrics, weights: ScoringWeights) -> Float {
        let avgFormScore = metrics.averageFormScore
        let peakFormScore = metrics.peakFormScore
        let formConsistency = metrics.formConsistency
        
        // Base form score with peak performance bonus
        let baseScore = avgFormScore * 0.8 + peakFormScore * 0.2
        
        // Consistency multiplier (0.8 to 1.2)
        let consistencyMultiplier = 0.8 + (formConsistency * 0.4)
        
        // Excellence bonus for exceptional form
        let excellenceBonus = avgFormScore > PerformanceThresholds.excellentFormScore ? 0.1 : 0
        
        return min(1.0, baseScore * consistencyMultiplier + excellenceBonus)
    }
    
    private func calculateCompletionScore(_ metrics: ExerciseMetrics, format: CompetitionFormat) -> Float {
        let completionRate = Float(metrics.validReps) / Float(metrics.totalReps)
        let efficiencyScore = calculateEfficiencyScore(metrics, format: format)
        
        // Completion rate is primary factor
        let baseScore = completionRate * 0.7
        
        // Efficiency contributes to completion score
        let efficiencyComponent = efficiencyScore * 0.3
        
        return min(1.0, baseScore + efficiencyComponent)
    }
    
    private func calculateConsistencyScore(_ metrics: ConsistencyMetrics) -> Float {
        let formConsistency = metrics.formConsistency
        let timingConsistency = metrics.timingConsistency
        let rhythmConsistency = metrics.rhythmConsistency
        
        // Weighted average of consistency factors
        return (formConsistency * 0.5) + (timingConsistency * 0.3) + (rhythmConsistency * 0.2)
    }
    
    private func calculateEfficiencyScore(_ metrics: ExerciseMetrics, format: CompetitionFormat) -> Float {
        switch format {
        case .timeAttack:
            // Reward speed while maintaining form
            let speedScore = calculateSpeedScore(metrics)
            let formPenalty = metrics.averageFormScore < 0.6 ? 0.5 : 1.0
            return speedScore * formPenalty
            
        case .endurance:
            // Reward sustained performance
            return calculateEnduranceScore(metrics)
            
        case .formFocus:
            // Pure form scoring with time penalty for excessive slowness
            let formBonus = metrics.averageFormScore > 0.8 ? 1.2 : 1.0
            let timePenalty = metrics.averageRepTime > 4.0 ? 0.9 : 1.0
            return min(1.0, formBonus * timePenalty)
            
        case .bestOfThree:
            // Balanced efficiency
            return calculateBalancedEfficiency(metrics)
        }
    }
    
    private func calculateFormatSpecificScore(
        _ performanceData: CompetitionPerformanceData,
        format: CompetitionFormat
    ) -> Float {
        switch format {
        case .timeAttack:
            return calculateTimeAttackBonus(performanceData)
        case .endurance:
            return calculateEnduranceBonus(performanceData)
        case .formFocus:
            return calculateFormFocusBonus(performanceData)
        case .bestOfThree:
            return calculateBestOfThreeBonus(performanceData)
        }
    }
    
    private func calculatePenalties(
        _ performanceData: CompetitionPerformanceData,
        format: CompetitionFormat
    ) -> Float {
        var totalPenalties: Float = 0
        
        // Form degradation penalty
        if let degradation = detectFormDegradation(performanceData) {
            totalPenalties += degradation * 0.1
        }
        
        // Stalling penalty (taking too long per rep)
        let stallingPenalty = calculateStallingPenalty(performanceData)
        totalPenalties += stallingPenalty
        
        // Invalid rep penalty
        let invalidRepRatio = Float(performanceData.invalidReps) / Float(performanceData.totalReps)
        totalPenalties += invalidRepRatio * 0.2
        
        // Format-specific penalties
        switch format {
        case .timeAttack:
            // Penalty for excessive time
            if performanceData.totalDuration > format.duration * 0.8 {
                totalPenalties += 0.15
            }
        case .formFocus:
            // Heavy penalty for poor form
            if performanceData.averageFormScore < 0.7 {
                totalPenalties += 0.25
            }
        default:
            break
        }
        
        return totalPenalties
    }
    
    // MARK: - Helper Functions
    
    private func getWeightsForFormat(_ format: CompetitionFormat) -> ScoringWeights {
        switch format {
        case .formFocus:
            return ScoringWeights(
                form: ScoringWeights.FormatModifiers.formFocus,
                completion: 0.15,
                consistency: 0.05
            )
        case .timeAttack:
            return ScoringWeights(
                form: ScoringWeights.FormatModifiers.timeAttack,
                completion: 0.5,
                consistency: 0.2
            )
        case .endurance:
            return ScoringWeights(
                form: ScoringWeights.FormatModifiers.endurance,
                completion: 0.3,
                consistency: 0.2
            )
        case .bestOfThree:
            return ScoringWeights(
                form: ScoringWeights.formQuality,
                completion: ScoringWeights.completion,
                consistency: ScoringWeights.consistency
            )
        }
    }
    
    private func calculateExerciseMetrics(_ data: CompetitionPerformanceData) -> ExerciseMetrics {
        let validReps = data.repData.filter { $0.formScore >= PerformanceThresholds.minimumFormScore }
        let invalidReps = data.repData.count - validReps.count
        
        let formScores = validReps.map { $0.formScore }
        let averageFormScore = formScores.isEmpty ? 0 : formScores.reduce(0, +) / Float(formScores.count)
        let peakFormScore = formScores.max() ?? 0
        
        let repTimes = validReps.map { $0.duration }
        let averageRepTime = repTimes.isEmpty ? 0 : repTimes.reduce(0, +) / Double(repTimes.count)
        
        let formConsistency = calculateFormConsistency(formScores)
        
        return ExerciseMetrics(
            totalReps: data.totalReps,
            validReps: validReps.count,
            invalidReps: invalidReps,
            averageFormScore: averageFormScore,
            peakFormScore: peakFormScore,
            formConsistency: formConsistency,
            averageRepTime: averageRepTime
        )
    }
    
    private func calculateConsistencyMetrics(_ data: CompetitionPerformanceData) -> ConsistencyMetrics {
        let formScores = data.repData.map { $0.formScore }
        let repTimes = data.repData.map { $0.duration }
        
        let formConsistency = calculateFormConsistency(formScores)
        let timingConsistency = calculateTimingConsistency(repTimes)
        let rhythmConsistency = calculateRhythmConsistency(data.repData)
        
        return ConsistencyMetrics(
            formConsistency: formConsistency,
            timingConsistency: timingConsistency,
            rhythmConsistency: rhythmConsistency
        )
    }
    
    private func calculateFormConsistency(_ formScores: [Float]) -> Float {
        guard formScores.count >= 2 else { return 1.0 }
        
        let mean = formScores.reduce(0, +) / Float(formScores.count)
        let variance = formScores.map { pow($0 - mean, 2) }.reduce(0, +) / Float(formScores.count)
        let standardDeviation = sqrt(variance)
        
        // Convert to consistency score (lower deviation = higher consistency)
        return max(0, 1.0 - (standardDeviation * 2))
    }
    
    private func calculateTimingConsistency(_ repTimes: [TimeInterval]) -> Float {
        guard repTimes.count >= 2 else { return 1.0 }
        
        let mean = repTimes.reduce(0, +) / Double(repTimes.count)
        let variance = repTimes.map { pow($0 - mean, 2) }.reduce(0, +) / Double(repTimes.count)
        let standardDeviation = sqrt(variance)
        
        // Normalize by mean to get coefficient of variation
        let coefficientOfVariation = standardDeviation / mean
        return max(0, 1.0 - Float(coefficientOfVariation))
    }
    
    private func calculateRhythmConsistency(_ repData: [RepData]) -> Float {
        guard repData.count >= 3 else { return 1.0 }
        
        var intervals: [TimeInterval] = []
        for i in 1..<repData.count {
            let interval = repData[i].timestamp.timeIntervalSince(repData[i-1].timestamp)
            intervals.append(interval)
        }
        
        return calculateTimingConsistency(intervals)
    }
    
    private func calculateSpeedScore(_ metrics: ExerciseMetrics) -> Float {
        let idealRepTime: TimeInterval = 2.0 // Ideal rep time in seconds
        let timeDifference = abs(metrics.averageRepTime - idealRepTime)
        
        // Score decreases as time deviates from ideal
        return max(0, 1.0 - Float(timeDifference / idealRepTime))
    }
    
    private func calculateEnduranceScore(_ metrics: ExerciseMetrics) -> Float {
        // Reward maintaining form over time
        let formMaintenance = metrics.formConsistency
        let completionRate = Float(metrics.validReps) / Float(metrics.totalReps)
        
        return (formMaintenance * 0.6) + (completionRate * 0.4)
    }
    
    private func calculateBalancedEfficiency(_ metrics: ExerciseMetrics) -> Float {
        let formScore = metrics.averageFormScore
        let speedScore = calculateSpeedScore(metrics)
        let consistencyScore = metrics.formConsistency
        
        return (formScore * 0.5) + (speedScore * 0.3) + (consistencyScore * 0.2)
    }
    
    // MARK: - Format-Specific Bonuses
    
    private func calculateTimeAttackBonus(_ data: CompetitionPerformanceData) -> Float {
        let targetTime = 60.0 // Target completion time
        let timeSaved = max(0, targetTime - data.totalDuration)
        let timeBonus = Float(timeSaved / targetTime) * 0.2
        
        // Only award bonus if form is maintained
        let formThreshold: Float = 0.6
        return data.averageFormScore >= formThreshold ? timeBonus : 0
    }
    
    private func calculateEnduranceBonus(_ data: CompetitionPerformanceData) -> Float {
        // Bonus for sustained high form over long duration
        let enduranceThreshold: TimeInterval = 300 // 5 minutes
        if data.totalDuration >= enduranceThreshold && data.averageFormScore > 0.7 {
            return 0.15
        }
        return 0
    }
    
    private func calculateFormFocusBonus(_ data: CompetitionPerformanceData) -> Float {
        // Bonus for exceptional form
        if data.averageFormScore > 0.9 {
            return 0.25
        } else if data.averageFormScore > 0.8 {
            return 0.1
        }
        return 0
    }
    
    private func calculateBestOfThreeBonus(_ data: CompetitionPerformanceData) -> Float {
        // Balanced bonus for well-rounded performance
        let formBonus = data.averageFormScore > 0.8 ? 0.05 : 0
        let consistencyBonus = data.repData.count >= 10 ? 0.05 : 0
        return formBonus + consistencyBonus
    }
    
    // MARK: - Penalty Calculations
    
    private func detectFormDegradation(_ data: CompetitionPerformanceData) -> Float? {
        guard data.repData.count >= 6 else { return nil }
        
        let firstThird = Array(data.repData.prefix(data.repData.count / 3))
        let lastThird = Array(data.repData.suffix(data.repData.count / 3))
        
        let firstAverage = firstThird.map { $0.formScore }.reduce(0, +) / Float(firstThird.count)
        let lastAverage = lastThird.map { $0.formScore }.reduce(0, +) / Float(lastThird.count)
        
        let degradation = firstAverage - lastAverage
        return degradation > 0.2 ? degradation : nil
    }
    
    private func calculateStallingPenalty(_ data: CompetitionPerformanceData) -> Float {
        let stallingReps = data.repData.filter { $0.duration > PerformanceThresholds.maxRepTime }
        let stallingRatio = Float(stallingReps.count) / Float(data.repData.count)
        
        return stallingRatio * 0.15
    }
}

// MARK: - Supporting Data Structures

struct ScoringWeights {
    let form: Float
    let completion: Float
    let consistency: Float
}

struct ExerciseMetrics {
    let totalReps: Int
    let validReps: Int
    let invalidReps: Int
    let averageFormScore: Float
    let peakFormScore: Float
    let formConsistency: Float
    let averageRepTime: TimeInterval
}

struct ConsistencyMetrics {
    let formConsistency: Float
    let timingConsistency: Float
    let rhythmConsistency: Float
}

struct CompetitionScore: Codable {
    let totalScore: Float
    let formScore: Float
    let completionScore: Float
    let consistencyScore: Float
    let formatBonusScore: Float
    let penalties: Float
    let breakdown: ScoreBreakdown
    
    var normalizedScore: Int {
        // Convert to 0-1000 point scale
        return Int(totalScore * 1000)
    }
}

struct ScoreBreakdown: Codable {
    let validReps: Int
    let invalidReps: Int
    let averageFormScore: Float
    let peakFormScore: Float
    let formConsistency: Float
    let timingConsistency: Float
    let totalDuration: TimeInterval
}

struct CompetitionPerformanceData: Codable {
    let userId: String
    let exerciseType: ExerciseType
    let totalReps: Int
    let invalidReps: Int
    let averageFormScore: Float
    let totalDuration: TimeInterval
    let repData: [RepData]
    let sessionId: String
    let timestamp: Date
    
    // Motion analysis data for anti-cheat
    let motionSignature: MotionSignature
    let deviceMetadata: DeviceMetadata
}

struct RepData: Codable {
    let repNumber: Int
    let formScore: Float
    let duration: TimeInterval
    let timestamp: Date
    let jointAngles: [String: Float] // Joint name to angle mapping
    let detectedIssues: [String]
    let confidence: Float
}

struct MotionSignature: Codable {
    let exerciseType: ExerciseType
    let avgJointVelocity: Float
    let movementAmplitude: Float
    let naturalVariation: Float
    let temporalConsistency: Float
    let biomechanicalPlausibility: Float
}

struct DeviceMetadata: Codable {
    let deviceModel: String
    let osVersion: String
    let arKitVersion: String
    let cameraQuality: String
    let processingPower: Float
    let batteryLevel: Float
    let networkLatency: Float
}