//
//  CompetitionHistoryManager.swift
//  MotionFitPro
//
//  Created by <PERSON> on 2025-07-15.
//

import Foundation
import Combine

// MARK: - Competition History Manager

@MainActor
class CompetitionHistoryManager: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var recentCompetitions: [CompetitionHistoryEntry] = []
    @Published var competitionStats: CompetitionAnalytics = CompetitionAnalytics()
    @Published var progressTrends: [ProgressTrend] = []
    @Published var personalRecords: [PersonalRecord] = []
    @Published var achievementProgress: [AchievementProgress] = []
    
    // MARK: - Private Properties
    
    private let dataManager = CompetitionDataManager()
    private let achievementTracker = CompetitionAchievementTracker()
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        setupDataSubscriptions()
    }
    
    // MARK: - Public Interface
    
    func loadHistoryForUser(_ userId: String) async {
        do {
            let history = try await dataManager.loadCompetitionHistory(for: userId)
            recentCompetitions = Array(history.prefix(20)) // Show recent 20
            
            await updateAnalytics(from: history)
            await updateProgressTrends(from: history)
            await updatePersonalRecords(from: history)
            await updateAchievementProgress(userId: userId)
            
        } catch {
            print("Failed to load competition history: \(error)")
        }
    }
    
    func addCompetitionEntry(_ entry: CompetitionHistoryEntry) async {
        recentCompetitions.insert(entry, at: 0)
        
        // Keep only recent entries in memory
        if recentCompetitions.count > 50 {
            recentCompetitions = Array(recentCompetitions.prefix(50))
        }
        
        // Update analytics
        await updateAnalyticsWithNewEntry(entry)
        
        // Check for new personal records
        await checkForPersonalRecords(entry)
        
        // Update achievement progress
        await achievementTracker.processCompetitionResult(entry)
        
        // Persist to storage
        try? await dataManager.saveCompetitionEntry(entry)
    }
    
    func getDetailedStats(for exercise: ExerciseType) -> ExerciseDetailedStats {
        let exerciseEntries = recentCompetitions.filter { $0.exercise == exercise }
        
        return ExerciseDetailedStats(
            exercise: exercise,
            totalCompetitions: exerciseEntries.count,
            wins: exerciseEntries.filter { $0.result == .win }.count,
            losses: exerciseEntries.filter { $0.result == .loss }.count,
            averageScore: calculateAverageScore(exerciseEntries),
            bestScore: exerciseEntries.compactMap { $0.finalScore }.max() ?? 0,
            averageFormScore: calculateAverageFormScore(exerciseEntries),
            averageRating: calculateAverageRating(exerciseEntries),
            recentTrend: calculateRecentTrend(exerciseEntries),
            lastCompetition: exerciseEntries.first?.date
        )
    }
    
    func getCompetitionTimeline(timeframe: TimeFrame) -> [CompetitionTimelineEntry] {
        let cutoffDate = calculateCutoffDate(for: timeframe)
        let filteredEntries = recentCompetitions.filter { $0.date >= cutoffDate }
        
        return groupEntriesByDate(filteredEntries, timeframe: timeframe)
    }
    
    func exportHistory(format: ExportFormat) async throws -> Data {
        let allHistory = try await dataManager.loadCompleteHistory()
        
        switch format {
        case .json:
            return try JSONEncoder().encode(allHistory)
        case .csv:
            return try generateCSVData(from: allHistory)
        case .pdf:
            return try await generatePDFReport(from: allHistory)
        }
    }
    
    // MARK: - Analytics Updates
    
    private func updateAnalytics(from history: [CompetitionHistoryEntry]) async {
        let analytics = CompetitionAnalytics()
        
        analytics.totalCompetitions = history.count
        analytics.totalWins = history.filter { $0.result == .win }.count
        analytics.totalLosses = history.filter { $0.result == .loss }.count
        analytics.totalDraws = history.filter { $0.result == .draw }.count
        
        analytics.winRate = Float(analytics.totalWins) / Float(max(1, analytics.totalCompetitions))
        analytics.averageScore = calculateAverageScore(history)
        analytics.bestScore = history.compactMap { $0.finalScore }.max() ?? 0
        analytics.averageFormScore = calculateAverageFormScore(history)
        
        // Calculate format-specific stats
        analytics.formatStats = CompetitionFormat.allCases.reduce(into: [:]) { result, format in
            let formatEntries = history.filter { $0.format == format }
            result[format] = FormatStats(
                totalCompetitions: formatEntries.count,
                wins: formatEntries.filter { $0.result == .win }.count,
                averageScore: calculateAverageScore(formatEntries),
                bestScore: formatEntries.compactMap { $0.finalScore }.max() ?? 0
            )
        }
        
        // Calculate exercise-specific stats
        analytics.exerciseStats = ExerciseType.allCases.reduce(into: [:]) { result, exercise in
            let exerciseEntries = history.filter { $0.exercise == exercise }
            result[exercise] = ExerciseStats(
                totalCompetitions: exerciseEntries.count,
                wins: exerciseEntries.filter { $0.result == .win }.count,
                averageScore: calculateAverageScore(exerciseEntries),
                averageFormScore: calculateAverageFormScore(exerciseEntries),
                bestScore: exerciseEntries.compactMap { $0.finalScore }.max() ?? 0
            )
        }
        
        // Calculate streaks
        analytics.currentWinStreak = calculateCurrentWinStreak(history)
        analytics.longestWinStreak = calculateLongestWinStreak(history)
        
        // Calculate rating progression
        analytics.ratingProgression = calculateRatingProgression(history)
        
        competitionStats = analytics
    }
    
    private func updateAnalyticsWithNewEntry(_ entry: CompetitionHistoryEntry) async {
        competitionStats.totalCompetitions += 1
        
        switch entry.result {
        case .win:
            competitionStats.totalWins += 1
            competitionStats.currentWinStreak += 1
            competitionStats.longestWinStreak = max(competitionStats.longestWinStreak, competitionStats.currentWinStreak)
        case .loss:
            competitionStats.totalLosses += 1
            competitionStats.currentWinStreak = 0
        case .draw:
            competitionStats.totalDraws += 1
            competitionStats.currentWinStreak = 0
        }
        
        // Update win rate
        competitionStats.winRate = Float(competitionStats.totalWins) / Float(competitionStats.totalCompetitions)
        
        // Update scores if better
        if let score = entry.finalScore {
            competitionStats.bestScore = max(competitionStats.bestScore, score)
            
            // Recalculate average score (simplified)
            let totalScore = competitionStats.averageScore * Float(competitionStats.totalCompetitions - 1) + score
            competitionStats.averageScore = totalScore / Float(competitionStats.totalCompetitions)
        }
        
        // Update format stats
        if var formatStat = competitionStats.formatStats[entry.format] {
            formatStat.totalCompetitions += 1
            if entry.result == .win {
                formatStat.wins += 1
            }
            if let score = entry.finalScore {
                formatStat.bestScore = max(formatStat.bestScore, score)
                let totalScore = formatStat.averageScore * Float(formatStat.totalCompetitions - 1) + score
                formatStat.averageScore = totalScore / Float(formatStat.totalCompetitions)
            }
            competitionStats.formatStats[entry.format] = formatStat
        }
        
        // Update exercise stats
        if var exerciseStat = competitionStats.exerciseStats[entry.exercise] {
            exerciseStat.totalCompetitions += 1
            if entry.result == .win {
                exerciseStat.wins += 1
            }
            if let score = entry.finalScore {
                exerciseStat.bestScore = max(exerciseStat.bestScore, score)
                let totalScore = exerciseStat.averageScore * Float(exerciseStat.totalCompetitions - 1) + score
                exerciseStat.averageScore = totalScore / Float(exerciseStat.totalCompetitions)
            }
            if let formScore = entry.averageFormScore {
                let totalFormScore = exerciseStat.averageFormScore * Float(exerciseStat.totalCompetitions - 1) + formScore
                exerciseStat.averageFormScore = totalFormScore / Float(exerciseStat.totalCompetitions)
            }
            competitionStats.exerciseStats[entry.exercise] = exerciseStat
        }
    }
    
    // MARK: - Progress Trends
    
    private func updateProgressTrends(from history: [CompetitionHistoryEntry]) async {
        let timeframes: [TimeFrame] = [.week, .month, .threeMonths, .year]
        
        progressTrends = timeframes.compactMap { timeframe in
            let cutoffDate = calculateCutoffDate(for: timeframe)
            let recentEntries = history.filter { $0.date >= cutoffDate }
            
            guard recentEntries.count >= 3 else { return nil }
            
            return calculateProgressTrend(entries: recentEntries, timeframe: timeframe)
        }
    }
    
    private func calculateProgressTrend(entries: [CompetitionHistoryEntry], timeframe: TimeFrame) -> ProgressTrend {
        let sortedEntries = entries.sorted { $0.date < $1.date }
        
        // Split into first and second half
        let midpoint = sortedEntries.count / 2
        let firstHalf = Array(sortedEntries.prefix(midpoint))
        let secondHalf = Array(sortedEntries.suffix(sortedEntries.count - midpoint))
        
        let firstHalfAvg = calculateAverageScore(firstHalf)
        let secondHalfAvg = calculateAverageScore(secondHalf)
        
        let scoreImprovement = secondHalfAvg - firstHalfAvg
        let direction: TrendDirection = scoreImprovement > 5 ? .improving : (scoreImprovement < -5 ? .declining : .stable)
        
        // Calculate win rate trend
        let firstHalfWinRate = Float(firstHalf.filter { $0.result == .win }.count) / Float(max(1, firstHalf.count))
        let secondHalfWinRate = Float(secondHalf.filter { $0.result == .win }.count) / Float(max(1, secondHalf.count))
        let winRateChange = secondHalfWinRate - firstHalfWinRate
        
        return ProgressTrend(
            timeframe: timeframe,
            direction: direction,
            scoreImprovement: scoreImprovement,
            winRateChange: winRateChange,
            competitionsAnalyzed: entries.count,
            confidence: calculateTrendConfidence(entries.count)
        )
    }
    
    // MARK: - Personal Records
    
    private func updatePersonalRecords(from history: [CompetitionHistoryEntry]) async {
        var records: [PersonalRecord] = []
        
        // Overall best score
        if let bestOverall = history.compactMap({ $0.finalScore }).max() {
            if let bestEntry = history.first(where: { $0.finalScore == bestOverall }) {
                records.append(PersonalRecord(
                    type: .bestOverallScore,
                    value: bestOverall,
                    exercise: bestEntry.exercise,
                    format: bestEntry.format,
                    date: bestEntry.date,
                    description: "Best overall competition score"
                ))
            }
        }
        
        // Best scores per exercise
        for exercise in ExerciseType.allCases {
            let exerciseEntries = history.filter { $0.exercise == exercise }
            if let bestScore = exerciseEntries.compactMap({ $0.finalScore }).max(),
               let bestEntry = exerciseEntries.first(where: { $0.finalScore == bestScore }) {
                
                records.append(PersonalRecord(
                    type: .bestExerciseScore(exercise),
                    value: bestScore,
                    exercise: exercise,
                    format: bestEntry.format,
                    date: bestEntry.date,
                    description: "Best \(exercise.displayName) score"
                ))
            }
        }
        
        // Best form scores
        if let bestForm = history.compactMap({ $0.averageFormScore }).max() {
            if let bestEntry = history.first(where: { $0.averageFormScore == bestForm }) {
                records.append(PersonalRecord(
                    type: .bestFormScore,
                    value: bestForm,
                    exercise: bestEntry.exercise,
                    format: bestEntry.format,
                    date: bestEntry.date,
                    description: "Best form score achieved"
                ))
            }
        }
        
        // Longest win streak
        let longestStreak = calculateLongestWinStreak(history)
        if longestStreak > 0 {
            records.append(PersonalRecord(
                type: .longestWinStreak,
                value: Float(longestStreak),
                exercise: nil,
                format: nil,
                date: Date(), // Would need to calculate actual date
                description: "Longest win streak: \(longestStreak) competitions"
            ))
        }
        
        personalRecords = records.sorted { $0.date > $1.date }
    }
    
    private func checkForPersonalRecords(_ entry: CompetitionHistoryEntry) async {
        var newRecords: [PersonalRecord] = []
        
        // Check for new best overall score
        if let score = entry.finalScore {
            let previousBest = personalRecords.first { $0.type == .bestOverallScore }?.value ?? 0
            if score > previousBest {
                newRecords.append(PersonalRecord(
                    type: .bestOverallScore,
                    value: score,
                    exercise: entry.exercise,
                    format: entry.format,
                    date: entry.date,
                    description: "New best overall score!"
                ))
            }
            
            // Check for new best exercise score
            let previousExerciseBest = personalRecords.first {
                if case .bestExerciseScore(let exercise) = $0.type {
                    return exercise == entry.exercise
                }
                return false
            }?.value ?? 0
            
            if score > previousExerciseBest {
                newRecords.append(PersonalRecord(
                    type: .bestExerciseScore(entry.exercise),
                    value: score,
                    exercise: entry.exercise,
                    format: entry.format,
                    date: entry.date,
                    description: "New best \(entry.exercise.displayName) score!"
                ))
            }
        }
        
        // Check for new best form score
        if let formScore = entry.averageFormScore {
            let previousBestForm = personalRecords.first { $0.type == .bestFormScore }?.value ?? 0
            if formScore > previousBestForm {
                newRecords.append(PersonalRecord(
                    type: .bestFormScore,
                    value: formScore,
                    exercise: entry.exercise,
                    format: entry.format,
                    date: entry.date,
                    description: "New best form score!"
                ))
            }
        }
        
        // Add new records and sort
        personalRecords.append(contentsOf: newRecords)
        personalRecords.sort { $0.date > $1.date }
        
        // Notify about new records
        for record in newRecords {
            NotificationCenter.default.post(
                name: .newPersonalRecord,
                object: record
            )
        }
    }
    
    // MARK: - Achievement Progress
    
    private func updateAchievementProgress(userId: String) async {
        achievementProgress = await achievementTracker.getAchievementProgress(for: userId)
    }
    
    // MARK: - Helper Functions
    
    private func calculateAverageScore(_ entries: [CompetitionHistoryEntry]) -> Float {
        let scores = entries.compactMap { $0.finalScore }
        guard !scores.isEmpty else { return 0 }
        return scores.reduce(0, +) / Float(scores.count)
    }
    
    private func calculateAverageFormScore(_ entries: [CompetitionHistoryEntry]) -> Float {
        let formScores = entries.compactMap { $0.averageFormScore }
        guard !formScores.isEmpty else { return 0 }
        return formScores.reduce(0, +) / Float(formScores.count)
    }
    
    private func calculateAverageRating(_ entries: [CompetitionHistoryEntry]) -> Float {
        guard !entries.isEmpty else { return 0 }
        let totalRating = entries.map { $0.rating }.reduce(0, +)
        return Float(totalRating) / Float(entries.count)
    }
    
    private func calculateRecentTrend(_ entries: [CompetitionHistoryEntry]) -> TrendDirection {
        guard entries.count >= 4 else { return .stable }
        
        let recent = Array(entries.prefix(entries.count / 2))
        let older = Array(entries.suffix(entries.count / 2))
        
        let recentAvg = calculateAverageScore(recent)
        let olderAvg = calculateAverageScore(older)
        
        let improvement = recentAvg - olderAvg
        
        if improvement > 10 { return .improving }
        if improvement < -10 { return .declining }
        return .stable
    }
    
    private func calculateCurrentWinStreak(_ history: [CompetitionHistoryEntry]) -> Int {
        var streak = 0
        for entry in history {
            if entry.result == .win {
                streak += 1
            } else {
                break
            }
        }
        return streak
    }
    
    private func calculateLongestWinStreak(_ history: [CompetitionHistoryEntry]) -> Int {
        var longestStreak = 0
        var currentStreak = 0
        
        for entry in history.reversed() {
            if entry.result == .win {
                currentStreak += 1
                longestStreak = max(longestStreak, currentStreak)
            } else {
                currentStreak = 0
            }
        }
        
        return longestStreak
    }
    
    private func calculateRatingProgression(_ history: [CompetitionHistoryEntry]) -> [RatingPoint] {
        return history.reversed().map { entry in
            RatingPoint(
                rating: entry.rating,
                date: entry.date,
                change: entry.ratingChange
            )
        }
    }
    
    private func calculateCutoffDate(for timeframe: TimeFrame) -> Date {
        let calendar = Calendar.current
        let now = Date()
        
        switch timeframe {
        case .week:
            return calendar.date(byAdding: .day, value: -7, to: now) ?? now
        case .month:
            return calendar.date(byAdding: .month, value: -1, to: now) ?? now
        case .threeMonths:
            return calendar.date(byAdding: .month, value: -3, to: now) ?? now
        case .year:
            return calendar.date(byAdding: .year, value: -1, to: now) ?? now
        case .allTime:
            return Date.distantPast
        }
    }
    
    private func calculateTrendConfidence(_ sampleSize: Int) -> Float {
        switch sampleSize {
        case 0..<5: return 0.3
        case 5..<10: return 0.6
        case 10..<20: return 0.8
        default: return 0.95
        }
    }
    
    private func groupEntriesByDate(_ entries: [CompetitionHistoryEntry], timeframe: TimeFrame) -> [CompetitionTimelineEntry] {
        let calendar = Calendar.current
        let grouped = Dictionary(grouping: entries) { entry in
            switch timeframe {
            case .week, .month:
                return calendar.startOfDay(for: entry.date)
            case .threeMonths, .year:
                return calendar.dateInterval(of: .weekOfYear, for: entry.date)?.start ?? entry.date
            case .allTime:
                return calendar.dateInterval(of: .month, for: entry.date)?.start ?? entry.date
            }
        }
        
        return grouped.map { date, entries in
            CompetitionTimelineEntry(
                date: date,
                competitions: entries,
                totalCompetitions: entries.count,
                wins: entries.filter { $0.result == .win }.count,
                averageScore: calculateAverageScore(entries)
            )
        }.sorted { $0.date > $1.date }
    }
    
    // MARK: - Data Export
    
    private func generateCSVData(from history: [CompetitionHistoryEntry]) throws -> Data {
        var csvString = "Date,Exercise,Format,Result,Score,Form Score,Rating,Rating Change\n"
        
        for entry in history {
            let line = [
                DateFormatter.iso8601.string(from: entry.date),
                entry.exercise.displayName,
                entry.format.displayName,
                entry.result.rawValue,
                entry.finalScore?.description ?? "",
                entry.averageFormScore?.description ?? "",
                entry.rating.description,
                entry.ratingChange.description
            ].joined(separator: ",")
            
            csvString += line + "\n"
        }
        
        return csvString.data(using: .utf8) ?? Data()
    }
    
    private func generatePDFReport(from history: [CompetitionHistoryEntry]) async throws -> Data {
        // This would generate a comprehensive PDF report
        // For now, returning empty data
        return Data()
    }
    
    // MARK: - Data Subscriptions
    
    private func setupDataSubscriptions() {
        achievementTracker.$newAchievements
            .receive(on: DispatchQueue.main)
            .sink { [weak self] achievements in
                // Handle new achievements
                print("New achievements unlocked: \(achievements)")
            }
            .store(in: &cancellables)
    }
}

// MARK: - Data Manager

class CompetitionDataManager {
    func loadCompetitionHistory(for userId: String) async throws -> [CompetitionHistoryEntry] {
        // This would load from CoreData/CloudKit
        return []
    }
    
    func loadCompleteHistory() async throws -> [CompetitionHistoryEntry] {
        // This would load complete history from storage
        return []
    }
    
    func saveCompetitionEntry(_ entry: CompetitionHistoryEntry) async throws {
        // This would save to CoreData/CloudKit
    }
}

// MARK: - Achievement Tracker

class CompetitionAchievementTracker: ObservableObject {
    @Published var newAchievements: [Achievement] = []
    
    func processCompetitionResult(_ entry: CompetitionHistoryEntry) async {
        // Process achievement logic
    }
    
    func getAchievementProgress(for userId: String) async -> [AchievementProgress] {
        return []
    }
}

// MARK: - Supporting Data Structures

class CompetitionAnalytics: ObservableObject {
    var totalCompetitions: Int = 0
    var totalWins: Int = 0
    var totalLosses: Int = 0
    var totalDraws: Int = 0
    var winRate: Float = 0
    var averageScore: Float = 0
    var bestScore: Float = 0
    var averageFormScore: Float = 0
    var currentWinStreak: Int = 0
    var longestWinStreak: Int = 0
    var formatStats: [CompetitionFormat: FormatStats] = [:]
    var exerciseStats: [ExerciseType: ExerciseStats] = [:]
    var ratingProgression: [RatingPoint] = []
}

struct FormatStats {
    var totalCompetitions: Int
    var wins: Int
    var averageScore: Float
    var bestScore: Float
    
    var winRate: Float {
        guard totalCompetitions > 0 else { return 0 }
        return Float(wins) / Float(totalCompetitions)
    }
}

struct ExerciseStats {
    var totalCompetitions: Int
    var wins: Int
    var averageScore: Float
    var averageFormScore: Float
    var bestScore: Float
    
    var winRate: Float {
        guard totalCompetitions > 0 else { return 0 }
        return Float(wins) / Float(totalCompetitions)
    }
}

struct RatingPoint: Identifiable {
    let id = UUID()
    let rating: Int
    let date: Date
    let change: Int
}

struct ProgressTrend {
    let timeframe: TimeFrame
    let direction: TrendDirection
    let scoreImprovement: Float
    let winRateChange: Float
    let competitionsAnalyzed: Int
    let confidence: Float
}

struct PersonalRecord: Identifiable {
    let id = UUID()
    let type: RecordType
    let value: Float
    let exercise: ExerciseType?
    let format: CompetitionFormat?
    let date: Date
    let description: String
}

enum RecordType: Equatable {
    case bestOverallScore
    case bestExerciseScore(ExerciseType)
    case bestFormScore
    case longestWinStreak
    
    var displayName: String {
        switch self {
        case .bestOverallScore: return "Best Overall Score"
        case .bestExerciseScore(let exercise): return "Best \(exercise.displayName) Score"
        case .bestFormScore: return "Best Form Score"
        case .longestWinStreak: return "Longest Win Streak"
        }
    }
}

struct ExerciseDetailedStats {
    let exercise: ExerciseType
    let totalCompetitions: Int
    let wins: Int
    let losses: Int
    let averageScore: Float
    let bestScore: Float
    let averageFormScore: Float
    let averageRating: Float
    let recentTrend: TrendDirection
    let lastCompetition: Date?
    
    var winRate: Float {
        guard totalCompetitions > 0 else { return 0 }
        return Float(wins) / Float(totalCompetitions)
    }
}

struct CompetitionTimelineEntry: Identifiable {
    let id = UUID()
    let date: Date
    let competitions: [CompetitionHistoryEntry]
    let totalCompetitions: Int
    let wins: Int
    let averageScore: Float
    
    var winRate: Float {
        guard totalCompetitions > 0 else { return 0 }
        return Float(wins) / Float(totalCompetitions)
    }
}

struct AchievementProgress: Identifiable {
    let id = UUID()
    let achievement: Achievement
    let currentProgress: Int
    let targetProgress: Int
    let isUnlocked: Bool
    let unlockedDate: Date?
    
    var progressPercentage: Float {
        guard targetProgress > 0 else { return 0 }
        return min(1.0, Float(currentProgress) / Float(targetProgress))
    }
}

enum TimeFrame: String, CaseIterable {
    case week = "week"
    case month = "month"
    case threeMonths = "three_months"
    case year = "year"
    case allTime = "all_time"
    
    var displayName: String {
        switch self {
        case .week: return "This Week"
        case .month: return "This Month"
        case .threeMonths: return "Last 3 Months"
        case .year: return "This Year"
        case .allTime: return "All Time"
        }
    }
}

enum ExportFormat: String, CaseIterable {
    case json = "json"
    case csv = "csv"
    case pdf = "pdf"
    
    var displayName: String {
        switch self {
        case .json: return "JSON"
        case .csv: return "CSV"
        case .pdf: return "PDF Report"
        }
    }
}

// MARK: - Notifications

extension Notification.Name {
    static let newPersonalRecord = Notification.Name("newPersonalRecord")
}