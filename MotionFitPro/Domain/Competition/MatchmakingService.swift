//
//  MatchmakingService.swift
//  MotionFitPro
//
//  Created by <PERSON> on 2025-07-15.
//

import Foundation
import Combine

// MARK: - Matchmaking Service

class MatchmakingService: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var isSearching: Bool = false
    @Published var estimatedWaitTime: TimeInterval = 0
    @Published var matchQuality: MatchQuality = .unknown
    @Published var currentSearchCriteria: SearchCriteria?
    
    // MARK: - Private Properties
    
    private let networkManager = CompetitionNetworkManager()
    private var searchTimer: Timer?
    private var searchStartTime: Date?
    private var expandedSearchRadius: Int = 0
    private let maxSearchRadius: Int = 5
    private let searchRadiusExpansionInterval: TimeInterval = 30.0
    
    // Matchmaking parameters
    private struct MatchmakingConfig {
        static let idealRatingDifference: Int = 50
        static let maxRatingDifference: Int = 200
        static let preferredPoolSize: Int = 8
        static let minPoolSize: Int = 2
        static let maxWaitTime: TimeInterval = 300 // 5 minutes
        static let skillGroupSize: Int = 100 // Rating points per skill group
    }
    
    // MARK: - Public Interface
    
    func findMatch(
        for userProfile: CompetitiveUserProfile,
        format: CompetitionFormat,
        exercise: ExerciseType
    ) async throws -> Competition? {
        
        let criteria = SearchCriteria(
            userProfile: userProfile,
            format: format,
            exercise: exercise,
            maxRatingDifference: MatchmakingConfig.idealRatingDifference,
            preferredOpponents: []
        )
        
        return try await performMatchmaking(criteria: criteria)
    }
    
    func startMatchmakingSearch(
        userProfile: CompetitiveUserProfile,
        format: CompetitionFormat,
        exercise: ExerciseType,
        preferredOpponents: [String] = []
    ) async throws -> Competition {
        
        guard !isSearching else {
            throw MatchmakingError.alreadySearching
        }
        
        let criteria = SearchCriteria(
            userProfile: userProfile,
            format: format,
            exercise: exercise,
            maxRatingDifference: MatchmakingConfig.idealRatingDifference,
            preferredOpponents: preferredOpponents
        )
        
        return try await startProgressiveSearch(criteria: criteria)
    }
    
    func cancelMatchmaking() {
        isSearching = false
        searchTimer?.invalidate()
        searchTimer = nil
        searchStartTime = nil
        expandedSearchRadius = 0
        currentSearchCriteria = nil
    }
    
    // MARK: - Core Matchmaking Logic
    
    private func performMatchmaking(criteria: SearchCriteria) async throws -> Competition? {
        // 1. Find existing competitions that match criteria
        let existingMatches = try await findExistingMatches(criteria: criteria)
        
        if let bestMatch = selectBestMatch(from: existingMatches, criteria: criteria) {
            return bestMatch
        }
        
        // 2. Find potential opponents for new competition
        let potentialOpponents = try await findPotentialOpponents(criteria: criteria)
        
        if potentialOpponents.count >= MatchmakingConfig.minPoolSize {
            return try await createNewCompetition(criteria: criteria, opponents: potentialOpponents)
        }
        
        return nil
    }
    
    private func startProgressiveSearch(criteria: SearchCriteria) async throws -> Competition {
        await MainActor.run {
            isSearching = true
            searchStartTime = Date()
            currentSearchCriteria = criteria
            expandedSearchRadius = 0
        }
        
        startSearchTimer()
        
        // Progressive search with expanding criteria
        while isSearching {
            let expandedCriteria = expandSearchCriteria(criteria)
            
            if let match = try await performMatchmaking(criteria: expandedCriteria) {
                await MainActor.run {
                    cancelMatchmaking()
                }
                return match
            }
            
            // Check for timeout
            if let startTime = searchStartTime,
               Date().timeIntervalSince(startTime) > MatchmakingConfig.maxWaitTime {
                throw MatchmakingError.searchTimeout
            }
            
            // Wait before expanding search further
            try await Task.sleep(nanoseconds: UInt64(searchRadiusExpansionInterval * 1_000_000_000))
            expandedSearchRadius += 1
            
            if expandedSearchRadius > maxSearchRadius {
                throw MatchmakingError.noOpponentsFound
            }
        }
        
        throw MatchmakingError.searchCancelled
    }
    
    // MARK: - Search Criteria Management
    
    private func expandSearchCriteria(_ originalCriteria: SearchCriteria) -> SearchCriteria {
        var expandedCriteria = originalCriteria
        
        // Expand rating difference tolerance
        let baseRatingDifference = MatchmakingConfig.idealRatingDifference
        let expansion = expandedSearchRadius * 25 // Increase by 25 rating points per expansion
        expandedCriteria.maxRatingDifference = min(
            MatchmakingConfig.maxRatingDifference,
            baseRatingDifference + expansion
        )
        
        // Consider cross-exercise matching after several expansions
        if expandedSearchRadius >= 3 {
            expandedCriteria.allowCrossExercise = true
        }
        
        // Allow different formats after maximum expansions
        if expandedSearchRadius >= maxSearchRadius {
            expandedCriteria.allowDifferentFormats = true
        }
        
        return expandedCriteria
    }
    
    // MARK: - Opponent Finding
    
    private func findExistingMatches(criteria: SearchCriteria) async throws -> [Competition] {
        // This would query the server for existing competitions
        // For now, we'll simulate with empty array
        return []
    }
    
    private func findPotentialOpponents(criteria: SearchCriteria) async throws -> [CompetitiveUserProfile] {
        // Build search parameters for the server
        let searchParams = OpponentSearchParams(
            exerciseType: criteria.exercise,
            minRating: criteria.userProfile.skillRating.currentRating - criteria.maxRatingDifference,
            maxRating: criteria.userProfile.skillRating.currentRating + criteria.maxRatingDifference,
            excludeUserIds: [criteria.userProfile.userId] + criteria.userProfile.blockedUsers,
            preferredUserIds: criteria.preferredOpponents,
            maxResults: MatchmakingConfig.preferredPoolSize
        )
        
        // This would query the server for potential opponents
        // For now, we'll return empty array
        return []
    }
    
    // MARK: - Match Selection and Creation
    
    private func selectBestMatch(from competitions: [Competition], criteria: SearchCriteria) -> Competition? {
        guard !competitions.isEmpty else { return nil }
        
        let scoredMatches = competitions.map { competition in
            (competition: competition, score: calculateMatchScore(competition, criteria: criteria))
        }
        
        let bestMatch = scoredMatches.max { $0.score < $1.score }
        return bestMatch?.competition
    }
    
    private func calculateMatchScore(_ competition: Competition, criteria: SearchCriteria) -> Float {
        var score: Float = 0.0
        
        // Rating similarity (higher score for closer ratings)
        let userRating = criteria.userProfile.skillRating.currentRating
        let opponentRatings = competition.participants.map { $0.skillRating }
        
        if !opponentRatings.isEmpty {
            let averageOpponentRating = opponentRatings.reduce(0, +) / opponentRatings.count
            let ratingDifference = abs(userRating - averageOpponentRating)
            let maxDifference = MatchmakingConfig.maxRatingDifference
            
            let ratingScore = max(0, Float(maxDifference - ratingDifference) / Float(maxDifference))
            score += ratingScore * 0.4
        }
        
        // Exercise match (perfect match gets full points)
        if competition.exercise == criteria.exercise {
            score += 0.3
        }
        
        // Format match
        if competition.format == criteria.format {
            score += 0.2
        }
        
        // Preferred opponents bonus
        let preferredOpponentsCount = competition.participants.filter { participant in
            criteria.preferredOpponents.contains(participant.userId)
        }.count
        
        if preferredOpponentsCount > 0 {
            score += Float(preferredOpponentsCount) * 0.1
        }
        
        return score
    }
    
    private func createNewCompetition(
        criteria: SearchCriteria,
        opponents: [CompetitiveUserProfile]
    ) async throws -> Competition {
        
        // Select best opponents from the pool
        let selectedOpponents = selectOptimalOpponents(
            from: opponents,
            for: criteria.userProfile,
            format: criteria.format
        )
        
        // Create competition
        let competition = Competition(
            id: UUID(),
            format: criteria.format,
            exercise: criteria.exercise,
            creatorId: criteria.userProfile.userId,
            skillLevelRequirement: criteria.userProfile.skillRating.skillLevel,
            isPublic: false, // Matchmade competitions are private
            state: .waitingForPlayers,
            maxParticipants: criteria.format.maxParticipants,
            settings: CompetitionSettings(format: criteria.format)
        )
        
        return competition
    }
    
    private func selectOptimalOpponents(
        from candidates: [CompetitiveUserProfile],
        for user: CompetitiveUserProfile,
        format: CompetitionFormat
    ) -> [CompetitiveUserProfile] {
        
        let maxOpponents = format.maxParticipants - 1 // Exclude the user
        
        // Score each candidate
        let scoredCandidates = candidates.map { candidate in
            (profile: candidate, score: calculateOpponentScore(candidate, against: user))
        }
        
        // Sort by score and take the best ones
        let bestOpponents = scoredCandidates
            .sorted { $0.score > $1.score }
            .prefix(maxOpponents)
            .map { $0.profile }
        
        return Array(bestOpponents)
    }
    
    private func calculateOpponentScore(_ opponent: CompetitiveUserProfile, against user: CompetitiveUserProfile) -> Float {
        var score: Float = 0.0
        
        // Rating similarity (closer ratings = higher score)
        let ratingDifference = abs(opponent.skillRating.currentRating - user.skillRating.currentRating)
        let maxDifference = MatchmakingConfig.maxRatingDifference
        let ratingScore = max(0, Float(maxDifference - ratingDifference) / Float(maxDifference))
        score += ratingScore * 0.5
        
        // Activity level (more recent activity = higher score)
        let daysSinceLastActive = Date().timeIntervalSince(opponent.lastActiveDate) / (24 * 3600)
        let activityScore = max(0, 1.0 - Float(daysSinceLastActive) / 30.0) // Penalize after 30 days
        score += activityScore * 0.2
        
        // Win rate similarity (avoid too easy or too hard opponents)
        let winRateDifference = abs(opponent.winRate - user.winRate)
        let winRateScore = max(0, 1.0 - winRateDifference * 2.0) // Penalize large win rate differences
        score += winRateScore * 0.2
        
        // Friend bonus
        if user.friends.contains(opponent.userId) {
            score += 0.1
        }
        
        return score
    }
    
    // MARK: - Search Timer Management
    
    private func startSearchTimer() {
        searchTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateSearchProgress()
            }
        }
    }
    
    @MainActor
    private func updateSearchProgress() {
        guard let startTime = searchStartTime else { return }
        
        let elapsed = Date().timeIntervalSince(startTime)
        
        // Update estimated wait time based on current search radius
        estimatedWaitTime = calculateEstimatedWaitTime(elapsed: elapsed)
        
        // Update match quality based on how much we've expanded the search
        matchQuality = calculateCurrentMatchQuality()
    }
    
    private func calculateEstimatedWaitTime(elapsed: TimeInterval) -> TimeInterval {
        // Base estimate on historical data and current search expansion
        let baseWaitTime: TimeInterval = 60.0 // 1 minute base
        let expansionPenalty = TimeInterval(expandedSearchRadius) * 30.0
        
        let estimatedTotal = baseWaitTime + expansionPenalty
        let remaining = max(0, estimatedTotal - elapsed)
        
        return remaining
    }
    
    private func calculateCurrentMatchQuality() -> MatchQuality {
        switch expandedSearchRadius {
        case 0: return .excellent
        case 1: return .good
        case 2: return .fair
        case 3: return .poor
        default: return .veryPoor
        }
    }
}

// MARK: - Supporting Data Structures

struct SearchCriteria {
    let userProfile: CompetitiveUserProfile
    let format: CompetitionFormat
    let exercise: ExerciseType
    var maxRatingDifference: Int
    let preferredOpponents: [String]
    var allowCrossExercise: Bool = false
    var allowDifferentFormats: Bool = false
}

struct OpponentSearchParams {
    let exerciseType: ExerciseType
    let minRating: Int
    let maxRating: Int
    let excludeUserIds: [String]
    let preferredUserIds: [String]
    let maxResults: Int
}

enum MatchQuality: String, CaseIterable {
    case unknown = "unknown"
    case veryPoor = "very_poor"
    case poor = "poor"
    case fair = "fair"
    case good = "good"
    case excellent = "excellent"
    
    var displayName: String {
        switch self {
        case .unknown: return "Unknown"
        case .veryPoor: return "Very Poor"
        case .poor: return "Poor"
        case .fair: return "Fair"
        case .good: return "Good"
        case .excellent: return "Excellent"
        }
    }
    
    var color: String {
        switch self {
        case .unknown: return "#9E9E9E"   // Gray
        case .veryPoor: return "#F44336"  // Red
        case .poor: return "#FF5722"      // Deep Orange
        case .fair: return "#FF9800"      // Orange
        case .good: return "#4CAF50"      // Green
        case .excellent: return "#2196F3" // Blue
        }
    }
    
    var description: String {
        switch self {
        case .unknown: return "Analyzing potential matches..."
        case .veryPoor: return "Match quality may be suboptimal"
        case .poor: return "Limited suitable opponents found"
        case .fair: return "Decent match quality expected"
        case .good: return "Good match quality expected"
        case .excellent: return "Excellent match quality expected"
        }
    }
}

// MARK: - Network Manager Placeholder

class CompetitionNetworkManager: ObservableObject {
    @Published var availableCompetitions: [Competition] = []
    @Published var competitionInvites: [CompetitionInvite] = []
    
    func createCompetition(_ competition: Competition) async throws {
        // Implementation would send competition to server
    }
    
    func joinCompetition(_ competitionId: UUID, userId: String) async throws {
        // Implementation would join user to competition on server
    }
    
    func leaveCompetition(_ competitionId: UUID, userId: String) async throws {
        // Implementation would remove user from competition on server
    }
    
    func updateCompetitionState(_ competition: Competition) async throws {
        // Implementation would update competition state on server
    }
    
    func submitPerformance(_ submission: CompetitionSubmission) async throws {
        // Implementation would submit performance data to server
    }
    
    func endCompetition(_ competitionId: UUID) async throws {
        // Implementation would end competition on server
    }
    
    func getCompetitionResults(_ competitionId: UUID) async throws -> CompetitionResults? {
        // Implementation would fetch competition results from server
        return nil
    }
    
    func loadUserProfile() async throws -> CompetitiveUserProfile {
        // Implementation would load user profile from server
        return CompetitiveUserProfile.createDefault()
    }
    
    func updateUserProfile(_ profile: CompetitiveUserProfile) async throws {
        // Implementation would update user profile on server
    }
    
    func refreshAvailableCompetitions() async throws {
        // Implementation would refresh available competitions from server
    }
}

struct CompetitionSubmission {
    let userId: String
    let competitionId: UUID
    let performanceData: CompetitionPerformanceData
    let score: CompetitionScore
    let timestamp: Date
    let validationHash: String
}

struct CompetitionResults {
    let competitionId: UUID
    let format: CompetitionFormat
    let exercise: ExerciseType
    let participants: [CompetitionParticipant]
    let endTime: Date
    
    func getUserPlacement(_ userId: String) -> Int? {
        return participants.first { $0.userId == userId }?.finalRating
    }
}

// MARK: - Errors

enum MatchmakingError: LocalizedError {
    case alreadySearching
    case searchTimeout
    case noOpponentsFound
    case searchCancelled
    case serverError(String)
    
    var errorDescription: String? {
        switch self {
        case .alreadySearching:
            return "Already searching for a match"
        case .searchTimeout:
            return "Search timed out - no suitable opponents found"
        case .noOpponentsFound:
            return "No opponents found matching your criteria"
        case .searchCancelled:
            return "Search was cancelled"
        case .serverError(let message):
            return "Server error: \(message)"
        }
    }
}