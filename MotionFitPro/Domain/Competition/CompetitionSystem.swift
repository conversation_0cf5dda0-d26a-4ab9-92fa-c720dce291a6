//
//  CompetitionSystem.swift
//  MotionFitPro
//
//  Created by <PERSON> on 2025-07-15.
//

import Foundation
import Combine

// MARK: - Competition System

@MainActor
class CompetitionSystem: ObservableObject {
    @Published var activeCompetition: Competition?
    @Published var availableCompetitions: [Competition] = []
    @Published var competitionInvites: [CompetitionInvite] = []
    @Published var currentUserProfile: CompetitiveUserProfile?
    @Published var connectionQuality: ConnectionQuality = .good
    @Published var liveLeaderboard: LiveLeaderboard?
    
    private let networkManager = CompetitionNetworkManager()
    private let scoringSystem = StandardizedScoringSystem()
    private let antiCheatSystem = AntiCheatSystem()
    private let matchmakingService = MatchmakingService()
    private let realTimeSync = RealTimeSyncManager()
    
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        setupNetworkSubscriptions()
        loadUserProfile()
    }
    
    // MARK: - Competition Creation & Joining
    
    func createCompetition(
        format: CompetitionFormat,
        exercise: ExerciseType,
        skillLevel: SkillLevel? = nil,
        isPublic: Bool = true
    ) async throws -> Competition {
        let competition = Competition(
            id: UUID(),
            format: format,
            exercise: exercise,
            creatorId: currentUserProfile?.userId ?? "",
            skillLevelRequirement: skillLevel,
            isPublic: isPublic,
            state: .waitingForPlayers,
            maxParticipants: format.maxParticipants,
            settings: CompetitionSettings(format: format)
        )
        
        try await networkManager.createCompetition(competition)
        
        if isPublic {
            availableCompetitions.append(competition)
        }
        
        return competition
    }
    
    func joinCompetition(_ competitionId: UUID) async throws {
        guard let userProfile = currentUserProfile else {
            throw CompetitionError.userNotAuthenticated
        }
        
        // Check skill level eligibility
        if let competition = availableCompetitions.first(where: { $0.id == competitionId }),
           let requiredSkill = competition.skillLevelRequirement {
            guard userProfile.skillRating.isEligibleFor(requiredSkill) else {
                throw CompetitionError.skillLevelMismatch
            }
        }
        
        try await networkManager.joinCompetition(competitionId, userId: userProfile.userId)
        
        // Start real-time sync for this competition
        realTimeSync.subscribeToCompetition(competitionId)
    }
    
    func leaveCompetition(_ competitionId: UUID) async throws {
        try await networkManager.leaveCompetition(competitionId, userId: currentUserProfile?.userId ?? "")
        realTimeSync.unsubscribeFromCompetition(competitionId)
        
        if activeCompetition?.id == competitionId {
            activeCompetition = nil
            liveLeaderboard = nil
        }
    }
    
    // MARK: - Matchmaking
    
    func findMatchedCompetition(
        format: CompetitionFormat,
        exercise: ExerciseType
    ) async throws -> Competition? {
        guard let userProfile = currentUserProfile else {
            throw CompetitionError.userNotAuthenticated
        }
        
        return try await matchmakingService.findMatch(
            for: userProfile,
            format: format,
            exercise: exercise
        )
    }
    
    func quickMatch(exercise: ExerciseType) async throws -> Competition {
        // Try to find existing match first
        if let existingMatch = try await findMatchedCompetition(
            format: .bestOfThree,
            exercise: exercise
        ) {
            try await joinCompetition(existingMatch.id)
            return existingMatch
        }
        
        // Create new competition with skill-based matching
        let skillLevel = currentUserProfile?.skillRating.skillLevel ?? .intermediate
        return try await createCompetition(
            format: .bestOfThree,
            exercise: exercise,
            skillLevel: skillLevel,
            isPublic: true
        )
    }
    
    // MARK: - Competition Execution
    
    func startCompetition(_ competitionId: UUID) async throws {
        guard let competition = availableCompetitions.first(where: { $0.id == competitionId }) else {
            throw CompetitionError.competitionNotFound
        }
        
        let updatedCompetition = competition.withState(.starting)
        try await networkManager.updateCompetitionState(updatedCompetition)
        
        activeCompetition = updatedCompetition
        
        // Initialize live leaderboard
        liveLeaderboard = LiveLeaderboard(
            competitionId: competitionId,
            participants: competition.participants
        )
        
        // Start synchronized countdown
        try await realTimeSync.startSynchronizedCountdown(competitionId: competitionId)
    }
    
    func submitPerformanceData(
        _ data: CompetitionPerformanceData
    ) async throws {
        guard let competition = activeCompetition else {
            throw CompetitionError.noActiveCompetition
        }
        
        // Anti-cheat validation
        let validationResult = try await antiCheatSystem.validatePerformance(data)
        guard validationResult.isValid else {
            throw CompetitionError.suspiciousActivity(validationResult.reason)
        }
        
        // Calculate standardized score
        let score = scoringSystem.calculateScore(
            performanceData: data,
            format: competition.format
        )
        
        let submission = CompetitionSubmission(
            userId: currentUserProfile?.userId ?? "",
            competitionId: competition.id,
            performanceData: data,
            score: score,
            timestamp: Date(),
            validationHash: validationResult.hash
        )
        
        try await networkManager.submitPerformance(submission)
        
        // Update local leaderboard immediately
        liveLeaderboard?.updateScore(
            userId: submission.userId,
            score: score.totalScore
        )
    }
    
    func endCompetition(_ competitionId: UUID) async throws {
        try await networkManager.endCompetition(competitionId)
        
        if activeCompetition?.id == competitionId {
            // Process final results
            if let finalResults = try await networkManager.getCompetitionResults(competitionId) {
                await processCompetitionResults(finalResults)
            }
            
            activeCompetition = nil
            liveLeaderboard = nil
        }
        
        realTimeSync.unsubscribeFromCompetition(competitionId)
    }
    
    // MARK: - Results Processing
    
    private func processCompetitionResults(_ results: CompetitionResults) async {
        guard var userProfile = currentUserProfile else { return }
        
        // Update skill rating (ELO-like system)
        let ratingChange = calculateRatingChange(
            userProfile: userProfile,
            results: results
        )
        
        userProfile.skillRating.updateRating(change: ratingChange)
        
        // Update competition history
        let historyEntry = CompetitionHistoryEntry(
            competitionId: results.competitionId,
            format: results.format,
            exercise: results.exercise,
            placement: results.getUserPlacement(userProfile.userId),
            rating: userProfile.skillRating.currentRating,
            ratingChange: ratingChange,
            date: results.endTime
        )
        
        userProfile.competitionHistory.append(historyEntry)
        
        // Update win/loss record
        if let placement = results.getUserPlacement(userProfile.userId) {
            if placement == 1 {
                userProfile.statistics.wins += 1
            } else {
                userProfile.statistics.losses += 1
            }
        }
        
        currentUserProfile = userProfile
        
        // Save updated profile
        try? await networkManager.updateUserProfile(userProfile)
    }
    
    private func calculateRatingChange(
        userProfile: CompetitiveUserProfile,
        results: CompetitionResults
    ) -> Int {
        // Simplified ELO calculation
        let userPlacement = results.getUserPlacement(userProfile.userId) ?? results.participants.count
        let expectedScore = calculateExpectedScore(
            userRating: userProfile.skillRating.currentRating,
            opponentRatings: results.participants.compactMap { participant in
                participant.userId != userProfile.userId ? participant.finalRating : nil
            }
        )
        
        let actualScore = Float(results.participants.count - userPlacement + 1) / Float(results.participants.count)
        let kFactor = userProfile.skillRating.kFactor
        
        return Int(Float(kFactor) * (actualScore - expectedScore))
    }
    
    private func calculateExpectedScore(userRating: Int, opponentRatings: [Int]) -> Float {
        guard !opponentRatings.isEmpty else { return 0.5 }
        
        let averageOpponentRating = Float(opponentRatings.reduce(0, +)) / Float(opponentRatings.count)
        let ratingDifference = Float(userRating) - averageOpponentRating
        
        return 1.0 / (1.0 + pow(10.0, -ratingDifference / 400.0))
    }
    
    // MARK: - Network Setup
    
    private func setupNetworkSubscriptions() {
        realTimeSync.$competitionUpdates
            .receive(on: DispatchQueue.main)
            .sink { [weak self] update in
                self?.handleCompetitionUpdate(update)
            }
            .store(in: &cancellables)
        
        realTimeSync.$connectionQuality
            .receive(on: DispatchQueue.main)
            .assign(to: &$connectionQuality)
        
        networkManager.$availableCompetitions
            .receive(on: DispatchQueue.main)
            .assign(to: &$availableCompetitions)
        
        networkManager.$competitionInvites
            .receive(on: DispatchQueue.main)
            .assign(to: &$competitionInvites)
    }
    
    private func handleCompetitionUpdate(_ update: CompetitionUpdate) {
        switch update.type {
        case .stateChanged:
            if activeCompetition?.id == update.competitionId {
                activeCompetition = activeCompetition?.withState(update.newState ?? .ended)
            }
            
        case .participantJoined, .participantLeft:
            updateActiveCompetitionParticipants(update)
            
        case .scoreUpdate:
            if let scoreUpdate = update.scoreData {
                liveLeaderboard?.updateScore(
                    userId: scoreUpdate.userId,
                    score: scoreUpdate.score
                )
            }
            
        case .competitionEnded:
            Task {
                try? await endCompetition(update.competitionId)
            }
        }
    }
    
    private func updateActiveCompetitionParticipants(_ update: CompetitionUpdate) {
        guard var competition = activeCompetition,
              competition.id == update.competitionId else { return }
        
        switch update.type {
        case .participantJoined:
            if let newParticipant = update.participant {
                competition.participants.append(newParticipant)
            }
        case .participantLeft:
            if let participantId = update.participant?.userId {
                competition.participants.removeAll { $0.userId == participantId }
            }
        default:
            break
        }
        
        activeCompetition = competition
    }
    
    private func loadUserProfile() {
        Task {
            do {
                currentUserProfile = try await networkManager.loadUserProfile()
            } catch {
                print("Failed to load user profile: \(error)")
                // Create default profile
                currentUserProfile = CompetitiveUserProfile.createDefault()
            }
        }
    }
    
    // MARK: - Public Interface
    
    func getCompetitionHistory() -> [CompetitionHistoryEntry] {
        return currentUserProfile?.competitionHistory ?? []
    }
    
    func getUserStats() -> CompetitionStatistics {
        return currentUserProfile?.statistics ?? CompetitionStatistics()
    }
    
    func getSkillRating() -> SkillRating {
        return currentUserProfile?.skillRating ?? SkillRating()
    }
    
    func refreshAvailableCompetitions() async {
        try? await networkManager.refreshAvailableCompetitions()
    }
}

// MARK: - Competition Models

struct Competition: Identifiable, Codable {
    let id: UUID
    let format: CompetitionFormat
    let exercise: ExerciseType
    let creatorId: String
    let skillLevelRequirement: SkillLevel?
    let isPublic: Bool
    var state: CompetitionState
    let maxParticipants: Int
    var participants: [CompetitionParticipant]
    let settings: CompetitionSettings
    let createdAt: Date
    var startTime: Date?
    var endTime: Date?
    
    init(
        id: UUID,
        format: CompetitionFormat,
        exercise: ExerciseType,
        creatorId: String,
        skillLevelRequirement: SkillLevel?,
        isPublic: Bool,
        state: CompetitionState,
        maxParticipants: Int,
        settings: CompetitionSettings
    ) {
        self.id = id
        self.format = format
        self.exercise = exercise
        self.creatorId = creatorId
        self.skillLevelRequirement = skillLevelRequirement
        self.isPublic = isPublic
        self.state = state
        self.maxParticipants = maxParticipants
        self.participants = []
        self.settings = settings
        self.createdAt = Date()
    }
    
    func withState(_ newState: CompetitionState) -> Competition {
        var updated = self
        updated.state = newState
        return updated
    }
}

enum CompetitionFormat: String, CaseIterable, Codable {
    case bestOfThree = "best_of_three"
    case endurance = "endurance"
    case formFocus = "form_focus"
    case timeAttack = "time_attack"
    
    var displayName: String {
        switch self {
        case .bestOfThree: return "Best of 3"
        case .endurance: return "Endurance Challenge"
        case .formFocus: return "Form Focus"
        case .timeAttack: return "Time Attack"
        }
    }
    
    var description: String {
        switch self {
        case .bestOfThree: return "First to win 2 rounds"
        case .endurance: return "Longest sustained performance"
        case .formFocus: return "Perfect technique wins"
        case .timeAttack: return "Fastest completion time"
        }
    }
    
    var maxParticipants: Int {
        switch self {
        case .bestOfThree: return 2
        case .endurance: return 8
        case .formFocus: return 4
        case .timeAttack: return 6
        }
    }
    
    var duration: TimeInterval {
        switch self {
        case .bestOfThree: return 300 // 5 minutes max
        case .endurance: return 900   // 15 minutes
        case .formFocus: return 180   // 3 minutes
        case .timeAttack: return 120  // 2 minutes
        }
    }
}

enum CompetitionState: String, Codable {
    case waitingForPlayers = "waiting"
    case starting = "starting"
    case active = "active"
    case paused = "paused"
    case ending = "ending"
    case ended = "ended"
    case cancelled = "cancelled"
}

struct CompetitionSettings: Codable {
    let format: CompetitionFormat
    let allowSpectators: Bool
    let requireCameraVerification: Bool
    let difficultyLevel: ExerciseDifficulty
    let antiCheatLevel: AntiCheatLevel
    
    init(format: CompetitionFormat) {
        self.format = format
        self.allowSpectators = format != .bestOfThree
        self.requireCameraVerification = true
        self.difficultyLevel = .intermediate
        self.antiCheatLevel = .high
    }
}

enum AntiCheatLevel: String, Codable {
    case basic = "basic"
    case standard = "standard"
    case high = "high"
    case maximum = "maximum"
}

struct CompetitionParticipant: Identifiable, Codable {
    let id = UUID()
    let userId: String
    let username: String
    let skillRating: Int
    let avatarUrl: String?
    var isReady: Bool
    var currentScore: Float
    var finalRating: Int?
    let joinTime: Date
    
    init(userId: String, username: String, skillRating: Int, avatarUrl: String? = nil) {
        self.userId = userId
        self.username = username
        self.skillRating = skillRating
        self.avatarUrl = avatarUrl
        self.isReady = false
        self.currentScore = 0
        self.joinTime = Date()
    }
}

struct CompetitionInvite: Identifiable, Codable {
    let id = UUID()
    let competitionId: UUID
    let fromUserId: String
    let fromUsername: String
    let toUserId: String
    let message: String?
    let createdAt: Date
    let expiresAt: Date
    var status: InviteStatus
    
    enum InviteStatus: String, Codable {
        case pending = "pending"
        case accepted = "accepted"
        case declined = "declined"
        case expired = "expired"
    }
}

// MARK: - Competition Errors

enum CompetitionError: LocalizedError {
    case userNotAuthenticated
    case competitionNotFound
    case competitionFull
    case skillLevelMismatch
    case noActiveCompetition
    case suspiciousActivity(String)
    case networkError(String)
    case invalidPerformanceData
    
    var errorDescription: String? {
        switch self {
        case .userNotAuthenticated:
            return "User not authenticated"
        case .competitionNotFound:
            return "Competition not found"
        case .competitionFull:
            return "Competition is full"
        case .skillLevelMismatch:
            return "Skill level doesn't match requirements"
        case .noActiveCompetition:
            return "No active competition"
        case .suspiciousActivity(let reason):
            return "Suspicious activity detected: \(reason)"
        case .networkError(let message):
            return "Network error: \(message)"
        case .invalidPerformanceData:
            return "Invalid performance data"
        }
    }
}