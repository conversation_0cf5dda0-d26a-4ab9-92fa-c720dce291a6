//
//  AntiCheatSystem.swift
//  MotionFitPro
//
//  Created by <PERSON> on 2025-07-15.
//

import Foundation
import CoreML
import ARKit

// MARK: - Anti-Cheat Detection System

class AntiCheatSystem {
    
    // MARK: - Detection Thresholds
    
    private struct DetectionThresholds {
        // Motion analysis thresholds
        static let maxUnrealisticVelocity: Float = 5.0    // m/s
        static let minNaturalVariation: Float = 0.1       // Coefficient of variation
        static let maxTemporalInconsistency: Float = 0.3  // Standard deviation
        static let minBiomechanicalPlausibility: Float = 0.7
        
        // Device and environment thresholds
        static let minCameraQuality: Float = 0.6
        static let maxNetworkLatencyMs: Float = 200.0
        static let minTrackingConfidence: Float = 0.8
        static let maxFrameDropRate: Float = 0.1
        
        // Performance anomaly thresholds
        static let maxFormScoreJump: Float = 0.3          // Between consecutive reps
        static let maxSpeedIncreaseRatio: Float = 2.0     // Speed improvement limit
        static let minConsistencyScore: Float = 0.4       // Overall performance consistency
        static let maxPerfectionRate: Float = 0.95        // Too many perfect reps
        
        // Behavioral pattern thresholds
        static let maxIdenticalRepSequence: Int = 3       // Identical reps in sequence
        static let minHumanErrorRate: Float = 0.05        // Natural error occurrence
        static let maxRobotPatternScore: Float = 0.8      // Robotic movement detection
    }
    
    // MARK: - Main Validation Function
    
    func validatePerformance(_ data: CompetitionPerformanceData) async throws -> ValidationResult {
        var suspiciousActivities: [SuspiciousActivity] = []
        var confidenceScore: Float = 1.0
        
        // 1. Motion Analysis Validation
        let motionAnalysis = analyzeMotionPatterns(data.motionSignature, repData: data.repData)
        suspiciousActivities.append(contentsOf: motionAnalysis.suspiciousActivities)
        confidenceScore *= motionAnalysis.confidenceMultiplier
        
        // 2. Device and Environment Validation
        let deviceValidation = validateDeviceIntegrity(data.deviceMetadata)
        suspiciousActivities.append(contentsOf: deviceValidation.suspiciousActivities)
        confidenceScore *= deviceValidation.confidenceMultiplier
        
        // 3. Performance Pattern Analysis
        let performanceAnalysis = analyzePerformancePatterns(data.repData)
        suspiciousActivities.append(contentsOf: performanceAnalysis.suspiciousActivities)
        confidenceScore *= performanceAnalysis.confidenceMultiplier
        
        // 4. Temporal Consistency Analysis
        let temporalAnalysis = analyzeTemporalConsistency(data.repData)
        suspiciousActivities.append(contentsOf: temporalAnalysis.suspiciousActivities)
        confidenceScore *= temporalAnalysis.confidenceMultiplier
        
        // 5. Cross-Reference Historical Patterns
        let historicalAnalysis = await analyzeHistoricalConsistency(data)
        suspiciousActivities.append(contentsOf: historicalAnalysis.suspiciousActivities)
        confidenceScore *= historicalAnalysis.confidenceMultiplier
        
        // Determine overall validation result
        let riskLevel = calculateRiskLevel(suspiciousActivities)
        let isValid = riskLevel != .high && confidenceScore > 0.5
        
        return ValidationResult(
            isValid: isValid,
            confidenceScore: confidenceScore,
            riskLevel: riskLevel,
            suspiciousActivities: suspiciousActivities,
            reason: isValid ? nil : generateFailureReason(suspiciousActivities),
            hash: generateValidationHash(data)
        )
    }
    
    // MARK: - Motion Pattern Analysis
    
    private func analyzeMotionPatterns(
        _ signature: MotionSignature,
        repData: [RepData]
    ) -> AnalysisResult {
        var suspiciousActivities: [SuspiciousActivity] = []
        var confidenceMultiplier: Float = 1.0
        
        // Check for unrealistic joint velocities
        if signature.avgJointVelocity > DetectionThresholds.maxUnrealisticVelocity {
            suspiciousActivities.append(SuspiciousActivity(
                type: .unrealisticMotion,
                severity: .high,
                description: "Joint velocities exceed human physical limits",
                confidence: 0.9,
                detectedValue: signature.avgJointVelocity,
                threshold: DetectionThresholds.maxUnrealisticVelocity
            ))
            confidenceMultiplier *= 0.3
        }
        
        // Check for insufficient natural variation
        if signature.naturalVariation < DetectionThresholds.minNaturalVariation {
            suspiciousActivities.append(SuspiciousActivity(
                type: .roboticMovement,
                severity: .medium,
                description: "Movement patterns lack natural human variation",
                confidence: 0.8,
                detectedValue: signature.naturalVariation,
                threshold: DetectionThresholds.minNaturalVariation
            ))
            confidenceMultiplier *= 0.7
        }
        
        // Check temporal consistency
        if signature.temporalConsistency > DetectionThresholds.maxTemporalInconsistency {
            suspiciousActivities.append(SuspiciousActivity(
                type: .temporalManipulation,
                severity: .medium,
                description: "Timing patterns suggest artificial acceleration",
                confidence: 0.75,
                detectedValue: signature.temporalConsistency,
                threshold: DetectionThresholds.maxTemporalInconsistency
            ))
            confidenceMultiplier *= 0.8
        }
        
        // Check biomechanical plausibility
        if signature.biomechanicalPlausibility < DetectionThresholds.minBiomechanicalPlausibility {
            suspiciousActivities.append(SuspiciousActivity(
                type: .biomechanicalViolation,
                severity: .high,
                description: "Joint movements violate human biomechanical constraints",
                confidence: 0.85,
                detectedValue: signature.biomechanicalPlausibility,
                threshold: DetectionThresholds.minBiomechanicalPlausibility
            ))
            confidenceMultiplier *= 0.4
        }
        
        // Analyze individual rep joint angles for consistency
        let jointAngleAnalysis = analyzeJointAnglePatterns(repData)
        suspiciousActivities.append(contentsOf: jointAngleAnalysis)
        
        return AnalysisResult(
            suspiciousActivities: suspiciousActivities,
            confidenceMultiplier: confidenceMultiplier
        )
    }
    
    // MARK: - Device Integrity Validation
    
    private func validateDeviceIntegrity(_ metadata: DeviceMetadata) -> AnalysisResult {
        var suspiciousActivities: [SuspiciousActivity] = []
        var confidenceMultiplier: Float = 1.0
        
        // Check camera quality
        if let cameraQuality = Float(metadata.cameraQuality), 
           cameraQuality < DetectionThresholds.minCameraQuality {
            suspiciousActivities.append(SuspiciousActivity(
                type: .deviceManipulation,
                severity: .medium,
                description: "Camera quality suggests potential video manipulation",
                confidence: 0.7,
                detectedValue: cameraQuality,
                threshold: DetectionThresholds.minCameraQuality
            ))
            confidenceMultiplier *= 0.8
        }
        
        // Check network latency for potential replay attacks
        if metadata.networkLatency > DetectionThresholds.maxNetworkLatencyMs {
            suspiciousActivities.append(SuspiciousActivity(
                type: .networkManipulation,
                severity: .low,
                description: "High network latency suggests potential data buffering",
                confidence: 0.6,
                detectedValue: metadata.networkLatency,
                threshold: DetectionThresholds.maxNetworkLatencyMs
            ))
            confidenceMultiplier *= 0.9
        }
        
        // Check device consistency (prevent device switching mid-competition)
        if isDeviceSwitchingDetected(metadata) {
            suspiciousActivities.append(SuspiciousActivity(
                type: .deviceSwitching,
                severity: .high,
                description: "Device characteristics changed during competition",
                confidence: 0.9,
                detectedValue: 1.0,
                threshold: 0.0
            ))
            confidenceMultiplier *= 0.2
        }
        
        return AnalysisResult(
            suspiciousActivities: suspiciousActivities,
            confidenceMultiplier: confidenceMultiplier
        )
    }
    
    // MARK: - Performance Pattern Analysis
    
    private func analyzePerformancePatterns(_ repData: [RepData]) -> AnalysisResult {
        var suspiciousActivities: [SuspiciousActivity] = []
        var confidenceMultiplier: Float = 1.0
        
        // Check for sudden form score improvements
        let formScores = repData.map { $0.formScore }
        let formJumps = analyzeFormScoreJumps(formScores)
        suspiciousActivities.append(contentsOf: formJumps)
        
        // Check for identical rep sequences
        let identicalSequences = findIdenticalRepSequences(repData)
        suspiciousActivities.append(contentsOf: identicalSequences)
        
        // Check for too-perfect performance
        let perfectionRate = calculatePerfectionRate(formScores)
        if perfectionRate > DetectionThresholds.maxPerfectionRate {
            suspiciousActivities.append(SuspiciousActivity(
                type: .unrealisticPerformance,
                severity: .medium,
                description: "Performance too consistent to be human",
                confidence: 0.75,
                detectedValue: perfectionRate,
                threshold: DetectionThresholds.maxPerfectionRate
            ))
            confidenceMultiplier *= 0.7
        }
        
        // Check for insufficient human error patterns
        let errorRate = calculateNaturalErrorRate(repData)
        if errorRate < DetectionThresholds.minHumanErrorRate {
            suspiciousActivities.append(SuspiciousActivity(
                type: .roboticMovement,
                severity: .medium,
                description: "Lack of natural human movement variations",
                confidence: 0.8,
                detectedValue: errorRate,
                threshold: DetectionThresholds.minHumanErrorRate
            ))
            confidenceMultiplier *= 0.8
        }
        
        return AnalysisResult(
            suspiciousActivities: suspiciousActivities,
            confidenceMultiplier: confidenceMultiplier
        )
    }
    
    // MARK: - Temporal Consistency Analysis
    
    private func analyzeTemporalConsistency(_ repData: [RepData]) -> AnalysisResult {
        var suspiciousActivities: [SuspiciousActivity] = []
        var confidenceMultiplier: Float = 1.0
        
        // Analyze rep timing patterns
        let intervals = calculateRepIntervals(repData)
        let timingConsistency = calculateTimingConsistency(intervals)
        
        if timingConsistency > 0.95 { // Too consistent timing
            suspiciousActivities.append(SuspiciousActivity(
                type: .roboticMovement,
                severity: .medium,
                description: "Rep timing too consistent to be human",
                confidence: 0.8,
                detectedValue: timingConsistency,
                threshold: 0.95
            ))
            confidenceMultiplier *= 0.75
        }
        
        // Check for timestamp manipulation
        if detectTimestampManipulation(repData) {
            suspiciousActivities.append(SuspiciousActivity(
                type: .temporalManipulation,
                severity: .high,
                description: "Timestamp inconsistencies detected",
                confidence: 0.9,
                detectedValue: 1.0,
                threshold: 0.0
            ))
            confidenceMultiplier *= 0.3
        }
        
        return AnalysisResult(
            suspiciousActivities: suspiciousActivities,
            confidenceMultiplier: confidenceMultiplier
        )
    }
    
    // MARK: - Historical Consistency Analysis
    
    private func analyzeHistoricalConsistency(_ data: CompetitionPerformanceData) async -> AnalysisResult {
        var suspiciousActivities: [SuspiciousActivity] = []
        var confidenceMultiplier: Float = 1.0
        
        // This would integrate with user's historical performance data
        // For now, we'll do basic checks
        
        // Check for impossible improvement rates
        let currentAvgFormScore = data.averageFormScore
        // In a real implementation, we'd compare with historical data
        
        // Check for performance inconsistency with skill level
        // This would require integration with the user profile system
        
        return AnalysisResult(
            suspiciousActivities: suspiciousActivities,
            confidenceMultiplier: confidenceMultiplier
        )
    }
    
    // MARK: - Helper Analysis Functions
    
    private func analyzeJointAnglePatterns(_ repData: [RepData]) -> [SuspiciousActivity] {
        var suspiciousActivities: [SuspiciousActivity] = []
        
        // Check for identical joint angles across reps
        let jointAngleSets = repData.map { $0.jointAngles }
        var identicalCount = 0
        
        for i in 1..<jointAngleSets.count {
            if areJointAnglesIdentical(jointAngleSets[i-1], jointAngleSets[i]) {
                identicalCount += 1
            }
        }
        
        if identicalCount > DetectionThresholds.maxIdenticalRepSequence {
            suspiciousActivities.append(SuspiciousActivity(
                type: .dataManipulation,
                severity: .high,
                description: "Identical joint angles detected across multiple reps",
                confidence: 0.95,
                detectedValue: Float(identicalCount),
                threshold: Float(DetectionThresholds.maxIdenticalRepSequence)
            ))
        }
        
        return suspiciousActivities
    }
    
    private func analyzeFormScoreJumps(_ formScores: [Float]) -> [SuspiciousActivity] {
        var suspiciousActivities: [SuspiciousActivity] = []
        
        for i in 1..<formScores.count {
            let jump = formScores[i] - formScores[i-1]
            if jump > DetectionThresholds.maxFormScoreJump {
                suspiciousActivities.append(SuspiciousActivity(
                    type: .unrealisticImprovement,
                    severity: .medium,
                    description: "Sudden form score improvement detected",
                    confidence: 0.8,
                    detectedValue: jump,
                    threshold: DetectionThresholds.maxFormScoreJump
                ))
            }
        }
        
        return suspiciousActivities
    }
    
    private func findIdenticalRepSequences(_ repData: [RepData]) -> [SuspiciousActivity] {
        var suspiciousActivities: [SuspiciousActivity] = []
        
        var consecutiveIdentical = 0
        for i in 1..<repData.count {
            if areRepsIdentical(repData[i-1], repData[i]) {
                consecutiveIdentical += 1
            } else {
                consecutiveIdentical = 0
            }
            
            if consecutiveIdentical >= DetectionThresholds.maxIdenticalRepSequence {
                suspiciousActivities.append(SuspiciousActivity(
                    type: .dataManipulation,
                    severity: .high,
                    description: "Identical rep sequences detected",
                    confidence: 0.9,
                    detectedValue: Float(consecutiveIdentical),
                    threshold: Float(DetectionThresholds.maxIdenticalRepSequence)
                ))
                break
            }
        }
        
        return suspiciousActivities
    }
    
    private func calculatePerfectionRate(_ formScores: [Float]) -> Float {
        let perfectReps = formScores.filter { $0 > 0.95 }.count
        return Float(perfectReps) / Float(formScores.count)
    }
    
    private func calculateNaturalErrorRate(_ repData: [RepData]) -> Float {
        let errorReps = repData.filter { !$0.detectedIssues.isEmpty }.count
        return Float(errorReps) / Float(repData.count)
    }
    
    private func calculateRepIntervals(_ repData: [RepData]) -> [TimeInterval] {
        var intervals: [TimeInterval] = []
        for i in 1..<repData.count {
            let interval = repData[i].timestamp.timeIntervalSince(repData[i-1].timestamp)
            intervals.append(interval)
        }
        return intervals
    }
    
    private func calculateTimingConsistency(_ intervals: [TimeInterval]) -> Float {
        guard intervals.count > 1 else { return 1.0 }
        
        let mean = intervals.reduce(0, +) / Double(intervals.count)
        let variance = intervals.map { pow($0 - mean, 2) }.reduce(0, +) / Double(intervals.count)
        let standardDeviation = sqrt(variance)
        
        let coefficientOfVariation = standardDeviation / mean
        return 1.0 - Float(coefficientOfVariation)
    }
    
    private func detectTimestampManipulation(_ repData: [RepData]) -> Bool {
        // Check for non-monotonic timestamps
        for i in 1..<repData.count {
            if repData[i].timestamp <= repData[i-1].timestamp {
                return true
            }
        }
        
        // Check for unrealistic timestamp gaps
        let intervals = calculateRepIntervals(repData)
        for interval in intervals {
            if interval < 0.1 || interval > 30.0 { // Rep intervals outside reasonable range
                return true
            }
        }
        
        return false
    }
    
    private func areJointAnglesIdentical(_ angles1: [String: Float], _ angles2: [String: Float]) -> Bool {
        guard angles1.keys == angles2.keys else { return false }
        
        for key in angles1.keys {
            guard let angle1 = angles1[key], let angle2 = angles2[key] else { return false }
            if abs(angle1 - angle2) > 1.0 { // Allow 1 degree tolerance
                return false
            }
        }
        
        return true
    }
    
    private func areRepsIdentical(_ rep1: RepData, _ rep2: RepData) -> Bool {
        return abs(rep1.formScore - rep2.formScore) < 0.01 &&
               abs(rep1.duration - rep2.duration) < 0.1 &&
               areJointAnglesIdentical(rep1.jointAngles, rep2.jointAngles)
    }
    
    private func isDeviceSwitchingDetected(_ metadata: DeviceMetadata) -> Bool {
        // This would check against stored device fingerprints
        // For now, basic implementation
        return false
    }
    
    // MARK: - Risk Assessment
    
    private func calculateRiskLevel(_ activities: [SuspiciousActivity]) -> RiskLevel {
        let highSeverityCount = activities.filter { $0.severity == .high }.count
        let mediumSeverityCount = activities.filter { $0.severity == .medium }.count
        
        if highSeverityCount >= 2 {
            return .high
        } else if highSeverityCount >= 1 || mediumSeverityCount >= 3 {
            return .medium
        } else if mediumSeverityCount >= 1 {
            return .low
        } else {
            return .none
        }
    }
    
    private func generateFailureReason(_ activities: [SuspiciousActivity]) -> String {
        let highPriorityActivities = activities
            .filter { $0.severity == .high }
            .sorted { $0.confidence > $1.confidence }
        
        if let primaryIssue = highPriorityActivities.first {
            return primaryIssue.description
        }
        
        let mediumPriorityActivities = activities
            .filter { $0.severity == .medium }
            .sorted { $0.confidence > $1.confidence }
        
        return mediumPriorityActivities.first?.description ?? "Multiple suspicious patterns detected"
    }
    
    private func generateValidationHash(_ data: CompetitionPerformanceData) -> String {
        // Generate cryptographic hash of performance data for integrity
        let hashableData = "\(data.userId)\(data.sessionId)\(data.timestamp.timeIntervalSince1970)"
        return hashableData.hash.description
    }
}

// MARK: - Supporting Data Structures

struct ValidationResult {
    let isValid: Bool
    let confidenceScore: Float
    let riskLevel: RiskLevel
    let suspiciousActivities: [SuspiciousActivity]
    let reason: String?
    let hash: String
}

enum RiskLevel: String, CaseIterable {
    case none = "none"
    case low = "low"
    case medium = "medium"
    case high = "high"
    
    var description: String {
        switch self {
        case .none: return "No Risk"
        case .low: return "Low Risk"
        case .medium: return "Medium Risk"
        case .high: return "High Risk"
        }
    }
}

struct SuspiciousActivity {
    let type: SuspiciousActivityType
    let severity: ActivitySeverity
    let description: String
    let confidence: Float
    let detectedValue: Float
    let threshold: Float
    
    var riskScore: Float {
        return confidence * severity.multiplier
    }
}

enum SuspiciousActivityType: String, CaseIterable {
    case unrealisticMotion = "unrealistic_motion"
    case roboticMovement = "robotic_movement"
    case temporalManipulation = "temporal_manipulation"
    case biomechanicalViolation = "biomechanical_violation"
    case deviceManipulation = "device_manipulation"
    case networkManipulation = "network_manipulation"
    case deviceSwitching = "device_switching"
    case unrealisticPerformance = "unrealistic_performance"
    case unrealisticImprovement = "unrealistic_improvement"
    case dataManipulation = "data_manipulation"
    
    var displayName: String {
        switch self {
        case .unrealisticMotion: return "Unrealistic Motion"
        case .roboticMovement: return "Robotic Movement"
        case .temporalManipulation: return "Temporal Manipulation"
        case .biomechanicalViolation: return "Biomechanical Violation"
        case .deviceManipulation: return "Device Manipulation"
        case .networkManipulation: return "Network Manipulation"
        case .deviceSwitching: return "Device Switching"
        case .unrealisticPerformance: return "Unrealistic Performance"
        case .unrealisticImprovement: return "Unrealistic Improvement"
        case .dataManipulation: return "Data Manipulation"
        }
    }
}

enum ActivitySeverity: String, CaseIterable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    
    var multiplier: Float {
        switch self {
        case .low: return 1.0
        case .medium: return 2.0
        case .high: return 3.0
        }
    }
    
    var displayName: String {
        switch self {
        case .low: return "Low"
        case .medium: return "Medium"
        case .high: return "High"
        }
    }
}

struct AnalysisResult {
    let suspiciousActivities: [SuspiciousActivity]
    let confidenceMultiplier: Float
}

// MARK: - Extensions

extension String {
    var hash: Int {
        var hasher = Hasher()
        hasher.combine(self)
        return hasher.finalize()
    }
}