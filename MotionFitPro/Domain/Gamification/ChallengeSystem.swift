//
//  ChallengeSystem.swift
//  MotionFitPro
//
//  Created by <PERSON> on 2025-07-15.
//

import Foundation
import SwiftUI

// MARK: - Challenge Protocol

protocol Challenge: Identifiable {
    var id: UUID { get }
    var title: String { get }
    var description: String { get }
    var target: Int { get }
    var current: Int { get set }
    var isCompleted: Bool { get }
    var reward: ChallengeReward { get }
    var difficulty: ChallengeDifficulty { get }
    var category: ChallengeCategory { get }
    var icon: String { get }
    var color: Color { get }
    
    mutating func updateProgress(session: WorkoutSession, stats: SessionStats)
    func getProgress() -> Float
}

// MARK: - Daily Challenge

struct DailyChallenge: Challenge {
    let id = UUID()
    let title: String
    let description: String
    let target: Int
    var current: Int = 0
    let reward: ChallengeReward
    let difficulty: ChallengeDifficulty
    let category: ChallengeCategory
    let icon: String
    let date: Date
    let type: DailyChallengeType
    
    var isCompleted: Bool { current >= target }
    
    var color: Color {
        category.color
    }
    
    mutating func updateProgress(session: WorkoutSession, stats: SessionStats) {
        switch type {
        case .totalReps:
            current += stats.totalReps
        case .perfectFormReps:
            current += stats.perfectFormReps
        case .specificExercise(let exercise):
            current += stats.repsPerExercise[exercise] ?? 0
        case .sessionDuration:
            current += Int(session.duration / 60) // Convert to minutes
        case .formScore:
            if stats.averageFormScore >= 0.8 {
                current += 1
            }
        case .programCompletion(let programName):
            if session.program == programName {
                current += 1
            }
        case .caloriesBurned:
            current += stats.estimatedCalories
        case .consistency:
            current += 1 // Increment for each workout
        }
    }
    
    func getProgress() -> Float {
        return min(1.0, Float(current) / Float(target))
    }
    
    static func generateDailyChallenges(for date: Date) -> [DailyChallenge] {
        let challengePool = [
            // Rep-based challenges
            DailyChallenge(
                title: "Rep Master",
                description: "Complete 50 total reps",
                target: 50,
                reward: ChallengeReward(points: 50, type: .points),
                difficulty: .easy,
                category: .reps,
                icon: "number.circle.fill",
                date: date,
                type: .totalReps
            ),
            
            DailyChallenge(
                title: "Perfect Form Focus",
                description: "Complete 20 reps with perfect form",
                target: 20,
                reward: ChallengeReward(points: 75, type: .points),
                difficulty: .medium,
                category: .form,
                icon: "target",
                date: date,
                type: .perfectFormReps
            ),
            
            // Exercise-specific challenges
            DailyChallenge(
                title: "Squat Champion",
                description: "Complete 30 squats",
                target: 30,
                reward: ChallengeReward(points: 60, type: .points),
                difficulty: .medium,
                category: .exercise,
                icon: "figure.strengthtraining.traditional",
                date: date,
                type: .specificExercise(.squat)
            ),
            
            DailyChallenge(
                title: "Push-up Power",
                description: "Complete 20 push-ups",
                target: 20,
                reward: ChallengeReward(points: 65, type: .points),
                difficulty: .medium,
                category: .exercise,
                icon: "figure.strengthtraining.functional",
                date: date,
                type: .specificExercise(.pushUp)
            ),
            
            // Time-based challenges
            DailyChallenge(
                title: "Time Trial",
                description: "Exercise for 10 minutes",
                target: 10,
                reward: ChallengeReward(points: 80, type: .points),
                difficulty: .medium,
                category: .endurance,
                icon: "timer",
                date: date,
                type: .sessionDuration
            ),
            
            // Form challenges
            DailyChallenge(
                title: "Form Perfectionist",
                description: "Complete 1 session with 80%+ form score",
                target: 1,
                reward: ChallengeReward(points: 100, type: .badge("Form Master")),
                difficulty: .hard,
                category: .form,
                icon: "star.fill",
                date: date,
                type: .formScore
            ),
            
            // Calorie challenges
            DailyChallenge(
                title: "Calorie Burner",
                description: "Burn 100 calories",
                target: 100,
                reward: ChallengeReward(points: 90, type: .points),
                difficulty: .medium,
                category: .calories,
                icon: "flame.fill",
                date: date,
                type: .caloriesBurned
            )
        ]
        
        // Randomly select 3 challenges of different categories
        var selectedChallenges: [DailyChallenge] = []
        var usedCategories: Set<ChallengeCategory> = []
        
        let shuffledPool = challengePool.shuffled()
        
        for challenge in shuffledPool {
            if !usedCategories.contains(challenge.category) && selectedChallenges.count < 3 {
                selectedChallenges.append(challenge)
                usedCategories.insert(challenge.category)
            }
        }
        
        // Ensure we have exactly 3 challenges
        while selectedChallenges.count < 3 {
            let remaining = challengePool.filter { !selectedChallenges.contains { $0.id == $1.id } }
            if let additional = remaining.randomElement() {
                selectedChallenges.append(additional)
            }
        }
        
        return selectedChallenges
    }
}

// MARK: - Weekly Challenge

struct WeeklyChallenge: Challenge {
    let id = UUID()
    let title: String
    let description: String
    let target: Int
    var current: Int = 0
    let reward: ChallengeReward
    let difficulty: ChallengeDifficulty
    let category: ChallengeCategory
    let icon: String
    let weekStart: Date
    let type: WeeklyChallengeType
    
    var isCompleted: Bool { current >= target }
    
    var color: Color {
        category.color
    }
    
    mutating func updateProgress(session: WorkoutSession, stats: SessionStats) {
        switch type {
        case .totalWorkouts:
            current += 1
        case .totalReps:
            current += stats.totalReps
        case .totalDuration:
            current += Int(session.duration / 60) // Convert to minutes
        case .streak:
            // Streak logic would be handled by the achievement system
            break
        case .programMastery(let program):
            if session.program == program {
                current += 1
            }
        case .exerciseVariety:
            // Track unique exercises (simplified)
            current = max(current, session.exercises.count)
        case .consistentForm:
            if stats.averageFormScore >= 0.75 {
                current += 1
            }
        case .calorieGoal:
            current += stats.estimatedCalories
        }
    }
    
    func getProgress() -> Float {
        return min(1.0, Float(current) / Float(target))
    }
    
    static func generateWeeklyChallenges(for weekStart: Date) -> [WeeklyChallenge] {
        return [
            WeeklyChallenge(
                title: "Weekly Warrior",
                description: "Complete 5 workouts this week",
                target: 5,
                reward: ChallengeReward(points: 200, type: .badge("Weekly Warrior")),
                difficulty: .medium,
                category: .consistency,
                icon: "calendar.badge.plus",
                weekStart: weekStart,
                type: .totalWorkouts
            ),
            
            WeeklyChallenge(
                title: "Rep Monster",
                description: "Complete 300 total reps this week",
                target: 300,
                reward: ChallengeReward(points: 250, type: .points),
                difficulty: .hard,
                category: .reps,
                icon: "number.circle.fill",
                weekStart: weekStart,
                type: .totalReps
            ),
            
            WeeklyChallenge(
                title: "Endurance Master",
                description: "Exercise for 60 minutes total this week",
                target: 60,
                reward: ChallengeReward(points: 300, type: .badge("Endurance Master")),
                difficulty: .hard,
                category: .endurance,
                icon: "timer",
                weekStart: weekStart,
                type: .totalDuration
            )
        ]
    }
}

// MARK: - Challenge Types

enum DailyChallengeType {
    case totalReps
    case perfectFormReps
    case specificExercise(ExerciseType)
    case sessionDuration
    case formScore
    case programCompletion(String)
    case caloriesBurned
    case consistency
}

enum WeeklyChallengeType {
    case totalWorkouts
    case totalReps
    case totalDuration
    case streak
    case programMastery(String)
    case exerciseVariety
    case consistentForm
    case calorieGoal
}

// MARK: - Challenge Categories

enum ChallengeCategory: String, CaseIterable {
    case reps = "reps"
    case form = "form"
    case exercise = "exercise"
    case endurance = "endurance"
    case calories = "calories"
    case consistency = "consistency"
    case program = "program"
    
    var displayName: String {
        switch self {
        case .reps: return "Reps"
        case .form: return "Form"
        case .exercise: return "Exercise"
        case .endurance: return "Endurance"
        case .calories: return "Calories"
        case .consistency: return "Consistency"
        case .program: return "Program"
        }
    }
    
    var color: Color {
        switch self {
        case .reps: return .blue
        case .form: return .green
        case .exercise: return .orange
        case .endurance: return .purple
        case .calories: return .red
        case .consistency: return .yellow
        case .program: return .pink
        }
    }
}

// MARK: - Challenge Difficulty

enum ChallengeDifficulty: String, CaseIterable {
    case easy = "easy"
    case medium = "medium"
    case hard = "hard"
    case extreme = "extreme"
    
    var displayName: String {
        switch self {
        case .easy: return "Easy"
        case .medium: return "Medium"
        case .hard: return "Hard"
        case .extreme: return "Extreme"
        }
    }
    
    var color: Color {
        switch self {
        case .easy: return .green
        case .medium: return .yellow
        case .hard: return .orange
        case .extreme: return .red
        }
    }
    
    var multiplier: Float {
        switch self {
        case .easy: return 1.0
        case .medium: return 1.5
        case .hard: return 2.0
        case .extreme: return 3.0
        }
    }
}

// MARK: - Challenge Rewards

struct ChallengeReward {
    let points: Int
    let type: RewardType
    
    enum RewardType {
        case points
        case badge(String)
        case unlock(String)
        case bonus(String)
    }
    
    var description: String {
        switch type {
        case .points:
            return "\(points) points"
        case .badge(let name):
            return "Badge: \(name)"
        case .unlock(let item):
            return "Unlock: \(item)"
        case .bonus(let bonus):
            return "Bonus: \(bonus)"
        }
    }
}

// MARK: - Challenge Manager

@MainActor
class ChallengeManager: ObservableObject {
    @Published var dailyChallenges: [DailyChallenge] = []
    @Published var weeklyChallenges: [WeeklyChallenge] = []
    @Published var completedChallenges: [String] = [] // Challenge IDs
    @Published var streakBonusActive: Bool = false
    
    private let calendar = Calendar.current
    
    init() {
        refreshChallenges()
    }
    
    func refreshChallenges() {
        refreshDailyChallenges()
        refreshWeeklyChallenges()
    }
    
    private func refreshDailyChallenges() {
        let today = Date()
        
        // Check if we need new daily challenges
        if dailyChallenges.isEmpty || !calendar.isDate(dailyChallenges.first?.date ?? Date.distantPast, inSameDayAs: today) {
            dailyChallenges = DailyChallenge.generateDailyChallenges(for: today)
        }
    }
    
    private func refreshWeeklyChallenges() {
        let today = Date()
        let startOfWeek = calendar.dateInterval(of: .weekOfYear, for: today)?.start ?? today
        
        // Check if we need new weekly challenges
        if weeklyChallenges.isEmpty || !calendar.isDate(weeklyChallenges.first?.weekStart ?? Date.distantPast, inSameDayAs: startOfWeek) {
            weeklyChallenges = WeeklyChallenge.generateWeeklyChallenges(for: startOfWeek)
        }
    }
    
    func updateProgress(session: WorkoutSession, stats: SessionStats) {
        // Update daily challenges
        for i in 0..<dailyChallenges.count {
            let wasCompleted = dailyChallenges[i].isCompleted
            dailyChallenges[i].updateProgress(session: session, stats: stats)
            
            // Check for new completion
            if !wasCompleted && dailyChallenges[i].isCompleted {
                completedChallenges.append(dailyChallenges[i].id.uuidString)
                celebrateChallenge(dailyChallenges[i])
            }
        }
        
        // Update weekly challenges
        for i in 0..<weeklyChallenges.count {
            let wasCompleted = weeklyChallenges[i].isCompleted
            weeklyChallenges[i].updateProgress(session: session, stats: stats)
            
            // Check for new completion
            if !wasCompleted && weeklyChallenges[i].isCompleted {
                completedChallenges.append(weeklyChallenges[i].id.uuidString)
                celebrateChallenge(weeklyChallenges[i])
            }
        }
        
        checkStreakBonus()
    }
    
    private func celebrateChallenge<T: Challenge>(_ challenge: T) {
        NotificationCenter.default.post(
            name: .challengeCompleted,
            object: challenge
        )
    }
    
    private func checkStreakBonus() {
        let dailyCompleted = dailyChallenges.filter { $0.isCompleted }.count
        let weeklyCompleted = weeklyChallenges.filter { $0.isCompleted }.count
        
        // Activate streak bonus if all daily challenges are completed
        streakBonusActive = dailyCompleted == dailyChallenges.count && dailyChallenges.count > 0
    }
    
    func getTotalCompletedToday() -> Int {
        return dailyChallenges.filter { $0.isCompleted }.count
    }
    
    func getTotalCompletedThisWeek() -> Int {
        return weeklyChallenges.filter { $0.isCompleted }.count
    }
    
    func getAvailableRewards() -> Int {
        let dailyRewards = dailyChallenges.filter { $0.isCompleted }.map { $0.reward.points }.reduce(0, +)
        let weeklyRewards = weeklyChallenges.filter { $0.isCompleted }.map { $0.reward.points }.reduce(0, +)
        return dailyRewards + weeklyRewards
    }
}