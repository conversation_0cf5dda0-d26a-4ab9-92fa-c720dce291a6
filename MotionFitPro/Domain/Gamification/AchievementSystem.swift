//
//  AchievementSystem.swift
//  MotionFitPro
//
//  Created by <PERSON> on 2025-07-15.
//

import Foundation
import SwiftUI

// MARK: - Achievement System

@MainActor
class AchievementSystem: ObservableObject {
    @Published var unlockedAchievements: [Achievement] = []
    @Published var availableAchievements: [Achievement] = []
    @Published var recentlyUnlocked: [Achievement] = []
    @Published var currentStreak: Int = 0
    @Published var totalPoints: Int = 0
    @Published var currentLevel: UserLevel = .beginner(0)
    @Published var dailyChallenges: [DailyChallenge] = []
    @Published var weeklyChallenges: [WeeklyChallenge] = []
    
    private var userStats: UserStats = UserStats()
    private var lastWorkoutDate: Date?
    
    init() {
        setupAchievements()
        generateDailyChallenges()
        generateWeeklyChallenges()
        loadUserProgress()
    }
    
    // MARK: - Achievement Processing
    
    func processWorkoutCompletion(
        _ session: WorkoutSession,
        stats: SessionStats
    ) {
        updateUserStats(with: session, stats: stats)
        updateStreak()
        
        let newAchievements = checkAchievements(session: session, stats: stats)
        
        for achievement in newAchievements {
            unlockAchievement(achievement)
        }
        
        updateChallengeProgress(session: session, stats: stats)
        updateLevel()
    }
    
    private func checkAchievements(
        session: WorkoutSession,
        stats: SessionStats
    ) -> [Achievement] {
        var newlyUnlocked: [Achievement] = []
        
        for achievement in availableAchievements {
            if achievement.isUnlocked(userStats: userStats, session: session, stats: stats) {
                newlyUnlocked.append(achievement)
            }
        }
        
        return newlyUnlocked
    }
    
    private func unlockAchievement(_ achievement: Achievement) {
        guard !unlockedAchievements.contains(where: { $0.id == achievement.id }) else { return }
        
        unlockedAchievements.append(achievement)
        recentlyUnlocked.append(achievement)
        totalPoints += achievement.points
        
        // Remove from available
        availableAchievements.removeAll { $0.id == achievement.id }
        
        // Trigger celebration animation
        scheduleAchievementCelebration(achievement)
        
        // Auto-clear recent after delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
            self.recentlyUnlocked.removeAll { $0.id == achievement.id }
        }
    }
    
    private func scheduleAchievementCelebration(_ achievement: Achievement) {
        // This would trigger epic animations in the UI
        NotificationCenter.default.post(
            name: .achievementUnlocked,
            object: achievement
        )
    }
    
    // MARK: - Streak Management
    
    private func updateStreak() {
        let today = Calendar.current.startOfDay(for: Date())
        
        if let lastDate = lastWorkoutDate {
            let lastWorkoutDay = Calendar.current.startOfDay(for: lastDate)
            let daysBetween = Calendar.current.dateComponents([.day], from: lastWorkoutDay, to: today).day ?? 0
            
            if daysBetween == 1 {
                // Consecutive day
                currentStreak += 1
            } else if daysBetween == 0 {
                // Same day - no change to streak
                return
            } else {
                // Streak broken
                currentStreak = 1
            }
        } else {
            // First workout
            currentStreak = 1
        }
        
        lastWorkoutDate = Date()
    }
    
    // MARK: - Level System
    
    private func updateLevel() {
        let newLevel = UserLevel.levelForPoints(totalPoints)
        
        if newLevel != currentLevel {
            let oldLevel = currentLevel
            currentLevel = newLevel
            
            // Trigger level up celebration
            NotificationCenter.default.post(
                name: .levelUp,
                object: ["old": oldLevel, "new": newLevel]
            )
        }
    }
    
    // MARK: - Challenge Management
    
    private func generateDailyChallenges() {
        let today = Date()
        let existingChallengeDate = dailyChallenges.first?.date
        
        // Generate new challenges if none exist or it's a new day
        if existingChallengeDate == nil || !Calendar.current.isDate(existingChallengeDate!, inSameDayAs: today) {
            dailyChallenges = DailyChallenge.generateDailyChallenges(for: today)
        }
    }
    
    private func generateWeeklyChallenges() {
        let today = Date()
        let calendar = Calendar.current
        let startOfWeek = calendar.dateInterval(of: .weekOfYear, for: today)?.start ?? today
        
        let existingChallengeWeek = weeklyChallenges.first?.weekStart
        
        // Generate new challenges if none exist or it's a new week
        if existingChallengeWeek == nil || !calendar.isDate(existingChallengeWeek!, inSameDayAs: startOfWeek) {
            weeklyChallenges = WeeklyChallenge.generateWeeklyChallenges(for: startOfWeek)
        }
    }
    
    private func updateChallengeProgress(session: WorkoutSession, stats: SessionStats) {
        // Update daily challenges
        for i in 0..<dailyChallenges.count {
            dailyChallenges[i].updateProgress(session: session, stats: stats)
        }
        
        // Update weekly challenges
        for i in 0..<weeklyChallenges.count {
            weeklyChallenges[i].updateProgress(session: session, stats: stats)
        }
    }
    
    // MARK: - User Stats Management
    
    private func updateUserStats(with session: WorkoutSession, stats: SessionStats) {
        userStats.totalWorkouts += 1
        userStats.totalReps += stats.totalReps
        userStats.totalDuration += session.duration
        userStats.totalCaloriesBurned += stats.estimatedCalories
        
        if stats.averageFormScore > userStats.bestFormScore {
            userStats.bestFormScore = stats.averageFormScore
        }
        
        if session.duration > userStats.longestSession {
            userStats.longestSession = session.duration
        }
        
        // Update exercise-specific stats
        for exercise in session.exercises {
            userStats.exerciseStats[exercise, default: ExerciseStats()].totalReps += stats.repsPerExercise[exercise] ?? 0
            userStats.exerciseStats[exercise, default: ExerciseStats()].totalSessions += 1
        }
        
        // Update program-specific stats
        if let program = session.program {
            userStats.programStats[program, default: ProgramStats()].completions += 1
            userStats.programStats[program, default: ProgramStats()].totalDuration += session.duration
        }
    }
    
    // MARK: - Setup Methods
    
    private func setupAchievements() {
        availableAchievements = Achievement.allAchievements
    }
    
    private func loadUserProgress() {
        // In a real app, this would load from UserDefaults or Core Data
        // For now, we'll use default values
    }
    
    // MARK: - Public Interface
    
    func getProgressToNextLevel() -> Float {
        return currentLevel.progressToNext(currentPoints: totalPoints)
    }
    
    func getAchievementProgress(_ achievement: Achievement) -> Float {
        return achievement.getProgress(userStats: userStats)
    }
    
    func getCompletedChallenges() -> [Challenge] {
        let dailyCompleted = dailyChallenges.filter { $0.isCompleted }
        let weeklyCompleted = weeklyChallenges.filter { $0.isCompleted }
        
        return dailyCompleted as [Challenge] + weeklyCompleted as [Challenge]
    }
}

// MARK: - Achievement Model

struct Achievement: Identifiable, Equatable {
    let id = UUID()
    let title: String
    let description: String
    let category: AchievementCategory
    let tier: AchievementTier
    let points: Int
    let icon: String
    let unlockCondition: AchievementCondition
    let secretAchievement: Bool
    
    var color: Color {
        tier.color
    }
    
    var gradient: LinearGradient {
        tier.gradient
    }
    
    func isUnlocked(userStats: UserStats, session: WorkoutSession?, stats: SessionStats?) -> Bool {
        return unlockCondition.check(userStats: userStats, session: session, stats: stats)
    }
    
    func getProgress(userStats: UserStats) -> Float {
        return unlockCondition.getProgress(userStats: userStats)
    }
    
    static func == (lhs: Achievement, rhs: Achievement) -> Bool {
        lhs.id == rhs.id
    }
}

enum AchievementCategory: String, CaseIterable {
    case form = "form"
    case endurance = "endurance"
    case speed = "speed"
    case consistency = "consistency"
    case mastery = "mastery"
    case special = "special"
    
    var displayName: String {
        switch self {
        case .form: return "Form Master"
        case .endurance: return "Endurance King"
        case .speed: return "Speed Demon"
        case .consistency: return "Dedication"
        case .mastery: return "Exercise Mastery"
        case .special: return "Special"
        }
    }
    
    var color: Color {
        switch self {
        case .form: return .blue
        case .endurance: return .green
        case .speed: return .orange
        case .consistency: return .purple
        case .mastery: return .red
        case .special: return .yellow
        }
    }
}

enum AchievementTier: String, CaseIterable {
    case bronze = "bronze"
    case silver = "silver"
    case gold = "gold"
    case platinum = "platinum"
    case diamond = "diamond"
    
    var color: Color {
        switch self {
        case .bronze: return Color(red: 0.8, green: 0.5, blue: 0.2)
        case .silver: return Color(red: 0.75, green: 0.75, blue: 0.75)
        case .gold: return Color(red: 1.0, green: 0.84, blue: 0.0)
        case .platinum: return Color(red: 0.9, green: 0.9, blue: 0.95)
        case .diamond: return Color(red: 0.7, green: 0.9, blue: 1.0)
        }
    }
    
    var gradient: LinearGradient {
        switch self {
        case .bronze:
            return LinearGradient(colors: [Color(red: 0.8, green: 0.5, blue: 0.2), Color(red: 0.6, green: 0.3, blue: 0.1)], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .silver:
            return LinearGradient(colors: [Color(red: 0.9, green: 0.9, blue: 0.9), Color(red: 0.6, green: 0.6, blue: 0.6)], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .gold:
            return LinearGradient(colors: [Color(red: 1.0, green: 0.95, blue: 0.3), Color(red: 0.8, green: 0.6, blue: 0.0)], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .platinum:
            return LinearGradient(colors: [Color(red: 1.0, green: 1.0, blue: 1.0), Color(red: 0.8, green: 0.8, blue: 0.9)], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .diamond:
            return LinearGradient(colors: [Color(red: 0.9, green: 0.95, blue: 1.0), Color(red: 0.5, green: 0.7, blue: 0.9)], startPoint: .topLeading, endPoint: .bottomTrailing)
        }
    }
    
    var points: Int {
        switch self {
        case .bronze: return 100
        case .silver: return 250
        case .gold: return 500
        case .platinum: return 1000
        case .diamond: return 2500
        }
    }
}

// MARK: - Achievement Conditions

enum AchievementCondition {
    case totalWorkouts(Int)
    case totalReps(Int)
    case perfectFormSession(Float) // Minimum form score
    case streak(Int)
    case exerciseMastery(ExerciseType, Int) // Exercise type and rep count
    case programCompletion(String, Int) // Program name and completion count
    case sessionDuration(TimeInterval)
    case caloriesBurned(Int)
    case custom((UserStats, WorkoutSession?, SessionStats?) -> Bool)
    
    func check(userStats: UserStats, session: WorkoutSession?, stats: SessionStats?) -> Bool {
        switch self {
        case .totalWorkouts(let target):
            return userStats.totalWorkouts >= target
        case .totalReps(let target):
            return userStats.totalReps >= target
        case .perfectFormSession(let minScore):
            return stats?.averageFormScore ?? 0 >= minScore
        case .streak(let target):
            return userStats.currentStreak >= target
        case .exerciseMastery(let exercise, let target):
            return userStats.exerciseStats[exercise]?.totalReps ?? 0 >= target
        case .programCompletion(let programName, let target):
            return userStats.programStats.values.first(where: { $0.completions >= target }) != nil
        case .sessionDuration(let target):
            return session?.duration ?? 0 >= target
        case .caloriesBurned(let target):
            return userStats.totalCaloriesBurned >= target
        case .custom(let condition):
            return condition(userStats, session, stats)
        }
    }
    
    func getProgress(userStats: UserStats) -> Float {
        switch self {
        case .totalWorkouts(let target):
            return min(1.0, Float(userStats.totalWorkouts) / Float(target))
        case .totalReps(let target):
            return min(1.0, Float(userStats.totalReps) / Float(target))
        case .streak(let target):
            return min(1.0, Float(userStats.currentStreak) / Float(target))
        case .exerciseMastery(let exercise, let target):
            let current = userStats.exerciseStats[exercise]?.totalReps ?? 0
            return min(1.0, Float(current) / Float(target))
        case .caloriesBurned(let target):
            return min(1.0, Float(userStats.totalCaloriesBurned) / Float(target))
        default:
            return 0.0 // Some conditions can't show progress
        }
    }
}

// MARK: - User Level System

enum UserLevel: Equatable {
    case beginner(Int)
    case intermediate(Int)
    case advanced(Int)
    case expert(Int)
    case master(Int)
    
    var title: String {
        switch self {
        case .beginner: return "Fitness Rookie"
        case .intermediate: return "Workout Warrior"
        case .advanced: return "Fitness Champion"
        case .expert: return "Elite Athlete"
        case .master: return "Fitness Master"
        }
    }
    
    var description: String {
        switch self {
        case .beginner(let points): return "Building foundations • \(points) pts"
        case .intermediate(let points): return "Getting stronger • \(points) pts"
        case .advanced(let points): return "Serious athlete • \(points) pts"
        case .expert(let points): return "Elite performance • \(points) pts"
        case .master(let points): return "Legendary status • \(points) pts"
        }
    }
    
    var color: Color {
        switch self {
        case .beginner: return .green
        case .intermediate: return .blue
        case .advanced: return .orange
        case .expert: return .purple
        case .master: return .red
        }
    }
    
    var requiredPoints: Int {
        switch self {
        case .beginner: return 0
        case .intermediate: return 1000
        case .advanced: return 2500
        case .expert: return 5000
        case .master: return 10000
        }
    }
    
    var nextLevelPoints: Int? {
        switch self {
        case .beginner: return 1000
        case .intermediate: return 2500
        case .advanced: return 5000
        case .expert: return 10000
        case .master: return nil
        }
    }
    
    func progressToNext(currentPoints: Int) -> Float {
        guard let nextPoints = nextLevelPoints else { return 1.0 }
        let currentRequired = requiredPoints
        let range = nextPoints - currentRequired
        let progress = currentPoints - currentRequired
        return max(0.0, min(1.0, Float(progress) / Float(range)))
    }
    
    static func levelForPoints(_ points: Int) -> UserLevel {
        switch points {
        case 0..<1000: return .beginner(points)
        case 1000..<2500: return .intermediate(points)
        case 2500..<5000: return .advanced(points)
        case 5000..<10000: return .expert(points)
        default: return .master(points)
        }
    }
}

// MARK: - Supporting Models

struct UserStats {
    var totalWorkouts: Int = 0
    var totalReps: Int = 0
    var totalDuration: TimeInterval = 0
    var totalCaloriesBurned: Int = 0
    var bestFormScore: Float = 0
    var longestSession: TimeInterval = 0
    var currentStreak: Int = 0
    var exerciseStats: [ExerciseType: ExerciseStats] = [:]
    var programStats: [String: ProgramStats] = [:]
}

struct ExerciseStats {
    var totalReps: Int = 0
    var totalSessions: Int = 0
    var bestFormScore: Float = 0
    var averageFormScore: Float = 0
}

struct ProgramStats {
    var completions: Int = 0
    var totalDuration: TimeInterval = 0
    var bestTime: TimeInterval = 0
}

struct WorkoutSession {
    let id = UUID()
    let date: Date
    let duration: TimeInterval
    let exercises: [ExerciseType]
    let program: String?
}

struct SessionStats {
    let totalReps: Int
    let averageFormScore: Float
    let estimatedCalories: Int
    let repsPerExercise: [ExerciseType: Int]
    let perfectFormReps: Int
}

// MARK: - Notification Names

extension Notification.Name {
    static let achievementUnlocked = Notification.Name("achievementUnlocked")
    static let levelUp = Notification.Name("levelUp")
    static let challengeCompleted = Notification.Name("challengeCompleted")
}

// MARK: - Achievement Definitions

extension Achievement {
    static let allAchievements: [Achievement] = [
        // Form Master Achievements
        Achievement(
            title: "Perfect Form",
            description: "Complete a session with 90%+ form score",
            category: .form,
            tier: .bronze,
            points: 100,
            icon: "target",
            unlockCondition: .perfectFormSession(0.9),
            secretAchievement: false
        ),
        
        Achievement(
            title: "Form Master",
            description: "Complete 10 sessions with 85%+ form score",
            category: .form,
            tier: .silver,
            points: 250,
            icon: "star.fill",
            unlockCondition: .custom({ stats, _, _ in
                // Custom logic for counting high-form sessions
                return stats.totalWorkouts >= 10 && stats.bestFormScore >= 0.85
            }),
            secretAchievement: false
        ),
        
        // Endurance Achievements
        Achievement(
            title: "Iron Will",
            description: "Complete a 20-minute session",
            category: .endurance,
            tier: .bronze,
            points: 100,
            icon: "timer",
            unlockCondition: .sessionDuration(1200),
            secretAchievement: false
        ),
        
        Achievement(
            title: "Endurance King",
            description: "Complete 100 total workouts",
            category: .endurance,
            tier: .gold,
            points: 500,
            icon: "crown.fill",
            unlockCondition: .totalWorkouts(100),
            secretAchievement: false
        ),
        
        // Speed Demon Achievements
        Achievement(
            title: "Speed Demon",
            description: "Complete 1000 total reps",
            category: .speed,
            tier: .silver,
            points: 250,
            icon: "bolt.fill",
            unlockCondition: .totalReps(1000),
            secretAchievement: false
        ),
        
        // Consistency Achievements
        Achievement(
            title: "On Fire",
            description: "Maintain a 7-day workout streak",
            category: .consistency,
            tier: .gold,
            points: 500,
            icon: "flame.fill",
            unlockCondition: .streak(7),
            secretAchievement: false
        ),
        
        Achievement(
            title: "Unstoppable",
            description: "Maintain a 30-day workout streak",
            category: .consistency,
            tier: .diamond,
            points: 2500,
            icon: "infinity",
            unlockCondition: .streak(30),
            secretAchievement: false
        ),
        
        // Exercise Mastery
        Achievement(
            title: "Squat Master",
            description: "Complete 500 squats",
            category: .mastery,
            tier: .silver,
            points: 250,
            icon: "figure.strengthtraining.traditional",
            unlockCondition: .exerciseMastery(.squat, 500),
            secretAchievement: false
        ),
        
        Achievement(
            title: "Push-up Pro",
            description: "Complete 300 push-ups",
            category: .mastery,
            tier: .silver,
            points: 250,
            icon: "figure.strengthtraining.functional",
            unlockCondition: .exerciseMastery(.pushUp, 300),
            secretAchievement: false
        ),
        
        // Special Achievements
        Achievement(
            title: "Calorie Crusher",
            description: "Burn 5000 total calories",
            category: .special,
            tier: .platinum,
            points: 1000,
            icon: "flame.circle.fill",
            unlockCondition: .caloriesBurned(5000),
            secretAchievement: false
        ),
        
        Achievement(
            title: "The Chosen One",
            description: "Unlock all other achievements",
            category: .special,
            tier: .diamond,
            points: 5000,
            icon: "star.circle.fill",
            unlockCondition: .custom({ _, _, _ in false }), // Special logic needed
            secretAchievement: true
        )
    ]
}