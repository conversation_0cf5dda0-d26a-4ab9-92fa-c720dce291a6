import Foundation
import ARKit
import CoreImage
import simd
import os.log

/// Processes camera frames from ARKit and extracts body pose data
@MainActor
final class CameraFrameProcessor: ObservableObject, Sendable {
    
    // MARK: - Published Properties
    @Published var latestPoseData: BodyPoseData?
    @Published var processingStats: ProcessingStats = ProcessingStats()
    @Published var isProcessing: Bool = false
    
    // MARK: - Private Properties
    private let logger = Logger(subsystem: "MotionFitPro", category: "CameraFrameProcessor")
    private let processingQueue = DispatchQueue(label: "com.motionfitpro.frameprocessing", qos: .userInteractive)
    private var frameCount: Int = 0
    private var lastProcessTime: Date = Date()
    
    // Performance tracking
    private var processingTimes: [TimeInterval] = []
    private let maxProcessingHistory = 60 // Keep last 60 frame times
    
    // MARK: - Initialization
    nonisolated init() {
        logger.info("CameraFrameProcessor initialized")
    }
    
    // MARK: - Frame Processing
    
    /// Process an ARFrame and extract body pose data
    func processFrame(_ frame: ARFrame) async -> BodyPoseData? {
        let startTime = Date()
        isProcessing = true
        defer { 
            Task { @MainActor in
                isProcessing = false
                updateProcessingStats(processingTime: Date().timeIntervalSince(startTime))
            }
        }
        
        frameCount += 1
        logger.debug("Processing frame \(self.frameCount)")
        
        // Look for body anchors in the frame
        let bodyAnchors = frame.anchors.compactMap { $0 as? ARBodyAnchor }
        
        guard let bodyAnchor = bodyAnchors.first else {
            logger.debug("No body anchor found in frame")
            return nil
        }
        
        // Create body pose data from the anchor
        guard let poseData = BodyPoseData(from: bodyAnchor) else {
            logger.warning("Failed to create BodyPoseData from ARBodyAnchor")
            return nil
        }
        
        // Update latest pose data on main thread
        await MainActor.run {
            latestPoseData = poseData
        }
        
        logger.debug("Successfully processed frame with confidence: \(poseData.confidence)")
        return poseData
    }
    
    /// Process frame data asynchronously for better performance
    func processFrameAsync(_ frame: ARFrame) {
        Task {
            let _ = await processFrame(frame)
        }
    }
    
    // MARK: - Processing Statistics
    
    private func updateProcessingStats(processingTime: TimeInterval) {
        let currentTime = Date()
        
        // Add to processing time history
        processingTimes.append(processingTime)
        if processingTimes.count > maxProcessingHistory {
            processingTimes.removeFirst()
        }
        
        // Calculate FPS
        let timeSinceLastFrame = currentTime.timeIntervalSince(lastProcessTime)
        let currentFPS = timeSinceLastFrame > 0 ? 1.0 / timeSinceLastFrame : 0
        lastProcessTime = currentTime
        
        // Update stats
        processingStats = ProcessingStats(
            framesProcessed: frameCount,
            averageProcessingTime: processingTimes.reduce(0, +) / Double(processingTimes.count),
            currentFPS: currentFPS,
            lastProcessingTime: processingTime,
            hasValidPose: latestPoseData != nil
        )
    }
    
    // MARK: - Quality Assessment
    
    /// Assess the quality of the current pose for exercise analysis
    func assessPoseQuality() -> PoseQuality {
        guard let poseData = latestPoseData else {
            return PoseQuality(overall: 0.0, confidence: 0.0, completeness: 0.0, stability: 0.0)
        }
        
        let confidence = poseData.confidence
        let completeness = calculateCompleteness(poseData: poseData)
        let stability = calculateStability()
        let overall = (confidence + completeness + stability) / 3.0
        
        return PoseQuality(
            overall: overall,
            confidence: confidence,
            completeness: completeness,
            stability: stability
        )
    }
    
    private func calculateCompleteness(poseData: BodyPoseData) -> Float {
        let requiredJoints = ["root", "head", "left_arm", "right_arm", "left_leg", "right_leg"]
        let availableJoints = requiredJoints.filter { poseData.joints[$0] != nil }
        return Float(availableJoints.count) / Float(requiredJoints.count)
    }
    
    private func calculateStability() -> Float {
        // Simple stability calculation based on processing consistency
        guard processingTimes.count > 10 else { return 0.5 }
        
        let recentTimes = Array(processingTimes.suffix(10))
        let average = recentTimes.reduce(0, +) / Double(recentTimes.count)
        let variance = recentTimes.map { pow($0 - average, 2) }.reduce(0, +) / Double(recentTimes.count)
        let stability = max(0, 1.0 - Float(variance * 10)) // Scale variance to 0-1
        
        return stability
    }
    
    // MARK: - Frame Rate Management
    
    /// Check if frame should be processed based on target FPS
    func shouldProcessFrame(targetFPS: Double = 30.0) -> Bool {
        let timeSinceLastFrame = Date().timeIntervalSince(lastProcessTime)
        let targetInterval = 1.0 / targetFPS
        return timeSinceLastFrame >= targetInterval
    }
    
    // MARK: - Debug and Testing
    
    /// Generate mock pose data for testing
    func generateMockPoseData() -> BodyPoseData {
        return BodyPoseData.mockStandingPose()
    }
    
    /// Reset processing statistics
    func resetStats() {
        frameCount = 0
        processingTimes.removeAll()
        processingStats = ProcessingStats()
        logger.info("Processing statistics reset")
    }
}

// MARK: - Supporting Data Structures

/// Statistics about frame processing performance
struct ProcessingStats: Sendable {
    let framesProcessed: Int
    let averageProcessingTime: TimeInterval
    let currentFPS: Double
    let lastProcessingTime: TimeInterval
    let hasValidPose: Bool
    
    init(framesProcessed: Int = 0,
         averageProcessingTime: TimeInterval = 0,
         currentFPS: Double = 0,
         lastProcessingTime: TimeInterval = 0,
         hasValidPose: Bool = false) {
        self.framesProcessed = framesProcessed
        self.averageProcessingTime = averageProcessingTime
        self.currentFPS = currentFPS
        self.lastProcessingTime = lastProcessingTime
        self.hasValidPose = hasValidPose
    }
}

/// Quality assessment of pose data
struct PoseQuality: Sendable {
    let overall: Float
    let confidence: Float
    let completeness: Float
    let stability: Float
    
    var isGoodQuality: Bool {
        return overall > 0.7
    }
    
    var qualityDescription: String {
        switch overall {
        case 0.8...1.0:
            return "Excellent"
        case 0.6..<0.8:
            return "Good"
        case 0.4..<0.6:
            return "Fair"
        case 0.2..<0.4:
            return "Poor"
        default:
            return "Very Poor"
        }
    }
}

// MARK: - Logger Extension for Proper Access

extension Logger {
    /// Create a logger with subsystem and category
    static func create(subsystem: String, category: String) -> Logger {
        return Logger(subsystem: subsystem, category: category)
    }
}