import Foundation
import ARKit
import Combine
import simd

// MARK: - Real AR Tracking Service

/// Production AR tracking service using ARKit body tracking
@MainActor
final class RealARTrackingService: NSObject, ARTrackingServiceProtocol, ObservableObject {
    
    // MARK: - Properties
    
    @Published var isTracking = false
    @Published var trackingQuality: TrackingQuality = .poor
    @Published var isARSupported = false
    @Published var currentError: AppError?
    
    private let bodyPoseSubject = PassthroughSubject<BodyPoseData, Never>()
    private let logger = Logger()
    
    // AR Session
    private var arSession: ARSession?
    private var arConfiguration: ARBodyTrackingConfiguration?
    
    // Tracking state
    private var frameCount = 0
    private var lastTrackingUpdate = Date()
    private var trackingHistory: [TrackingQualityMeasurement] = []
    
    // Performance monitoring
    private let maxHistoryCount = 100
    private let qualityUpdateInterval: TimeInterval = 1.0
    
    // MARK: - Initialization
    
    override init() {
        super.init()
        checkARSupport()
        setupARSession()
    }
    
    deinit {
        stopTracking()
    }
    
    // MARK: - AR Support Check
    
    private func checkARSupport() {
        isARSupported = ARBodyTrackingConfiguration.isSupported
        
        if !isARSupported {
            logger.error("AR Body Tracking not supported on this device", category: .arTracking)
            currentError = .arNotSupported
        } else {
            logger.info("AR Body Tracking is supported", category: .arTracking)
        }
    }
    
    // MARK: - AR Session Setup
    
    private func setupARSession() {
        guard isARSupported else { return }
        
        arSession = ARSession()
        arSession?.delegate = self
        
        arConfiguration = ARBodyTrackingConfiguration()
        arConfiguration?.automaticImageScaleEstimationEnabled = true
        arConfiguration?.automaticSkeletonScaleEstimationEnabled = true
        
        logger.info("AR session configured", category: .arTracking)
    }
    
    // MARK: - Tracking Control
    
    func startTracking() async throws {
        guard isARSupported else {
            throw AppError.arNotSupported
        }
        
        guard let session = arSession,
              let configuration = arConfiguration else {
            throw AppError.arTrackingFailed
        }
        
        do {
            // Request camera permission
            let cameraPermission = await requestCameraPermission()
            guard cameraPermission else {
                throw AppError.cameraPermissionDenied
            }
            
            // Start AR session
            session.run(configuration, options: [.resetTracking, .removeExistingAnchors])
            
            isTracking = true
            frameCount = 0
            lastTrackingUpdate = Date()
            
            logger.info("AR tracking started successfully", category: .arTracking)
            
        } catch {
            logger.error("Failed to start AR tracking: \(error)", category: .arTracking)
            throw AppError.arTrackingFailed
        }
    }
    
    func stopTracking() {
        arSession?.pause()
        isTracking = false
        trackingQuality = .poor
        
        logger.info("AR tracking stopped", category: .arTracking)
    }
    
    func pauseTracking() {
        arSession?.pause()
        isTracking = false
        
        logger.info("AR tracking paused", category: .arTracking)
    }
    
    func resumeTracking() {
        guard let session = arSession,
              let configuration = arConfiguration else {
            logger.error("Cannot resume tracking - session not configured", category: .arTracking)
            return
        }
        
        session.run(configuration)
        isTracking = true
        
        logger.info("AR tracking resumed", category: .arTracking)
    }
    
    // MARK: - Publisher
    
    var bodyPosePublisher: AnyPublisher<BodyPoseData, Never> {
        bodyPoseSubject.eraseToAnyPublisher()
    }
    
    // MARK: - Permission Handling
    
    private func requestCameraPermission() async -> Bool {
        return await withCheckedContinuation { continuation in
            AVCaptureDevice.requestAccess(for: .video) { granted in
                DispatchQueue.main.async {
                    continuation.resume(returning: granted)
                }
            }
        }
    }
    
    // MARK: - Tracking Quality Assessment
    
    private func updateTrackingQuality(_ frame: ARFrame, bodyAnchor: ARBodyAnchor?) {
        let now = Date()
        
        // Only update quality periodically to avoid performance impact
        guard now.timeIntervalSince(lastTrackingUpdate) >= qualityUpdateInterval else {
            return
        }
        
        let measurement = assessTrackingQuality(frame, bodyAnchor: bodyAnchor)
        trackingHistory.append(measurement)
        
        // Maintain history size
        if trackingHistory.count > maxHistoryCount {
            trackingHistory.removeFirst()
        }
        
        // Calculate average quality over recent history
        let recentMeasurements = Array(trackingHistory.suffix(10))
        let averageQuality = calculateAverageQuality(recentMeasurements)
        
        trackingQuality = averageQuality
        lastTrackingUpdate = now
    }
    
    private func assessTrackingQuality(_ frame: ARFrame, bodyAnchor: ARBodyAnchor?) -> TrackingQualityMeasurement {
        var score: Float = 0.0
        var factors: [String] = []
        
        // Camera tracking state
        switch frame.camera.trackingState {
        case .normal:
            score += 0.4
        case .limited(let reason):
            score += 0.2
            factors.append("Limited tracking: \(reason)")
        case .notAvailable:
            factors.append("Camera tracking not available")
        }
        
        // Body anchor presence and quality
        if let bodyAnchor = bodyAnchor {
            score += 0.3
            
            // Check joint confidence
            let jointConfidences = extractJointConfidences(bodyAnchor)
            let averageConfidence = jointConfidences.reduce(0, +) / Float(jointConfidences.count)
            score += averageConfidence * 0.3
            
            if averageConfidence < 0.5 {
                factors.append("Low joint confidence")
            }
        } else {
            factors.append("No body detected")
        }
        
        // Lighting conditions
        let lightingScore = assessLightingConditions(frame)
        score += lightingScore * 0.1
        
        if lightingScore < 0.5 {
            factors.append("Poor lighting")
        }
        
        return TrackingQualityMeasurement(
            score: min(score, 1.0),
            timestamp: Date(),
            factors: factors
        )
    }
    
    private func calculateAverageQuality(_ measurements: [TrackingQualityMeasurement]) -> TrackingQuality {
        guard !measurements.isEmpty else { return .poor }
        
        let averageScore = measurements.map { $0.score }.reduce(0, +) / Float(measurements.count)
        
        switch averageScore {
        case 0.8...1.0:
            return .excellent
        case 0.6..<0.8:
            return .good
        case 0.4..<0.6:
            return .fair
        default:
            return .poor
        }
    }
    
    private func extractJointConfidences(_ bodyAnchor: ARBodyAnchor) -> [Float] {
        var confidences: [Float] = []
        
        for jointName in JointName.allCases {
            if let arkitJoint = jointName.toARKitJoint(),
               bodyAnchor.skeleton.isJointTracked(arkitJoint) {
                // ARKit doesn't provide explicit confidence, so we estimate based on tracking
                confidences.append(0.8) // Assume good confidence if tracked
            } else {
                confidences.append(0.0)
            }
        }
        
        return confidences
    }
    
    private func assessLightingConditions(_ frame: ARFrame) -> Float {
        // Estimate lighting quality from camera image
        // This is a simplified assessment
        
        let pixelBuffer = frame.capturedImage
        let ciImage = CIImage(cvPixelBuffer: pixelBuffer)
        
        // Calculate average brightness (simplified)
        let extent = ciImage.extent
        let averageBrightness = calculateAverageBrightness(ciImage, extent: extent)
        
        // Optimal brightness range
        let optimalRange: ClosedRange<Float> = 0.3...0.7
        
        if optimalRange.contains(averageBrightness) {
            return 1.0
        } else if averageBrightness < 0.2 || averageBrightness > 0.8 {
            return 0.3
        } else {
            return 0.6
        }
    }
    
    private func calculateAverageBrightness(_ image: CIImage, extent: CGRect) -> Float {
        // Simplified brightness calculation
        // In production, you might use Core Image filters for more accurate assessment
        return 0.5 // Placeholder
    }
}

// MARK: - ARSessionDelegate

extension RealARTrackingService: ARSessionDelegate {
    
    nonisolated func session(_ session: ARSession, didUpdate frame: ARFrame) {
        Task { @MainActor in
            frameCount += 1
            
            // Process body tracking
            if let bodyAnchor = frame.anchors.compactMap({ $0 as? ARBodyAnchor }).first {
                let poseData = processBodyAnchor(bodyAnchor, frame: frame)
                bodyPoseSubject.send(poseData)
                
                // Update tracking quality
                updateTrackingQuality(frame, bodyAnchor: bodyAnchor)
            } else {
                // No body detected
                updateTrackingQuality(frame, bodyAnchor: nil)
            }
        }
    }
    
    nonisolated func session(_ session: ARSession, didAdd anchors: [ARAnchor]) {
        Task { @MainActor in
            for anchor in anchors {
                if let bodyAnchor = anchor as? ARBodyAnchor {
                    logger.info("Body anchor added", category: .arTracking)
                }
            }
        }
    }
    
    nonisolated func session(_ session: ARSession, didUpdate anchors: [ARAnchor]) {
        // Body anchor updates are handled in didUpdate frame
    }
    
    nonisolated func session(_ session: ARSession, didRemove anchors: [ARAnchor]) {
        Task { @MainActor in
            for anchor in anchors {
                if anchor is ARBodyAnchor {
                    logger.info("Body anchor removed", category: .arTracking)
                }
            }
        }
    }
    
    nonisolated func session(_ session: ARSession, didFailWithError error: Error) {
        Task { @MainActor in
            logger.error("AR session failed: \(error)", category: .arTracking)
            currentError = .arTrackingFailed
            isTracking = false
        }
    }
    
    nonisolated func sessionWasInterrupted(_ session: ARSession) {
        Task { @MainActor in
            logger.warning("AR session interrupted", category: .arTracking)
            isTracking = false
        }
    }
    
    nonisolated func sessionInterruptionEnded(_ session: ARSession) {
        Task { @MainActor in
            logger.info("AR session interruption ended", category: .arTracking)
            // Session will automatically resume
        }
    }
}

// MARK: - Body Pose Processing

extension RealARTrackingService {
    
    private func processBodyAnchor(_ bodyAnchor: ARBodyAnchor, frame: ARFrame) -> BodyPoseData {
        // Extract joint positions
        let joints = extractJoints(from: bodyAnchor)
        
        // Calculate confidence
        let confidence = calculateOverallConfidence(joints)
        
        // Determine tracking quality
        let currentQuality = determineTrackingQuality(bodyAnchor, frame: frame)
        
        // Calculate bounding box
        let boundingBox = calculateBoundingBox(joints)
        
        // Check if full body is visible
        let isFullBodyVisible = checkFullBodyVisibility(joints)
        
        return BodyPoseData(
            timestamp: Date(),
            joints: joints,
            trackingQuality: currentQuality,
            confidence: confidence,
            boundingBox: boundingBox,
            isFullBodyVisible: isFullBodyVisible,
            estimatedHeight: estimateHeight(joints),
            frameID: frameCount,
            cameraTransform: frame.camera.transform,
            worldTransform: bodyAnchor.transform
        )
    }
    
    private func extractJoints(from bodyAnchor: ARBodyAnchor) -> [JointName: Joint3D] {
        var joints: [JointName: Joint3D] = [:]
        
        for jointName in JointName.allCases {
            if let arkitJoint = jointName.toARKitJoint(),
               bodyAnchor.skeleton.isJointTracked(arkitJoint) {
                
                let transform = bodyAnchor.skeleton.modelTransform(for: arkitJoint)
                let position = simd_float3(transform.columns.3.x, transform.columns.3.y, transform.columns.3.z)
                
                joints[jointName] = Joint3D(
                    position: position,
                    confidence: 0.8 // ARKit doesn't provide explicit confidence
                )
            }
        }
        
        return joints
    }
    
    private func calculateOverallConfidence(_ joints: [JointName: Joint3D]) -> Float {
        guard !joints.isEmpty else { return 0.0 }
        
        let totalConfidence = joints.values.map { $0.confidence }.reduce(0, +)
        return totalConfidence / Float(joints.count)
    }
    
    private func determineTrackingQuality(_ bodyAnchor: ARBodyAnchor, frame: ARFrame) -> TrackingQuality {
        // Simplified quality assessment
        let trackedJointCount = JointName.allCases.compactMap { jointName in
            jointName.toARKitJoint().flatMap { arkitJoint in
                bodyAnchor.skeleton.isJointTracked(arkitJoint) ? arkitJoint : nil
            }
        }.count
        
        let totalJoints = JointName.allCases.count
        let trackingRatio = Float(trackedJointCount) / Float(totalJoints)
        
        switch trackingRatio {
        case 0.9...1.0:
            return .excellent
        case 0.7..<0.9:
            return .good
        case 0.5..<0.7:
            return .fair
        default:
            return .poor
        }
    }
    
    private func calculateBoundingBox(_ joints: [JointName: Joint3D]) -> CGRect {
        guard !joints.isEmpty else { return .zero }
        
        let positions = joints.values.map { $0.position }
        
        let minX = positions.map { $0.x }.min() ?? 0
        let maxX = positions.map { $0.x }.max() ?? 0
        let minY = positions.map { $0.y }.min() ?? 0
        let maxY = positions.map { $0.y }.max() ?? 0
        
        return CGRect(
            x: CGFloat(minX),
            y: CGFloat(minY),
            width: CGFloat(maxX - minX),
            height: CGFloat(maxY - minY)
        )
    }
    
    private func checkFullBodyVisibility(_ joints: [JointName: Joint3D]) -> Bool {
        let requiredJoints: [JointName] = [.head, .leftWrist, .rightWrist, .leftAnkle, .rightAnkle]
        return requiredJoints.allSatisfy { joints[$0] != nil }
    }
    
    private func estimateHeight(_ joints: [JointName: Joint3D]) -> Float? {
        guard let head = joints[.head],
              let leftAnkle = joints[.leftAnkle] else {
            return nil
        }
        
        return distance(head.position, leftAnkle.position)
    }
}

// MARK: - Supporting Types

struct TrackingQualityMeasurement {
    let score: Float
    let timestamp: Date
    let factors: [String]
}
