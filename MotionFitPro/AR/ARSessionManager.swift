//
//  ARSessionManager.swift
//  MotionFitPro
//
//  Created by Cascade on 2025-07-14.
//

import Foundation
import ARKit
import Combine
import os.log

// Type alias to avoid naming conflict
typealias PoseTrackingQuality = MotionFitPro.TrackingQuality

/// Manages the ARKit session for body tracking, processes frame data, and monitors performance.
@MainActor
class ARSessionManager: NSObject, ObservableObject {

    // MARK: - Published Properties

    @Published var sessionState: ARSessionState = .stopped
    @Published var trackingQuality: TrackingQuality = .initializing
    @Published var latestPose: BodyPoseData?
    @Published var performanceMetrics = PerformanceMetrics()
    @Published var isBodyDetected = false
    @Published var bodyTrackingConfidence: Float = 0.0

    // MARK: - Public Properties

    static let shared = ARSessionManager()

    // MARK: - Private Properties

    let session = ARSession()
    // private let frameProcessor = CameraFrameProcessor() // Not implemented yet
    private var cancellables = Set<AnyCancellable>()
    private let logger = Logger.shared

    private var frameCounter = 0
    private var lastFrameTimestamp: TimeInterval = 0
    private var bodyAnchor: ARBodyAnchor?
    private var poseHistory: [BodyPoseData] = []
    private let maxPoseHistory = 30 // Keep last 30 poses for stability analysis

    // MARK: - Initialization

    private override init() {
        super.init()
        session.delegate = self
        subscribeToPublishers()
    }

    private func subscribeToPublishers() {
        // frameProcessor subscription disabled until CameraFrameProcessor is implemented
        /*
        frameProcessor.$latestPoseData
            .receive(on: DispatchQueue.main)
            .sink { [weak self] poseData in
                self?.latestPose = poseData
                self?.updateBodyTrackingState(poseData)
            }
            .store(in: &cancellables)
        */
    }

    // MARK: - Session Management

    /// Configures and starts the AR body tracking session.
    func startSession() {
        guard ARBodyTrackingConfiguration.isSupported else {
            logger.error("AR Body Tracking is not supported on this device.")
            sessionState = .failed(ARError(.unsupportedConfiguration))
            return
        }

        let configuration = ARBodyTrackingConfiguration()

        // Enable high-frame-rate capturing on supported devices (iPhone 13 Pro and later)
        if let highestFrameRate = ARBodyTrackingConfiguration.supportedVideoFormats.first(where: { $0.framesPerSecond >= 60 }) {
            configuration.videoFormat = highestFrameRate
            logger.info("Configured for 60 FPS video format.", category: .arSession)
        } else {
            logger.info("60 FPS not supported, using default frame rate.", category: .arSession)
        }

        configuration.automaticSkeletonScaleEstimationEnabled = true
        configuration.isAutoFocusEnabled = true

        session.run(configuration, options: [.resetTracking, .removeExistingAnchors])
        sessionState = .running
        logger.info("AR session started.", category: .arSession)
    }

    /// Pauses the AR session.
    func stopSession() {
        session.pause()
        sessionState = .stopped
        logger.info("AR session stopped.", category: .arSession)
    }

    // MARK: - Device Capability Check

    static var isSupported: Bool {
        return ARBodyTrackingConfiguration.isSupported
    }
}

// MARK: - ARSessionDelegate
extension ARSessionManager: @preconcurrency ARSessionDelegate {
    func session(_ session: ARSession, didUpdate frame: ARFrame) {
        // frameProcessor.processFrameAsync(frame) // Disabled until CameraFrameProcessor is implemented
        updatePerformanceMetrics(frame: frame)
        updateTrackingQuality(for: frame.camera)
        processBodyAnchors(in: frame)
    }

    func session(_ session: ARSession, didAdd anchors: [ARAnchor]) {
        for anchor in anchors {
            if let bodyAnchor = anchor as? ARBodyAnchor {
                processBodyAnchor(bodyAnchor)
            }
        }
    }

    func session(_ session: ARSession, didUpdate anchors: [ARAnchor]) {
        for anchor in anchors {
            if let bodyAnchor = anchor as? ARBodyAnchor {
                processBodyAnchor(bodyAnchor)
            }
        }
    }

    func session(_ session: ARSession, didFailWithError error: Error) {
        logger.error("ARSession failed with error: \(error.localizedDescription)")
        DispatchQueue.main.async {
            self.sessionState = .failed(error)
        }
    }

    func sessionWasInterrupted(_ session: ARSession) {
        logger.warning("ARSession was interrupted.")
        DispatchQueue.main.async {
            self.sessionState = .interrupted
            self.trackingQuality = .poor("Session interrupted")
        }
    }

    func sessionInterruptionEnded(_ session: ARSession) {
        logger.info("ARSession interruption ended. Attempting to restart.")
        startSession()
    }
}

// MARK: - Performance & Quality Monitoring
extension ARSessionManager {
    private func updatePerformanceMetrics(frame: ARFrame) {
        frameCounter += 1
        let currentTime = frame.timestamp
        let deltaTime = currentTime - lastFrameTimestamp

        if deltaTime >= 1.0 { // Update FPS every second
            let fps = Double(frameCounter) / deltaTime
            DispatchQueue.main.async {
                self.performanceMetrics.fps = fps
            }
            frameCounter = 0
            lastFrameTimestamp = currentTime
            logger.debug("FPS: \(String(format: "%.2f", fps))")
        }
    }

    private func updateTrackingQuality(for camera: ARCamera) {
        let newQuality: TrackingQuality
        switch camera.trackingState {
        case .normal:
            newQuality = .good
        case .notAvailable:
            newQuality = .poor("Tracking not available.")
        case .limited(let reason):
            let reasonText: String
            switch reason {
            case .excessiveMotion:
                reasonText = "Excessive motion."
            case .insufficientFeatures:
                reasonText = "Insufficient features."
            case .initializing:
                newQuality = .initializing
                return
            case .relocalizing:
                newQuality = .poor("Relocalizing...")
                return
            @unknown default:
                reasonText = "Unknown reason."
            }
            newQuality = .limited(reasonText)
            logger.warning("Tracking quality is limited: \(reasonText)")
        }
        
        if newQuality != self.trackingQuality {
            DispatchQueue.main.async {
                self.trackingQuality = newQuality
            }
        }
    }

    // MARK: - Body Tracking Methods

    private func processBodyAnchors(in frame: ARFrame) {
        let bodyAnchors = frame.anchors.compactMap { $0 as? ARBodyAnchor }

        if let bodyAnchor = bodyAnchors.first {
            self.bodyAnchor = bodyAnchor
            DispatchQueue.main.async {
                self.isBodyDetected = true
                self.bodyTrackingConfidence = bodyAnchor.isTracked ? 0.9 : 0.3
            }
        } else {
            DispatchQueue.main.async {
                self.isBodyDetected = false
                self.bodyTrackingConfidence = 0.0
            }
        }
    }

    func processBodyAnchor(_ bodyAnchor: ARBodyAnchor) {
        guard let poseData = createBodyPoseData(from: bodyAnchor) else {
            logger.warning("Failed to create BodyPoseData from ARBodyAnchor")
            return
        }

        // Add to pose history for stability analysis
        addToPoseHistory(poseData)

        DispatchQueue.main.async {
            self.latestPose = poseData
            self.bodyTrackingConfidence = poseData.confidence
        }

        logger.debug("Processed body anchor with confidence: \(poseData.confidence)")
    }

    private func createBodyPoseData(from bodyAnchor: ARBodyAnchor) -> BodyPoseData? {
        let skeleton = bodyAnchor.skeleton
        let jointNames = ARSkeletonDefinition.defaultBody3D.jointNames
        var joints: [JointName: Joint3D] = [:]

        for jointName in jointNames {
            let arJointName = ARSkeleton.JointName(rawValue: jointName)
            guard let motionJointName = mapARJointToMotionJoint(arJointName) else {
                continue
            }

            let modelTransform = skeleton.modelTransform(for: arJointName) ?? matrix_identity_float4x4
            let position = simd_make_float3(modelTransform.columns.3)
            let confidence: Float = bodyAnchor.isTracked ? 0.9 : 0.3

            joints[motionJointName] = Joint3D(
                position: position,
                confidence: confidence,
                isTracked: bodyAnchor.isTracked
            )
        }

        let trackingQuality = determineTrackingQuality(from: bodyAnchor, joints: joints)
        let boundingBox = calculateBoundingBox(from: joints)

        return BodyPoseData(
            joints: joints,
            trackingQuality: trackingQuality,
            confidence: bodyAnchor.isTracked ? 0.9 : 0.3,
            boundingBox: boundingBox,
            isFullBodyVisible: joints.count >= 15, // Minimum joints for full body
            estimatedHeight: Float(bodyAnchor.estimatedScaleFactor),
            frameID: frameCounter,
            cameraTransform: bodyAnchor.transform
        )
    }

    private func mapARJointToMotionJoint(_ arJoint: ARSkeleton.JointName) -> JointName? {
        switch arJoint.rawValue {
        case "head": return .head
        case "neck_1": return .neck
        case "root": return .root
        case "spine_1": return .spine1
        case "spine_2": return .spine2
        case "spine_3": return .spine3
        case "spine_4": return .spine4
        case "spine_5": return .spine5
        case "spine_6": return .spine6
        case "spine_7": return .spine7
        case "left_shoulder_1": return .leftShoulder
        case "left_arm": return .leftUpperArm
        case "left_forearm": return .leftLowerArm
        case "left_hand": return .leftHand
        case "right_shoulder_1": return .rightShoulder
        case "right_arm": return .rightUpperArm
        case "right_forearm": return .rightLowerArm
        case "right_hand": return .rightHand
        case "left_upLeg": return .leftUpperLeg
        case "left_leg": return .leftLowerLeg
        case "left_foot": return .leftFoot
        case "left_toes": return .leftToes
        case "right_upLeg": return .rightUpperLeg
        case "right_leg": return .rightLowerLeg
        case "right_foot": return .rightFoot
        case "right_toes": return .rightToes
        default: return nil
        }
    }

    private func determineTrackingQuality(from bodyAnchor: ARBodyAnchor, joints: [JointName: Joint3D]) -> PoseTrackingQuality {
        if !bodyAnchor.isTracked {
            return .poor
        }

        let trackedJoints = joints.values.filter { $0.isTracked && $0.confidence > 0.7 }
        let trackingRatio = Float(trackedJoints.count) / Float(joints.count)

        switch trackingRatio {
        case 0.9...1.0: return .excellent
        case 0.7..<0.9: return .good
        case 0.5..<0.7: return .limited
        case 0.3..<0.5: return .poor
        default: return .initializing
        }
    }

    private func calculateBoundingBox(from joints: [JointName: Joint3D]) -> CGRect {
        guard !joints.isEmpty else { return .zero }

        let positions = joints.values.map { $0.position }
        let minX = positions.map { $0.x }.min() ?? 0
        let maxX = positions.map { $0.x }.max() ?? 0
        let minY = positions.map { $0.y }.min() ?? 0
        let maxY = positions.map { $0.y }.max() ?? 0

        return CGRect(
            x: CGFloat(minX),
            y: CGFloat(minY),
            width: CGFloat(maxX - minX),
            height: CGFloat(maxY - minY)
        )
    }

    private func addToPoseHistory(_ poseData: BodyPoseData) {
        poseHistory.append(poseData)
        if poseHistory.count > maxPoseHistory {
            poseHistory.removeFirst()
        }
    }

    private func updateBodyTrackingState(_ poseData: BodyPoseData?) {
        if let poseData = poseData {
            isBodyDetected = true
            bodyTrackingConfidence = poseData.confidence
        } else {
            isBodyDetected = false
            bodyTrackingConfidence = 0.0
        }
    }

    // MARK: - Public API

    /// Get the current body pose data
    func getCurrentPose() -> BodyPoseData? {
        return latestPose
    }

    /// Get pose history for stability analysis
    func getPoseHistory() -> [BodyPoseData] {
        return poseHistory
    }

    /// Check if body tracking is active and reliable
    func isBodyTrackingReliable() -> Bool {
        return isBodyDetected && bodyTrackingConfidence > 0.7
    }

    /// Reset pose history
    func resetPoseHistory() {
        poseHistory.removeAll()
        logger.info("Pose history reset")
    }
}

// MARK: - Supporting Types

extension ARSessionManager {
    enum ARSessionState: Equatable {
        case running
        case stopped
        case failed(Error)
        case interrupted

        static func == (lhs: ARSessionManager.ARSessionState, rhs: ARSessionManager.ARSessionState) -> Bool {
            switch (lhs, rhs) {
            case (.running, .running), (.stopped, .stopped), (.interrupted, .interrupted):
                return true
            case (.failed(let lError), .failed(let rError)):
                return (lError as NSError).code == (rError as NSError).code
            default:
                return false
            }
        }
    }

    enum TrackingQuality: Equatable {
        case initializing
        case good
        case limited(String)
        case poor(String)
    }

    struct PerformanceMetrics {
        var fps: Double = 0.0
    }
}