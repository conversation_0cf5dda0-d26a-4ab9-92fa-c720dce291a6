import XCTest
import SwiftData
@testable import MotionFitPro

@MainActor
final class DataLayerTests: XCTestCase {

    var modelContainer: ModelContainer!
    var modelContext: ModelContext!

    override func setUp() {
        super.setUp()
        do {
            let schema = Schema([
                UserProfile.self,
                WorkoutSession.self,
                ExercisePerformance.self,
                SetPerformance.self,
                RepPerformance.self
            ])
            let config = ModelConfiguration(isStoredInMemoryOnly: true)
            modelContainer = try ModelContainer(for: schema, configurations: config)
            modelContext = modelContainer.mainContext
        } catch {
            fatalError("Failed to setup SwiftData container for testing: \(error)")
        }
    }

    override func tearDown() {
        modelContainer = nil
        modelContext = nil
        super.tearDown()
    }

    func testUserProfileCreation() {
        let profile = UserProfile(name: "Test User", dateOfBirth: Date(), goal: "Test Goal")
        modelContext.insert(profile)
        
        let descriptor = FetchDescriptor<UserProfile>()
        let fetchedProfiles = try! modelContext.fetch(descriptor)
        
        XCTAssertEqual(fetchedProfiles.count, 1)
        XCTAssertEqual(fetchedProfiles.first?.name, "Test User")
    }

    func testWorkoutSessionCreationAndRelationship() {
        let profile = UserProfile(name: "Test User", dateOfBirth: Date(), goal: "Test Goal")
        let session = WorkoutSession(name: "Test Workout", startTime: Date())
        session.userProfile = profile
        
        modelContext.insert(profile)
        modelContext.insert(session)
        
        XCTAssertNotNil(session.userProfile)
        XCTAssertEqual(session.userProfile?.name, "Test User")
        XCTAssertEqual(profile.workoutSessions?.count, 1)
        XCTAssertEqual(profile.workoutSessions?.first?.name, "Test Workout")
    }
    
    func testFullDataHierarchy() {
        // 1. Create Profile
        let profile = UserProfile(name: "Hierarchy User", dateOfBirth: Date(), goal: "Test Hierarchy")
        modelContext.insert(profile)
        
        // 2. Create Session
        let session = WorkoutSession(name: "Hierarchy Workout")
        session.userProfile = profile
        modelContext.insert(session)
        
        // 3. Create Exercise
        let exercise = ExercisePerformance(exerciseName: "Squats", category: .strength, difficulty: .intermediate, equipment: .barbell, instructions: "Go low!")
        exercise.workoutSession = session
        modelContext.insert(exercise)
        
        // 4. Create Set
        let set = SetPerformance(setNumber: 1, targetReps: 10, weight: 50.0)
        set.exercise = exercise
        modelContext.insert(set)
        
        // 5. Create Rep
        let rep = RepPerformance(repNumber: 1, formScore: 95, timingScore: 90, rangeOfMotionScore: 85)
        rep.set = set
        modelContext.insert(rep)
        
        // Verify relationships
        XCTAssertEqual(profile.workoutSessions?.first?.exercises?.first?.sets?.first?.reps?.first?.formScore, 95)
        XCTAssertEqual(rep.set?.exercise?.workoutSession?.userProfile?.name, "Hierarchy User")
    }
    
    func testCascadeDelete() throws {
        // Setup the full hierarchy
        testFullDataHierarchy()
        
        // Fetch the profile
        let descriptor = FetchDescriptor<UserProfile>(predicate: #Predicate { $0.name == "Hierarchy User" })
        let profileToDelete = try XCTUnwrap(modelContext.fetch(descriptor).first)
        
        // Delete the profile
        modelContext.delete(profileToDelete)
        try modelContext.save()
        
        // Verify that everything has been deleted
        XCTAssertEqual(try modelContext.fetch(FetchDescriptor<UserProfile>()).count, 0)
        XCTAssertEqual(try modelContext.fetch(FetchDescriptor<WorkoutSession>()).count, 0)
        XCTAssertEqual(try modelContext.fetch(FetchDescriptor<ExercisePerformance>()).count, 0)
        XCTAssertEqual(try modelContext.fetch(FetchDescriptor<SetPerformance>()).count, 0)
        XCTAssertEqual(try modelContext.fetch(FetchDescriptor<RepPerformance>()).count, 0)
    }
    
    func testWorkoutRepository() {
        let repository = WorkoutRepository(modelContext: modelContext)
        
        // Create a user
        repository.createUserProfile(name: "Repo User", dateOfBirth: Date(), goal: "Test Repo")
        let profile = repository.fetchUserProfile()
        XCTAssertNotNil(profile)
        XCTAssertEqual(profile?.name, "Repo User")
        
        // Start a session
        let session = repository.startWorkoutSession(name: "Repo Workout", userProfile: profile!)
        XCTAssertEqual(repository.fetchAllWorkoutSessions().count, 1)
        
        // End the session
        repository.endWorkoutSession(session)
        XCTAssertNotNil(session.endTime)
    }
}
