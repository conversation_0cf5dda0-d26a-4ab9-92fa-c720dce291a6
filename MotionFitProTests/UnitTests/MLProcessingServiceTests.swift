import XCTest
import Combine
@testable import MotionFitPro

// MARK: - ML Processing Service Tests

final class MLProcessingServiceTests: TestBase {
    
    // MARK: - Properties
    
    private var mlService: RealMLProcessingService!
    private var mockPoseData: BodyPoseData!
    
    // MARK: - Setup
    
    override func setUp() {
        super.setUp()
        
        mlService = RealMLProcessingService()
        mockPoseData = createMockBodyPoseData()
    }
    
    override func tearDown() {
        mlService = nil
        mockPoseData = nil
        
        super.tearDown()
    }
    
    // MARK: - Process Body Pose Tests
    
    func testProcessBodyPose_WithValidData_ReturnsAnalysis() async throws {
        // Given
        let poseData = createMockBodyPoseData()
        
        // When
        let analysis = try await mlService.processBodyPose(poseData)
        
        // Then
        XCTAssertNotNil(analysis)
        XCTAssertEqual(analysis.poseData.timestamp, poseData.timestamp)
        XCTAssertGreaterThan(analysis.formScore, 0.0)
        XCTAssertLessThanOrEqual(analysis.formScore, 1.0)
        XCTAssertGreaterThan(analysis.confidence, 0.0)
    }
    
    func testProcessBodyPose_WithLowQualityData_ReturnsLowConfidence() async throws {
        // Given
        var lowQualityPoseData = createMockBodyPoseData()
        lowQualityPoseData.confidence = 0.3
        
        // When
        let analysis = try await mlService.processBodyPose(lowQualityPoseData)
        
        // Then
        XCTAssertLessThan(analysis.confidence, 0.5)
    }
    
    func testProcessBodyPose_Performance() async {
        // Given
        let poseData = createMockBodyPoseData()
        
        // When & Then
        await measureAsyncPerformance {
            _ = try await mlService.processBodyPose(poseData)
        }
    }
    
    // MARK: - Exercise Classification Tests
    
    func testClassifyExercise_WithSquatPose_ReturnsSquat() async throws {
        // Given
        let squatPoseData = createMockSquatPoseData()
        
        // When
        let exerciseType = try await mlService.classifyExercise(squatPoseData)
        
        // Then
        XCTAssertEqual(exerciseType, .squat)
    }
    
    func testClassifyExercise_WithUnknownPose_ReturnsUnknown() async throws {
        // Given
        let unknownPoseData = createMockUnknownPoseData()
        
        // When
        let exerciseType = try await mlService.classifyExercise(unknownPoseData)
        
        // Then
        XCTAssertEqual(exerciseType, .unknown)
    }
    
    // MARK: - Form Analysis Tests
    
    func testAnalyzeForm_WithGoodForm_ReturnsHighScore() async throws {
        // Given
        let goodFormPoseData = createMockGoodFormPoseData()
        
        // When
        let formAnalysis = try await mlService.analyzeForm(goodFormPoseData, for: .squat)
        
        // Then
        XCTAssertGreaterThan(formAnalysis.overallScore, 0.7)
        XCTAssertEqual(formAnalysis.exerciseType, .squat)
        XCTAssertTrue(formAnalysis.issues.isEmpty)
    }
    
    func testAnalyzeForm_WithPoorForm_ReturnsLowScore() async throws {
        // Given
        let poorFormPoseData = createMockPoorFormPoseData()
        
        // When
        let formAnalysis = try await mlService.analyzeForm(poorFormPoseData, for: .squat)
        
        // Then
        XCTAssertLessThan(formAnalysis.overallScore, 0.5)
        XCTAssertFalse(formAnalysis.issues.isEmpty)
    }
    
    func testAnalyzeForm_WithCriticalIssues_ReturnsSafetyWarning() async throws {
        // Given
        let dangerousPoseData = createMockDangerousPoseData()
        
        // When
        let formAnalysis = try await mlService.analyzeForm(dangerousPoseData, for: .squat)
        
        // Then
        XCTAssertTrue(formAnalysis.hasCriticalIssues)
        XCTAssertTrue(formAnalysis.issues.contains { $0.severity == .critical })
    }
    
    // MARK: - Movement Phase Detection Tests
    
    func testDetectMovementPhase_WithEccentricMotion_ReturnsEccentric() async throws {
        // Given
        let eccentricPoseData = createMockEccentricPoseData()
        
        // When
        let phase = try await mlService.detectMovementPhase(eccentricPoseData, for: .squat)
        
        // Then
        XCTAssertEqual(phase, .eccentric)
    }
    
    func testDetectMovementPhase_WithConcentricMotion_ReturnsConcentric() async throws {
        // Given
        let concentricPoseData = createMockConcentricPoseData()
        
        // When
        let phase = try await mlService.detectMovementPhase(concentricPoseData, for: .squat)
        
        // Then
        XCTAssertEqual(phase, .concentric)
    }
    
    // MARK: - Error Handling Tests
    
    func testProcessBodyPose_WithInvalidData_ThrowsError() async {
        // Given
        let invalidPoseData = createMockInvalidPoseData()
        
        // When & Then
        await XCTAssertThrowsError(
            try await mlService.processBodyPose(invalidPoseData)
        ) { error in
            XCTAssertTrue(error is MLProcessingError)
        }
    }
    
    // MARK: - Memory Management Tests
    
    func testMLService_DoesNotLeakMemory() {
        // Given
        weak var weakService: RealMLProcessingService?
        
        // When
        autoreleasepool {
            let service = RealMLProcessingService()
            weakService = service
            // Service goes out of scope
        }
        
        // Then
        XCTAssertNil(weakService, "MLProcessingService should be deallocated")
    }
    
    // MARK: - Concurrent Processing Tests
    
    func testProcessBodyPose_ConcurrentRequests_HandlesCorrectly() async throws {
        // Given
        let poseDataArray = (0..<10).map { _ in createMockBodyPoseData() }
        
        // When
        let results = try await withThrowingTaskGroup(of: ExerciseAnalysis.self) { group in
            for poseData in poseDataArray {
                group.addTask {
                    return try await self.mlService.processBodyPose(poseData)
                }
            }
            
            var analyses: [ExerciseAnalysis] = []
            for try await analysis in group {
                analyses.append(analysis)
            }
            return analyses
        }
        
        // Then
        XCTAssertEqual(results.count, poseDataArray.count)
        XCTAssertTrue(results.allSatisfy { $0.confidence > 0 })
    }
    
    // MARK: - Mock Data Helpers
    
    private func createMockSquatPoseData() -> BodyPoseData {
        var poseData = createMockBodyPoseData()
        
        // Modify joints to represent a squat position
        if var leftKnee = poseData.joints[.leftKnee],
           var rightKnee = poseData.joints[.rightKnee] {
            leftKnee.position.y = -0.5 // Lower position
            rightKnee.position.y = -0.5
            poseData.joints[.leftKnee] = leftKnee
            poseData.joints[.rightKnee] = rightKnee
        }
        
        return poseData
    }
    
    private func createMockUnknownPoseData() -> BodyPoseData {
        var poseData = createMockBodyPoseData()
        
        // Set very low confidence to simulate unknown pose
        poseData.confidence = 0.2
        for (jointName, var joint) in poseData.joints {
            joint.confidence = 0.2
            poseData.joints[jointName] = joint
        }
        
        return poseData
    }
    
    private func createMockGoodFormPoseData() -> BodyPoseData {
        var poseData = createMockBodyPoseData()
        
        // Set high confidence and good alignment
        poseData.confidence = 0.95
        for (jointName, var joint) in poseData.joints {
            joint.confidence = 0.9
            poseData.joints[jointName] = joint
        }
        
        return poseData
    }
    
    private func createMockPoorFormPoseData() -> BodyPoseData {
        var poseData = createMockBodyPoseData()
        
        // Simulate poor form with misaligned joints
        if var leftKnee = poseData.joints[.leftKnee],
           var rightKnee = poseData.joints[.rightKnee] {
            leftKnee.position.x = 0.5 // Knee valgus
            rightKnee.position.x = -0.5
            poseData.joints[.leftKnee] = leftKnee
            poseData.joints[.rightKnee] = rightKnee
        }
        
        return poseData
    }
    
    private func createMockDangerousPoseData() -> BodyPoseData {
        var poseData = createMockBodyPoseData()
        
        // Simulate dangerous form
        if var spine = poseData.joints[.spine6] {
            spine.position.x = 1.0 // Extreme forward lean
            poseData.joints[.spine6] = spine
        }
        
        return poseData
    }
    
    private func createMockEccentricPoseData() -> BodyPoseData {
        var poseData = createMockBodyPoseData()
        
        // Simulate lowering phase
        if var hips = poseData.joints[.root] {
            hips.position.y = -0.3 // Lowering
            poseData.joints[.root] = hips
        }
        
        return poseData
    }
    
    private func createMockConcentricPoseData() -> BodyPoseData {
        var poseData = createMockBodyPoseData()
        
        // Simulate rising phase
        if var hips = poseData.joints[.root] {
            hips.position.y = 0.3 // Rising
            poseData.joints[.root] = hips
        }
        
        return poseData
    }
    
    private func createMockInvalidPoseData() -> BodyPoseData {
        var poseData = createMockBodyPoseData()
        
        // Remove critical joints to make data invalid
        poseData.joints.removeValue(forKey: .root)
        poseData.joints.removeValue(forKey: .spine6)
        poseData.confidence = 0.0
        
        return poseData
    }
}
