import XCTest
import ARKit
import CoreML
import simd
import Network
@testable import MotionFitPro

class EdgeCaseTests: XCTestCase {
    
    var arSessionManager: ARSessionManager!
    var mlProcessingManager: MLProcessingManager!
    var repCountingEngine: RepCountingEngine!
    var networkMonitor: NetworkMonitor!
    var thermalManager: ThermalManager!
    var edgeCaseSimulator: EdgeCaseSimulator!
    
    override func setUpWithError() throws {
        super.setUp()
        
        arSessionManager = ARSessionManager.shared
        mlProcessingManager = MLProcessingManager.shared
        repCountingEngine = RepCountingEngine()
        networkMonitor = NetworkMonitor.shared
        thermalManager = ThermalManager.shared
        edgeCaseSimulator = EdgeCaseSimulator()
        
        setupEdgeCaseTestEnvironment()
    }
    
    override func tearDownWithError() throws {
        cleanupEdgeCaseTestEnvironment()
        
        arSessionManager = nil
        mlProcessingManager = nil
        repCountingEngine = nil
        networkMonitor = nil
        thermalManager = nil
        edgeCaseSimulator = nil
        
        super.tearDown()
    }
    
    // MARK: - Poor Lighting Conditions Tests
    
    func testPoorLighting_VeryDimEnvironment() async throws {
        // Given: Very dim lighting conditions (< 10 lux)
        edgeCaseSimulator.setLightingCondition(.veryDim)
        
        var trackingResults: [TrackingResult] = []
        var confidenceReadings: [Float] = []
        
        // When: Attempt workout in very dim lighting
        try await startARSession()
        repCountingEngine.startCounting(for: .squat)
        
        for frameIndex in 0..<300 { // 10 seconds at 30 FPS
            let arFrame = edgeCaseSimulator.createDimLightingFrame(frameIndex: frameIndex)
            
            let trackingResult = await processFrameWithTracking(arFrame)
            trackingResults.append(trackingResult)
            
            if let poseData = trackingResult.poseData {
                confidenceReadings.append(poseData.confidence)
            }
            
            try await Task.sleep(nanoseconds: 33_333_333)
        }
        
        await stopARSession()
        
        // Then: Should gracefully handle poor tracking
        let avgConfidence = confidenceReadings.reduce(0, +) / Float(confidenceReadings.count)
        let lowConfidenceCount = confidenceReadings.filter { $0 < 0.3 }.count
        let trackingLossCount = trackingResults.filter { !$0.trackingSuccessful }.count
        
        XCTAssertGreaterThan(avgConfidence, 0.2, "Should maintain some tracking in dim light")
        XCTAssertLessThan(Double(trackingLossCount) / Double(trackingResults.count), 0.8, "Should not lose tracking completely")
        
        // Verify fallback mechanisms
        let fallbackActivationCount = trackingResults.filter { $0.fallbackModeActivated }.count
        XCTAssertGreaterThan(fallbackActivationCount, 0, "Should activate fallback tracking modes")
    }
    
    func testPoorLighting_BacklitConditions() async throws {
        // Given: Strong backlighting creating silhouettes
        edgeCaseSimulator.setLightingCondition(.backlit)
        
        var poseDetectionResults: [PoseDetectionResult] = []
        
        // When: Attempt body detection with backlighting
        try await startARSession()
        
        for frameIndex in 0..<200 {
            let arFrame = edgeCaseSimulator.createBacklitFrame(frameIndex: frameIndex)
            let result = await attemptPoseDetection(arFrame)
            poseDetectionResults.append(result)
            
            try await Task.sleep(nanoseconds: 50_000_000) // 20 FPS for edge case
        }
        
        await stopARSession()
        
        // Then: Should handle challenging lighting conditions
        let successfulDetections = poseDetectionResults.filter { $0.poseDetected }.count
        let partialDetections = poseDetectionResults.filter { $0.partialDetection }.count
        
        XCTAssertGreaterThan(successfulDetections + partialDetections, poseDetectionResults.count / 2, "Should detect poses at least 50% of the time")
        
        // Verify error recovery
        let recoveryAttempts = poseDetectionResults.filter { $0.recoveryAttempted }.count
        XCTAssertGreaterThan(recoveryAttempts, 0, "Should attempt pose detection recovery")
    }
    
    func testPoorLighting_RapidLightChanges() async throws {
        // Given: Rapidly changing lighting conditions
        var lightingResults: [LightingAdaptationResult] = []
        
        // When: Simulate rapid lighting changes (like walking under streetlights)
        try await startARSession()
        repCountingEngine.startCounting(for: .squat)
        
        for cycle in 0..<10 {
            // Bright phase
            edgeCaseSimulator.setLightingCondition(.bright)
            for frameIndex in 0..<30 { // 1 second
                let arFrame = edgeCaseSimulator.createDynamicLightingFrame(frameIndex: frameIndex, cycle: cycle)
                let result = await processFrameWithLightingAdaptation(arFrame)
                lightingResults.append(result)
                try await Task.sleep(nanoseconds: 33_333_333)
            }
            
            // Dark phase
            edgeCaseSimulator.setLightingCondition(.veryDim)
            for frameIndex in 0..<30 { // 1 second
                let arFrame = edgeCaseSimulator.createDynamicLightingFrame(frameIndex: frameIndex, cycle: cycle)
                let result = await processFrameWithLightingAdaptation(arFrame)
                lightingResults.append(result)
                try await Task.sleep(nanoseconds: 33_333_333)
            }
        }
        
        await stopARSession()
        
        // Then: Should adapt to lighting changes
        let adaptationCount = lightingResults.filter { $0.adaptationSuccessful }.count
        let stabilitCount = lightingResults.filter { $0.trackingStable }.count
        
        XCTAssertGreaterThan(Double(adaptationCount) / Double(lightingResults.count), 0.7, "Should adapt to lighting changes 70% of the time")
        XCTAssertGreaterThan(Double(stabilitCount) / Double(lightingResults.count), 0.6, "Should maintain stable tracking 60% of the time")
    }
    
    // MARK: - Unusual Body Types Tests
    
    func testUnusualBodyTypes_VeryTallPerson() async throws {
        // Given: Very tall person (2.1m height)
        let tallPersonCharacteristics = BodyCharacteristics(
            height: 2.1,
            armSpan: 2.2,
            legLength: 1.1,
            torsoLength: 0.8,
            bodyType: .tall
        )
        
        edgeCaseSimulator.setBodyCharacteristics(tallPersonCharacteristics)
        
        var poseAnalysisResults: [PoseAnalysisResult] = []
        
        // When: Perform squats with unusual proportions
        try await startARSession()
        repCountingEngine.startCounting(for: .squat)
        
        // Simulate 5 complete squats
        for repIndex in 0..<5 {
            let squatFrames = edgeCaseSimulator.createTallPersonSquatSequence(repIndex: repIndex)
            
            for arFrame in squatFrames {
                let result = await analyzeUnusualBodyPose(arFrame, characteristics: tallPersonCharacteristics)
                poseAnalysisResults.append(result)
                
                try await Task.sleep(nanoseconds: 33_333_333)
            }
        }
        
        await stopARSession()
        
        // Then: Should handle unusual proportions
        let successfulAnalyses = poseAnalysisResults.filter { $0.analysisSuccessful }.count
        let calibrationAdjustments = poseAnalysisResults.filter { $0.calibrationAdjusted }.count
        let detectedReps = repCountingEngine.getCurrentRepCount()
        
        XCTAssertGreaterThan(Double(successfulAnalyses) / Double(poseAnalysisResults.count), 0.8, "Should analyze tall person poses successfully")
        XCTAssertGreaterThan(calibrationAdjustments, 0, "Should make calibration adjustments for unusual proportions")
        XCTAssertGreaterThanOrEqual(detectedReps, 3, "Should detect at least 3 out of 5 reps for tall person")
    }
    
    func testUnusualBodyTypes_ShortStatuePerson() async throws {
        // Given: Person with short stature (1.3m height)
        let shortPersonCharacteristics = BodyCharacteristics(
            height: 1.3,
            armSpan: 1.25,
            legLength: 0.6,
            torsoLength: 0.5,
            bodyType: .short
        )
        
        edgeCaseSimulator.setBodyCharacteristics(shortPersonCharacteristics)
        
        var adaptationResults: [AdaptationResult] = []
        
        // When: Test exercise recognition with short stature
        try await startARSession()
        repCountingEngine.startCounting(for: .pushUp)
        
        for frameIndex in 0..<240 { // 8 seconds of push-up simulation
            let arFrame = edgeCaseSimulator.createShortPersonFrame(frameIndex: frameIndex, exercise: .pushUp)
            let result = await processFrameWithAdaptation(arFrame, characteristics: shortPersonCharacteristics)
            adaptationResults.append(result)
            
            try await Task.sleep(nanoseconds: 33_333_333)
        }
        
        await stopARSession()
        
        // Then: Should adapt to different body proportions
        let successfulAdaptations = adaptationResults.filter { $0.adaptationSuccessful }.count
        let scaleAdjustments = adaptationResults.filter { $0.scaleAdjusted }.count
        
        XCTAssertGreaterThan(Double(successfulAdaptations) / Double(adaptationResults.count), 0.75, "Should adapt to short stature successfully")
        XCTAssertGreaterThan(scaleAdjustments, 0, "Should make scale adjustments")
    }
    
    func testUnusualBodyTypes_AtypicalProportions() async throws {
        // Given: Person with atypical proportions (e.g., long torso, short legs)
        let atypicalCharacteristics = BodyCharacteristics(
            height: 1.7,
            armSpan: 1.9, // Unusually long arms
            legLength: 0.7, // Short legs relative to height
            torsoLength: 0.8, // Long torso
            bodyType: .atypical
        )
        
        edgeCaseSimulator.setBodyCharacteristics(atypicalCharacteristics)
        
        var formAnalysisResults: [FormAnalysisResult] = []
        
        // When: Analyze form with atypical proportions
        try await startARSession()
        repCountingEngine.startCounting(for: .squat)
        
        for frameIndex in 0..<180 { // 6 seconds
            let arFrame = edgeCaseSimulator.createAtypicalProportionsFrame(frameIndex: frameIndex)
            let result = await analyzeFormWithAdaptation(arFrame, characteristics: atypicalCharacteristics)
            formAnalysisResults.append(result)
            
            try await Task.sleep(nanoseconds: 33_333_333)
        }
        
        await stopARSession()
        
        // Then: Should provide accurate form analysis despite atypical proportions
        let accurateAnalyses = formAnalysisResults.filter { $0.formAnalysisAccurate }.count
        let compensationAdjustments = formAnalysisResults.filter { $0.compensationAdjustmentMade }.count
        
        XCTAssertGreaterThan(Double(accurateAnalyses) / Double(formAnalysisResults.count), 0.7, "Should provide accurate form analysis despite atypical proportions")
        XCTAssertGreaterThan(compensationAdjustments, 0, "Should make compensation adjustments")
    }
    
    // MARK: - Network Interruption Tests
    
    func testNetworkInterruption_CompleteOutage() async throws {
        // Given: Stable network that will be interrupted
        networkMonitor.startMonitoring()
        
        var syncResults: [SyncResult] = []
        var workoutContinuity: [WorkoutContinuityCheck] = []
        
        // When: Start workout and simulate network outage
        try await startARSession()
        repCountingEngine.startCounting(for: .squat)
        
        // Normal operation for 30 seconds
        for frameIndex in 0..<900 {
            let arFrame = createStandardARFrame(frameIndex: frameIndex)
            await processFrameWithNetworkSync(arFrame)
            
            // Simulate network outage after 10 seconds
            if frameIndex == 300 {
                edgeCaseSimulator.simulateNetworkOutage()
            }
            
            // Check workout continuity
            if frameIndex % 150 == 0 { // Every 5 seconds
                let continuityCheck = checkWorkoutContinuity()
                workoutContinuity.append(continuityCheck)
            }
            
            try await Task.sleep(nanoseconds: 33_333_333)
        }
        
        // Restore network and check sync
        edgeCaseSimulator.restoreNetwork()
        
        let finalSyncResult = try await attemptDataSync()
        syncResults.append(finalSyncResult)
        
        await stopARSession()
        networkMonitor.stopMonitoring()
        
        // Then: Workout should continue without network
        let continuousOperationCount = workoutContinuity.filter { $0.workoutContinuous }.count
        let dataIntegrityCount = workoutContinuity.filter { $0.dataIntegrityMaintained }.count
        
        XCTAssertEqual(continuousOperationCount, workoutContinuity.count, "Workout should continue during network outage")
        XCTAssertEqual(dataIntegrityCount, workoutContinuity.count, "Data integrity should be maintained offline")
        XCTAssertTrue(finalSyncResult.syncSuccessful, "Should sync data when network returns")
    }
    
    func testNetworkInterruption_IntermittentConnectivity() async throws {
        // Given: Intermittent network connectivity
        networkMonitor.startMonitoring()
        
        var connectionAttempts: [ConnectionAttempt] = []
        var dataRetentionResults: [DataRetentionResult] = []
        
        // When: Simulate workout with spotty connectivity
        try await startARSession()
        repCountingEngine.startCounting(for: .squat)
        
        for cycle in 0..<20 { // 20 cycles of connectivity changes
            // Connected phase (2 seconds)
            edgeCaseSimulator.setNetworkCondition(.connected)
            
            for frameIndex in 0..<60 {
                let arFrame = createStandardARFrame(frameIndex: frameIndex)
                await processFrameWithNetworkSync(arFrame)
                try await Task.sleep(nanoseconds: 33_333_333)
            }
            
            let connectAttempt = attemptNetworkOperation()
            connectionAttempts.append(connectAttempt)
            
            // Disconnected phase (3 seconds)
            edgeCaseSimulator.setNetworkCondition(.disconnected)
            
            for frameIndex in 0..<90 {
                let arFrame = createStandardARFrame(frameIndex: frameIndex)
                await processFrameWithNetworkSync(arFrame)
                try await Task.sleep(nanoseconds: 33_333_333)
            }
            
            let retentionResult = checkDataRetention()
            dataRetentionResults.append(retentionResult)
        }
        
        await stopARSession()
        networkMonitor.stopMonitoring()
        
        // Then: Should handle intermittent connectivity gracefully
        let successfulConnections = connectionAttempts.filter { $0.successful }.count
        let dataRetentionSuccess = dataRetentionResults.filter { $0.dataRetained }.count
        
        XCTAssertGreaterThan(successfulConnections, 15, "Should succeed when network is available")
        XCTAssertEqual(dataRetentionSuccess, dataRetentionResults.count, "Should retain all data during disconnections")
    }
    
    func testNetworkInterruption_SlowConnection() async throws {
        // Given: Very slow network connection
        edgeCaseSimulator.setNetworkCondition(.verySlow)
        networkMonitor.startMonitoring()
        
        var syncTimeouts: [SyncTimeout] = []
        var adaptiveResults: [AdaptiveBehaviorResult] = []
        
        // When: Attempt sync operations with slow connection
        try await startARSession()
        
        for attempt in 0..<10 {
            let syncStart = Date()
            let syncResult = try await attemptSlowNetworkSync()
            let syncDuration = Date().timeIntervalSince(syncStart)
            
            syncTimeouts.append(SyncTimeout(
                duration: syncDuration,
                successful: syncResult.successful,
                timedOut: syncDuration > 30.0
            ))
            
            let adaptiveResult = checkAdaptiveBehavior()
            adaptiveResults.append(adaptiveResult)
            
            try await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds between attempts
        }
        
        await stopARSession()
        networkMonitor.stopMonitoring()
        
        // Then: Should adapt to slow connections
        let timeoutCount = syncTimeouts.filter { $0.timedOut }.count
        let adaptiveResponses = adaptiveResults.filter { $0.adaptedToSlowNetwork }.count
        
        XCTAssertLessThan(timeoutCount, 5, "Should not timeout excessively on slow connections")
        XCTAssertGreaterThan(adaptiveResponses, 5, "Should adapt behavior for slow connections")
    }
    
    // MARK: - Thermal Throttling Tests
    
    func testThermalThrottling_CriticalTemperature() async throws {
        // Given: Device approaching critical thermal state
        thermalManager.startMonitoring()
        edgeCaseSimulator.simulateThermalCondition(.critical)
        
        var performanceMetrics: [ThermalPerformanceMetric] = []
        var throttlingActions: [ThrottlingAction] = []
        
        // When: Continue workout under thermal stress
        try await startARSession()
        repCountingEngine.startCounting(for: .squat)
        
        for frameIndex in 0..<600 { // 20 seconds under thermal stress
            let arFrame = createStandardARFrame(frameIndex: frameIndex)
            
            let processingStart = Date()
            await processFrameWithThermalMonitoring(arFrame)
            let processingTime = Date().timeIntervalSince(processingStart)
            
            let thermalState = getCurrentThermalState()
            performanceMetrics.append(ThermalPerformanceMetric(
                frameIndex: frameIndex,
                processingTime: processingTime,
                thermalState: thermalState,
                throttlingActive: isThrottlingActive()
            ))
            
            if frameIndex % 60 == 0 { // Check every 2 seconds
                let throttlingAction = checkThrottlingActions()
                throttlingActions.append(throttlingAction)
            }
            
            try await Task.sleep(nanoseconds: 50_000_000) // Reduced frame rate under thermal stress
        }
        
        await stopARSession()
        thermalManager.stopMonitoring()
        edgeCaseSimulator.resetThermalCondition()
        
        // Then: Should implement thermal throttling appropriately
        let throttlingActivated = performanceMetrics.filter { $0.throttlingActive }.count
        let performanceDegraded = performanceMetrics.filter { $0.processingTime > 0.05 }.count
        let safetyMeasures = throttlingActions.filter { $0.safetyMeasureActivated }.count
        
        XCTAssertGreaterThan(throttlingActivated, 0, "Should activate thermal throttling")
        XCTAssertGreaterThan(safetyMeasures, 0, "Should implement safety measures")
        // Performance degradation is expected under thermal stress
        XCTAssertLessThan(Double(performanceDegraded) / Double(performanceMetrics.count), 0.8, "Should limit performance degradation")
    }
    
    func testThermalThrottling_AdaptiveFrameRate() async throws {
        // Given: Gradually increasing thermal load
        thermalManager.startMonitoring()
        
        var frameRateAdaptations: [FrameRateAdaptation] = []
        
        // When: Simulate gradual thermal increase
        try await startARSession()
        
        let thermalStates: [ThermalState] = [.nominal, .fair, .serious, .critical]
        
        for (index, thermalState) in thermalStates.enumerated() {
            edgeCaseSimulator.simulateThermalCondition(thermalState)
            
            var frameRates: [Double] = []
            
            for frameIndex in 0..<150 { // 5 seconds per thermal state
                let frameStart = Date()
                
                let arFrame = createStandardARFrame(frameIndex: frameIndex)
                await processFrameWithThermalMonitoring(arFrame)
                
                let frameEnd = Date()
                let frameTime = frameEnd.timeIntervalSince(frameStart)
                let actualFrameRate = 1.0 / frameTime
                frameRates.append(actualFrameRate)
                
                // Adaptive frame rate based on thermal state
                let targetFrameRate = getAdaptiveFrameRate(for: thermalState)
                let targetInterval = 1.0 / targetFrameRate
                
                try await Task.sleep(nanoseconds: UInt64(targetInterval * 1_000_000_000))
            }
            
            let avgFrameRate = frameRates.reduce(0, +) / Double(frameRates.count)
            frameRateAdaptations.append(FrameRateAdaptation(
                thermalState: thermalState,
                targetFrameRate: getAdaptiveFrameRate(for: thermalState),
                actualFrameRate: avgFrameRate,
                adaptationSuccessful: abs(avgFrameRate - getAdaptiveFrameRate(for: thermalState)) < 5
            ))
        }
        
        await stopARSession()
        thermalManager.stopMonitoring()
        edgeCaseSimulator.resetThermalCondition()
        
        // Then: Should adapt frame rate based on thermal state
        let successfulAdaptations = frameRateAdaptations.filter { $0.adaptationSuccessful }.count
        
        XCTAssertEqual(successfulAdaptations, frameRateAdaptations.count, "Should successfully adapt frame rate to thermal conditions")
        
        // Verify frame rate reduction under thermal stress
        let nominalFrameRate = frameRateAdaptations.first { $0.thermalState == .nominal }?.actualFrameRate ?? 0
        let criticalFrameRate = frameRateAdaptations.first { $0.thermalState == .critical }?.actualFrameRate ?? 0
        
        XCTAssertGreaterThan(nominalFrameRate, criticalFrameRate, "Frame rate should be reduced under thermal stress")
    }
    
    // MARK: - Rapid Exercise Transitions Tests
    
    func testRapidExerciseTransitions_SquatToPushUp() async throws {
        // Given: Rapid transition between different exercises
        var transitionResults: [ExerciseTransitionResult] = []
        
        // When: Rapidly switch between squats and push-ups
        try await startARSession()
        
        for cycle in 0..<5 {
            // Start with squats
            repCountingEngine.startCounting(for: .squat)
            
            for frameIndex in 0..<60 { // 2 seconds of squats
                let arFrame = createSquatFrame(frameIndex: frameIndex)
                await processFrameWithExerciseTracking(arFrame, expectedExercise: .squat)
                try await Task.sleep(nanoseconds: 33_333_333)
            }
            
            // Rapid transition to push-ups
            let transitionStart = Date()
            repCountingEngine.startCounting(for: .pushUp)
            
            for frameIndex in 0..<60 { // 2 seconds of push-ups
                let arFrame = createPushUpFrame(frameIndex: frameIndex)
                await processFrameWithExerciseTracking(arFrame, expectedExercise: .pushUp)
                try await Task.sleep(nanoseconds: 33_333_333)
            }
            
            let transitionTime = Date().timeIntervalSince(transitionStart)
            
            transitionResults.append(ExerciseTransitionResult(
                fromExercise: .squat,
                toExercise: .pushUp,
                transitionTime: transitionTime,
                recognitionSuccessful: checkExerciseRecognition(.pushUp),
                repCountReset: repCountingEngine.getCurrentRepCount() == 0
            ))
        }
        
        await stopARSession()
        
        // Then: Should handle rapid exercise transitions
        let successfulTransitions = transitionResults.filter { $0.recognitionSuccessful }.count
        let avgTransitionTime = transitionResults.map { $0.transitionTime }.reduce(0, +) / Double(transitionResults.count)
        let repCountResets = transitionResults.filter { $0.repCountReset }.count
        
        XCTAssertGreaterThan(successfulTransitions, 3, "Should successfully recognize exercise transitions")
        XCTAssertLessThan(avgTransitionTime, 2.0, "Should transition quickly between exercises")
        XCTAssertEqual(repCountResets, transitionResults.count, "Should reset rep count on exercise change")
    }
    
    func testRapidExerciseTransitions_MultipleExerciseSequence() async throws {
        // Given: Rapid sequence through multiple exercises
        let exerciseSequence: [ExerciseType] = [.squat, .pushUp, .plank, .burpee, .mountainClimber]
        var sequenceResults: [ExerciseSequenceResult] = []
        
        // When: Rapidly cycle through exercise sequence
        try await startARSession()
        
        for exercise in exerciseSequence {
            let exerciseStart = Date()
            repCountingEngine.startCounting(for: exercise)
            
            for frameIndex in 0..<90 { // 3 seconds per exercise
                let arFrame = createExerciseFrame(exercise: exercise, frameIndex: frameIndex)
                await processFrameWithExerciseTracking(arFrame, expectedExercise: exercise)
                try await Task.sleep(nanoseconds: 33_333_333)
            }
            
            let exerciseDuration = Date().timeIntervalSince(exerciseStart)
            
            sequenceResults.append(ExerciseSequenceResult(
                exercise: exercise,
                duration: exerciseDuration,
                recognitionAccuracy: calculateRecognitionAccuracy(for: exercise),
                stabilizationTime: measureStabilizationTime(for: exercise)
            ))
        }
        
        await stopARSession()
        
        // Then: Should maintain accuracy across rapid transitions
        let avgRecognitionAccuracy = sequenceResults.map { $0.recognitionAccuracy }.reduce(0, +) / Double(sequenceResults.count)
        let maxStabilizationTime = sequenceResults.map { $0.stabilizationTime }.max() ?? 0
        
        XCTAssertGreaterThan(avgRecognitionAccuracy, 0.8, "Should maintain high recognition accuracy across exercises")
        XCTAssertLessThan(maxStabilizationTime, 1.0, "Should stabilize exercise recognition within 1 second")
    }
    
    func testRapidExerciseTransitions_ConfusionRecovery() async throws {
        // Given: Intentionally confusing exercise transitions
        var confusionRecoveryResults: [ConfusionRecoveryResult] = []
        
        // When: Create ambiguous movements between exercises
        try await startARSession()
        
        for testIndex in 0..<3 {
            // Start with clear squat
            repCountingEngine.startCounting(for: .squat)
            
            for frameIndex in 0..<30 {
                let arFrame = createSquatFrame(frameIndex: frameIndex)
                await processFrameWithExerciseTracking(arFrame, expectedExercise: .squat)
                try await Task.sleep(nanoseconds: 33_333_333)
            }
            
            // Create ambiguous transition frames
            let confusionStart = Date()
            
            for frameIndex in 0..<60 { // 2 seconds of ambiguous movement
                let arFrame = createAmbiguousTransitionFrame(frameIndex: frameIndex)
                await processFrameWithExerciseTracking(arFrame, expectedExercise: .unknown)
                try await Task.sleep(nanoseconds: 33_333_333)
            }
            
            // Transition to clear push-up
            repCountingEngine.startCounting(for: .pushUp)
            
            var recoveryTime: TimeInterval?
            
            for frameIndex in 0..<90 { // 3 seconds to recover
                let arFrame = createPushUpFrame(frameIndex: frameIndex)
                await processFrameWithExerciseTracking(arFrame, expectedExercise: .pushUp)
                
                if recoveryTime == nil && checkExerciseRecognition(.pushUp) {
                    recoveryTime = Date().timeIntervalSince(confusionStart)
                }
                
                try await Task.sleep(nanoseconds: 33_333_333)
            }
            
            confusionRecoveryResults.append(ConfusionRecoveryResult(
                testIndex: testIndex,
                recoverySuccessful: recoveryTime != nil,
                recoveryTime: recoveryTime ?? 5.0,
                finalExerciseRecognized: checkExerciseRecognition(.pushUp)
            ))
        }
        
        await stopARSession()
        
        // Then: Should recover from confusion states
        let successfulRecoveries = confusionRecoveryResults.filter { $0.recoverySuccessful }.count
        let avgRecoveryTime = confusionRecoveryResults.compactMap { $0.recoveryTime }.reduce(0, +) / Double(confusionRecoveryResults.count)
        
        XCTAssertGreaterThan(successfulRecoveries, 2, "Should successfully recover from confusion")
        XCTAssertLessThan(avgRecoveryTime, 3.0, "Should recover within 3 seconds")
    }
    
    // MARK: - Helper Methods
    
    private func setupEdgeCaseTestEnvironment() {
        arSessionManager.setTestMode(true)
        mlProcessingManager.setTestMode(true)
        edgeCaseSimulator.enableAllSimulations()
    }
    
    private func cleanupEdgeCaseTestEnvironment() {
        arSessionManager.setTestMode(false)
        mlProcessingManager.setTestMode(false)
        edgeCaseSimulator.disableAllSimulations()
    }
    
    private func startARSession() async throws {
        let configuration = ARBodyTrackingConfiguration()
        await arSessionManager.startSession(with: configuration)
        try await Task.sleep(nanoseconds: 1_000_000_000)
    }
    
    private func stopARSession() async {
        await arSessionManager.stopSession()
        try? await Task.sleep(nanoseconds: 500_000_000)
    }
    
    // Mock processing methods
    private func processFrameWithTracking(_ frame: MockARFrame) async -> TrackingResult {
        // Mock implementation
        return TrackingResult(
            trackingSuccessful: true,
            poseData: frame.bodyPoseData,
            fallbackModeActivated: false
        )
    }
    
    private func attemptPoseDetection(_ frame: MockARFrame) async -> PoseDetectionResult {
        // Mock implementation
        return PoseDetectionResult(
            poseDetected: frame.bodyPoseData != nil,
            partialDetection: false,
            recoveryAttempted: false
        )
    }
    
    private func processFrameWithLightingAdaptation(_ frame: MockARFrame) async -> LightingAdaptationResult {
        // Mock implementation
        return LightingAdaptationResult(
            adaptationSuccessful: true,
            trackingStable: true
        )
    }
    
    private func analyzeUnusualBodyPose(_ frame: MockARFrame, characteristics: BodyCharacteristics) async -> PoseAnalysisResult {
        // Mock implementation
        return PoseAnalysisResult(
            analysisSuccessful: true,
            calibrationAdjusted: true
        )
    }
    
    private func processFrameWithAdaptation(_ frame: MockARFrame, characteristics: BodyCharacteristics) async -> AdaptationResult {
        // Mock implementation
        return AdaptationResult(
            adaptationSuccessful: true,
            scaleAdjusted: true
        )
    }
    
    private func analyzeFormWithAdaptation(_ frame: MockARFrame, characteristics: BodyCharacteristics) async -> FormAnalysisResult {
        // Mock implementation
        return FormAnalysisResult(
            formAnalysisAccurate: true,
            compensationAdjustmentMade: true
        )
    }
    
    private func processFrameWithNetworkSync(_ frame: MockARFrame) async {
        // Mock implementation
    }
    
    private func checkWorkoutContinuity() -> WorkoutContinuityCheck {
        // Mock implementation
        return WorkoutContinuityCheck(
            workoutContinuous: true,
            dataIntegrityMaintained: true
        )
    }
    
    private func attemptDataSync() async throws -> SyncResult {
        // Mock implementation
        return SyncResult(syncSuccessful: true)
    }
    
    private func attemptNetworkOperation() -> ConnectionAttempt {
        // Mock implementation
        return ConnectionAttempt(successful: true)
    }
    
    private func checkDataRetention() -> DataRetentionResult {
        // Mock implementation
        return DataRetentionResult(dataRetained: true)
    }
    
    private func attemptSlowNetworkSync() async throws -> SyncResult {
        // Mock implementation with delay
        try await Task.sleep(nanoseconds: 5_000_000_000) // 5 seconds
        return SyncResult(successful: true)
    }
    
    private func checkAdaptiveBehavior() -> AdaptiveBehaviorResult {
        // Mock implementation
        return AdaptiveBehaviorResult(adaptedToSlowNetwork: true)
    }
    
    private func processFrameWithThermalMonitoring(_ frame: MockARFrame) async {
        // Mock implementation with thermal considerations
    }
    
    private func getCurrentThermalState() -> ThermalState {
        return .nominal // Mock implementation
    }
    
    private func isThrottlingActive() -> Bool {
        return false // Mock implementation
    }
    
    private func checkThrottlingActions() -> ThrottlingAction {
        return ThrottlingAction(safetyMeasureActivated: false)
    }
    
    private func getAdaptiveFrameRate(for thermalState: ThermalState) -> Double {
        switch thermalState {
        case .nominal: return 60.0
        case .fair: return 45.0
        case .serious: return 30.0
        case .critical: return 20.0
        }
    }
    
    private func processFrameWithExerciseTracking(_ frame: MockARFrame, expectedExercise: ExerciseType) async {
        // Mock implementation
    }
    
    private func checkExerciseRecognition(_ exercise: ExerciseType) -> Bool {
        return true // Mock implementation
    }
    
    private func calculateRecognitionAccuracy(for exercise: ExerciseType) -> Double {
        return 0.9 // Mock implementation
    }
    
    private func measureStabilizationTime(for exercise: ExerciseType) -> TimeInterval {
        return 0.5 // Mock implementation
    }
    
    // Frame creation methods
    private func createStandardARFrame(frameIndex: Int) -> MockARFrame {
        return MockARFrame(timestamp: Date(), bodyPoseData: nil, frameIndex: frameIndex)
    }
    
    private func createSquatFrame(frameIndex: Int) -> MockARFrame {
        return MockARFrame(timestamp: Date(), bodyPoseData: nil, frameIndex: frameIndex)
    }
    
    private func createPushUpFrame(frameIndex: Int) -> MockARFrame {
        return MockARFrame(timestamp: Date(), bodyPoseData: nil, frameIndex: frameIndex)
    }
    
    private func createExerciseFrame(exercise: ExerciseType, frameIndex: Int) -> MockARFrame {
        return MockARFrame(timestamp: Date(), bodyPoseData: nil, frameIndex: frameIndex)
    }
    
    private func createAmbiguousTransitionFrame(frameIndex: Int) -> MockARFrame {
        return MockARFrame(timestamp: Date(), bodyPoseData: nil, frameIndex: frameIndex)
    }
}

// MARK: - Supporting Types and Enums

enum ThermalState {
    case nominal, fair, serious, critical
}

enum BodyType {
    case tall, short, atypical
}

struct BodyCharacteristics {
    let height: Double
    let armSpan: Double
    let legLength: Double
    let torsoLength: Double
    let bodyType: BodyType
}

struct TrackingResult {
    let trackingSuccessful: Bool
    let poseData: BodyPoseData?
    let fallbackModeActivated: Bool
}

struct PoseDetectionResult {
    let poseDetected: Bool
    let partialDetection: Bool
    let recoveryAttempted: Bool
}

struct LightingAdaptationResult {
    let adaptationSuccessful: Bool
    let trackingStable: Bool
}

struct PoseAnalysisResult {
    let analysisSuccessful: Bool
    let calibrationAdjusted: Bool
}

struct AdaptationResult {
    let adaptationSuccessful: Bool
    let scaleAdjusted: Bool
}

struct FormAnalysisResult {
    let formAnalysisAccurate: Bool
    let compensationAdjustmentMade: Bool
}

struct WorkoutContinuityCheck {
    let workoutContinuous: Bool
    let dataIntegrityMaintained: Bool
}

struct SyncResult {
    let syncSuccessful: Bool
    let successful: Bool
    
    init(syncSuccessful: Bool) {
        self.syncSuccessful = syncSuccessful
        self.successful = syncSuccessful
    }
    
    init(successful: Bool) {
        self.successful = successful
        self.syncSuccessful = successful
    }
}

struct ConnectionAttempt {
    let successful: Bool
}

struct DataRetentionResult {
    let dataRetained: Bool
}

struct SyncTimeout {
    let duration: TimeInterval
    let successful: Bool
    let timedOut: Bool
}

struct AdaptiveBehaviorResult {
    let adaptedToSlowNetwork: Bool
}

struct ThermalPerformanceMetric {
    let frameIndex: Int
    let processingTime: TimeInterval
    let thermalState: ThermalState
    let throttlingActive: Bool
}

struct ThrottlingAction {
    let safetyMeasureActivated: Bool
}

struct FrameRateAdaptation {
    let thermalState: ThermalState
    let targetFrameRate: Double
    let actualFrameRate: Double
    let adaptationSuccessful: Bool
}

struct ExerciseTransitionResult {
    let fromExercise: ExerciseType
    let toExercise: ExerciseType
    let transitionTime: TimeInterval
    let recognitionSuccessful: Bool
    let repCountReset: Bool
}

struct ExerciseSequenceResult {
    let exercise: ExerciseType
    let duration: TimeInterval
    let recognitionAccuracy: Double
    let stabilizationTime: TimeInterval
}

struct ConfusionRecoveryResult {
    let testIndex: Int
    let recoverySuccessful: Bool
    let recoveryTime: TimeInterval
    let finalExerciseRecognized: Bool
}

// MARK: - Mock Classes

class EdgeCaseSimulator {
    func enableAllSimulations() {}
    func disableAllSimulations() {}
    func setLightingCondition(_ condition: LightingCondition) {}
    func setBodyCharacteristics(_ characteristics: BodyCharacteristics) {}
    func simulateNetworkOutage() {}
    func restoreNetwork() {}
    func setNetworkCondition(_ condition: NetworkCondition) {}
    func simulateThermalCondition(_ state: ThermalState) {}
    func resetThermalCondition() {}
    
    func createDimLightingFrame(frameIndex: Int) -> MockARFrame {
        return MockARFrame(timestamp: Date(), bodyPoseData: nil, frameIndex: frameIndex)
    }
    
    func createBacklitFrame(frameIndex: Int) -> MockARFrame {
        return MockARFrame(timestamp: Date(), bodyPoseData: nil, frameIndex: frameIndex)
    }
    
    func createDynamicLightingFrame(frameIndex: Int, cycle: Int) -> MockARFrame {
        return MockARFrame(timestamp: Date(), bodyPoseData: nil, frameIndex: frameIndex)
    }
    
    func createTallPersonSquatSequence(repIndex: Int) -> [MockARFrame] {
        return []
    }
    
    func createShortPersonFrame(frameIndex: Int, exercise: ExerciseType) -> MockARFrame {
        return MockARFrame(timestamp: Date(), bodyPoseData: nil, frameIndex: frameIndex)
    }
    
    func createAtypicalProportionsFrame(frameIndex: Int) -> MockARFrame {
        return MockARFrame(timestamp: Date(), bodyPoseData: nil, frameIndex: frameIndex)
    }
}

enum LightingCondition {
    case veryDim, backlit, bright, normal, dim
}

enum NetworkCondition {
    case connected, disconnected, verySlow
}

class NetworkMonitor {
    static let shared = NetworkMonitor()
    func startMonitoring() {}
    func stopMonitoring() {}
}

class ThermalManager {
    static let shared = ThermalManager()
    func startMonitoring() {}
    func stopMonitoring() {}
}