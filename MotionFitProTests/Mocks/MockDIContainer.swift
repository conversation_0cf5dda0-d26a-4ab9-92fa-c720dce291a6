import Foundation
@testable import MotionFitPro

// MARK: - Mock Dependency Injection Container

final class MockDIContainer {
    
    // MARK: - Mock Services
    
    let mockLogger = MockLoggerService()
    let mockHapticService = MockHapticService()
    let mockAudioService = MockAudioService()
    let mockMLProcessingService = MockMLProcessingService()
    let mockARTrackingService = MockARTrackingService()
    let mockFeedbackService = MockFeedbackService()
    let mockCoachingService = MockCoachingService()
    let mockAnalyticsService = MockAnalyticsService()
    let mockSecurityService = MockSecurityService()
    let mockPrivacyService = MockPrivacyService()
    
    // MARK: - Repository Mocks
    
    let mockUserProfileRepository = MockUserProfileRepository()
    let mockWorkoutRepository = MockWorkoutRepository()
    let mockExerciseRepository = MockExerciseRepository()
    
    // MARK: - Registration
    
    func registerMockServices() {
        // Register all mock services
        DIContainer.shared.registerSingleton(LoggerServiceProtocol.self) { [weak self] in
            self?.mockLogger ?? MockLoggerService()
        }
        
        DIContainer.shared.registerSingleton(HapticServiceProtocol.self) { [weak self] in
            self?.mockHapticService ?? MockHapticService()
        }
        
        DIContainer.shared.registerSingleton(AudioServiceProtocol.self) { [weak self] in
            self?.mockAudioService ?? MockAudioService()
        }
        
        DIContainer.shared.registerSingleton(MLProcessingServiceProtocol.self) { [weak self] in
            self?.mockMLProcessingService ?? MockMLProcessingService()
        }
        
        DIContainer.shared.registerSingleton(ARTrackingServiceProtocol.self) { [weak self] in
            self?.mockARTrackingService ?? MockARTrackingService()
        }
        
        DIContainer.shared.registerSingleton(FeedbackServiceProtocol.self) { [weak self] in
            self?.mockFeedbackService ?? MockFeedbackService()
        }
        
        DIContainer.shared.registerSingleton(CoachingServiceProtocol.self) { [weak self] in
            self?.mockCoachingService ?? MockCoachingService()
        }
        
        DIContainer.shared.registerSingleton(AnalyticsServiceProtocol.self) { [weak self] in
            self?.mockAnalyticsService ?? MockAnalyticsService()
        }
        
        DIContainer.shared.registerSingleton(SecurityServiceProtocol.self) { [weak self] in
            self?.mockSecurityService ?? MockSecurityService()
        }
        
        DIContainer.shared.registerSingleton(PrivacyServiceProtocol.self) { [weak self] in
            self?.mockPrivacyService ?? MockPrivacyService()
        }
        
        // Register repository mocks
        DIContainer.shared.registerSingleton(UserProfileRepositoryProtocol.self) { [weak self] in
            self?.mockUserProfileRepository ?? MockUserProfileRepository()
        }
        
        DIContainer.shared.registerSingleton(WorkoutRepositoryProtocol.self) { [weak self] in
            self?.mockWorkoutRepository ?? MockWorkoutRepository()
        }
        
        DIContainer.shared.registerSingleton(ExerciseRepositoryProtocol.self) { [weak self] in
            self?.mockExerciseRepository ?? MockExerciseRepository()
        }
    }
    
    // MARK: - Reset Methods
    
    func resetAllMocks() {
        mockLogger.reset()
        mockHapticService.reset()
        mockAudioService.reset()
        mockMLProcessingService.reset()
        mockARTrackingService.reset()
        mockFeedbackService.reset()
        mockCoachingService.reset()
        mockAnalyticsService.reset()
        mockSecurityService.reset()
        mockPrivacyService.reset()
        
        mockUserProfileRepository.reset()
        mockWorkoutRepository.reset()
        mockExerciseRepository.reset()
    }
    
    // MARK: - Configuration Methods
    
    func configureMockMLService(shouldSucceed: Bool = true, processingTime: TimeInterval = 0.1) {
        mockMLProcessingService.shouldSucceed = shouldSucceed
        mockMLProcessingService.processingTime = processingTime
    }
    
    func configureMockARService(shouldTrack: Bool = true, trackingQuality: TrackingQuality = .good) {
        mockARTrackingService.shouldTrack = shouldTrack
        mockARTrackingService.trackingQuality = trackingQuality
    }
    
    func configureMockRepositories(shouldSucceed: Bool = true) {
        mockUserProfileRepository.shouldSucceed = shouldSucceed
        mockWorkoutRepository.shouldSucceed = shouldSucceed
        mockExerciseRepository.shouldSucceed = shouldSucceed
    }
}

// MARK: - Mock Protocol

protocol MockService {
    func reset()
}

// MARK: - Mock Logger Service

final class MockLoggerService: LoggerServiceProtocol, MockService {
    private(set) var loggedMessages: [String] = []
    private(set) var loggedLevels: [LogLevel] = []
    private(set) var loggedCategories: [LogCategory] = []
    
    func log(_ message: String, level: LogLevel, category: LogCategory, file: String, function: String, line: Int) {
        loggedMessages.append(message)
        loggedLevels.append(level)
        loggedCategories.append(category)
    }
    
    func reset() {
        loggedMessages.removeAll()
        loggedLevels.removeAll()
        loggedCategories.removeAll()
    }
    
    func hasLoggedMessage(containing text: String) -> Bool {
        return loggedMessages.contains { $0.contains(text) }
    }
    
    func hasLoggedLevel(_ level: LogLevel) -> Bool {
        return loggedLevels.contains(level)
    }
    
    func hasLoggedCategory(_ category: LogCategory) -> Bool {
        return loggedCategories.contains(category)
    }
}

// MARK: - Mock Haptic Service

final class MockHapticService: HapticServiceProtocol, MockService {
    private(set) var triggeredFeedbacks: [HapticFeedbackType] = []
    private(set) var customPatterns: [HapticPattern] = []
    
    func triggerSuccess() {
        triggeredFeedbacks.append(.success)
    }
    
    func triggerError() {
        triggeredFeedbacks.append(.error)
    }
    
    func triggerWarning() {
        triggeredFeedbacks.append(.warning)
    }
    
    func triggerSelection() {
        triggeredFeedbacks.append(.selection)
    }
    
    func triggerImpact(_ style: UIImpactFeedbackGenerator.FeedbackStyle) {
        triggeredFeedbacks.append(.impact(style))
    }
    
    func triggerCustomPattern(_ pattern: HapticPattern) {
        customPatterns.append(pattern)
    }
    
    func reset() {
        triggeredFeedbacks.removeAll()
        customPatterns.removeAll()
    }
    
    func hasTriggered(_ type: HapticFeedbackType) -> Bool {
        return triggeredFeedbacks.contains { $0.matches(type) }
    }
}

// MARK: - Mock Audio Service

final class MockAudioService: AudioServiceProtocol, MockService {
    private(set) var spokenTexts: [String] = []
    private(set) var playedSounds: [String] = []
    private(set) var speechPriorities: [SpeechPriority] = []
    
    func speak(_ text: String, priority: SpeechPriority) {
        spokenTexts.append(text)
        speechPriorities.append(priority)
    }
    
    func stopSpeaking() {
        // Mock implementation
    }
    
    func playSound(_ soundName: String) {
        playedSounds.append(soundName)
    }
    
    func setVolume(_ volume: Float) {
        // Mock implementation
    }
    
    func reset() {
        spokenTexts.removeAll()
        playedSounds.removeAll()
        speechPriorities.removeAll()
    }
    
    func hasSpoken(_ text: String) -> Bool {
        return spokenTexts.contains(text)
    }
    
    func hasPlayedSound(_ soundName: String) -> Bool {
        return playedSounds.contains(soundName)
    }
}

// MARK: - Mock ML Processing Service

final class MockMLProcessingService: MLProcessingServiceProtocol, MockService {
    var shouldSucceed = true
    var processingTime: TimeInterval = 0.1
    private(set) var processedPoses: [BodyPoseData] = []
    private(set) var classifiedExercises: [BodyPoseData] = []
    
    func processBodyPose(_ poseData: BodyPoseData) async throws -> ExerciseAnalysis {
        processedPoses.append(poseData)
        
        if !shouldSucceed {
            throw TestError.mockNotConfigured
        }
        
        // Simulate processing time
        try await Task.sleep(nanoseconds: UInt64(processingTime * 1_000_000_000))
        
        return ExerciseAnalysis(
            exercise: .squat,
            formScore: 0.8,
            repPhase: .middle,
            poseData: poseData,
            confidence: 0.8
        )
    }
    
    func classifyExercise(_ poseData: BodyPoseData) async throws -> ExerciseType {
        classifiedExercises.append(poseData)
        
        if !shouldSucceed {
            throw TestError.mockNotConfigured
        }
        
        return .squat
    }
    
    func analyzeForm(_ poseData: BodyPoseData, for exercise: ExerciseType) async throws -> FormAnalysis {
        if !shouldSucceed {
            throw TestError.mockNotConfigured
        }
        
        return FormAnalysis(
            overallScore: 0.8,
            exerciseType: exercise
        )
    }
    
    func detectMovementPhase(_ poseData: BodyPoseData, for exercise: ExerciseType) async throws -> MovementPhase {
        if !shouldSucceed {
            throw TestError.mockNotConfigured
        }
        
        return .middle
    }
    
    func reset() {
        shouldSucceed = true
        processingTime = 0.1
        processedPoses.removeAll()
        classifiedExercises.removeAll()
    }
}

// MARK: - Mock AR Tracking Service

final class MockARTrackingService: ARTrackingServiceProtocol, MockService {
    var shouldTrack = true
    var trackingQuality: TrackingQuality = .good
    private(set) var isTracking = false
    private(set) var startTrackingCalled = false
    private(set) var stopTrackingCalled = false
    
    private let bodyPoseSubject = PassthroughSubject<BodyPoseData, Never>()
    
    var bodyPosePublisher: AnyPublisher<BodyPoseData, Never> {
        bodyPoseSubject.eraseToAnyPublisher()
    }
    
    func startTracking() async throws {
        startTrackingCalled = true
        
        if !shouldTrack {
            throw TestError.mockNotConfigured
        }
        
        isTracking = true
    }
    
    func stopTracking() {
        stopTrackingCalled = true
        isTracking = false
    }
    
    func pauseTracking() {
        isTracking = false
    }
    
    func resumeTracking() {
        isTracking = true
    }
    
    func simulatePoseData(_ poseData: BodyPoseData) {
        bodyPoseSubject.send(poseData)
    }
    
    func reset() {
        shouldTrack = true
        trackingQuality = .good
        isTracking = false
        startTrackingCalled = false
        stopTrackingCalled = false
    }
}

// MARK: - Helper Extensions

extension HapticFeedbackType {
    func matches(_ other: HapticFeedbackType) -> Bool {
        switch (self, other) {
        case (.success, .success), (.error, .error), (.warning, .warning), (.selection, .selection):
            return true
        case (.impact(let style1), .impact(let style2)):
            return style1 == style2
        default:
            return false
        }
    }
}
