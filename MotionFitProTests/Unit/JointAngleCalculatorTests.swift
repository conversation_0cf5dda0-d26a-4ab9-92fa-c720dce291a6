import XCTest
import simd
@testable import MotionFitPro

class JointAngleCalculatorTests: XCTestCase {
    
    var calculator: JointAngleCalculator!
    
    override func setUpWithError() throws {
        super.setUp()
        calculator = JointAngleCalculator()
    }
    
    override func tearDownWithError() throws {
        calculator = nil
        super.tearDown()
    }
    
    // MARK: - Basic Angle Calculations
    
    func testCalculateAngleBetweenVectors_RightAngle() {
        // Given: Two perpendicular vectors
        let vector1 = simd_float3(1, 0, 0)
        let vector2 = simd_float3(0, 1, 0)
        
        // When: Calculate angle
        let angle = calculator.calculateAngle(between: vector1, and: vector2)
        
        // Then: Angle should be 90 degrees (π/2 radians)
        XCTAssertEqual(angle, Float.pi / 2, accuracy: 0.001, "Right angle should be π/2 radians")
    }
    
    func testCalculateAngleBetweenVectors_StraightLine() {
        // Given: Two parallel vectors
        let vector1 = simd_float3(1, 0, 0)
        let vector2 = simd_float3(2, 0, 0)
        
        // When: Calculate angle
        let angle = calculator.calculateAngle(between: vector1, and: vector2)
        
        // Then: Angle should be 0 degrees
        XCTAssertEqual(angle, 0, accuracy: 0.001, "Parallel vectors should have 0 angle")
    }
    
    func testCalculateAngleBetweenVectors_OppositeDirection() {
        // Given: Two opposite vectors
        let vector1 = simd_float3(1, 0, 0)
        let vector2 = simd_float3(-1, 0, 0)
        
        // When: Calculate angle
        let angle = calculator.calculateAngle(between: vector1, and: vector2)
        
        // Then: Angle should be 180 degrees (π radians)
        XCTAssertEqual(angle, Float.pi, accuracy: 0.001, "Opposite vectors should have π angle")
    }
    
    // MARK: - Knee Angle Calculations
    
    func testCalculateKneeAngle_StraightLeg() {
        // Given: Straight leg position (hip, knee, ankle aligned)
        let hipPosition = simd_float3(0, 1, 0)
        let kneePosition = simd_float3(0, 0.5, 0)
        let anklePosition = simd_float3(0, 0, 0)
        
        // When: Calculate knee angle
        let kneeAngle = calculator.calculateKneeAngle(
            hip: hipPosition,
            knee: kneePosition,
            ankle: anklePosition
        )
        
        // Then: Knee angle should be 180 degrees (straight)
        XCTAssertEqual(kneeAngle, Float.pi, accuracy: 0.01, "Straight leg should have π angle")
    }
    
    func testCalculateKneeAngle_NinetyDegreeBend() {
        // Given: 90-degree knee bend
        let hipPosition = simd_float3(0, 1, 0)
        let kneePosition = simd_float3(0, 0.5, 0)
        let anklePosition = simd_float3(0.5, 0.5, 0) // Ankle to the side for 90-degree angle
        
        // When: Calculate knee angle
        let kneeAngle = calculator.calculateKneeAngle(
            hip: hipPosition,
            knee: kneePosition,
            ankle: anklePosition
        )
        
        // Then: Knee angle should be 90 degrees
        XCTAssertEqual(kneeAngle, Float.pi / 2, accuracy: 0.1, "90-degree bend should be π/2 angle")
    }
    
    func testCalculateKneeAngle_DeepSquat() {
        // Given: Deep squat position (acute knee angle)
        let hipPosition = simd_float3(0, 0.8, 0)
        let kneePosition = simd_float3(0, 0.3, 0)
        let anklePosition = simd_float3(-0.3, 0, 0) // Ankle back for deep squat
        
        // When: Calculate knee angle
        let kneeAngle = calculator.calculateKneeAngle(
            hip: hipPosition,
            knee: kneePosition,
            ankle: anklePosition
        )
        
        // Then: Knee angle should be acute (less than π/2)
        XCTAssertLessThan(kneeAngle, Float.pi / 2, "Deep squat should have acute knee angle")
        XCTAssertGreaterThan(kneeAngle, 0, "Knee angle should be positive")
    }
    
    // MARK: - Elbow Angle Calculations
    
    func testCalculateElbowAngle_StraightArm() {
        // Given: Straight arm position
        let shoulderPosition = simd_float3(0, 1, 0)
        let elbowPosition = simd_float3(0.3, 1, 0)
        let wristPosition = simd_float3(0.6, 1, 0)
        
        // When: Calculate elbow angle
        let elbowAngle = calculator.calculateElbowAngle(
            shoulder: shoulderPosition,
            elbow: elbowPosition,
            wrist: wristPosition
        )
        
        // Then: Elbow angle should be 180 degrees
        XCTAssertEqual(elbowAngle, Float.pi, accuracy: 0.01, "Straight arm should have π angle")
    }
    
    func testCalculateElbowAngle_RightAngleBend() {
        // Given: 90-degree elbow bend
        let shoulderPosition = simd_float3(0, 1, 0)
        let elbowPosition = simd_float3(0.3, 1, 0)
        let wristPosition = simd_float3(0.3, 0.7, 0) // Wrist down for 90-degree angle
        
        // When: Calculate elbow angle
        let elbowAngle = calculator.calculateElbowAngle(
            shoulder: shoulderPosition,
            elbow: elbowPosition,
            wrist: wristPosition
        )
        
        // Then: Elbow angle should be 90 degrees
        XCTAssertEqual(elbowAngle, Float.pi / 2, accuracy: 0.1, "90-degree elbow bend should be π/2 angle")
    }
    
    // MARK: - Hip Angle Calculations
    
    func testCalculateHipAngle_Standing() {
        // Given: Standing position
        let shoulderPosition = simd_float3(0, 1.7, 0)
        let hipPosition = simd_float3(0, 1, 0)
        let kneePosition = simd_float3(0, 0.5, 0)
        
        // When: Calculate hip angle
        let hipAngle = calculator.calculateHipAngle(
            shoulder: shoulderPosition,
            hip: hipPosition,
            knee: kneePosition
        )
        
        // Then: Hip angle should be 180 degrees (straight)
        XCTAssertEqual(hipAngle, Float.pi, accuracy: 0.01, "Standing should have straight hip angle")
    }
    
    func testCalculateHipAngle_SquatPosition() {
        // Given: Squat position
        let shoulderPosition = simd_float3(0, 1.5, 0)
        let hipPosition = simd_float3(0, 0.8, 0)
        let kneePosition = simd_float3(0.2, 0.3, 0) // Knees forward in squat
        
        // When: Calculate hip angle
        let hipAngle = calculator.calculateHipAngle(
            shoulder: shoulderPosition,
            hip: hipPosition,
            knee: kneePosition
        )
        
        // Then: Hip angle should be acute (flexed)
        XCTAssertLessThan(hipAngle, Float.pi, "Squat should have flexed hip angle")
        XCTAssertGreaterThan(hipAngle, Float.pi / 3, "Hip flexion shouldn't be too extreme")
    }
    
    // MARK: - Complex Multi-Joint Analysis
    
    func testCalculateSquatDepth_FullSquat() {
        // Given: Full squat position
        let poseData = createSquatPoseData(
            hipHeight: 0.6,
            kneeAngle: Float.pi / 3, // 60 degrees
            anklePosition: simd_float3(0.1, 0, 0)
        )
        
        // When: Calculate squat depth
        let depth = calculator.calculateSquatDepth(poseData: poseData)
        
        // Then: Should recognize as full squat
        XCTAssertGreaterThan(depth, 0.8, "Full squat should have high depth score")
    }
    
    func testCalculateSquatDepth_PartialSquat() {
        // Given: Partial squat position
        let poseData = createSquatPoseData(
            hipHeight: 0.9,
            kneeAngle: Float.pi * 2/3, // 120 degrees
            anklePosition: simd_float3(0.05, 0, 0)
        )
        
        // When: Calculate squat depth
        let depth = calculator.calculateSquatDepth(poseData: poseData)
        
        // Then: Should recognize as partial squat
        XCTAssertLessThan(depth, 0.6, "Partial squat should have lower depth score")
        XCTAssertGreaterThan(depth, 0.2, "Should still register some depth")
    }
    
    func testCalculatePushUpForm_PerfectForm() {
        // Given: Perfect push-up position
        let poseData = createPushUpPoseData(
            bodyAlignment: 0.98, // Nearly perfect alignment
            elbowAngle: Float.pi / 2, // 90 degrees at bottom
            wristAlignment: 0.95
        )
        
        // When: Calculate push-up form
        let formScore = calculator.calculatePushUpForm(poseData: poseData)
        
        // Then: Should score highly
        XCTAssertGreaterThan(formScore, 0.9, "Perfect form should score above 90%")
    }
    
    func testCalculatePushUpForm_PoorForm() {
        // Given: Poor push-up form
        let poseData = createPushUpPoseData(
            bodyAlignment: 0.6, // Sagging hips
            elbowAngle: Float.pi * 3/4, // Not low enough
            wristAlignment: 0.7 // Poor wrist position
        )
        
        // When: Calculate push-up form
        let formScore = calculator.calculatePushUpForm(poseData: poseData)
        
        // Then: Should score poorly
        XCTAssertLessThan(formScore, 0.5, "Poor form should score below 50%")
    }
    
    // MARK: - Edge Cases
    
    func testCalculateAngle_ZeroLengthVector() {
        // Given: One vector with zero length
        let vector1 = simd_float3(0, 0, 0)
        let vector2 = simd_float3(1, 0, 0)
        
        // When: Calculate angle
        let angle = calculator.calculateAngle(between: vector1, and: vector2)
        
        // Then: Should handle gracefully (return 0 or NaN check)
        XCTAssertTrue(angle.isNaN || angle == 0, "Zero vector should be handled gracefully")
    }
    
    func testCalculateAngle_VerySmallVectors() {
        // Given: Very small vectors (potential floating point issues)
        let vector1 = simd_float3(0.0001, 0, 0)
        let vector2 = simd_float3(0, 0.0001, 0)
        
        // When: Calculate angle
        let angle = calculator.calculateAngle(between: vector1, and: vector2)
        
        // Then: Should still calculate correctly
        XCTAssertEqual(angle, Float.pi / 2, accuracy: 0.01, "Small vectors should still calculate correctly")
    }
    
    func testCalculateAngle_LargeVectors() {
        // Given: Very large vectors
        let vector1 = simd_float3(1000, 0, 0)
        let vector2 = simd_float3(0, 1000, 0)
        
        // When: Calculate angle
        let angle = calculator.calculateAngle(between: vector1, and: vector2)
        
        // Then: Should normalize correctly
        XCTAssertEqual(angle, Float.pi / 2, accuracy: 0.001, "Large vectors should normalize correctly")
    }
    
    // MARK: - Performance Tests
    
    func testAngleCalculationPerformance() {
        // Given: Random vectors for performance testing
        let vector1 = simd_float3(Float.random(in: -1...1), Float.random(in: -1...1), Float.random(in: -1...1))
        let vector2 = simd_float3(Float.random(in: -1...1), Float.random(in: -1...1), Float.random(in: -1...1))
        
        // When: Measure performance of 10,000 calculations
        measure {
            for _ in 0..<10000 {
                _ = calculator.calculateAngle(between: vector1, and: vector2)
            }
        }
        
        // Then: Should complete within reasonable time (measured automatically by XCTest)
    }
    
    // MARK: - Helper Methods
    
    private func createSquatPoseData(hipHeight: Float, kneeAngle: Float, anklePosition: simd_float3) -> BodyPoseData {
        var joints: [String: Joint] = [:]
        
        // Create squat pose joints
        joints["root"] = Joint(position: simd_float3(0, hipHeight, 0), confidence: 0.95)
        joints["left_leg"] = Joint(position: simd_float3(-0.1, hipHeight * 0.6, 0), confidence: 0.95)
        joints["right_leg"] = Joint(position: simd_float3(0.1, hipHeight * 0.6, 0), confidence: 0.95)
        joints["left_foot"] = Joint(position: anklePosition, confidence: 0.9)
        joints["right_foot"] = Joint(position: simd_float3(-anklePosition.x, anklePosition.y, anklePosition.z), confidence: 0.9)
        
        return BodyPoseData(joints: joints, confidence: 0.9, timestamp: Date())
    }
    
    private func createPushUpPoseData(bodyAlignment: Float, elbowAngle: Float, wristAlignment: Float) -> BodyPoseData {
        var joints: [String: Joint] = [:]
        
        // Create push-up pose joints with varying alignment quality
        let baseY: Float = 0.2 + (1.0 - bodyAlignment) * 0.1 // Simulate sagging
        
        joints["head"] = Joint(position: simd_float3(0, baseY + 0.1, 0), confidence: 0.95)
        joints["spine_7"] = Joint(position: simd_float3(0, baseY, 0), confidence: 0.95)
        joints["root"] = Joint(position: simd_float3(0, baseY - 0.1, 0), confidence: 0.95)
        joints["left_arm"] = Joint(position: simd_float3(-0.3, baseY, 0), confidence: 0.9)
        joints["right_arm"] = Joint(position: simd_float3(0.3, baseY, 0), confidence: 0.9)
        
        // Wrist position based on alignment quality
        let wristY = baseY - 0.2 * wristAlignment
        joints["left_hand"] = Joint(position: simd_float3(-0.35, wristY, 0), confidence: 0.85)
        joints["right_hand"] = Joint(position: simd_float3(0.35, wristY, 0), confidence: 0.85)
        
        return BodyPoseData(joints: joints, confidence: 0.9, timestamp: Date())
    }
}

// MARK: - Mock Joint Angle Calculator

class JointAngleCalculator {
    
    func calculateAngle(between vector1: simd_float3, and vector2: simd_float3) -> Float {
        // Handle zero vectors
        let length1 = length(vector1)
        let length2 = length(vector2)
        
        guard length1 > 0 && length2 > 0 else {
            return Float.nan
        }
        
        // Normalize vectors
        let normalized1 = vector1 / length1
        let normalized2 = vector2 / length2
        
        // Calculate dot product
        let dotProduct = dot(normalized1, normalized2)
        
        // Clamp to handle floating point errors
        let clampedDot = max(-1.0, min(1.0, dotProduct))
        
        // Return angle in radians
        return acos(clampedDot)
    }
    
    func calculateKneeAngle(hip: simd_float3, knee: simd_float3, ankle: simd_float3) -> Float {
        let thighVector = hip - knee
        let shinVector = ankle - knee
        return calculateAngle(between: thighVector, and: shinVector)
    }
    
    func calculateElbowAngle(shoulder: simd_float3, elbow: simd_float3, wrist: simd_float3) -> Float {
        let upperArmVector = shoulder - elbow
        let forearmVector = wrist - elbow
        return calculateAngle(between: upperArmVector, and: forearmVector)
    }
    
    func calculateHipAngle(shoulder: simd_float3, hip: simd_float3, knee: simd_float3) -> Float {
        let torsoVector = shoulder - hip
        let thighVector = knee - hip
        return calculateAngle(between: torsoVector, and: thighVector)
    }
    
    func calculateSquatDepth(poseData: BodyPoseData) -> Float {
        guard let hip = poseData.joints["root"],
              let leftKnee = poseData.joints["left_leg"],
              let rightKnee = poseData.joints["right_leg"] else {
            return 0.0
        }
        
        let hipHeight = hip.position.y
        let averageKneeHeight = (leftKnee.position.y + rightKnee.position.y) / 2
        let hipToKneeDistance = hipHeight - averageKneeHeight
        
        // Normalize depth score based on expected range
        let maxDepth: Float = 0.8 // Maximum expected hip-to-knee distance
        return min(1.0, hipToKneeDistance / maxDepth)
    }
    
    func calculatePushUpForm(poseData: BodyPoseData) -> Float {
        var formScore: Float = 0.0
        var componentCount = 0
        
        // Check body alignment
        if let head = poseData.joints["head"],
           let spine = poseData.joints["spine_7"],
           let hip = poseData.joints["root"] {
            let alignment = calculateBodyAlignment(head: head.position, spine: spine.position, hip: hip.position)
            formScore += alignment
            componentCount += 1
        }
        
        // Check arm position
        if let leftShoulder = poseData.joints["left_arm"],
           let leftElbow = poseData.joints["left_arm"], // Simplified - would use separate elbow joint
           let leftWrist = poseData.joints["left_hand"] {
            let armForm = calculateArmForm(shoulder: leftShoulder.position, elbow: leftElbow.position, wrist: leftWrist.position)
            formScore += armForm
            componentCount += 1
        }
        
        return componentCount > 0 ? formScore / Float(componentCount) : 0.0
    }
    
    private func calculateBodyAlignment(head: simd_float3, spine: simd_float3, hip: simd_float3) -> Float {
        let headToSpine = distance(head, spine)
        let spineToHip = distance(spine, hip)
        let headToHip = distance(head, hip)
        
        // Check if points are roughly in a line (triangle inequality approach)
        let linearity = abs(headToSpine + spineToHip - headToHip) / headToHip
        return max(0.0, 1.0 - linearity * 5.0) // Scale and invert
    }
    
    private func calculateArmForm(shoulder: simd_float3, elbow: simd_float3, wrist: simd_float3) -> Float {
        let angle = calculateElbowAngle(shoulder: shoulder, elbow: elbow, wrist: wrist)
        
        // Ideal push-up elbow angle is around 90 degrees at the bottom
        let ideal: Float = Float.pi / 2
        let deviation = abs(angle - ideal)
        
        return max(0.0, 1.0 - deviation / ideal)
    }
}