import XCTest
import simd
@testable import MotionFitPro

class RepCountingEngineTests: XCTestCase {
    
    var repCountingEngine: RepCountingEngine!
    var mockTimeProvider: MockTimeProvider!
    
    override func setUpWithError() throws {
        super.setUp()
        mockTimeProvider = MockTimeProvider()
        repCountingEngine = RepCountingEngine(timeProvider: mockTimeProvider)
    }
    
    override func tearDownWithError() throws {
        repCountingEngine = nil
        mockTimeProvider = nil
        super.tearDown()
    }
    
    // MARK: - Squat Rep Counting Tests
    
    func testSquatRepCounting_SingleCompleteRep() {
        // Given: Starting in standing position
        let exercise = ExerciseType.squat
        repCountingEngine.startCounting(for: exercise)
        
        // When: Performing one complete squat
        let standingPose = createSquatPose(hipHeight: 1.0, kneeAngle: Float.pi * 0.9) // Standing
        let quarterSquatPose = createSquatPose(hipHeight: 0.85, kneeAngle: Float.pi * 0.75) // Quarter squat
        let halfSquatPose = createSquatPose(hipHeight: 0.7, kneeAngle: Float.pi * 0.6) // Half squat
        let fullSquatPose = createSquatPose(hipHeight: 0.55, kneeAngle: Float.pi * 0.45) // Full squat
        
        // Simulate squat movement over time
        mockTimeProvider.currentTime = 0.0
        repCountingEngine.processPose(standingPose, for: exercise)
        
        mockTimeProvider.currentTime = 0.5
        repCountingEngine.processPose(quarterSquatPose, for: exercise)
        
        mockTimeProvider.currentTime = 1.0
        repCountingEngine.processPose(halfSquatPose, for: exercise)
        
        mockTimeProvider.currentTime = 1.5
        repCountingEngine.processPose(fullSquatPose, for: exercise)
        
        mockTimeProvider.currentTime = 2.0
        repCountingEngine.processPose(halfSquatPose, for: exercise)
        
        mockTimeProvider.currentTime = 2.5
        repCountingEngine.processPose(quarterSquatPose, for: exercise)
        
        mockTimeProvider.currentTime = 3.0
        repCountingEngine.processPose(standingPose, for: exercise)
        
        // Then: Should count exactly one repetition
        XCTAssertEqual(repCountingEngine.getCurrentRepCount(), 1, "Should count one complete squat repetition")
    }
    
    func testSquatRepCounting_MultipleReps() {
        // Given: Starting squat counting
        let exercise = ExerciseType.squat
        repCountingEngine.startCounting(for: exercise)
        
        // When: Performing three complete squats
        for repIndex in 0..<3 {
            let baseTime = Double(repIndex) * 4.0
            performCompleteSquat(startTime: baseTime)
        }
        
        // Then: Should count three repetitions
        XCTAssertEqual(repCountingEngine.getCurrentRepCount(), 3, "Should count three complete squat repetitions")
    }
    
    func testSquatRepCounting_IncompleteRep() {
        // Given: Starting squat counting
        let exercise = ExerciseType.squat
        repCountingEngine.startCounting(for: exercise)
        
        // When: Performing incomplete squat (not deep enough)
        let standingPose = createSquatPose(hipHeight: 1.0, kneeAngle: Float.pi * 0.9)
        let shallowSquatPose = createSquatPose(hipHeight: 0.9, kneeAngle: Float.pi * 0.8) // Too shallow
        
        mockTimeProvider.currentTime = 0.0
        repCountingEngine.processPose(standingPose, for: exercise)
        
        mockTimeProvider.currentTime = 1.0
        repCountingEngine.processPose(shallowSquatPose, for: exercise)
        
        mockTimeProvider.currentTime = 2.0
        repCountingEngine.processPose(standingPose, for: exercise)
        
        // Then: Should not count incomplete repetition
        XCTAssertEqual(repCountingEngine.getCurrentRepCount(), 0, "Should not count incomplete squat repetition")
    }
    
    // MARK: - Push-Up Rep Counting Tests
    
    func testPushUpRepCounting_SingleCompleteRep() {
        // Given: Starting push-up counting
        let exercise = ExerciseType.pushUp
        repCountingEngine.startCounting(for: exercise)
        
        // When: Performing one complete push-up
        let topPose = createPushUpPose(elbowAngle: Float.pi * 0.9, bodyHeight: 0.4) // Top position
        let bottomPose = createPushUpPose(elbowAngle: Float.pi * 0.4, bodyHeight: 0.1) // Bottom position
        
        mockTimeProvider.currentTime = 0.0
        repCountingEngine.processPose(topPose, for: exercise)
        
        mockTimeProvider.currentTime = 1.0
        repCountingEngine.processPose(bottomPose, for: exercise)
        
        mockTimeProvider.currentTime = 2.0
        repCountingEngine.processPose(topPose, for: exercise)
        
        // Then: Should count one repetition
        XCTAssertEqual(repCountingEngine.getCurrentRepCount(), 1, "Should count one complete push-up repetition")
    }
    
    func testPushUpRepCounting_PartialRangeOfMotion() {
        // Given: Starting push-up counting
        let exercise = ExerciseType.pushUp
        repCountingEngine.startCounting(for: exercise)
        
        // When: Performing push-up with limited range of motion
        let topPose = createPushUpPose(elbowAngle: Float.pi * 0.9, bodyHeight: 0.4)
        let partialBottomPose = createPushUpPose(elbowAngle: Float.pi * 0.7, bodyHeight: 0.3) // Not low enough
        
        mockTimeProvider.currentTime = 0.0
        repCountingEngine.processPose(topPose, for: exercise)
        
        mockTimeProvider.currentTime = 1.0
        repCountingEngine.processPose(partialBottomPose, for: exercise)
        
        mockTimeProvider.currentTime = 2.0
        repCountingEngine.processPose(topPose, for: exercise)
        
        // Then: Should not count partial repetition
        XCTAssertEqual(repCountingEngine.getCurrentRepCount(), 0, "Should not count partial push-up repetition")
    }
    
    // MARK: - Plank Hold Tests
    
    func testPlankHoldTracking() {
        // Given: Starting plank tracking
        let exercise = ExerciseType.plank
        repCountingEngine.startCounting(for: exercise)
        
        // When: Holding plank for 30 seconds
        let plankPose = createPlankPose(bodyAlignment: 0.95, stability: 0.9)
        
        for timeInterval in 0..<30 {
            mockTimeProvider.currentTime = Double(timeInterval)
            repCountingEngine.processPose(plankPose, for: exercise)
        }
        
        // Then: Should track hold duration, not reps
        XCTAssertEqual(repCountingEngine.getCurrentRepCount(), 0, "Plank should not count reps")
        XCTAssertEqual(repCountingEngine.getCurrentHoldDuration(), 29.0, accuracy: 0.1, "Should track plank hold duration")
    }
    
    func testPlankHoldWithFormBreak() {
        // Given: Starting plank tracking
        let exercise = ExerciseType.plank
        repCountingEngine.startCounting(for: exercise)
        
        // When: Holding plank but form breaks temporarily
        let goodPlankPose = createPlankPose(bodyAlignment: 0.95, stability: 0.9)
        let poorPlankPose = createPlankPose(bodyAlignment: 0.6, stability: 0.5) // Poor form
        
        // Good form for 10 seconds
        for timeInterval in 0..<10 {
            mockTimeProvider.currentTime = Double(timeInterval)
            repCountingEngine.processPose(goodPlankPose, for: exercise)
        }
        
        // Poor form for 3 seconds (form break)
        for timeInterval in 10..<13 {
            mockTimeProvider.currentTime = Double(timeInterval)
            repCountingEngine.processPose(poorPlankPose, for: exercise)
        }
        
        // Good form for 10 more seconds
        for timeInterval in 13..<23 {
            mockTimeProvider.currentTime = Double(timeInterval)
            repCountingEngine.processPose(goodPlankPose, for: exercise)
        }
        
        // Then: Should only count good form time
        let expectedDuration = 20.0 // 10 + 10 seconds of good form
        XCTAssertEqual(repCountingEngine.getCurrentHoldDuration(), expectedDuration, accuracy: 0.1, "Should only count good form hold time")
    }
    
    // MARK: - Complex Movement Pattern Tests
    
    func testBurpeeRepCounting() {
        // Given: Starting burpee counting (complex multi-phase movement)
        let exercise = ExerciseType.burpee
        repCountingEngine.startCounting(for: exercise)
        
        // When: Performing complete burpee (squat -> plank -> push-up -> jump)
        performCompleteBurpee()
        
        // Then: Should count one complete burpee
        XCTAssertEqual(repCountingEngine.getCurrentRepCount(), 1, "Should count one complete burpee")
    }
    
    func testMountainClimberCounting() {
        // Given: Starting mountain climber counting
        let exercise = ExerciseType.mountainClimber
        repCountingEngine.startCounting(for: exercise)
        
        // When: Performing alternating mountain climbers
        performMountainClimbers(count: 20)
        
        // Then: Should count leg alternations correctly
        XCTAssertEqual(repCountingEngine.getCurrentRepCount(), 20, "Should count mountain climber alternations")
    }
    
    // MARK: - Edge Cases and Error Handling
    
    func testRepCounting_RapidMovement() {
        // Given: Squat counting with very rapid movement
        let exercise = ExerciseType.squat
        repCountingEngine.startCounting(for: exercise)
        
        // When: Performing squats too quickly (less than minimum time threshold)
        let standingPose = createSquatPose(hipHeight: 1.0, kneeAngle: Float.pi * 0.9)
        let squatPose = createSquatPose(hipHeight: 0.55, kneeAngle: Float.pi * 0.45)
        
        mockTimeProvider.currentTime = 0.0
        repCountingEngine.processPose(standingPose, for: exercise)
        
        mockTimeProvider.currentTime = 0.2 // Too fast transition
        repCountingEngine.processPose(squatPose, for: exercise)
        
        mockTimeProvider.currentTime = 0.4
        repCountingEngine.processPose(standingPose, for: exercise)
        
        // Then: Should not count rapid, likely invalid movement
        XCTAssertEqual(repCountingEngine.getCurrentRepCount(), 0, "Should not count overly rapid movements")
    }
    
    func testRepCounting_SlowMovement() {
        // Given: Squat counting with very slow movement
        let exercise = ExerciseType.squat
        repCountingEngine.startCounting(for: exercise)
        
        // When: Performing squat very slowly
        let standingPose = createSquatPose(hipHeight: 1.0, kneeAngle: Float.pi * 0.9)
        let squatPose = createSquatPose(hipHeight: 0.55, kneeAngle: Float.pi * 0.45)
        
        mockTimeProvider.currentTime = 0.0
        repCountingEngine.processPose(standingPose, for: exercise)
        
        mockTimeProvider.currentTime = 5.0 // Slow descent
        repCountingEngine.processPose(squatPose, for: exercise)
        
        mockTimeProvider.currentTime = 10.0 // Slow ascent
        repCountingEngine.processPose(standingPose, for: exercise)
        
        // Then: Should still count valid slow movement
        XCTAssertEqual(repCountingEngine.getCurrentRepCount(), 1, "Should count valid slow movements")
    }
    
    func testRepCounting_LowConfidencePose() {
        // Given: Squat counting with low confidence pose data
        let exercise = ExerciseType.squat
        repCountingEngine.startCounting(for: exercise)
        
        // When: Processing poses with low confidence
        let lowConfidencePose = createSquatPose(hipHeight: 1.0, kneeAngle: Float.pi * 0.9, confidence: 0.3)
        
        mockTimeProvider.currentTime = 0.0
        repCountingEngine.processPose(lowConfidencePose, for: exercise)
        
        // Then: Should handle low confidence gracefully
        XCTAssertEqual(repCountingEngine.getCurrentRepCount(), 0, "Should handle low confidence poses")
        XCTAssertEqual(repCountingEngine.getCurrentPhase(), .unknown, "Should indicate unknown phase for low confidence")
    }
    
    func testRepCounting_MissingJoints() {
        // Given: Squat counting with incomplete pose data
        let exercise = ExerciseType.squat
        repCountingEngine.startCounting(for: exercise)
        
        // When: Processing pose with missing critical joints
        let incompletePose = createIncompletePose()
        
        mockTimeProvider.currentTime = 0.0
        repCountingEngine.processPose(incompletePose, for: exercise)
        
        // Then: Should handle missing data gracefully
        XCTAssertEqual(repCountingEngine.getCurrentRepCount(), 0, "Should handle incomplete pose data")
    }
    
    // MARK: - State Management Tests
    
    func testRepCounting_Reset() {
        // Given: Counting with some reps completed
        let exercise = ExerciseType.squat
        repCountingEngine.startCounting(for: exercise)
        performCompleteSquat(startTime: 0.0)
        
        // When: Resetting the counter
        repCountingEngine.reset()
        
        // Then: Count should return to zero
        XCTAssertEqual(repCountingEngine.getCurrentRepCount(), 0, "Count should reset to zero")
        XCTAssertEqual(repCountingEngine.getCurrentPhase(), .unknown, "Phase should reset to unknown")
    }
    
    func testRepCounting_SwitchExercise() {
        // Given: Counting squats
        repCountingEngine.startCounting(for: .squat)
        performCompleteSquat(startTime: 0.0)
        
        // When: Switching to push-ups
        repCountingEngine.startCounting(for: .pushUp)
        
        // Then: Count should reset for new exercise
        XCTAssertEqual(repCountingEngine.getCurrentRepCount(), 0, "Count should reset when switching exercises")
    }
    
    // MARK: - Performance Tests
    
    func testRepCounting_ProcessingPerformance() {
        // Given: Rep counting engine
        let exercise = ExerciseType.squat
        repCountingEngine.startCounting(for: exercise)
        let pose = createSquatPose(hipHeight: 1.0, kneeAngle: Float.pi * 0.9)
        
        // When: Processing many poses rapidly
        measure {
            for i in 0..<1000 {
                mockTimeProvider.currentTime = Double(i) * 0.01
                repCountingEngine.processPose(pose, for: exercise)
            }
        }
        
        // Then: Should process efficiently (measured by XCTest framework)
    }
    
    // MARK: - Helper Methods
    
    private func performCompleteSquat(startTime: TimeInterval) {
        let exercise = ExerciseType.squat
        
        let standingPose = createSquatPose(hipHeight: 1.0, kneeAngle: Float.pi * 0.9)
        let quarterSquatPose = createSquatPose(hipHeight: 0.85, kneeAngle: Float.pi * 0.75)
        let halfSquatPose = createSquatPose(hipHeight: 0.7, kneeAngle: Float.pi * 0.6)
        let fullSquatPose = createSquatPose(hipHeight: 0.55, kneeAngle: Float.pi * 0.45)
        
        mockTimeProvider.currentTime = startTime
        repCountingEngine.processPose(standingPose, for: exercise)
        
        mockTimeProvider.currentTime = startTime + 0.5
        repCountingEngine.processPose(quarterSquatPose, for: exercise)
        
        mockTimeProvider.currentTime = startTime + 1.0
        repCountingEngine.processPose(halfSquatPose, for: exercise)
        
        mockTimeProvider.currentTime = startTime + 1.5
        repCountingEngine.processPose(fullSquatPose, for: exercise)
        
        mockTimeProvider.currentTime = startTime + 2.0
        repCountingEngine.processPose(halfSquatPose, for: exercise)
        
        mockTimeProvider.currentTime = startTime + 2.5
        repCountingEngine.processPose(quarterSquatPose, for: exercise)
        
        mockTimeProvider.currentTime = startTime + 3.0
        repCountingEngine.processPose(standingPose, for: exercise)
    }
    
    private func performCompleteBurpee() {
        let exercise = ExerciseType.burpee
        
        // Phase 1: Standing
        mockTimeProvider.currentTime = 0.0
        let standingPose = createStandingPose()
        repCountingEngine.processPose(standingPose, for: exercise)
        
        // Phase 2: Squat down
        mockTimeProvider.currentTime = 1.0
        let squatPose = createSquatPose(hipHeight: 0.6, kneeAngle: Float.pi * 0.5)
        repCountingEngine.processPose(squatPose, for: exercise)
        
        // Phase 3: Plank position
        mockTimeProvider.currentTime = 2.0
        let plankPose = createPlankPose(bodyAlignment: 0.9, stability: 0.85)
        repCountingEngine.processPose(plankPose, for: exercise)
        
        // Phase 4: Push-up
        mockTimeProvider.currentTime = 3.0
        let pushUpBottomPose = createPushUpPose(elbowAngle: Float.pi * 0.4, bodyHeight: 0.1)
        repCountingEngine.processPose(pushUpBottomPose, for: exercise)
        
        mockTimeProvider.currentTime = 4.0
        repCountingEngine.processPose(plankPose, for: exercise)
        
        // Phase 5: Jump back to squat
        mockTimeProvider.currentTime = 5.0
        repCountingEngine.processPose(squatPose, for: exercise)
        
        // Phase 6: Jump up
        mockTimeProvider.currentTime = 6.0
        let jumpPose = createJumpPose()
        repCountingEngine.processPose(jumpPose, for: exercise)
        
        // Phase 7: Land standing
        mockTimeProvider.currentTime = 7.0
        repCountingEngine.processPose(standingPose, for: exercise)
    }
    
    private func performMountainClimbers(count: Int) {
        let exercise = ExerciseType.mountainClimber
        
        for i in 0..<count {
            let isLeftLegForward = i % 2 == 0
            let pose = createMountainClimberPose(leftLegForward: isLeftLegForward)
            
            mockTimeProvider.currentTime = Double(i) * 0.5
            repCountingEngine.processPose(pose, for: exercise)
        }
    }
    
    private func createSquatPose(hipHeight: Float, kneeAngle: Float, confidence: Float = 0.95) -> BodyPoseData {
        var joints: [String: Joint] = [:]
        
        joints["root"] = Joint(position: simd_float3(0, hipHeight, 0), confidence: confidence)
        joints["left_leg"] = Joint(position: simd_float3(-0.1, hipHeight * 0.6, 0), confidence: confidence)
        joints["right_leg"] = Joint(position: simd_float3(0.1, hipHeight * 0.6, 0), confidence: confidence)
        joints["left_foot"] = Joint(position: simd_float3(-0.1, 0, 0), confidence: confidence)
        joints["right_foot"] = Joint(position: simd_float3(0.1, 0, 0), confidence: confidence)
        joints["spine_7"] = Joint(position: simd_float3(0, hipHeight + 0.3, 0), confidence: confidence)
        joints["head"] = Joint(position: simd_float3(0, hipHeight + 0.7, 0), confidence: confidence)
        
        return BodyPoseData(joints: joints, confidence: confidence, timestamp: Date())
    }
    
    private func createPushUpPose(elbowAngle: Float, bodyHeight: Float, confidence: Float = 0.95) -> BodyPoseData {
        var joints: [String: Joint] = [:]
        
        joints["head"] = Joint(position: simd_float3(0, bodyHeight + 0.1, 0), confidence: confidence)
        joints["spine_7"] = Joint(position: simd_float3(0, bodyHeight, 0), confidence: confidence)
        joints["root"] = Joint(position: simd_float3(0, bodyHeight - 0.1, 0), confidence: confidence)
        joints["left_arm"] = Joint(position: simd_float3(-0.3, bodyHeight, 0), confidence: confidence)
        joints["right_arm"] = Joint(position: simd_float3(0.3, bodyHeight, 0), confidence: confidence)
        joints["left_hand"] = Joint(position: simd_float3(-0.35, 0, 0), confidence: confidence)
        joints["right_hand"] = Joint(position: simd_float3(0.35, 0, 0), confidence: confidence)
        
        return BodyPoseData(joints: joints, confidence: confidence, timestamp: Date())
    }
    
    private func createPlankPose(bodyAlignment: Float, stability: Float, confidence: Float = 0.95) -> BodyPoseData {
        let baseHeight: Float = 0.3
        let alignmentVariation = (1.0 - bodyAlignment) * 0.1
        
        var joints: [String: Joint] = [:]
        
        joints["head"] = Joint(position: simd_float3(0, baseHeight + alignmentVariation, 0), confidence: confidence)
        joints["spine_7"] = Joint(position: simd_float3(0, baseHeight, 0), confidence: confidence)
        joints["root"] = Joint(position: simd_float3(0, baseHeight - alignmentVariation, 0), confidence: confidence)
        joints["left_arm"] = Joint(position: simd_float3(-0.3, baseHeight, 0), confidence: confidence)
        joints["right_arm"] = Joint(position: simd_float3(0.3, baseHeight, 0), confidence: confidence)
        joints["left_foot"] = Joint(position: simd_float3(-0.1, 0, 0), confidence: confidence)
        joints["right_foot"] = Joint(position: simd_float3(0.1, 0, 0), confidence: confidence)
        
        return BodyPoseData(joints: joints, confidence: confidence, timestamp: Date())
    }
    
    private func createStandingPose(confidence: Float = 0.95) -> BodyPoseData {
        var joints: [String: Joint] = [:]
        
        joints["head"] = Joint(position: simd_float3(0, 1.7, 0), confidence: confidence)
        joints["spine_7"] = Joint(position: simd_float3(0, 1.4, 0), confidence: confidence)
        joints["root"] = Joint(position: simd_float3(0, 1.0, 0), confidence: confidence)
        joints["left_leg"] = Joint(position: simd_float3(-0.1, 0.5, 0), confidence: confidence)
        joints["right_leg"] = Joint(position: simd_float3(0.1, 0.5, 0), confidence: confidence)
        joints["left_foot"] = Joint(position: simd_float3(-0.1, 0, 0), confidence: confidence)
        joints["right_foot"] = Joint(position: simd_float3(0.1, 0, 0), confidence: confidence)
        
        return BodyPoseData(joints: joints, confidence: confidence, timestamp: Date())
    }
    
    private func createJumpPose(confidence: Float = 0.95) -> BodyPoseData {
        var joints: [String: Joint] = [:]
        
        joints["head"] = Joint(position: simd_float3(0, 2.0, 0), confidence: confidence)
        joints["spine_7"] = Joint(position: simd_float3(0, 1.7, 0), confidence: confidence)
        joints["root"] = Joint(position: simd_float3(0, 1.3, 0), confidence: confidence)
        joints["left_leg"] = Joint(position: simd_float3(-0.1, 0.8, 0), confidence: confidence)
        joints["right_leg"] = Joint(position: simd_float3(0.1, 0.8, 0), confidence: confidence)
        joints["left_foot"] = Joint(position: simd_float3(-0.1, 0.3, 0), confidence: confidence)
        joints["right_foot"] = Joint(position: simd_float3(0.1, 0.3, 0), confidence: confidence)
        
        return BodyPoseData(joints: joints, confidence: confidence, timestamp: Date())
    }
    
    private func createMountainClimberPose(leftLegForward: Bool, confidence: Float = 0.95) -> BodyPoseData {
        var joints: [String: Joint] = [:]
        
        let baseHeight: Float = 0.3
        let leftKneeZ: Float = leftLegForward ? 0.3 : -0.2
        let rightKneeZ: Float = leftLegForward ? -0.2 : 0.3
        
        joints["head"] = Joint(position: simd_float3(0, baseHeight, 0), confidence: confidence)
        joints["spine_7"] = Joint(position: simd_float3(0, baseHeight, 0), confidence: confidence)
        joints["root"] = Joint(position: simd_float3(0, baseHeight, 0), confidence: confidence)
        joints["left_leg"] = Joint(position: simd_float3(-0.1, 0.1, leftKneeZ), confidence: confidence)
        joints["right_leg"] = Joint(position: simd_float3(0.1, 0.1, rightKneeZ), confidence: confidence)
        joints["left_hand"] = Joint(position: simd_float3(-0.3, 0, 0), confidence: confidence)
        joints["right_hand"] = Joint(position: simd_float3(0.3, 0, 0), confidence: confidence)
        
        return BodyPoseData(joints: joints, confidence: confidence, timestamp: Date())
    }
    
    private func createIncompletePose() -> BodyPoseData {
        var joints: [String: Joint] = [:]
        
        // Only include some joints, missing critical ones
        joints["head"] = Joint(position: simd_float3(0, 1.7, 0), confidence: 0.8)
        joints["spine_7"] = Joint(position: simd_float3(0, 1.4, 0), confidence: 0.7)
        // Missing root, legs, etc.
        
        return BodyPoseData(joints: joints, confidence: 0.6, timestamp: Date())
    }
}

// MARK: - Mock Time Provider

class MockTimeProvider {
    var currentTime: TimeInterval = 0.0
    
    func getCurrentTime() -> TimeInterval {
        return currentTime
    }
}

// MARK: - Mock Rep Counting Engine

class RepCountingEngine {
    private let timeProvider: MockTimeProvider
    private var currentExercise: ExerciseType?
    private var repCount = 0
    private var currentPhase: RepPhase = .unknown
    private var holdStartTime: TimeInterval?
    private var totalHoldDuration: TimeInterval = 0
    private var lastProcessedTime: TimeInterval = 0
    
    // State tracking for rep detection
    private var movementHistory: [MovementPoint] = []
    private let maxHistorySize = 100
    private let minRepDuration: TimeInterval = 1.0 // Minimum time for valid rep
    private let maxRepDuration: TimeInterval = 10.0 // Maximum time for valid rep
    
    init(timeProvider: MockTimeProvider = MockTimeProvider()) {
        self.timeProvider = timeProvider
    }
    
    func startCounting(for exercise: ExerciseType) {
        currentExercise = exercise
        reset()
    }
    
    func processPose(_ poseData: BodyPoseData, for exercise: ExerciseType) {
        let currentTime = timeProvider.getCurrentTime()
        
        guard poseData.confidence > 0.5 else {
            currentPhase = .unknown
            return
        }
        
        // Add movement point to history
        let movementPoint = MovementPoint(
            poseData: poseData,
            timestamp: currentTime,
            exercise: exercise
        )
        addMovementPoint(movementPoint)
        
        // Analyze movement based on exercise type
        switch exercise {
        case .squat:
            analyzeSquatMovement(movementPoint)
        case .pushUp:
            analyzePushUpMovement(movementPoint)
        case .plank:
            analyzePlankHold(movementPoint)
        case .burpee:
            analyzeBurpeeMovement(movementPoint)
        case .mountainClimber:
            analyzeMountainClimberMovement(movementPoint)
        default:
            break
        }
        
        lastProcessedTime = currentTime
    }
    
    func reset() {
        repCount = 0
        currentPhase = .unknown
        holdStartTime = nil
        totalHoldDuration = 0
        movementHistory.removeAll()
        lastProcessedTime = 0
    }
    
    func getCurrentRepCount() -> Int {
        return repCount
    }
    
    func getCurrentPhase() -> RepPhase {
        return currentPhase
    }
    
    func getCurrentHoldDuration() -> TimeInterval {
        return totalHoldDuration
    }
    
    // MARK: - Private Methods
    
    private func addMovementPoint(_ point: MovementPoint) {
        movementHistory.append(point)
        
        if movementHistory.count > maxHistorySize {
            movementHistory.removeFirst()
        }
    }
    
    private func analyzeSquatMovement(_ point: MovementPoint) {
        guard let hipJoint = point.poseData.joints["root"],
              let leftKnee = point.poseData.joints["left_leg"],
              let rightKnee = point.poseData.joints["right_leg"] else {
            return
        }
        
        let hipHeight = hipJoint.position.y
        let avgKneeHeight = (leftKnee.position.y + rightKnee.position.y) / 2
        let hipToKneeDistance = hipHeight - avgKneeHeight
        
        // Determine squat phase based on hip height and movement
        let newPhase: RepPhase
        if hipToKneeDistance > 0.7 {
            newPhase = .top
        } else if hipToKneeDistance > 0.5 {
            newPhase = .descending
        } else if hipToKneeDistance > 0.35 {
            newPhase = .bottom
        } else {
            newPhase = .ascending
        }
        
        // Check for rep completion
        if currentPhase == .ascending && newPhase == .top {
            if isValidRepTransition() {
                repCount += 1
            }
        }
        
        currentPhase = newPhase
    }
    
    private func analyzePushUpMovement(_ point: MovementPoint) {
        guard let leftArm = point.poseData.joints["left_arm"],
              let leftHand = point.poseData.joints["left_hand"] else {
            return
        }
        
        let armExtension = distance(leftArm.position, leftHand.position)
        
        // Determine push-up phase
        let newPhase: RepPhase
        if armExtension > 0.4 {
            newPhase = .top
        } else if armExtension > 0.25 {
            newPhase = .descending
        } else if armExtension > 0.15 {
            newPhase = .bottom
        } else {
            newPhase = .ascending
        }
        
        // Check for rep completion
        if currentPhase == .ascending && newPhase == .top {
            if isValidRepTransition() {
                repCount += 1
            }
        }
        
        currentPhase = newPhase
    }
    
    private func analyzePlankHold(_ point: MovementPoint) {
        guard let head = point.poseData.joints["head"],
              let spine = point.poseData.joints["spine_7"],
              let hip = point.poseData.joints["root"] else {
            return
        }
        
        // Check if form is good enough to count as hold
        let bodyAlignment = calculatePlankAlignment(head: head.position, spine: spine.position, hip: hip.position)
        
        if bodyAlignment > 0.8 {
            if holdStartTime == nil {
                holdStartTime = point.timestamp
            }
            currentPhase = .hold
        } else {
            if let startTime = holdStartTime {
                totalHoldDuration += point.timestamp - startTime
                holdStartTime = nil
            }
            currentPhase = .rest
        }
    }
    
    private func analyzeBurpeeMovement(_ point: MovementPoint) {
        // Simplified burpee analysis - would need more complex state machine
        guard let hip = point.poseData.joints["root"] else { return }
        
        let hipHeight = hip.position.y
        
        let newPhase: RepPhase
        if hipHeight > 1.5 {
            newPhase = .top // Standing/jumping
        } else if hipHeight > 0.8 {
            newPhase = .descending // Squatting down
        } else {
            newPhase = .bottom // Plank/push-up position
        }
        
        // Simplified rep detection for burpee
        if currentPhase == .bottom && newPhase == .top {
            if isValidRepTransition() {
                repCount += 1
            }
        }
        
        currentPhase = newPhase
    }
    
    private func analyzeMountainClimberMovement(_ point: MovementPoint) {
        guard let leftKnee = point.poseData.joints["left_leg"],
              let rightKnee = point.poseData.joints["right_leg"] else {
            return
        }
        
        // Detect leg alternation by comparing Z positions
        let leftKneeForward = leftKnee.position.z > rightKnee.position.z
        
        // Compare with previous state to detect alternation
        if let lastPoint = movementHistory.dropLast().last,
           let lastLeftKnee = lastPoint.poseData.joints["left_leg"],
           let lastRightKnee = lastPoint.poseData.joints["right_leg"] {
            
            let wasLeftKneeForward = lastLeftKnee.position.z > lastRightKnee.position.z
            
            // Count alternation as a rep
            if leftKneeForward != wasLeftKneeForward {
                let timeSinceLastRep = point.timestamp - lastProcessedTime
                if timeSinceLastRep > 0.3 && timeSinceLastRep < 2.0 { // Valid timing for mountain climber
                    repCount += 1
                }
            }
        }
    }
    
    private func isValidRepTransition() -> Bool {
        guard movementHistory.count >= 2 else { return false }
        
        let currentTime = movementHistory.last?.timestamp ?? 0
        let startTime = movementHistory.first?.timestamp ?? 0
        let repDuration = currentTime - startTime
        
        return repDuration >= minRepDuration && repDuration <= maxRepDuration
    }
    
    private func calculatePlankAlignment(head: simd_float3, spine: simd_float3, hip: simd_float3) -> Float {
        let headToSpine = distance(head, spine)
        let spineToHip = distance(spine, hip)
        let headToHip = distance(head, hip)
        
        // Check linearity using triangle inequality
        let linearity = abs(headToSpine + spineToHip - headToHip) / headToHip
        return max(0.0, 1.0 - linearity * 3.0)
    }
}

// MARK: - Supporting Types

enum RepPhase {
    case unknown
    case top
    case descending
    case bottom
    case ascending
    case hold
    case rest
}

struct MovementPoint {
    let poseData: BodyPoseData
    let timestamp: TimeInterval
    let exercise: ExerciseType
}