import XCTest
@testable import MotionFitPro

final class WorkoutSettingsTests: XCTestCase {
    
    // MARK: - Test Data
    
    private func createDefaultWorkoutSettings() -> WorkoutSettings {
        return WorkoutSettings()
    }
    
    private func createCustomWorkoutSettings() -> WorkoutSettings {
        return WorkoutSettings(
            duration: .long,
            difficulty: .advanced,
            restBetweenSets: 45,
            restBetweenExercises: 90,
            coachingPersonality: .motivational,
            audioFeedback: false,
            hapticFeedback: false,
            formStrictness: .strict,
            autoProgressSets: true,
            countdownEnabled: false,
            musicEnabled: false,
            voiceCoachingEnabled: false,
            realTimeFeedback: false,
            pauseOnFormError: true,
            targetHeartRateZone: .anaerobic,
            preferredExerciseTypes: [.strength, .cardio],
            avoidedEquipment: [.barbell, .kettlebell],
            accessibilityMode: .voiceOver,
            cameraPosition: .back,
            trackingMode: .performance
        )
    }
    
    // MARK: - Initialization Tests
    
    func testDefaultWorkoutSettingsInitialization() {
        let settings = createDefaultWorkoutSettings()
        
        XCTAssertEqual(settings.duration, .medium)
        XCTAssertEqual(settings.difficulty, .beginner)
        XCTAssertEqual(settings.restBetweenSets, 60)
        XCTAssertEqual(settings.restBetweenExercises, 120)
        XCTAssertEqual(settings.coachingPersonality, .encouraging)
        XCTAssertTrue(settings.audioFeedback)
        XCTAssertTrue(settings.hapticFeedback)
        XCTAssertEqual(settings.formStrictness, .moderate)
        XCTAssertFalse(settings.autoProgressSets)
        XCTAssertTrue(settings.countdownEnabled)
        XCTAssertTrue(settings.musicEnabled)
        XCTAssertTrue(settings.voiceCoachingEnabled)
        XCTAssertTrue(settings.realTimeFeedback)
        XCTAssertFalse(settings.pauseOnFormError)
        XCTAssertNil(settings.targetHeartRateZone)
        XCTAssertTrue(settings.preferredExerciseTypes.isEmpty)
        XCTAssertTrue(settings.avoidedEquipment.isEmpty)
        XCTAssertEqual(settings.accessibilityMode, .standard)
        XCTAssertEqual(settings.cameraPosition, .front)
        XCTAssertEqual(settings.trackingMode, .full)
    }
    
    func testCustomWorkoutSettingsInitialization() {
        let settings = createCustomWorkoutSettings()
        
        XCTAssertEqual(settings.duration, .long)
        XCTAssertEqual(settings.difficulty, .advanced)
        XCTAssertEqual(settings.restBetweenSets, 45)
        XCTAssertEqual(settings.restBetweenExercises, 90)
        XCTAssertEqual(settings.coachingPersonality, .motivational)
        XCTAssertFalse(settings.audioFeedback)
        XCTAssertFalse(settings.hapticFeedback)
        XCTAssertEqual(settings.formStrictness, .strict)
        XCTAssertTrue(settings.autoProgressSets)
        XCTAssertFalse(settings.countdownEnabled)
        XCTAssertFalse(settings.musicEnabled)
        XCTAssertFalse(settings.voiceCoachingEnabled)
        XCTAssertFalse(settings.realTimeFeedback)
        XCTAssertTrue(settings.pauseOnFormError)
        XCTAssertEqual(settings.targetHeartRateZone, .anaerobic)
        XCTAssertEqual(settings.preferredExerciseTypes.count, 2)
        XCTAssertTrue(settings.preferredExerciseTypes.contains(.strength))
        XCTAssertTrue(settings.preferredExerciseTypes.contains(.cardio))
        XCTAssertEqual(settings.avoidedEquipment.count, 2)
        XCTAssertTrue(settings.avoidedEquipment.contains(.barbell))
        XCTAssertTrue(settings.avoidedEquipment.contains(.kettlebell))
        XCTAssertEqual(settings.accessibilityMode, .voiceOver)
        XCTAssertEqual(settings.cameraPosition, .back)
        XCTAssertEqual(settings.trackingMode, .performance)
    }
    
    // MARK: - Codable Tests
    
    func testWorkoutSettingsCodable() throws {
        let originalSettings = createCustomWorkoutSettings()
        
        // Encode
        let encoder = JSONEncoder()
        let data = try encoder.encode(originalSettings)
        
        // Decode
        let decoder = JSONDecoder()
        let decodedSettings = try decoder.decode(WorkoutSettings.self, from: data)
        
        // Verify all properties
        XCTAssertEqual(originalSettings.duration, decodedSettings.duration)
        XCTAssertEqual(originalSettings.difficulty, decodedSettings.difficulty)
        XCTAssertEqual(originalSettings.restBetweenSets, decodedSettings.restBetweenSets)
        XCTAssertEqual(originalSettings.restBetweenExercises, decodedSettings.restBetweenExercises)
        XCTAssertEqual(originalSettings.coachingPersonality, decodedSettings.coachingPersonality)
        XCTAssertEqual(originalSettings.audioFeedback, decodedSettings.audioFeedback)
        XCTAssertEqual(originalSettings.hapticFeedback, decodedSettings.hapticFeedback)
        XCTAssertEqual(originalSettings.formStrictness, decodedSettings.formStrictness)
        XCTAssertEqual(originalSettings.autoProgressSets, decodedSettings.autoProgressSets)
        XCTAssertEqual(originalSettings.targetHeartRateZone, decodedSettings.targetHeartRateZone)
        XCTAssertEqual(originalSettings.preferredExerciseTypes, decodedSettings.preferredExerciseTypes)
        XCTAssertEqual(originalSettings.avoidedEquipment, decodedSettings.avoidedEquipment)
        XCTAssertEqual(originalSettings.accessibilityMode, decodedSettings.accessibilityMode)
        XCTAssertEqual(originalSettings.cameraPosition, decodedSettings.cameraPosition)
        XCTAssertEqual(originalSettings.trackingMode, decodedSettings.trackingMode)
    }
    
    // MARK: - WorkoutDuration Tests
    
    func testWorkoutDurationDisplayNames() {
        XCTAssertEqual(WorkoutDuration.quick.displayName, "Quick (5-15 min)")
        XCTAssertEqual(WorkoutDuration.short.displayName, "Short (15-30 min)")
        XCTAssertEqual(WorkoutDuration.medium.displayName, "Medium (30-45 min)")
        XCTAssertEqual(WorkoutDuration.long.displayName, "Long (45-60 min)")
        XCTAssertEqual(WorkoutDuration.extended.displayName, "Extended (60+ min)")
        XCTAssertEqual(WorkoutDuration.custom.displayName, "Custom")
    }
    
    func testWorkoutDurationTimeRanges() {
        XCTAssertEqual(WorkoutDuration.quick.timeRange, 300...900)
        XCTAssertEqual(WorkoutDuration.short.timeRange, 900...1800)
        XCTAssertEqual(WorkoutDuration.medium.timeRange, 1800...2700)
        XCTAssertEqual(WorkoutDuration.long.timeRange, 2700...3600)
        XCTAssertEqual(WorkoutDuration.extended.timeRange, 3600...7200)
        XCTAssertEqual(WorkoutDuration.custom.timeRange, 0...7200)
    }
    
    func testWorkoutDurationRecommendedExerciseCount() {
        XCTAssertEqual(WorkoutDuration.quick.recommendedExerciseCount, 3)
        XCTAssertEqual(WorkoutDuration.short.recommendedExerciseCount, 5)
        XCTAssertEqual(WorkoutDuration.medium.recommendedExerciseCount, 8)
        XCTAssertEqual(WorkoutDuration.long.recommendedExerciseCount, 12)
        XCTAssertEqual(WorkoutDuration.extended.recommendedExerciseCount, 15)
        XCTAssertEqual(WorkoutDuration.custom.recommendedExerciseCount, 8)
    }
    
    // MARK: - FormStrictness Tests
    
    func testFormStrictnessDisplayNames() {
        XCTAssertEqual(FormStrictness.relaxed.displayName, "Relaxed")
        XCTAssertEqual(FormStrictness.moderate.displayName, "Moderate")
        XCTAssertEqual(FormStrictness.strict.displayName, "Strict")
        XCTAssertEqual(FormStrictness.expert.displayName, "Expert")
    }
    
    func testFormStrictnessDescriptions() {
        XCTAssertEqual(FormStrictness.relaxed.description, "Focus on movement, less strict form requirements")
        XCTAssertEqual(FormStrictness.moderate.description, "Balanced approach to form and movement")
        XCTAssertEqual(FormStrictness.strict.description, "High standards for proper form")
        XCTAssertEqual(FormStrictness.expert.description, "Professional-level form requirements")
    }
    
    func testFormStrictnessThresholds() {
        XCTAssertEqual(FormStrictness.relaxed.formThreshold, 0.6)
        XCTAssertEqual(FormStrictness.moderate.formThreshold, 0.75)
        XCTAssertEqual(FormStrictness.strict.formThreshold, 0.85)
        XCTAssertEqual(FormStrictness.expert.formThreshold, 0.95)
    }
    
    func testFormStrictnessCorrectionFrequency() {
        XCTAssertEqual(FormStrictness.relaxed.correctionFrequency, 0.3)
        XCTAssertEqual(FormStrictness.moderate.correctionFrequency, 0.5)
        XCTAssertEqual(FormStrictness.strict.correctionFrequency, 0.8)
        XCTAssertEqual(FormStrictness.expert.correctionFrequency, 1.0)
    }
    
    // MARK: - HeartRateZone Tests
    
    func testHeartRateZoneDisplayNames() {
        XCTAssertEqual(HeartRateZone.recovery.displayName, "Recovery (50-60%)")
        XCTAssertEqual(HeartRateZone.aerobic.displayName, "Aerobic (60-70%)")
        XCTAssertEqual(HeartRateZone.anaerobic.displayName, "Anaerobic (70-80%)")
        XCTAssertEqual(HeartRateZone.threshold.displayName, "Threshold (80-90%)")
        XCTAssertEqual(HeartRateZone.maximum.displayName, "Maximum (90-100%)")
    }
    
    func testHeartRateZoneIntensityRanges() {
        XCTAssertEqual(HeartRateZone.recovery.intensityRange, 0.5...0.6)
        XCTAssertEqual(HeartRateZone.aerobic.intensityRange, 0.6...0.7)
        XCTAssertEqual(HeartRateZone.anaerobic.intensityRange, 0.7...0.8)
        XCTAssertEqual(HeartRateZone.threshold.intensityRange, 0.8...0.9)
        XCTAssertEqual(HeartRateZone.maximum.intensityRange, 0.9...1.0)
    }
    
    func testHeartRateZoneColors() {
        XCTAssertEqual(HeartRateZone.recovery.color, "blue")
        XCTAssertEqual(HeartRateZone.aerobic.color, "green")
        XCTAssertEqual(HeartRateZone.anaerobic.color, "yellow")
        XCTAssertEqual(HeartRateZone.threshold.color, "orange")
        XCTAssertEqual(HeartRateZone.maximum.color, "red")
    }
    
    // MARK: - AccessibilityMode Tests
    
    func testAccessibilityModeDisplayNames() {
        XCTAssertEqual(AccessibilityMode.standard.displayName, "Standard")
        XCTAssertEqual(AccessibilityMode.voiceOver.displayName, "VoiceOver")
        XCTAssertEqual(AccessibilityMode.largeText.displayName, "Large Text")
        XCTAssertEqual(AccessibilityMode.highContrast.displayName, "High Contrast")
        XCTAssertEqual(AccessibilityMode.reducedMotion.displayName, "Reduced Motion")
        XCTAssertEqual(AccessibilityMode.assistive.displayName, "Assistive Mode")
    }
    
    func testAccessibilityModeDescriptions() {
        XCTAssertEqual(AccessibilityMode.standard.description, "Standard accessibility features")
        XCTAssertEqual(AccessibilityMode.voiceOver.description, "Enhanced VoiceOver support")
        XCTAssertEqual(AccessibilityMode.largeText.description, "Larger text and UI elements")
        XCTAssertEqual(AccessibilityMode.highContrast.description, "High contrast colors and borders")
        XCTAssertEqual(AccessibilityMode.reducedMotion.description, "Minimal animations and transitions")
        XCTAssertEqual(AccessibilityMode.assistive.description, "Full assistive technology support")
    }
    
    // MARK: - TrackingMode Tests
    
    func testTrackingModeDisplayNames() {
        XCTAssertEqual(TrackingMode.full.displayName, "Full Body")
        XCTAssertEqual(TrackingMode.upper.displayName, "Upper Body")
        XCTAssertEqual(TrackingMode.lower.displayName, "Lower Body")
        XCTAssertEqual(TrackingMode.core.displayName, "Core Focus")
        XCTAssertEqual(TrackingMode.performance.displayName, "High Performance")
        XCTAssertEqual(TrackingMode.battery.displayName, "Battery Saver")
    }
    
    func testTrackingModeDescriptions() {
        XCTAssertEqual(TrackingMode.full.description, "Track entire body for comprehensive analysis")
        XCTAssertEqual(TrackingMode.upper.description, "Focus on upper body movements")
        XCTAssertEqual(TrackingMode.lower.description, "Focus on lower body movements")
        XCTAssertEqual(TrackingMode.core.description, "Emphasize core and torso tracking")
        XCTAssertEqual(TrackingMode.performance.description, "Maximum accuracy and frame rate")
        XCTAssertEqual(TrackingMode.battery.description, "Reduced tracking for longer battery life")
    }
    
    func testTrackingModeFrameRates() {
        XCTAssertEqual(TrackingMode.full.frameRate, 30)
        XCTAssertEqual(TrackingMode.upper.frameRate, 30)
        XCTAssertEqual(TrackingMode.lower.frameRate, 30)
        XCTAssertEqual(TrackingMode.core.frameRate, 30)
        XCTAssertEqual(TrackingMode.performance.frameRate, 60)
        XCTAssertEqual(TrackingMode.battery.frameRate, 15)
    }
    
    // MARK: - Validation Tests
    
    func testWorkoutSettingsValidation() {
        let validSettings = createDefaultWorkoutSettings()
        XCTAssertTrue(validSettings.isValid)
        
        let customValidSettings = createCustomWorkoutSettings()
        XCTAssertTrue(customValidSettings.isValid)
    }
    
    func testInvalidRestTimes() {
        var invalidSettings = createDefaultWorkoutSettings()
        invalidSettings.restBetweenSets = -10 // Invalid negative rest time
        XCTAssertFalse(invalidSettings.isValid)
        
        invalidSettings.restBetweenSets = 400 // Invalid too long rest time
        XCTAssertFalse(invalidSettings.isValid)
        
        invalidSettings.restBetweenSets = 60 // Valid again
        invalidSettings.restBetweenExercises = -5 // Invalid negative rest time
        XCTAssertFalse(invalidSettings.isValid)
        
        invalidSettings.restBetweenExercises = 700 // Invalid too long rest time
        XCTAssertFalse(invalidSettings.isValid)
    }
    
    // MARK: - Recommended Settings Tests
    
    func testRecommendedSettingsForBeginner() {
        let beginnerSettings = WorkoutSettings.recommended(for: .beginner)
        
        XCTAssertEqual(beginnerSettings.duration, .short)
        XCTAssertEqual(beginnerSettings.difficulty, .beginner)
        XCTAssertEqual(beginnerSettings.restBetweenSets, 90)
        XCTAssertEqual(beginnerSettings.restBetweenExercises, 180)
        XCTAssertEqual(beginnerSettings.formStrictness, .moderate)
        XCTAssertTrue(beginnerSettings.realTimeFeedback)
        XCTAssertTrue(beginnerSettings.pauseOnFormError)
    }
    
    func testRecommendedSettingsForIntermediate() {
        let intermediateSettings = WorkoutSettings.recommended(for: .intermediate)
        
        XCTAssertEqual(intermediateSettings.duration, .medium)
        XCTAssertEqual(intermediateSettings.difficulty, .intermediate)
        XCTAssertEqual(intermediateSettings.restBetweenSets, 60)
        XCTAssertEqual(intermediateSettings.restBetweenExercises, 120)
        XCTAssertEqual(intermediateSettings.formStrictness, .strict)
        XCTAssertTrue(intermediateSettings.autoProgressSets)
    }
    
    func testRecommendedSettingsForAdvanced() {
        let advancedSettings = WorkoutSettings.recommended(for: .advanced)
        
        XCTAssertEqual(advancedSettings.duration, .long)
        XCTAssertEqual(advancedSettings.difficulty, .advanced)
        XCTAssertEqual(advancedSettings.restBetweenSets, 45)
        XCTAssertEqual(advancedSettings.restBetweenExercises, 90)
        XCTAssertEqual(advancedSettings.formStrictness, .expert)
        XCTAssertEqual(advancedSettings.trackingMode, .performance)
    }
    
    // MARK: - Edge Cases
    
    func testZeroRestTimes() {
        var settings = createDefaultWorkoutSettings()
        settings.restBetweenSets = 0
        settings.restBetweenExercises = 0
        
        XCTAssertTrue(settings.isValid) // Zero rest time should be valid
    }
    
    func testMaximumRestTimes() {
        var settings = createDefaultWorkoutSettings()
        settings.restBetweenSets = 300 // 5 minutes - maximum allowed
        settings.restBetweenExercises = 600 // 10 minutes - maximum allowed
        
        XCTAssertTrue(settings.isValid)
    }
    
    func testEmptyPreferredExerciseTypes() {
        var settings = createDefaultWorkoutSettings()
        settings.preferredExerciseTypes = []
        
        XCTAssertTrue(settings.isValid)
        XCTAssertTrue(settings.preferredExerciseTypes.isEmpty)
    }
    
    func testAllExerciseTypesPreferred() {
        var settings = createDefaultWorkoutSettings()
        settings.preferredExerciseTypes = ExerciseCategory.allCases
        
        XCTAssertTrue(settings.isValid)
        XCTAssertEqual(settings.preferredExerciseTypes.count, ExerciseCategory.allCases.count)
    }
    
    func testAllEquipmentAvoided() {
        var settings = createDefaultWorkoutSettings()
        settings.avoidedEquipment = Equipment.allCases
        
        XCTAssertTrue(settings.isValid)
        XCTAssertEqual(settings.avoidedEquipment.count, Equipment.allCases.count)
    }
}
