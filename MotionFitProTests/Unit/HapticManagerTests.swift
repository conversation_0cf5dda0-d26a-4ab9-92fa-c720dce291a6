import XCTest
@testable import MotionFitPro

@MainActor
final class HapticManagerTests: XCTestCase {
    
    var hapticManager: HapticManager!
    
    override func setUp() {
        super.setUp()
        hapticManager = HapticManager.shared
    }
    
    override func tearDown() {
        hapticManager = nil
        super.tearDown()
    }
    
    // MARK: - Initialization Tests
    
    func testHapticManagerSingleton() {
        let manager1 = HapticManager.shared
        let manager2 = HapticManager.shared
        
        XCTAssertTrue(manager1 === manager2, "HapticManager should be a singleton")
    }
    
    func testInitialState() {
        XCTAssertTrue(hapticManager.isHapticsEnabled, "Haptics should be enabled by default")
        XCTAssertEqual(hapticManager.hapticIntensity, 1.0, "Haptic intensity should be 1.0 by default")
    }
    
    // MARK: - Settings Tests
    
    func testSetHapticsEnabled() {
        // Test enabling haptics
        hapticManager.setHapticsEnabled(true)
        XCTAssertTrue(hapticManager.isHapticsEnabled)
        
        // Test disabling haptics
        hapticManager.setHapticsEnabled(false)
        XCTAssertFalse(hapticManager.isHapticsEnabled)
    }
    
    func testSetHapticIntensity() {
        // Test valid intensity values
        hapticManager.setHapticIntensity(0.5)
        XCTAssertEqual(hapticManager.hapticIntensity, 0.5)
        
        hapticManager.setHapticIntensity(0.0)
        XCTAssertEqual(hapticManager.hapticIntensity, 0.0)
        
        hapticManager.setHapticIntensity(1.0)
        XCTAssertEqual(hapticManager.hapticIntensity, 1.0)
        
        // Test clamping of invalid values
        hapticManager.setHapticIntensity(-0.5)
        XCTAssertEqual(hapticManager.hapticIntensity, 0.0, "Negative intensity should be clamped to 0.0")
        
        hapticManager.setHapticIntensity(1.5)
        XCTAssertEqual(hapticManager.hapticIntensity, 1.0, "Intensity > 1.0 should be clamped to 1.0")
    }
    
    // MARK: - Haptic Trigger Tests
    
    func testTriggerHapticWhenEnabled() {
        hapticManager.setHapticsEnabled(true)
        
        // These should not crash when haptics are enabled
        XCTAssertNoThrow(hapticManager.trigger(.repCompleted))
        XCTAssertNoThrow(hapticManager.trigger(.setCompleted))
        XCTAssertNoThrow(hapticManager.trigger(.workoutStarted))
        XCTAssertNoThrow(hapticManager.trigger(.workoutCompleted))
        XCTAssertNoThrow(hapticManager.trigger(.formCorrection))
        XCTAssertNoThrow(hapticManager.trigger(.achievement))
        XCTAssertNoThrow(hapticManager.trigger(.error))
        XCTAssertNoThrow(hapticManager.trigger(.selection))
        XCTAssertNoThrow(hapticManager.trigger(.countdown))
        XCTAssertNoThrow(hapticManager.trigger(.warning))
        XCTAssertNoThrow(hapticManager.trigger(.success))
        XCTAssertNoThrow(hapticManager.trigger(.milestone))
    }
    
    func testTriggerHapticWhenDisabled() {
        hapticManager.setHapticsEnabled(false)
        
        // These should not crash when haptics are disabled
        XCTAssertNoThrow(hapticManager.trigger(.repCompleted))
        XCTAssertNoThrow(hapticManager.trigger(.setCompleted))
        XCTAssertNoThrow(hapticManager.trigger(.workoutCompleted))
    }
    
    // MARK: - Legacy Method Tests
    
    func testLegacyTriggerSuccess() {
        hapticManager.setHapticsEnabled(true)
        XCTAssertNoThrow(hapticManager.triggerSuccess())
    }
    
    func testLegacyTriggerImpact() {
        hapticManager.setHapticsEnabled(true)
        XCTAssertNoThrow(hapticManager.triggerImpact())
    }
    
    // MARK: - HapticType Enum Tests
    
    func testHapticTypeEnumCases() {
        let allCases: [HapticType] = [
            .repCompleted,
            .setCompleted,
            .workoutStarted,
            .workoutCompleted,
            .formCorrection,
            .achievement,
            .error,
            .selection,
            .countdown,
            .warning,
            .success,
            .milestone
        ]
        
        // Verify all cases can be created
        for hapticType in allCases {
            XCTAssertNoThrow(hapticManager.trigger(hapticType))
        }
    }
    
    // MARK: - Performance Tests
    
    func testHapticTriggerPerformance() {
        hapticManager.setHapticsEnabled(true)
        
        measure {
            for _ in 0..<100 {
                hapticManager.trigger(.repCompleted)
            }
        }
    }
    
    func testHapticTriggerPerformanceWhenDisabled() {
        hapticManager.setHapticsEnabled(false)
        
        measure {
            for _ in 0..<100 {
                hapticManager.trigger(.repCompleted)
            }
        }
    }
    
    // MARK: - Concurrent Access Tests
    
    func testConcurrentHapticTriggers() {
        hapticManager.setHapticsEnabled(true)
        
        let expectation = XCTestExpectation(description: "Concurrent haptic triggers")
        expectation.expectedFulfillmentCount = 10
        
        for i in 0..<10 {
            DispatchQueue.global(qos: .userInitiated).async {
                self.hapticManager.trigger(.repCompleted)
                expectation.fulfill()
            }
        }
        
        wait(for: [expectation], timeout: 5.0)
    }
    
    // MARK: - State Persistence Tests
    
    func testHapticSettingsPersistence() {
        // Set custom values
        hapticManager.setHapticsEnabled(false)
        hapticManager.setHapticIntensity(0.7)
        
        // Verify UserDefaults are updated
        XCTAssertFalse(UserDefaults.standard.bool(forKey: "haptics_enabled"))
        XCTAssertEqual(UserDefaults.standard.float(forKey: "haptic_intensity"), 0.7, accuracy: 0.001)
        
        // Reset to defaults
        hapticManager.setHapticsEnabled(true)
        hapticManager.setHapticIntensity(1.0)
        
        XCTAssertTrue(UserDefaults.standard.bool(forKey: "haptics_enabled"))
        XCTAssertEqual(UserDefaults.standard.float(forKey: "haptic_intensity"), 1.0, accuracy: 0.001)
    }
    
    // MARK: - Edge Cases
    
    func testRapidHapticTriggers() {
        hapticManager.setHapticsEnabled(true)
        
        // Rapidly trigger haptics - should not crash
        for _ in 0..<50 {
            hapticManager.trigger(.repCompleted)
            hapticManager.trigger(.formCorrection)
        }
    }
    
    func testMixedHapticTypes() {
        hapticManager.setHapticsEnabled(true)
        
        let hapticTypes: [HapticType] = [
            .repCompleted, .setCompleted, .achievement, .error, .success
        ]
        
        // Trigger different types in sequence
        for hapticType in hapticTypes {
            hapticManager.trigger(hapticType)
        }
    }
    
    func testHapticIntensityBoundaryValues() {
        // Test exact boundary values
        hapticManager.setHapticIntensity(0.0)
        XCTAssertEqual(hapticManager.hapticIntensity, 0.0)
        
        hapticManager.setHapticIntensity(1.0)
        XCTAssertEqual(hapticManager.hapticIntensity, 1.0)
        
        // Test very small positive value
        hapticManager.setHapticIntensity(0.001)
        XCTAssertEqual(hapticManager.hapticIntensity, 0.001, accuracy: 0.0001)
        
        // Test value very close to 1.0
        hapticManager.setHapticIntensity(0.999)
        XCTAssertEqual(hapticManager.hapticIntensity, 0.999, accuracy: 0.0001)
    }
    
    func testHapticTriggerWithZeroIntensity() {
        hapticManager.setHapticsEnabled(true)
        hapticManager.setHapticIntensity(0.0)
        
        // Should not crash even with zero intensity
        XCTAssertNoThrow(hapticManager.trigger(.repCompleted))
        XCTAssertNoThrow(hapticManager.trigger(.achievement))
    }
    
    func testToggleHapticsMultipleTimes() {
        // Rapidly toggle haptics on/off
        for i in 0..<10 {
            hapticManager.setHapticsEnabled(i % 2 == 0)
            hapticManager.trigger(.selection)
        }
        
        // Should end in a consistent state
        XCTAssertFalse(hapticManager.isHapticsEnabled) // Last iteration was i=9 (odd)
    }
}
