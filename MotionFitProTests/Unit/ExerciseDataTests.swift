import XCTest
@testable import MotionFitPro

final class ExerciseDataTests: XCTestCase {
    
    // MARK: - Test Data
    
    private func createSampleExercise() -> ExerciseData {
        return ExerciseData(
            name: "Test Push-up",
            type: .pushUp,
            category: .strength,
            difficulty: .beginner,
            equipment: .none,
            duration: 180,
            description: "Test exercise description",
            instructions: ["Step 1", "Step 2", "Step 3"],
            muscleGroups: [.chest, .shoulders, .triceps],
            caloriesBurned: 8.5,
            thumbnailName: "test_thumbnail",
            targetReps: 12,
            targetSets: 3
        )
    }
    
    // MARK: - Initialization Tests
    
    func testExerciseDataInitialization() {
        let exercise = createSampleExercise()
        
        XCTAssertEqual(exercise.name, "Test Push-up")
        XCTAssertEqual(exercise.type, .pushUp)
        XCTAssertEqual(exercise.category, .strength)
        XCTAssertEqual(exercise.difficulty, .beginner)
        XCTAssertEqual(exercise.equipment, .none)
        XCTAssertEqual(exercise.duration, 180)
        XCTAssertEqual(exercise.description, "Test exercise description")
        XCTAssertEqual(exercise.instructions.count, 3)
        XCTAssertEqual(exercise.muscleGroups.count, 3)
        XCTAssertEqual(exercise.caloriesBurned, 8.5)
        XCTAssertEqual(exercise.thumbnailName, "test_thumbnail")
        XCTAssertEqual(exercise.targetReps, 12)
        XCTAssertEqual(exercise.targetSets, 3)
    }
    
    func testExerciseDataWithDefaults() {
        let exercise = ExerciseData(
            name: "Simple Exercise",
            type: .squat,
            category: .strength,
            difficulty: .intermediate,
            equipment: .dumbbells,
            duration: 120,
            description: "Simple description",
            instructions: ["Do the exercise"],
            muscleGroups: [.quadriceps],
            caloriesBurned: 5.0,
            thumbnailName: "simple_thumbnail"
        )
        
        // Check default values
        XCTAssertEqual(exercise.biomechanicalPoints.count, 0)
        XCTAssertEqual(exercise.commonMistakes.count, 0)
        XCTAssertEqual(exercise.modifications.count, 0)
        XCTAssertEqual(exercise.progressionLevel, 1)
        XCTAssertEqual(exercise.restTime, 60)
        XCTAssertEqual(exercise.targetReps, 12)
        XCTAssertEqual(exercise.targetSets, 3)
    }
    
    // MARK: - Codable Tests
    
    func testExerciseDataCodable() throws {
        let originalExercise = createSampleExercise()
        
        // Encode
        let encoder = JSONEncoder()
        let data = try encoder.encode(originalExercise)
        
        // Decode
        let decoder = JSONDecoder()
        let decodedExercise = try decoder.decode(ExerciseData.self, from: data)
        
        // Verify
        XCTAssertEqual(originalExercise.id, decodedExercise.id)
        XCTAssertEqual(originalExercise.name, decodedExercise.name)
        XCTAssertEqual(originalExercise.type, decodedExercise.type)
        XCTAssertEqual(originalExercise.category, decodedExercise.category)
        XCTAssertEqual(originalExercise.difficulty, decodedExercise.difficulty)
        XCTAssertEqual(originalExercise.equipment, decodedExercise.equipment)
        XCTAssertEqual(originalExercise.duration, decodedExercise.duration)
        XCTAssertEqual(originalExercise.description, decodedExercise.description)
        XCTAssertEqual(originalExercise.instructions, decodedExercise.instructions)
        XCTAssertEqual(originalExercise.muscleGroups, decodedExercise.muscleGroups)
        XCTAssertEqual(originalExercise.caloriesBurned, decodedExercise.caloriesBurned)
        XCTAssertEqual(originalExercise.thumbnailName, decodedExercise.thumbnailName)
    }
    
    // MARK: - Enum Tests
    
    func testExerciseCategoryDisplayNames() {
        XCTAssertEqual(ExerciseCategory.strength.displayName, "Strength")
        XCTAssertEqual(ExerciseCategory.cardio.displayName, "Cardio")
        XCTAssertEqual(ExerciseCategory.flexibility.displayName, "Flexibility")
        XCTAssertEqual(ExerciseCategory.balance.displayName, "Balance")
        XCTAssertEqual(ExerciseCategory.plyometric.displayName, "Plyometric")
        XCTAssertEqual(ExerciseCategory.core.displayName, "Core")
        XCTAssertEqual(ExerciseCategory.functional.displayName, "Functional")
    }
    
    func testExerciseDifficultyMultipliers() {
        XCTAssertEqual(ExerciseDifficulty.beginner.multiplier, 0.8)
        XCTAssertEqual(ExerciseDifficulty.intermediate.multiplier, 1.0)
        XCTAssertEqual(ExerciseDifficulty.advanced.multiplier, 1.3)
        XCTAssertEqual(ExerciseDifficulty.expert.multiplier, 1.6)
    }
    
    func testEquipmentDisplayNames() {
        XCTAssertEqual(Equipment.none.displayName, "No Equipment")
        XCTAssertEqual(Equipment.dumbbells.displayName, "Dumbbells")
        XCTAssertEqual(Equipment.barbell.displayName, "Barbell")
        XCTAssertEqual(Equipment.kettlebell.displayName, "Kettlebell")
        XCTAssertEqual(Equipment.resistanceBand.displayName, "Resistance Band")
    }
    
    func testMuscleGroupDisplayNames() {
        XCTAssertEqual(MuscleGroup.chest.displayName, "Chest")
        XCTAssertEqual(MuscleGroup.back.displayName, "Back")
        XCTAssertEqual(MuscleGroup.shoulders.displayName, "Shoulders")
        XCTAssertEqual(MuscleGroup.biceps.displayName, "Biceps")
        XCTAssertEqual(MuscleGroup.triceps.displayName, "Triceps")
        XCTAssertEqual(MuscleGroup.core.displayName, "Core")
        XCTAssertEqual(MuscleGroup.quadriceps.displayName, "Quadriceps")
        XCTAssertEqual(MuscleGroup.hamstrings.displayName, "Hamstrings")
        XCTAssertEqual(MuscleGroup.glutes.displayName, "Glutes")
        XCTAssertEqual(MuscleGroup.calves.displayName, "Calves")
        XCTAssertEqual(MuscleGroup.fullBody.displayName, "Full Body")
    }
    
    // MARK: - BiomechanicalPoint Tests
    
    func testBiomechanicalPointCreation() {
        let point = BiomechanicalPoint(
            jointName: "leftShoulder",
            targetAngle: 90.0,
            tolerance: 15.0,
            phase: .middle
        )
        
        XCTAssertEqual(point.jointName, "leftShoulder")
        XCTAssertEqual(point.targetAngle, 90.0)
        XCTAssertEqual(point.tolerance, 15.0)
        XCTAssertEqual(point.phase, .middle)
    }
    
    func testExercisePhaseValues() {
        XCTAssertEqual(BiomechanicalPoint.ExercisePhase.start.rawValue, "start")
        XCTAssertEqual(BiomechanicalPoint.ExercisePhase.middle.rawValue, "middle")
        XCTAssertEqual(BiomechanicalPoint.ExercisePhase.end.rawValue, "end")
        XCTAssertEqual(BiomechanicalPoint.ExercisePhase.transition.rawValue, "transition")
    }
    
    // MARK: - ExerciseModification Tests
    
    func testExerciseModificationCreation() {
        let modification = ExerciseModification(
            name: "Knee Push-ups",
            description: "Perform on knees for easier variation",
            difficultyChange: -1,
            equipmentRequired: nil
        )
        
        XCTAssertEqual(modification.name, "Knee Push-ups")
        XCTAssertEqual(modification.description, "Perform on knees for easier variation")
        XCTAssertEqual(modification.difficultyChange, -1)
        XCTAssertNil(modification.equipmentRequired)
    }
    
    func testExerciseModificationWithEquipment() {
        let modification = ExerciseModification(
            name: "Weighted Push-ups",
            description: "Add weight for increased difficulty",
            difficultyChange: 2,
            equipmentRequired: .dumbbells
        )
        
        XCTAssertEqual(modification.name, "Weighted Push-ups")
        XCTAssertEqual(modification.description, "Add weight for increased difficulty")
        XCTAssertEqual(modification.difficultyChange, 2)
        XCTAssertEqual(modification.equipmentRequired, .dumbbells)
    }
    
    // MARK: - Hashable and Identifiable Tests
    
    func testExerciseDataHashable() {
        let exercise1 = createSampleExercise()
        let exercise2 = createSampleExercise()
        
        // Different instances should have different IDs and hashes
        XCTAssertNotEqual(exercise1.id, exercise2.id)
        XCTAssertNotEqual(exercise1.hashValue, exercise2.hashValue)
    }
    
    func testExerciseDataIdentifiable() {
        let exercise = createSampleExercise()
        XCTAssertNotNil(exercise.id)
        XCTAssertTrue(exercise.id is UUID)
    }
    
    // MARK: - Edge Cases
    
    func testEmptyInstructions() {
        let exercise = ExerciseData(
            name: "Test Exercise",
            type: .squat,
            category: .strength,
            difficulty: .beginner,
            equipment: .none,
            duration: 60,
            description: "Test",
            instructions: [], // Empty instructions
            muscleGroups: [.quadriceps],
            caloriesBurned: 5.0,
            thumbnailName: "test"
        )
        
        XCTAssertTrue(exercise.instructions.isEmpty)
    }
    
    func testZeroDurationAndCalories() {
        let exercise = ExerciseData(
            name: "Test Exercise",
            type: .plank,
            category: .core,
            difficulty: .beginner,
            equipment: .none,
            duration: 0, // Zero duration
            description: "Test",
            instructions: ["Hold position"],
            muscleGroups: [.core],
            caloriesBurned: 0.0, // Zero calories
            thumbnailName: "test"
        )
        
        XCTAssertEqual(exercise.duration, 0)
        XCTAssertEqual(exercise.caloriesBurned, 0.0)
    }
    
    func testLargeValues() {
        let exercise = ExerciseData(
            name: "Extreme Exercise",
            type: .burpee,
            category: .plyometric,
            difficulty: .expert,
            equipment: .none,
            duration: 3600, // 1 hour
            description: "Very long exercise",
            instructions: Array(repeating: "Step", count: 100), // Many instructions
            muscleGroups: MuscleGroup.allCases, // All muscle groups
            caloriesBurned: 1000.0, // High calories
            thumbnailName: "extreme",
            targetReps: 1000,
            targetSets: 100
        )
        
        XCTAssertEqual(exercise.duration, 3600)
        XCTAssertEqual(exercise.instructions.count, 100)
        XCTAssertEqual(exercise.muscleGroups.count, MuscleGroup.allCases.count)
        XCTAssertEqual(exercise.caloriesBurned, 1000.0)
        XCTAssertEqual(exercise.targetReps, 1000)
        XCTAssertEqual(exercise.targetSets, 100)
    }
}
