import XCTest
import simd
@testable import MotionFitPro

final class BodyPoseDataTests: XCTestCase {
    
    // MARK: - Test Data
    
    private func createSampleJoint3D(position: simd_float3 = simd_float3(0, 0, 0), confidence: Float = 0.9) -> Joint3D {
        return Joint3D(
            position: position,
            confidence: confidence,
            isTracked: true,
            parentJoint: nil,
            childJoints: []
        )
    }
    
    private func createSampleBodyPoseData() -> BodyPoseData {
        let joints: [JointName: Joint3D] = [
            .root: createSampleJoint3D(position: simd_float3(0, 0, 0), confidence: 0.95),
            .spine3: createSampleJoint3D(position: simd_float3(0, 0.3, 0), confidence: 0.9),
            .leftShoulder: createSampleJoint3D(position: simd_float3(-0.2, 0.4, 0), confidence: 0.85),
            .rightShoulder: createSampleJoint3D(position: simd_float3(0.2, 0.4, 0), confidence: 0.85),
            .leftUpperLeg: createSampleJoint3D(position: simd_float3(-0.1, -0.2, 0), confidence: 0.8),
            .rightUpperLeg: createSampleJoint3D(position: simd_float3(0.1, -0.2, 0), confidence: 0.8)
        ]
        
        return BodyPoseData(
            joints: joints,
            trackingQuality: .good,
            confidence: 0.87,
            boundingBox: CGRect(x: 0, y: 0, width: 100, height: 200),
            isFullBodyVisible: true,
            estimatedHeight: 1.75,
            frameID: 123
        )
    }
    
    // MARK: - Initialization Tests
    
    func testBodyPoseDataInitialization() {
        let poseData = createSampleBodyPoseData()
        
        XCTAssertEqual(poseData.joints.count, 6)
        XCTAssertEqual(poseData.trackingQuality, .good)
        XCTAssertEqual(poseData.confidence, 0.87, accuracy: 0.001)
        XCTAssertEqual(poseData.boundingBox.width, 100)
        XCTAssertEqual(poseData.boundingBox.height, 200)
        XCTAssertTrue(poseData.isFullBodyVisible)
        XCTAssertEqual(poseData.estimatedHeight, 1.75)
        XCTAssertEqual(poseData.frameID, 123)
    }
    
    func testBodyPoseDataWithDefaults() {
        let joints: [JointName: Joint3D] = [
            .root: createSampleJoint3D()
        ]
        
        let poseData = BodyPoseData(
            joints: joints,
            trackingQuality: .excellent,
            confidence: 0.95,
            boundingBox: CGRect.zero,
            isFullBodyVisible: false
        )
        
        // Check default values
        XCTAssertNil(poseData.estimatedHeight)
        XCTAssertEqual(poseData.frameID, 0)
        XCTAssertNil(poseData.cameraTransform)
    }
    
    // MARK: - Joint3D Tests
    
    func testJoint3DInitialization() {
        let position = simd_float3(1.0, 2.0, 3.0)
        let joint = Joint3D(
            position: position,
            confidence: 0.8,
            isTracked: true,
            parentJoint: .spine3,
            childJoints: [.leftUpperArm, .rightUpperArm]
        )
        
        XCTAssertEqual(joint.position.x, 1.0)
        XCTAssertEqual(joint.position.y, 2.0)
        XCTAssertEqual(joint.position.z, 3.0)
        XCTAssertEqual(joint.confidence, 0.8)
        XCTAssertTrue(joint.isTracked)
        XCTAssertEqual(joint.parentJoint, .spine3)
        XCTAssertEqual(joint.childJoints.count, 2)
        XCTAssertTrue(joint.childJoints.contains(.leftUpperArm))
        XCTAssertTrue(joint.childJoints.contains(.rightUpperArm))
    }
    
    func testJoint3DDistance() {
        let joint1 = createSampleJoint3D(position: simd_float3(0, 0, 0))
        let joint2 = createSampleJoint3D(position: simd_float3(3, 4, 0))
        
        let distance = joint1.distance(to: joint2)
        XCTAssertEqual(distance, 5.0, accuracy: 0.001) // 3-4-5 triangle
    }
    
    func testJoint3DAngle() {
        let centerJoint = createSampleJoint3D(position: simd_float3(0, 0, 0))
        let joint1 = createSampleJoint3D(position: simd_float3(1, 0, 0))
        let joint2 = createSampleJoint3D(position: simd_float3(0, 1, 0))
        
        let angle = centerJoint.angle(to: joint1, and: joint2)
        XCTAssertEqual(angle, Float.pi / 2, accuracy: 0.001) // 90 degrees
    }
    
    func testJoint3DAngleWithZeroVector() {
        let centerJoint = createSampleJoint3D(position: simd_float3(0, 0, 0))
        let joint1 = createSampleJoint3D(position: simd_float3(0, 0, 0)) // Same position
        let joint2 = createSampleJoint3D(position: simd_float3(1, 0, 0))
        
        let angle = centerJoint.angle(to: joint1, and: joint2)
        XCTAssertEqual(angle, 0.0) // Should return 0 for zero-length vector
    }
    
    // MARK: - JointName Tests
    
    func testJointNameDisplayNames() {
        XCTAssertEqual(JointName.head.displayName, "Head")
        XCTAssertEqual(JointName.neck.displayName, "Neck")
        XCTAssertEqual(JointName.root.displayName, "Hip Center")
        XCTAssertEqual(JointName.leftShoulder.displayName, "Left Shoulder")
        XCTAssertEqual(JointName.rightShoulder.displayName, "Right Shoulder")
        XCTAssertEqual(JointName.leftUpperLeg.displayName, "Left Thigh")
        XCTAssertEqual(JointName.rightUpperLeg.displayName, "Right Thigh")
    }
    
    func testJointNameSideDetection() {
        XCTAssertTrue(JointName.leftShoulder.isLeftSide)
        XCTAssertFalse(JointName.leftShoulder.isRightSide)
        
        XCTAssertTrue(JointName.rightShoulder.isRightSide)
        XCTAssertFalse(JointName.rightShoulder.isLeftSide)
        
        XCTAssertFalse(JointName.root.isLeftSide)
        XCTAssertFalse(JointName.root.isRightSide)
    }
    
    func testJointNameBodyPartDetection() {
        // Core joints
        XCTAssertTrue(JointName.root.isCore)
        XCTAssertTrue(JointName.spine3.isCore)
        XCTAssertFalse(JointName.leftShoulder.isCore)
        
        // Arm joints
        XCTAssertTrue(JointName.leftShoulder.isArm)
        XCTAssertTrue(JointName.rightUpperArm.isArm)
        XCTAssertFalse(JointName.root.isArm)
        
        // Leg joints
        XCTAssertTrue(JointName.leftUpperLeg.isLeg)
        XCTAssertTrue(JointName.rightFoot.isLeg)
        XCTAssertFalse(JointName.leftShoulder.isLeg)
    }
    
    // MARK: - TrackingQuality Tests
    
    func testTrackingQualityDisplayNames() {
        XCTAssertEqual(TrackingQuality.initializing.displayName, "Initializing")
        XCTAssertEqual(TrackingQuality.poor.displayName, "Poor")
        XCTAssertEqual(TrackingQuality.limited.displayName, "Limited")
        XCTAssertEqual(TrackingQuality.good.displayName, "Good")
        XCTAssertEqual(TrackingQuality.excellent.displayName, "Excellent")
    }
    
    func testTrackingQualityColors() {
        XCTAssertEqual(TrackingQuality.initializing.color, "gray")
        XCTAssertEqual(TrackingQuality.poor.color, "red")
        XCTAssertEqual(TrackingQuality.limited.color, "orange")
        XCTAssertEqual(TrackingQuality.good.color, "yellow")
        XCTAssertEqual(TrackingQuality.excellent.color, "green")
    }
    
    func testTrackingQualityConfidenceThresholds() {
        XCTAssertEqual(TrackingQuality.initializing.confidenceThreshold, 0.0)
        XCTAssertEqual(TrackingQuality.poor.confidenceThreshold, 0.3)
        XCTAssertEqual(TrackingQuality.limited.confidenceThreshold, 0.5)
        XCTAssertEqual(TrackingQuality.good.confidenceThreshold, 0.7)
        XCTAssertEqual(TrackingQuality.excellent.confidenceThreshold, 0.9)
    }
    
    // MARK: - Pose Analysis Tests
    
    func testJointAngleCalculation() {
        let poseData = createSampleBodyPoseData()
        
        // Test angle calculation between shoulder joints through spine
        let angle = poseData.jointAngle(
            at: .spine3,
            using: .leftShoulder,
            and: .rightShoulder
        )
        
        XCTAssertNotNil(angle)
        // The angle should be approximately 180 degrees (π radians) for shoulders
        XCTAssertEqual(angle!, Float.pi, accuracy: 0.1)
    }
    
    func testJointAngleWithMissingJoint() {
        let poseData = createSampleBodyPoseData()
        
        // Test with a joint that doesn't exist
        let angle = poseData.jointAngle(
            at: .head,
            using: .leftShoulder,
            and: .rightShoulder
        )
        
        XCTAssertNil(angle)
    }
    
    func testCenterOfMass() {
        let poseData = createSampleBodyPoseData()
        let centerOfMass = poseData.centerOfMass
        
        XCTAssertNotNil(centerOfMass)
        // Center of mass should be somewhere around the core joints
        XCTAssertEqual(centerOfMass!.x, 0.0, accuracy: 0.1)
        XCTAssertEqual(centerOfMass!.y, 0.15, accuracy: 0.1) // Average of root (0) and spine3 (0.3)
        XCTAssertEqual(centerOfMass!.z, 0.0, accuracy: 0.1)
    }
    
    func testCenterOfMassWithNoCoreJoints() {
        let joints: [JointName: Joint3D] = [
            .leftHand: createSampleJoint3D(position: simd_float3(-1, 0, 0)),
            .rightHand: createSampleJoint3D(position: simd_float3(1, 0, 0))
        ]
        
        let poseData = BodyPoseData(
            joints: joints,
            trackingQuality: .good,
            confidence: 0.8,
            boundingBox: CGRect.zero,
            isFullBodyVisible: false
        )
        
        XCTAssertNil(poseData.centerOfMass)
    }
    
    func testPoseStability() {
        let poseData1 = createSampleBodyPoseData()
        
        // Create a similar pose with slight movement
        var joints2 = poseData1.joints
        joints2[.root] = createSampleJoint3D(position: simd_float3(0.02, 0.02, 0), confidence: 0.95)
        
        let poseData2 = BodyPoseData(
            joints: joints2,
            trackingQuality: .good,
            confidence: 0.87,
            boundingBox: CGRect(x: 0, y: 0, width: 100, height: 200),
            isFullBodyVisible: true
        )
        
        // Should be stable with small movement
        XCTAssertTrue(poseData2.isStable(comparedTo: poseData1, threshold: 0.05))
        
        // Should not be stable with larger movement
        XCTAssertFalse(poseData2.isStable(comparedTo: poseData1, threshold: 0.01))
    }
    
    func testOverallConfidence() {
        let poseData = createSampleBodyPoseData()
        let overallConfidence = poseData.overallConfidence
        
        // Should be average of all joint confidences
        let expectedConfidence = (0.95 + 0.9 + 0.85 + 0.85 + 0.8 + 0.8) / 6.0
        XCTAssertEqual(overallConfidence, expectedConfidence, accuracy: 0.001)
    }
    
    func testHasCriticalJoints() {
        let poseData = createSampleBodyPoseData()
        XCTAssertTrue(poseData.hasCriticalJoints)
        
        // Test with missing critical joint
        let incompleteJoints: [JointName: Joint3D] = [
            .leftShoulder: createSampleJoint3D(),
            .rightShoulder: createSampleJoint3D()
            // Missing root and leg joints
        ]
        
        let incompletePoseData = BodyPoseData(
            joints: incompleteJoints,
            trackingQuality: .limited,
            confidence: 0.6,
            boundingBox: CGRect.zero,
            isFullBodyVisible: false
        )
        
        XCTAssertFalse(incompletePoseData.hasCriticalJoints)
    }
    
    // MARK: - Pose Validation Tests
    
    func testPoseValidation() {
        let validPoseData = createSampleBodyPoseData()
        XCTAssertTrue(validPoseData.isValid)
        
        // Test invalid pose with low confidence
        let invalidPoseData = BodyPoseData(
            joints: [:], // No joints
            trackingQuality: .poor,
            confidence: 0.2, // Low confidence
            boundingBox: CGRect.zero,
            isFullBodyVisible: false
        )
        
        XCTAssertFalse(invalidPoseData.isValid)
    }
    
    func testAnatomicalValidation() {
        let poseData = createSampleBodyPoseData()
        XCTAssertTrue(poseData.isAnatomicallyValid)
        
        // Test with unrealistic shoulder distance
        let unrealisticJoints: [JointName: Joint3D] = [
            .leftShoulder: createSampleJoint3D(position: simd_float3(-2.0, 0, 0)), // Too far
            .rightShoulder: createSampleJoint3D(position: simd_float3(2.0, 0, 0))  // Too far
        ]
        
        let unrealisticPoseData = BodyPoseData(
            joints: unrealisticJoints,
            trackingQuality: .good,
            confidence: 0.8,
            boundingBox: CGRect.zero,
            isFullBodyVisible: true
        )
        
        XCTAssertFalse(unrealisticPoseData.isAnatomicallyValid)
    }
    
    // MARK: - Edge Cases
    
    func testEmptyJoints() {
        let poseData = BodyPoseData(
            joints: [:],
            trackingQuality: .poor,
            confidence: 0.0,
            boundingBox: CGRect.zero,
            isFullBodyVisible: false
        )
        
        XCTAssertEqual(poseData.overallConfidence, 0.0)
        XCTAssertFalse(poseData.hasCriticalJoints)
        XCTAssertFalse(poseData.isValid)
        XCTAssertNil(poseData.centerOfMass)
    }
    
    func testUntrackedJoints() {
        let untrackedJoint = Joint3D(
            position: simd_float3(0, 0, 0),
            confidence: 0.0,
            isTracked: false
        )
        
        let joints: [JointName: Joint3D] = [
            .root: untrackedJoint
        ]
        
        let poseData = BodyPoseData(
            joints: joints,
            trackingQuality: .poor,
            confidence: 0.1,
            boundingBox: CGRect.zero,
            isFullBodyVisible: false
        )
        
        XCTAssertFalse(poseData.hasCriticalJoints)
    }
}
