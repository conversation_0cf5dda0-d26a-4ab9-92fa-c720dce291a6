import XCTest
import Combine
@testable import MotionFitPro

// MARK: - Test Base Class

/// Base class for all unit tests with common setup and utilities
class TestBase: XCTestCase {
    
    // MARK: - Properties
    
    var cancellables: Set<AnyCancellable>!
    var mockDIContainer: MockDIContainer!
    var testLogger: TestLogger!
    
    // MARK: - Setup and Teardown
    
    override func setUp() {
        super.setUp()
        
        cancellables = Set<AnyCancellable>()
        mockDIContainer = MockDIContainer()
        testLogger = TestLogger()
        
        setupTestEnvironment()
    }
    
    override func tearDown() {
        cancellables?.removeAll()
        cancellables = nil
        mockDIContainer = nil
        testLogger = nil
        
        cleanupTestEnvironment()
        
        super.tearDown()
    }
    
    // MARK: - Test Environment Setup
    
    private func setupTestEnvironment() {
        // Configure test-specific settings
        setupMockServices()
        setupTestData()
    }
    
    private func cleanupTestEnvironment() {
        // Clean up any test artifacts
        clearTestData()
        resetMockServices()
    }
    
    private func setupMockServices() {
        // Register mock services with the DI container
        mockDIContainer.registerMockServices()
    }
    
    private func resetMockServices() {
        // Reset all mock services to their initial state
        mockDIContainer.resetAllMocks()
    }
    
    private func setupTestData() {
        // Create any necessary test data
    }
    
    private func clearTestData() {
        // Clean up test data
    }
    
    // MARK: - Test Utilities
    
    /// Wait for an expectation with a default timeout
    func wait(for expectations: [XCTestExpectation], timeout: TimeInterval = 5.0) {
        wait(for: expectations, timeout: timeout)
    }
    
    /// Create an expectation for async testing
    func expectation(description: String) -> XCTestExpectation {
        return XCTestExpectation(description: description)
    }
    
    /// Wait for a publisher to emit a value
    func waitForPublisher<T: Publisher>(
        _ publisher: T,
        timeout: TimeInterval = 5.0,
        file: StaticString = #file,
        line: UInt = #line
    ) throws -> T.Output where T.Failure == Never {
        
        var result: T.Output?
        let expectation = XCTestExpectation(description: "Publisher expectation")
        
        publisher
            .sink { value in
                result = value
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        wait(for: [expectation], timeout: timeout)
        
        guard let unwrappedResult = result else {
            XCTFail("Publisher did not emit a value within timeout", file: file, line: line)
            throw TestError.publisherTimeout
        }
        
        return unwrappedResult
    }
    
    /// Wait for a publisher to complete
    func waitForCompletion<T: Publisher>(
        of publisher: T,
        timeout: TimeInterval = 5.0,
        file: StaticString = #file,
        line: UInt = #line
    ) throws where T.Failure: Error {
        
        var completionResult: Subscribers.Completion<T.Failure>?
        let expectation = XCTestExpectation(description: "Completion expectation")
        
        publisher
            .sink(
                receiveCompletion: { completion in
                    completionResult = completion
                    expectation.fulfill()
                },
                receiveValue: { _ in }
            )
            .store(in: &cancellables)
        
        wait(for: [expectation], timeout: timeout)
        
        guard let completion = completionResult else {
            XCTFail("Publisher did not complete within timeout", file: file, line: line)
            throw TestError.publisherTimeout
        }
        
        switch completion {
        case .finished:
            break
        case .failure(let error):
            throw error
        }
    }
    
    /// Assert that two floating point values are approximately equal
    func XCTAssertApproximatelyEqual(
        _ expression1: Double,
        _ expression2: Double,
        accuracy: Double = 0.001,
        _ message: String = "",
        file: StaticString = #file,
        line: UInt = #line
    ) {
        XCTAssertEqual(expression1, expression2, accuracy: accuracy, message, file: file, line: line)
    }
    
    /// Assert that two floating point values are approximately equal
    func XCTAssertApproximatelyEqual(
        _ expression1: Float,
        _ expression2: Float,
        accuracy: Float = 0.001,
        _ message: String = "",
        file: StaticString = #file,
        line: UInt = #line
    ) {
        XCTAssertEqual(expression1, expression2, accuracy: accuracy, message, file: file, line: line)
    }
    
    /// Assert that a value is within a range
    func XCTAssertInRange<T: Comparable>(
        _ value: T,
        min: T,
        max: T,
        _ message: String = "",
        file: StaticString = #file,
        line: UInt = #line
    ) {
        XCTAssertGreaterThanOrEqual(value, min, "Value \(value) is below minimum \(min). \(message)", file: file, line: line)
        XCTAssertLessThanOrEqual(value, max, "Value \(value) is above maximum \(max). \(message)", file: file, line: line)
    }
    
    /// Assert that an async operation throws a specific error
    func XCTAssertThrowsError<T>(
        _ expression: @autoclosure () async throws -> T,
        _ message: String = "",
        file: StaticString = #file,
        line: UInt = #line,
        _ errorHandler: (_ error: Error) -> Void = { _ in }
    ) async {
        do {
            _ = try await expression()
            XCTFail("Expected error to be thrown. \(message)", file: file, line: line)
        } catch {
            errorHandler(error)
        }
    }
    
    /// Assert that an async operation does not throw
    func XCTAssertNoThrow<T>(
        _ expression: @autoclosure () async throws -> T,
        _ message: String = "",
        file: StaticString = #file,
        line: UInt = #line
    ) async -> T? {
        do {
            return try await expression()
        } catch {
            XCTFail("Unexpected error thrown: \(error). \(message)", file: file, line: line)
            return nil
        }
    }
    
    // MARK: - Mock Data Factories
    
    func createMockUserProfile() -> UserProfile {
        return UserProfile(
            name: "Test User",
            age: 25,
            height: 175.0,
            weight: 70.0,
            fitnessLevel: .intermediate,
            goals: [.strength, .endurance]
        )
    }
    
    func createMockWorkoutSession() -> WorkoutSession {
        let session = WorkoutSession(userID: UUID(), workoutType: "test")
        session.startTime = Date()
        return session
    }
    
    func createMockBodyPoseData() -> BodyPoseData {
        var joints: [JointName: Joint3D] = [:]
        
        for jointName in JointName.allCases {
            joints[jointName] = Joint3D(
                position: simd_float3(
                    Float.random(in: -1...1),
                    Float.random(in: -1...1),
                    Float.random(in: -1...1)
                ),
                confidence: Float.random(in: 0.7...1.0)
            )
        }
        
        return BodyPoseData(
            joints: joints,
            trackingQuality: .good,
            confidence: 0.8,
            boundingBox: CGRect(x: 0, y: 0, width: 100, height: 200),
            isFullBodyVisible: true
        )
    }
    
    func createMockExerciseAnalysis() -> ExerciseAnalysis {
        return ExerciseAnalysis(
            exercise: .squat,
            formScore: 0.8,
            repPhase: .middle,
            poseData: createMockBodyPoseData(),
            confidence: 0.8
        )
    }
    
    func createMockFormAnalysis() -> FormAnalysis {
        return FormAnalysis(
            overallScore: 0.8,
            scores: [
                .jointAlignment: 0.9,
                .rangeOfMotion: 0.7,
                .stability: 0.8
            ],
            issues: [],
            recommendations: ["Keep up the good work!"],
            exerciseType: .squat
        )
    }
    
    // MARK: - Performance Testing
    
    func measurePerformance(of block: () throws -> Void) {
        measure {
            do {
                try block()
            } catch {
                XCTFail("Performance test failed with error: \(error)")
            }
        }
    }
    
    func measureAsyncPerformance(of block: () async throws -> Void) async {
        await measureAsync {
            do {
                try await block()
            } catch {
                XCTFail("Async performance test failed with error: \(error)")
            }
        }
    }
    
    // MARK: - Memory Testing
    
    func assertNoMemoryLeaks<T: AnyObject>(
        _ instance: T,
        file: StaticString = #file,
        line: UInt = #line
    ) {
        addTeardownBlock { [weak instance] in
            XCTAssertNil(instance, "Memory leak detected", file: file, line: line)
        }
    }
}

// MARK: - Test Errors

enum TestError: Error {
    case publisherTimeout
    case unexpectedValue
    case mockNotConfigured
    case testDataMissing
}

// MARK: - Test Logger

final class TestLogger: LoggerServiceProtocol {
    private var logs: [LogEntry] = []
    
    func log(_ message: String, level: LogLevel, category: LogCategory, file: String, function: String, line: Int) {
        let entry = LogEntry(
            message: message,
            level: level,
            category: category,
            timestamp: Date(),
            file: file,
            function: function,
            line: line
        )
        logs.append(entry)
    }
    
    func getLogs() -> [LogEntry] {
        return logs
    }
    
    func clearLogs() {
        logs.removeAll()
    }
    
    func getLogsForCategory(_ category: LogCategory) -> [LogEntry] {
        return logs.filter { $0.category == category }
    }
    
    func getLogsForLevel(_ level: LogLevel) -> [LogEntry] {
        return logs.filter { $0.level == level }
    }
}

struct LogEntry {
    let message: String
    let level: LogLevel
    let category: LogCategory
    let timestamp: Date
    let file: String
    let function: String
    let line: Int
}
