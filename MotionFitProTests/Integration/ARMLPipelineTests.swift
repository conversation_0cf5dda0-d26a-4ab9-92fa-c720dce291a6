import XCTest
import ARKit
import CoreML
import simd
import Combine
@testable import MotionFitPro

class ARMLPipelineTests: XCTestCase {
    
    var arSessionManager: ARSessionManager!
    var mlProcessingManager: MLProcessingManager!
    var repCountingEngine: RepCountingEngine!
    var performanceProfiler: PerformanceProfiler!
    
    var cancellables = Set<AnyCancellable>()
    
    override func setUpWithError() throws {
        super.setUp()
        
        // Initialize components with test configurations
        arSessionManager = ARSessionManager.shared
        mlProcessingManager = MLProcessingManager.shared
        repCountingEngine = RepCountingEngine()
        performanceProfiler = PerformanceProfiler.shared
        
        // Configure for testing
        setupTestEnvironment()
    }
    
    override func tearDownWithError() throws {
        cancellables.removeAll()
        cleanupTestEnvironment()
        
        arSessionManager = nil
        mlProcessingManager = nil
        repCountingEngine = nil
        performanceProfiler = nil
        
        super.tearDown()
    }
    
    // MARK: - Full Pipeline Tests
    
    func testFullPipeline_SquatExercise() async throws {
        // Given: AR session is running with squat exercise
        let expectation = XCTestExpectation(description: "Full squat pipeline processing")
        let exercise = ExerciseType.squat
        
        var detectedReps = 0
        var processedFrames = 0
        var mlInferenceTimes: [TimeInterval] = []
        
        // Set up pipeline monitoring
        setupPipelineMonitoring(
            repCountCallback: { reps in
                detectedReps = reps
            },
            frameProcessedCallback: { inferenceTime in
                processedFrames += 1
                mlInferenceTimes.append(inferenceTime)
                
                if processedFrames >= 10 {
                    expectation.fulfill()
                }
            }
        )
        
        // When: Simulate AR frames with squat movement
        try await startARSession()
        repCountingEngine.startCounting(for: exercise)
        
        // Simulate 10 frames of squat movement
        for frameIndex in 0..<10 {
            let squatPhase = SquatPhase.fromProgress(Double(frameIndex) / 9.0)
            let arFrame = createMockARFrame(for: squatPhase, frameIndex: frameIndex)
            
            await processARFrame(arFrame, for: exercise)
            
            // Small delay to simulate real-time processing
            try await Task.sleep(nanoseconds: 33_000_000) // ~30 FPS
        }
        
        // Then: Pipeline should process frames successfully
        await fulfillment(of: [expectation], timeout: 5.0)
        
        XCTAssertGreaterThan(processedFrames, 0, "Should process AR frames")
        XCTAssertTrue(mlInferenceTimes.allSatisfy { $0 < 0.1 }, "ML inference should be under 100ms")
        
        // Should detect at least partial movement
        XCTAssertGreaterThanOrEqual(detectedReps, 0, "Should track movement progress")
    }
    
    func testFullPipeline_MultipleExerciseTypes() async throws {
        // Given: Testing pipeline with different exercise types
        let exercises: [ExerciseType] = [.squat, .pushUp, .plank]
        var results: [ExerciseType: PipelineResult] = [:]
        
        for exercise in exercises {
            // When: Process each exercise type
            let result = try await processSingleExercise(exercise)
            results[exercise] = result
            
            // Brief pause between exercises
            try await Task.sleep(nanoseconds: 500_000_000) // 500ms
        }
        
        // Then: All exercises should process successfully
        for (exercise, result) in results {
            XCTAssertTrue(result.success, "Exercise \(exercise) should process successfully")
            XCTAssertLessThan(result.averageInferenceTime, 0.1, "Average inference time should be under 100ms for \(exercise)")
            XCTAssertGreaterThan(result.frameProcessingRate, 20, "Should maintain at least 20 FPS for \(exercise)")
        }
    }
    
    func testPipeline_PerformanceUnderLoad() async throws {
        // Given: High-frequency frame processing
        let totalFrames = 100
        let targetFPS = 60
        
        let startTime = Date()
        var processedFrames = 0
        var droppedFrames = 0
        
        // When: Process frames at high frequency
        try await startARSession()
        
        await withTaskGroup(of: Void.self) { group in
            for frameIndex in 0..<totalFrames {
                group.addTask {
                    let arFrame = self.createMockARFrame(for: .middle, frameIndex: frameIndex)
                    
                    do {
                        await self.processARFrame(arFrame, for: .squat)
                        processedFrames += 1
                    } catch {
                        droppedFrames += 1
                    }
                }
                
                // Maintain target FPS timing
                let frameDelay = 1.0 / Double(targetFPS)
                try? await Task.sleep(nanoseconds: UInt64(frameDelay * 1_000_000_000))
            }
        }
        
        let totalTime = Date().timeIntervalSince(startTime)
        let actualFPS = Double(processedFrames) / totalTime
        
        // Then: Should maintain reasonable performance
        XCTAssertGreaterThan(actualFPS, 30, "Should maintain at least 30 FPS under load")
        XCTAssertLessThan(Double(droppedFrames) / Double(totalFrames), 0.1, "Should drop less than 10% of frames")
    }
    
    // MARK: - Component Integration Tests
    
    func testARSessionToMLProcessing_DataFlow() async throws {
        // Given: AR session producing body pose data
        let expectation = XCTestExpectation(description: "AR to ML data flow")
        var receivedPoseData: BodyPoseData?
        
        // Set up data flow monitoring
        mlProcessingManager.onPoseDataReceived = { poseData in
            receivedPoseData = poseData
            expectation.fulfill()
        }
        
        // When: AR session produces a frame with body pose
        try await startARSession()
        let arFrame = createMockARFrameWithBodyPose()
        await arSessionManager.processFrame(arFrame)
        
        // Then: ML processing should receive pose data
        await fulfillment(of: [expectation], timeout: 2.0)
        
        XCTAssertNotNil(receivedPoseData, "ML processing should receive pose data from AR")
        XCTAssertGreaterThan(receivedPoseData?.confidence ?? 0, 0.5, "Pose data should have reasonable confidence")
        XCTAssertFalse(receivedPoseData?.joints.isEmpty ?? true, "Pose data should contain joint information")
    }
    
    func testMLProcessingToRepCounting_Integration() async throws {
        // Given: ML processing produces exercise analysis
        let expectation = XCTestExpectation(description: "ML to rep counting integration")
        let exercise = ExerciseType.squat
        var repCount = 0
        
        repCountingEngine.startCounting(for: exercise)
        
        // Monitor rep counting
        repCountingEngine.onRepDetected = { count in
            repCount = count
            if count > 0 {
                expectation.fulfill()
            }
        }
        
        // When: Process a complete squat sequence through ML
        let squatSequence = createCompleteSquatSequence()
        
        for (index, poseData) in squatSequence.enumerated() {
            let analysis = await mlProcessingManager.analyzeExercise(poseData, exerciseType: exercise)
            repCountingEngine.processAnalysis(analysis, for: exercise)
            
            // Simulate real-time processing
            try await Task.sleep(nanoseconds: 100_000_000) // 100ms between frames
        }
        
        // Then: Rep counting should detect the complete movement
        await fulfillment(of: [expectation], timeout: 5.0)
        XCTAssertGreaterThan(repCount, 0, "Should detect at least one repetition")
    }
    
    func testRealTimePerformanceMonitoring() async throws {
        // Given: Performance monitoring is active
        performanceProfiler.startMonitoring()
        
        let testDuration: TimeInterval = 5.0
        let startTime = Date()
        
        // When: Run pipeline for sustained period
        try await startARSession()
        repCountingEngine.startCounting(for: .squat)
        
        while Date().timeIntervalSince(startTime) < testDuration {
            let arFrame = createMockARFrame(for: .middle, frameIndex: 0)
            await processARFrame(arFrame, for: .squat)
            
            try await Task.sleep(nanoseconds: 33_000_000) // ~30 FPS
        }
        
        performanceProfiler.stopMonitoring()
        
        // Then: Performance should remain stable
        let metrics = performanceProfiler.currentMetrics
        
        XCTAssertGreaterThan(metrics.fps, 25, "Should maintain at least 25 FPS")
        XCTAssertLessThan(metrics.memoryUsage.usagePercentage, 80, "Memory usage should stay under 80%")
        XCTAssertLessThan(metrics.cpuUsage, 85, "CPU usage should stay under 85%")
    }
    
    // MARK: - Error Handling and Recovery Tests
    
    func testPipeline_ARSessionInterruption() async throws {
        // Given: AR session is running normally
        try await startARSession()
        let exercise = ExerciseType.squat
        repCountingEngine.startCounting(for: exercise)
        
        // When: AR session is interrupted
        await arSessionManager.pauseSession()
        
        // Process some frames during interruption
        for _ in 0..<5 {
            let arFrame = createMockARFrame(for: .middle, frameIndex: 0)
            await processARFrame(arFrame, for: exercise)
        }
        
        // Resume session
        try await arSessionManager.resumeSession()
        
        // Then: Pipeline should handle interruption gracefully
        XCTAssertFalse(arSessionManager.isSessionRunning, "Session should handle pause/resume")
        
        // Should be able to continue processing after resume
        let arFrame = createMockARFrame(for: .top, frameIndex: 0)
        await processARFrame(arFrame, for: exercise)
        
        XCTAssertTrue(true, "Pipeline should continue processing after interruption")
    }
    
    func testPipeline_LowConfidencePoseData() async throws {
        // Given: AR session producing low confidence pose data
        let exercise = ExerciseType.squat
        repCountingEngine.startCounting(for: exercise)
        
        var lowConfidenceFrames = 0
        var highConfidenceFrames = 0
        
        // When: Process mix of high and low confidence frames
        for i in 0..<20 {
            let confidence: Float = i % 4 == 0 ? 0.3 : 0.9 // Every 4th frame is low confidence
            let poseData = createMockPoseData(confidence: confidence)
            
            let analysis = await mlProcessingManager.analyzeExercise(poseData, exerciseType: exercise)
            
            if analysis.confidence < 0.5 {
                lowConfidenceFrames += 1
            } else {
                highConfidenceFrames += 1
            }
            
            repCountingEngine.processAnalysis(analysis, for: exercise)
        }
        
        // Then: Pipeline should handle low confidence data appropriately
        XCTAssertGreaterThan(lowConfidenceFrames, 0, "Should have processed low confidence frames")
        XCTAssertGreaterThan(highConfidenceFrames, 0, "Should have processed high confidence frames")
        
        // Rep counting should be conservative with low confidence data
        let repCount = repCountingEngine.getCurrentRepCount()
        XCTAssertLessThanOrEqual(repCount, 2, "Should be conservative with mixed confidence data")
    }
    
    func testPipeline_MLModelLoadFailure() async throws {
        // Given: ML model fails to load
        let expectation = XCTestExpectation(description: "ML model failure handling")
        
        // Simulate ML model failure
        mlProcessingManager.simulateModelFailure(true)
        
        var errorReceived = false
        mlProcessingManager.onError = { error in
            errorReceived = true
            expectation.fulfill()
        }
        
        // When: Attempt to process pose data
        let poseData = createMockPoseData(confidence: 0.9)
        
        do {
            _ = try await mlProcessingManager.analyzeExercise(poseData, exerciseType: .squat)
        } catch {
            // Expected to throw
        }
        
        // Then: Error should be handled gracefully
        await fulfillment(of: [expectation], timeout: 2.0)
        XCTAssertTrue(errorReceived, "Should receive error notification")
        
        // Cleanup
        mlProcessingManager.simulateModelFailure(false)
    }
    
    // MARK: - Memory and Resource Management Tests
    
    func testPipeline_MemoryManagement() async throws {
        // Given: Initial memory state
        let initialMemory = getCurrentMemoryUsage()
        
        // When: Process many frames
        try await startARSession()
        
        for i in 0..<100 {
            let arFrame = createMockARFrame(for: .middle, frameIndex: i)
            await processARFrame(arFrame, for: .squat)
            
            // Periodic memory checks
            if i % 20 == 0 {
                let currentMemory = getCurrentMemoryUsage()
                let memoryIncrease = currentMemory - initialMemory
                
                // Memory should not grow excessively
                XCTAssertLessThan(memoryIncrease, 100 * 1024 * 1024, "Memory increase should be under 100MB")
            }
        }
        
        // Force memory cleanup
        await performMemoryCleanup()
        
        // Then: Memory should be managed properly
        let finalMemory = getCurrentMemoryUsage()
        let totalIncrease = finalMemory - initialMemory
        
        XCTAssertLessThan(totalIncrease, 50 * 1024 * 1024, "Total memory increase should be under 50MB")
    }
    
    func testPipeline_ConcurrentExerciseProcessing() async throws {
        // Given: Multiple concurrent exercise processing requests
        let exercises: [ExerciseType] = [.squat, .pushUp, .plank]
        
        // When: Process different exercises concurrently
        await withTaskGroup(of: Void.self) { group in
            for exercise in exercises {
                group.addTask {
                    do {
                        let _ = try await self.processSingleExercise(exercise)
                    } catch {
                        XCTFail("Exercise \(exercise) processing failed: \(error)")
                    }
                }
            }
        }
        
        // Then: All exercises should complete without conflicts
        XCTAssertTrue(true, "Concurrent exercise processing should complete successfully")
    }
    
    // MARK: - Helper Methods
    
    private func setupTestEnvironment() {
        // Configure components for testing
        arSessionManager.setTestMode(true)
        mlProcessingManager.setTestMode(true)
        performanceProfiler.enableTestMetrics()
    }
    
    private func cleanupTestEnvironment() {
        arSessionManager.setTestMode(false)
        mlProcessingManager.setTestMode(false)
        performanceProfiler.disableTestMetrics()
    }
    
    private func setupPipelineMonitoring(
        repCountCallback: @escaping (Int) -> Void,
        frameProcessedCallback: @escaping (TimeInterval) -> Void
    ) {
        repCountingEngine.onRepCountChanged = repCountCallback
        mlProcessingManager.onFrameProcessed = frameProcessedCallback
    }
    
    private func startARSession() async throws {
        let configuration = ARBodyTrackingConfiguration()
        await arSessionManager.startSession(with: configuration)
        
        // Wait for session to initialize
        try await Task.sleep(nanoseconds: 500_000_000) // 500ms
    }
    
    private func processARFrame(_ arFrame: MockARFrame, for exercise: ExerciseType) async {
        let startTime = Date()
        
        // Simulate AR frame processing
        await arSessionManager.processFrame(arFrame)
        
        // Extract pose data and process through ML
        if let poseData = extractPoseData(from: arFrame) {
            let analysis = await mlProcessingManager.analyzeExercise(poseData, exerciseType: exercise)
            repCountingEngine.processAnalysis(analysis, for: exercise)
        }
        
        let processingTime = Date().timeIntervalSince(startTime)
        performanceProfiler.recordFrameTime(processingTime, for: .arKit)
    }
    
    private func processSingleExercise(_ exercise: ExerciseType) async throws -> PipelineResult {
        let startTime = Date()
        var inferenceTimes: [TimeInterval] = []
        var processedFrames = 0
        
        repCountingEngine.startCounting(for: exercise)
        
        for frameIndex in 0..<30 { // Process 30 frames
            let arFrame = createMockARFrame(for: .middle, frameIndex: frameIndex)
            
            let inferenceStartTime = Date()
            await processARFrame(arFrame, for: exercise)
            let inferenceTime = Date().timeIntervalSince(inferenceStartTime)
            
            inferenceTimes.append(inferenceTime)
            processedFrames += 1
        }
        
        let totalTime = Date().timeIntervalSince(startTime)
        let averageInferenceTime = inferenceTimes.reduce(0, +) / Double(inferenceTimes.count)
        let frameProcessingRate = Double(processedFrames) / totalTime
        
        return PipelineResult(
            success: true,
            averageInferenceTime: averageInferenceTime,
            frameProcessingRate: frameProcessingRate,
            totalFrames: processedFrames
        )
    }
    
    private func createMockARFrame(for phase: SquatPhase, frameIndex: Int) -> MockARFrame {
        let poseData = createSquatPoseData(for: phase)
        return MockARFrame(
            timestamp: Date(),
            bodyPoseData: poseData,
            frameIndex: frameIndex
        )
    }
    
    private func createMockARFrameWithBodyPose() -> MockARFrame {
        let poseData = createMockPoseData(confidence: 0.9)
        return MockARFrame(
            timestamp: Date(),
            bodyPoseData: poseData,
            frameIndex: 0
        )
    }
    
    private func createCompleteSquatSequence() -> [BodyPoseData] {
        let phases: [SquatPhase] = [.top, .descending, .middle, .bottom, .middle, .ascending, .top]
        return phases.map { createSquatPoseData(for: $0) }
    }
    
    private func createSquatPoseData(for phase: SquatPhase) -> BodyPoseData {
        let hipHeight: Float
        let kneeAngle: Float
        
        switch phase {
        case .top:
            hipHeight = 1.0
            kneeAngle = Float.pi * 0.9
        case .descending:
            hipHeight = 0.8
            kneeAngle = Float.pi * 0.7
        case .middle:
            hipHeight = 0.7
            kneeAngle = Float.pi * 0.6
        case .bottom:
            hipHeight = 0.55
            kneeAngle = Float.pi * 0.45
        case .ascending:
            hipHeight = 0.8
            kneeAngle = Float.pi * 0.7
        }
        
        var joints: [String: Joint] = [:]
        joints["root"] = Joint(position: simd_float3(0, hipHeight, 0), confidence: 0.95)
        joints["left_leg"] = Joint(position: simd_float3(-0.1, hipHeight * 0.6, 0), confidence: 0.95)
        joints["right_leg"] = Joint(position: simd_float3(0.1, hipHeight * 0.6, 0), confidence: 0.95)
        joints["left_foot"] = Joint(position: simd_float3(-0.1, 0, 0), confidence: 0.9)
        joints["right_foot"] = Joint(position: simd_float3(0.1, 0, 0), confidence: 0.9)
        joints["spine_7"] = Joint(position: simd_float3(0, hipHeight + 0.3, 0), confidence: 0.95)
        joints["head"] = Joint(position: simd_float3(0, hipHeight + 0.7, 0), confidence: 0.95)
        
        return BodyPoseData(joints: joints, confidence: 0.9, timestamp: Date())
    }
    
    private func createMockPoseData(confidence: Float) -> BodyPoseData {
        var joints: [String: Joint] = [:]
        
        joints["head"] = Joint(position: simd_float3(0, 1.7, 0), confidence: confidence)
        joints["spine_7"] = Joint(position: simd_float3(0, 1.4, 0), confidence: confidence)
        joints["root"] = Joint(position: simd_float3(0, 1.0, 0), confidence: confidence)
        joints["left_leg"] = Joint(position: simd_float3(-0.1, 0.5, 0), confidence: confidence)
        joints["right_leg"] = Joint(position: simd_float3(0.1, 0.5, 0), confidence: confidence)
        joints["left_foot"] = Joint(position: simd_float3(-0.1, 0, 0), confidence: confidence)
        joints["right_foot"] = Joint(position: simd_float3(0.1, 0, 0), confidence: confidence)
        
        return BodyPoseData(joints: joints, confidence: confidence, timestamp: Date())
    }
    
    private func extractPoseData(from arFrame: MockARFrame) -> BodyPoseData? {
        return arFrame.bodyPoseData
    }
    
    private func getCurrentMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let kerr = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        return kerr == KERN_SUCCESS ? info.resident_size : 0
    }
    
    private func performMemoryCleanup() async {
        // Force memory cleanup
        MemoryOptimizationManager.shared.optimizeMemoryUsage()
        
        // Give time for cleanup to complete
        try? await Task.sleep(nanoseconds: 500_000_000) // 500ms
    }
}

// MARK: - Supporting Types

enum SquatPhase {
    case top
    case descending
    case middle
    case bottom
    case ascending
    
    static func fromProgress(_ progress: Double) -> SquatPhase {
        switch progress {
        case 0..<0.2:
            return .top
        case 0.2..<0.4:
            return .descending
        case 0.4..<0.6:
            return .bottom
        case 0.6..<0.8:
            return .ascending
        default:
            return .top
        }
    }
}

struct PipelineResult {
    let success: Bool
    let averageInferenceTime: TimeInterval
    let frameProcessingRate: Double
    let totalFrames: Int
}

struct MockARFrame {
    let timestamp: Date
    let bodyPoseData: BodyPoseData?
    let frameIndex: Int
}

// MARK: - Mock Extensions for Testing

extension ARSessionManager {
    func setTestMode(_ enabled: Bool) {
        // Configure for testing
    }
    
    func processFrame(_ frame: MockARFrame) async {
        // Simulate frame processing
        try? await Task.sleep(nanoseconds: 10_000_000) // 10ms processing time
    }
    
    func pauseSession() async {
        // Simulate session pause
    }
    
    func resumeSession() async throws {
        // Simulate session resume
    }
    
    var isSessionRunning: Bool {
        return false // Mock implementation
    }
    
    func startSession(with configuration: ARConfiguration) async {
        // Mock session start
    }
}

extension MLProcessingManager {
    var onPoseDataReceived: ((BodyPoseData) -> Void)?
    var onFrameProcessed: ((TimeInterval) -> Void)?
    var onError: ((Error) -> Void)?
    
    func setTestMode(_ enabled: Bool) {
        // Configure for testing
    }
    
    func analyzeExercise(_ poseData: BodyPoseData, exerciseType: ExerciseType) async -> ExerciseAnalysis {
        let startTime = Date()
        
        // Simulate ML processing time
        try? await Task.sleep(nanoseconds: UInt64.random(in: 20_000_000...80_000_000)) // 20-80ms
        
        let processingTime = Date().timeIntervalSince(startTime)
        onFrameProcessed?(processingTime)
        
        if testModeFailure {
            onError?(MLProcessingError.modelLoadFailed)
            throw MLProcessingError.modelLoadFailed
        }
        
        // Return mock analysis
        return ExerciseAnalysis(
            exercise: exerciseType,
            timestamp: Date(),
            formScore: Double.random(in: 70...95),
            repPhase: .middle,
            poseData: poseData,
            detectedIssues: [],
            safetyRisk: .none,
            confidence: poseData.confidence
        )
    }
    
    private var testModeFailure = false
    
    func simulateModelFailure(_ shouldFail: Bool) {
        testModeFailure = shouldFail
    }
}

extension RepCountingEngine {
    var onRepCountChanged: ((Int) -> Void)?
    var onRepDetected: ((Int) -> Void)?
    
    func processAnalysis(_ analysis: ExerciseAnalysis, for exercise: ExerciseType) {
        // Process the analysis and update rep count
        // This would integrate with the existing processPose method
        
        onRepCountChanged?(getCurrentRepCount())
        
        if getCurrentRepCount() > 0 {
            onRepDetected?(getCurrentRepCount())
        }
    }
}

extension PerformanceProfiler {
    func enableTestMetrics() {
        // Enable additional metrics for testing
    }
    
    func disableTestMetrics() {
        // Disable test-specific metrics
    }
}

enum MLProcessingError: Error {
    case modelLoadFailed
    case inferenceTimeout
    case invalidInput
}