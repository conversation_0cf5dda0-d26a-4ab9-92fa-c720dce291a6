import XCTest
import CloudKit
import Combine
@testable import MotionFitPro

class CloudKitSyncTests: XCTestCase {
    
    var cloudKitManager: CloudKitManager!
    var workoutRepository: WorkoutRepository!
    var syncCoordinator: CloudSyncCoordinator!
    var backgroundProcessor: BackgroundProcessingOptimizer!
    
    var cancellables = Set<AnyCancellable>()
    var testContainer: CKContainer!
    
    override func setUpWithError() throws {
        super.setUp()
        
        // Use test container to avoid affecting production data
        testContainer = CKContainer(identifier: "iCloud.com.motionfitpro.test")
        
        cloudKitManager = CloudKitManager(container: testContainer)
        workoutRepository = WorkoutRepository(cloudKitManager: cloudKitManager)
        syncCoordinator = CloudSyncCoordinator(cloudKitManager: cloudKitManager)
        backgroundProcessor = BackgroundProcessingOptimizer.shared
        
        // Configure for testing
        setupTestEnvironment()
    }
    
    override func tearDownWithError() throws {
        cancellables.removeAll()
        cleanupTestData()
        
        cloudKitManager = nil
        workoutRepository = nil
        syncCoordinator = nil
        backgroundProcessor = nil
        
        super.tearDown()
    }
    
    // MARK: - Basic Sync Tests
    
    func testBasicWorkoutSync_UploadAndDownload() async throws {
        // Given: A workout session to sync
        let workout = createTestWorkout()
        
        // When: Upload workout to CloudKit
        let uploadResult = try await workoutRepository.save(workout)
        XCTAssertTrue(uploadResult.success, "Workout upload should succeed")
        
        // Clear local data to simulate fresh install
        try await workoutRepository.clearLocalData()
        
        // Download from CloudKit
        let downloadedWorkouts = try await workoutRepository.fetchAllWorkouts()
        
        // Then: Downloaded workout should match uploaded workout
        XCTAssertEqual(downloadedWorkouts.count, 1, "Should download one workout")
        
        let downloadedWorkout = downloadedWorkouts.first!
        XCTAssertEqual(downloadedWorkout.id, workout.id, "Workout IDs should match")
        XCTAssertEqual(downloadedWorkout.exerciseType, workout.exerciseType, "Exercise types should match")
        XCTAssertEqual(downloadedWorkout.repCount, workout.repCount, "Rep counts should match")
    }
    
    func testUserPreferencesSync() async throws {
        // Given: User preferences to sync
        let preferences = createTestUserPreferences()
        
        // When: Upload preferences
        try await cloudKitManager.saveUserPreferences(preferences)
        
        // Clear local preferences
        UserDefaults.standard.removeObject(forKey: "UserPreferences")
        
        // Download preferences
        let downloadedPreferences = try await cloudKitManager.fetchUserPreferences()
        
        // Then: Preferences should match
        XCTAssertNotNil(downloadedPreferences, "Should download preferences")
        XCTAssertEqual(downloadedPreferences?.coachingPersonality, preferences.coachingPersonality)
        XCTAssertEqual(downloadedPreferences?.targetRepsPerSet, preferences.targetRepsPerSet)
    }
    
    func testAchievementsSync() async throws {
        // Given: Achievement data to sync
        let achievements = createTestAchievements()
        
        // When: Upload achievements
        for achievement in achievements {
            try await cloudKitManager.saveAchievement(achievement)
        }
        
        // Download achievements
        let downloadedAchievements = try await cloudKitManager.fetchAchievements()
        
        // Then: All achievements should be synced
        XCTAssertEqual(downloadedAchievements.count, achievements.count, "All achievements should sync")
        
        for originalAchievement in achievements {
            let found = downloadedAchievements.contains { $0.id == originalAchievement.id }
            XCTAssertTrue(found, "Achievement \(originalAchievement.id) should be synced")
        }
    }
    
    // MARK: - Conflict Resolution Tests
    
    func testConflictResolution_WorkoutModified() async throws {
        // Given: A workout that exists on both local and cloud with modifications
        let baseWorkout = createTestWorkout()
        
        // Upload base workout
        try await workoutRepository.save(baseWorkout)
        
        // Simulate modifications on two different devices
        var localWorkout = baseWorkout
        localWorkout.repCount = 15
        localWorkout.modifiedDate = Date()
        
        var cloudWorkout = baseWorkout
        cloudWorkout.notes = "Modified on another device"
        cloudWorkout.modifiedDate = Date().addingTimeInterval(60) // 1 minute later
        
        // When: Conflict occurs during sync
        let conflictResolver = CloudKitConflictResolver()
        let resolvedWorkout = try await conflictResolver.resolveWorkoutConflict(
            local: localWorkout,
            cloud: cloudWorkout
        )
        
        // Then: Should resolve using last-modified-wins strategy
        XCTAssertEqual(resolvedWorkout.notes, cloudWorkout.notes, "Should use cloud version (newer)")
        XCTAssertEqual(resolvedWorkout.repCount, cloudWorkout.repCount, "Should use cloud rep count")
    }
    
    func testConflictResolution_UserPreferences() async throws {
        // Given: Conflicting user preferences
        let localPrefs = createTestUserPreferences()
        localPrefs.coachingPersonality = .motivational
        localPrefs.modifiedDate = Date()
        
        let cloudPrefs = createTestUserPreferences()
        cloudPrefs.coachingPersonality = .supportive
        cloudPrefs.targetRepsPerSet = 20
        cloudPrefs.modifiedDate = Date().addingTimeInterval(-30) // 30 seconds earlier
        
        // When: Resolving preference conflicts
        let conflictResolver = CloudKitConflictResolver()
        let resolvedPrefs = try await conflictResolver.resolvePreferencesConflict(
            local: localPrefs,
            cloud: cloudPrefs
        )
        
        // Then: Should merge preferences intelligently
        XCTAssertEqual(resolvedPrefs.coachingPersonality, localPrefs.coachingPersonality, "Should use local (newer) personality")
        XCTAssertEqual(resolvedPrefs.targetRepsPerSet, cloudPrefs.targetRepsPerSet, "Should merge other settings")
    }
    
    // MARK: - Network Interruption Tests
    
    func testSyncResilience_NetworkInterruption() async throws {
        // Given: A workout to sync during network issues
        let workout = createTestWorkout()
        let expectation = XCTestExpectation(description: "Sync completion after network recovery")
        
        var syncAttempts = 0
        var finalResult: SyncResult?
        
        // Monitor sync attempts
        syncCoordinator.onSyncAttempt = { attempt in
            syncAttempts = attempt
        }
        
        syncCoordinator.onSyncComplete = { result in
            finalResult = result
            expectation.fulfill()
        }
        
        // When: Network is unreliable
        cloudKitManager.simulateNetworkConditions(.unreliable)
        
        // Attempt sync
        try await syncCoordinator.syncWorkout(workout)
        
        // Simulate network recovery
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.cloudKitManager.simulateNetworkConditions(.stable)
        }
        
        // Then: Sync should eventually succeed with retries
        await fulfillment(of: [expectation], timeout: 10.0)
        
        XCTAssertNotNil(finalResult, "Should eventually get sync result")
        XCTAssertTrue(finalResult?.success ?? false, "Sync should eventually succeed")
        XCTAssertGreaterThan(syncAttempts, 1, "Should have retried after network issues")
    }
    
    func testSyncQueuing_OfflineToOnline() async throws {
        // Given: Multiple workouts created while offline
        let workouts = [
            createTestWorkout(exerciseType: .squat),
            createTestWorkout(exerciseType: .pushUp),
            createTestWorkout(exerciseType: .plank)
        ]
        
        // When: Device is offline
        cloudKitManager.simulateNetworkConditions(.offline)
        
        // Queue workouts for sync
        for workout in workouts {
            try await workoutRepository.save(workout) // Should queue for later sync
        }
        
        // Verify queued items
        let queuedItems = await syncCoordinator.getPendingSyncItems()
        XCTAssertEqual(queuedItems.count, workouts.count, "All workouts should be queued")
        
        // Come back online
        cloudKitManager.simulateNetworkConditions(.stable)
        
        // Trigger sync
        let syncResult = try await syncCoordinator.syncPendingItems()
        
        // Then: All queued items should sync successfully
        XCTAssertTrue(syncResult.success, "Queued sync should succeed")
        XCTAssertEqual(syncResult.syncedItemCount, workouts.count, "All workouts should sync")
    }
    
    // MARK: - Background Sync Tests
    
    func testBackgroundSync_Scheduling() async throws {
        // Given: Background sync is configured
        backgroundProcessor.enableLowPowerMode(false)
        
        let expectation = XCTestExpectation(description: "Background sync completion")
        var backgroundSyncCompleted = false
        
        // Monitor background sync
        backgroundProcessor.onBackgroundSyncComplete = { success in
            backgroundSyncCompleted = success
            expectation.fulfill()
        }
        
        // When: App enters background with pending sync items
        let workout = createTestWorkout()
        try await workoutRepository.save(workout)
        
        // Simulate app backgrounding
        NotificationCenter.default.post(name: UIApplication.didEnterBackgroundNotification, object: nil)
        
        // Then: Background sync should be scheduled and executed
        await fulfillment(of: [expectation], timeout: 5.0)
        XCTAssertTrue(backgroundSyncCompleted, "Background sync should complete successfully")
    }
    
    func testBackgroundSync_LowPowerMode() async throws {
        // Given: Device is in low power mode
        backgroundProcessor.enableLowPowerMode(true)
        
        // When: Attempting background sync
        let workout = createTestWorkout()
        try await workoutRepository.save(workout)
        
        let syncResult = try await backgroundProcessor.performBackgroundSync()
        
        // Then: Sync should be throttled but still functional
        XCTAssertTrue(syncResult.success, "Low power sync should still work")
        XCTAssertLessThan(syncResult.duration, 30.0, "Low power sync should be efficient")
    }
    
    // MARK: - Data Integrity Tests
    
    func testDataIntegrity_LargeWorkoutSession() async throws {
        // Given: A workout with extensive data
        let largeWorkout = createLargeTestWorkout()
        
        // When: Syncing large workout
        let uploadResult = try await workoutRepository.save(largeWorkout)
        XCTAssertTrue(uploadResult.success, "Large workout upload should succeed")
        
        // Clear and re-download
        try await workoutRepository.clearLocalData()
        let downloadedWorkouts = try await workoutRepository.fetchAllWorkouts()
        
        // Then: Data integrity should be maintained
        XCTAssertEqual(downloadedWorkouts.count, 1, "Should download large workout")
        
        let downloadedWorkout = downloadedWorkouts.first!
        XCTAssertEqual(downloadedWorkout.repHistory.count, largeWorkout.repHistory.count, "Rep history should be intact")
        XCTAssertEqual(downloadedWorkout.formAnalysisData.count, largeWorkout.formAnalysisData.count, "Form data should be intact")
    }
    
    func testDataIntegrity_ConcurrentModifications() async throws {
        // Given: Multiple concurrent sync operations
        let workouts = (0..<10).map { index in
            createTestWorkout(exerciseType: .squat, repCount: index * 5)
        }
        
        // When: Syncing concurrently
        await withTaskGroup(of: Void.self) { group in
            for workout in workouts {
                group.addTask {
                    do {
                        let _ = try await self.workoutRepository.save(workout)
                    } catch {
                        XCTFail("Concurrent sync failed for workout \(workout.id): \(error)")
                    }
                }
            }
        }
        
        // Then: All workouts should be synced correctly
        let allWorkouts = try await workoutRepository.fetchAllWorkouts()
        XCTAssertEqual(allWorkouts.count, workouts.count, "All concurrent workouts should sync")
        
        // Verify no data corruption
        for originalWorkout in workouts {
            let found = allWorkouts.first { $0.id == originalWorkout.id }
            XCTAssertNotNil(found, "Workout \(originalWorkout.id) should exist")
            XCTAssertEqual(found?.repCount, originalWorkout.repCount, "Rep count should be preserved")
        }
    }
    
    // MARK: - Performance Tests
    
    func testSyncPerformance_MultipleSessions() async throws {
        // Given: Multiple workout sessions to sync
        let sessionCount = 50
        let workouts = (0..<sessionCount).map { index in
            createTestWorkout(exerciseType: .squat, repCount: index)
        }
        
        // When: Measuring sync performance
        let startTime = Date()
        
        for workout in workouts {
            let _ = try await workoutRepository.save(workout)
        }
        
        let syncDuration = Date().timeIntervalSince(startTime)
        
        // Then: Sync should complete within reasonable time
        XCTAssertLessThan(syncDuration, 30.0, "Syncing 50 workouts should take less than 30 seconds")
        
        let averageSyncTime = syncDuration / Double(sessionCount)
        XCTAssertLessThan(averageSyncTime, 1.0, "Average sync time per workout should be under 1 second")
    }
    
    func testSyncPerformance_BatchOperations() async throws {
        // Given: Large batch of data to sync
        let batchSize = 100
        let workouts = (0..<batchSize).map { index in
            createTestWorkout(exerciseType: .squat, repCount: index)
        }
        
        // When: Using batch sync operations
        let startTime = Date()
        let batchResult = try await workoutRepository.saveBatch(workouts)
        let batchDuration = Date().timeIntervalSince(startTime)
        
        // Then: Batch operations should be more efficient
        XCTAssertTrue(batchResult.success, "Batch sync should succeed")
        XCTAssertLessThan(batchDuration, 15.0, "Batch sync should be faster than individual syncs")
    }
    
    // MARK: - Helper Methods
    
    private func setupTestEnvironment() {
        cloudKitManager.setTestMode(true)
        syncCoordinator.setTestMode(true)
    }
    
    private func cleanupTestData() {
        Task {
            try? await cloudKitManager.deleteAllTestData()
        }
    }
    
    private func createTestWorkout(exerciseType: ExerciseType = .squat, repCount: Int = 10) -> WorkoutSession {
        return WorkoutSession(
            id: UUID(),
            exerciseType: exerciseType,
            repCount: repCount,
            duration: TimeInterval(repCount * 3), // 3 seconds per rep
            startTime: Date(),
            endTime: Date().addingTimeInterval(TimeInterval(repCount * 3)),
            averageFormScore: Double.random(in: 80...95),
            repHistory: createRepHistory(count: repCount),
            formAnalysisData: createFormAnalysisData(count: repCount),
            notes: "Test workout session",
            createdDate: Date(),
            modifiedDate: Date()
        )
    }
    
    private func createLargeTestWorkout() -> WorkoutSession {
        let repCount = 100
        return WorkoutSession(
            id: UUID(),
            exerciseType: .squat,
            repCount: repCount,
            duration: TimeInterval(repCount * 3),
            startTime: Date(),
            endTime: Date().addingTimeInterval(TimeInterval(repCount * 3)),
            averageFormScore: 87.5,
            repHistory: createRepHistory(count: repCount),
            formAnalysisData: createFormAnalysisData(count: repCount * 10), // More detailed analysis
            notes: "Large test workout with extensive data",
            createdDate: Date(),
            modifiedDate: Date()
        )
    }
    
    private func createRepHistory(count: Int) -> [RepData] {
        return (0..<count).map { index in
            RepData(
                repNumber: index + 1,
                timestamp: Date().addingTimeInterval(TimeInterval(index * 3)),
                formScore: Double.random(in: 70...100),
                duration: TimeInterval.random(in: 2.5...3.5)
            )
        }
    }
    
    private func createFormAnalysisData(count: Int) -> [FormAnalysisPoint] {
        return (0..<count).map { index in
            FormAnalysisPoint(
                timestamp: Date().addingTimeInterval(TimeInterval(index) * 0.1),
                jointAngles: createRandomJointAngles(),
                formScore: Double.random(in: 70...100),
                detectedIssues: []
            )
        }
    }
    
    private func createRandomJointAngles() -> [String: Float] {
        return [
            "leftKnee": Float.random(in: 0...Float.pi),
            "rightKnee": Float.random(in: 0...Float.pi),
            "leftHip": Float.random(in: 0...Float.pi),
            "rightHip": Float.random(in: 0...Float.pi)
        ]
    }
    
    private func createTestUserPreferences() -> UserPreferences {
        return UserPreferences(
            coachingPersonality: .supportive,
            targetRepsPerSet: 15,
            targetSetsPerExercise: 3,
            preferredRestTime: 60,
            enableHapticFeedback: true,
            enableVoiceCoaching: true,
            createdDate: Date(),
            modifiedDate: Date()
        )
    }
    
    private func createTestAchievements() -> [Achievement] {
        return [
            Achievement(
                id: UUID(),
                title: "First Workout",
                description: "Complete your first workout",
                type: .milestone,
                earnedDate: Date(),
                exerciseType: .squat
            ),
            Achievement(
                id: UUID(),
                title: "Century Club",
                description: "Complete 100 reps in a single session",
                type: .repCount,
                earnedDate: Date(),
                exerciseType: .squat
            ),
            Achievement(
                id: UUID(),
                title: "Perfect Form",
                description: "Maintain 95%+ form score for entire workout",
                type: .formQuality,
                earnedDate: Date(),
                exerciseType: .pushUp
            )
        ]
    }
}

// MARK: - Mock Extensions and Supporting Types

enum NetworkCondition {
    case stable
    case unreliable
    case offline
}

extension CloudKitManager {
    func setTestMode(_ enabled: Bool) {
        // Configure for testing
    }
    
    func simulateNetworkConditions(_ condition: NetworkCondition) {
        // Simulate network conditions for testing
    }
    
    func saveUserPreferences(_ preferences: UserPreferences) async throws {
        // Mock save implementation
    }
    
    func fetchUserPreferences() async throws -> UserPreferences? {
        // Mock fetch implementation
        return nil
    }
    
    func saveAchievement(_ achievement: Achievement) async throws {
        // Mock save implementation
    }
    
    func fetchAchievements() async throws -> [Achievement] {
        // Mock fetch implementation
        return []
    }
    
    func deleteAllTestData() async throws {
        // Clean up test data
    }
}

extension WorkoutRepository {
    func save(_ workout: WorkoutSession) async throws -> SyncResult {
        // Mock save implementation
        return SyncResult(success: true, syncedItemCount: 1, duration: 0.5)
    }
    
    func saveBatch(_ workouts: [WorkoutSession]) async throws -> SyncResult {
        // Mock batch save implementation
        return SyncResult(success: true, syncedItemCount: workouts.count, duration: 2.0)
    }
    
    func clearLocalData() async throws {
        // Mock clear implementation
    }
    
    func fetchAllWorkouts() async throws -> [WorkoutSession] {
        // Mock fetch implementation
        return []
    }
}

class CloudSyncCoordinator {
    var onSyncAttempt: ((Int) -> Void)?
    var onSyncComplete: ((SyncResult) -> Void)?
    
    private let cloudKitManager: CloudKitManager
    
    init(cloudKitManager: CloudKitManager) {
        self.cloudKitManager = cloudKitManager
    }
    
    func setTestMode(_ enabled: Bool) {
        // Configure for testing
    }
    
    func syncWorkout(_ workout: WorkoutSession) async throws {
        // Mock sync implementation with retry logic
        var attempts = 0
        let maxAttempts = 3
        
        while attempts < maxAttempts {
            attempts += 1
            onSyncAttempt?(attempts)
            
            do {
                // Simulate sync attempt
                try await Task.sleep(nanoseconds: 500_000_000) // 500ms
                
                // Simulate success after retries
                if attempts >= 2 {
                    let result = SyncResult(success: true, syncedItemCount: 1, duration: 0.5)
                    onSyncComplete?(result)
                    return
                }
                
                // Simulate failure
                throw CloudKitError.networkUnavailable
            } catch {
                if attempts >= maxAttempts {
                    let result = SyncResult(success: false, syncedItemCount: 0, duration: 0.5)
                    onSyncComplete?(result)
                    throw error
                }
                
                // Wait before retry
                try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
            }
        }
    }
    
    func getPendingSyncItems() async -> [SyncItem] {
        // Mock implementation
        return []
    }
    
    func syncPendingItems() async throws -> SyncResult {
        // Mock implementation
        return SyncResult(success: true, syncedItemCount: 0, duration: 1.0)
    }
}

class CloudKitConflictResolver {
    func resolveWorkoutConflict(local: WorkoutSession, cloud: WorkoutSession) async throws -> WorkoutSession {
        // Use last-modified-wins strategy
        return local.modifiedDate > cloud.modifiedDate ? local : cloud
    }
    
    func resolvePreferencesConflict(local: UserPreferences, cloud: UserPreferences) async throws -> UserPreferences {
        // Merge preferences intelligently
        var resolved = local.modifiedDate > cloud.modifiedDate ? local : cloud
        
        // Merge specific fields from the other version if they're newer or more specific
        if local.modifiedDate > cloud.modifiedDate {
            // Keep local as base, but merge useful cloud settings
            resolved.targetRepsPerSet = cloud.targetRepsPerSet
        }
        
        return resolved
    }
}

extension BackgroundProcessingOptimizer {
    var onBackgroundSyncComplete: ((Bool) -> Void)?
    
    func performBackgroundSync() async throws -> SyncResult {
        // Mock background sync
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        let success = !lowPowerModeActive || Bool.random()
        onBackgroundSyncComplete?(success)
        
        return SyncResult(
            success: success,
            syncedItemCount: success ? 1 : 0,
            duration: lowPowerModeActive ? 0.5 : 1.0
        )
    }
}

struct SyncResult {
    let success: Bool
    let syncedItemCount: Int
    let duration: TimeInterval
}

struct SyncItem {
    let id: UUID
    let type: SyncItemType
    let data: Data
}

enum SyncItemType {
    case workout
    case preferences
    case achievement
}

enum CloudKitError: Error {
    case networkUnavailable
    case quotaExceeded
    case conflictDetected
}

// MARK: - Data Models for Testing

struct WorkoutSession {
    let id: UUID
    let exerciseType: ExerciseType
    let repCount: Int
    let duration: TimeInterval
    let startTime: Date
    let endTime: Date
    let averageFormScore: Double
    let repHistory: [RepData]
    let formAnalysisData: [FormAnalysisPoint]
    let notes: String
    let createdDate: Date
    var modifiedDate: Date
}

struct RepData {
    let repNumber: Int
    let timestamp: Date
    let formScore: Double
    let duration: TimeInterval
}

struct FormAnalysisPoint {
    let timestamp: Date
    let jointAngles: [String: Float]
    let formScore: Double
    let detectedIssues: [String]
}

struct UserPreferences {
    let coachingPersonality: CoachingPersonality
    let targetRepsPerSet: Int
    let targetSetsPerExercise: Int
    let preferredRestTime: TimeInterval
    let enableHapticFeedback: Bool
    let enableVoiceCoaching: Bool
    let createdDate: Date
    var modifiedDate: Date
}

struct Achievement {
    let id: UUID
    let title: String
    let description: String
    let type: AchievementType
    let earnedDate: Date
    let exerciseType: ExerciseType
}

enum AchievementType {
    case milestone
    case repCount
    case formQuality
    case consistency
}