import XCTest
import Combine
@testable import MotionFitPro

/// Integration tests for the complete workout flow from selection to completion
final class WorkoutFlowIntegrationTests: XCTestCase {
    
    var workoutCoordinator: WorkoutCoordinator!
    var arSessionManager: ARSessionManager!
    var mlProcessingManager: MLProcessingManager!
    var audioManager: AudioManager!
    var dataController: DataController!
    var cancellables: Set<AnyCancellable>!
    
    override func setUpWithError() throws {
        try super.setUpWithError()
        
        // Initialize managers
        arSessionManager = ARSessionManager.shared
        mlProcessingManager = MLProcessingManager.shared
        audioManager = AudioManager.shared
        dataController = DataController.shared
        
        // Initialize workout coordinator
        workoutCoordinator = WorkoutCoordinator()
        
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDownWithError() throws {
        cancellables?.removeAll()
        workoutCoordinator = nil
        
        try super.tearDownWithError()
    }
    
    // MARK: - Complete Workout Flow Tests
    
    func testCompleteWorkoutFlow() async throws {
        // Given: A set of exercises and workout settings
        let exercises = createTestExercises()
        let settings = createTestWorkoutSettings()
        
        // When: Starting a workout
        workoutCoordinator.startWorkout(exercises: exercises, settings: settings)
        
        // Then: Workout should be active
        XCTAssertTrue(workoutCoordinator.isWorkoutActive)
        XCTAssertEqual(workoutCoordinator.currentState, .exerciseIntro)
        XCTAssertEqual(workoutCoordinator.selectedExercises.count, exercises.count)
        
        // When: Starting the first exercise
        workoutCoordinator.startExercise()
        
        // Then: Exercise should be active
        XCTAssertEqual(workoutCoordinator.currentState, .active)
        XCTAssertEqual(workoutCoordinator.currentExerciseIndex, 0)
        XCTAssertEqual(workoutCoordinator.currentSet, 1)
        
        // When: Completing reps
        let targetReps = exercises[0].targetReps
        for _ in 1...targetReps {
            workoutCoordinator.completeRep()
        }
        
        // Then: Set should be completed
        XCTAssertEqual(workoutCoordinator.currentState, .setComplete)
        XCTAssertEqual(workoutCoordinator.currentRep, targetReps)
        
        // When: Completing all sets for the exercise
        let targetSets = exercises[0].targetSets
        for set in 2...targetSets {
            // Start next set
            workoutCoordinator.startExercise()
            
            // Complete reps
            for _ in 1...targetReps {
                workoutCoordinator.completeRep()
            }
        }
        
        // Then: Exercise should be completed
        XCTAssertEqual(workoutCoordinator.currentState, .exerciseComplete)
        
        // When: Moving to next exercise
        if exercises.count > 1 {
            // Should automatically move to next exercise
            await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
            XCTAssertEqual(workoutCoordinator.currentExerciseIndex, 1)
        }
        
        // When: Completing the workout
        workoutCoordinator.completeWorkout()
        
        // Then: Workout should be completed
        XCTAssertEqual(workoutCoordinator.currentState, .workoutComplete)
        XCTAssertFalse(workoutCoordinator.isWorkoutActive)
        XCTAssertTrue(workoutCoordinator.showingWorkoutSummary)
    }
    
    func testWorkoutPauseAndResume() async throws {
        // Given: An active workout
        let exercises = createTestExercises()
        let settings = createTestWorkoutSettings()
        
        workoutCoordinator.startWorkout(exercises: exercises, settings: settings)
        workoutCoordinator.startExercise()
        
        XCTAssertEqual(workoutCoordinator.currentState, .active)
        
        // When: Pausing the workout
        workoutCoordinator.pauseWorkout()
        
        // Then: Workout should be paused
        XCTAssertEqual(workoutCoordinator.currentState, .paused)
        
        // When: Resuming the workout
        workoutCoordinator.resumeWorkout()
        
        // Then: Workout should be active again
        XCTAssertEqual(workoutCoordinator.currentState, .active)
    }
    
    func testWorkoutSkipExercise() async throws {
        // Given: An active workout with multiple exercises
        let exercises = createTestExercises(count: 3)
        let settings = createTestWorkoutSettings()
        
        workoutCoordinator.startWorkout(exercises: exercises, settings: settings)
        workoutCoordinator.startExercise()
        
        let initialExerciseIndex = workoutCoordinator.currentExerciseIndex
        
        // When: Skipping the current exercise
        workoutCoordinator.skipExercise()
        
        // Then: Should move to next exercise
        XCTAssertEqual(workoutCoordinator.currentExerciseIndex, initialExerciseIndex + 1)
    }
    
    // MARK: - AR and ML Integration Tests
    
    func testARMLIntegration() async throws {
        // Given: AR and ML managers are initialized
        XCTAssertNotNil(arSessionManager)
        XCTAssertNotNil(mlProcessingManager)
        
        // When: Starting AR session
        arSessionManager.startSession()
        
        // Then: AR session should be running
        await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        XCTAssertEqual(arSessionManager.sessionState, .running)
        
        // When: Starting ML processing
        mlProcessingManager.startProcessing()
        
        // Then: ML processing should be active
        XCTAssertTrue(mlProcessingManager.isProcessing)
        
        // When: Setting target exercise
        mlProcessingManager.setTargetExercise(.squat)
        
        // Then: Current exercise should be set
        XCTAssertEqual(mlProcessingManager.currentExercise, .squat)
        
        // Cleanup
        arSessionManager.stopSession()
        mlProcessingManager.stopProcessing()
    }
    
    func testFormFeedbackIntegration() async throws {
        // Given: A workout with form feedback enabled
        let exercises = createTestExercises()
        var settings = createTestWorkoutSettings()
        settings.realTimeFeedback = true
        settings.formStrictness = .strict
        
        workoutCoordinator.startWorkout(exercises: exercises, settings: settings)
        
        // When: Processing pose data with poor form
        let poorFormPose = createPoorFormPoseData()
        await mlProcessingManager.processPoseData(poorFormPose)
        
        // Then: Form score should reflect poor form
        await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        XCTAssertLessThan(mlProcessingManager.formScore, 0.7)
        
        // When: Processing pose data with good form
        let goodFormPose = createGoodFormPoseData()
        await mlProcessingManager.processPoseData(goodFormPose)
        
        // Then: Form score should improve
        await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        XCTAssertGreaterThan(mlProcessingManager.formScore, 0.8)
    }
    
    // MARK: - Data Persistence Integration Tests
    
    func testWorkoutDataPersistence() async throws {
        // Given: A completed workout
        let exercises = createTestExercises()
        let settings = createTestWorkoutSettings()
        
        workoutCoordinator.startWorkout(exercises: exercises, settings: settings)
        
        // Simulate workout completion
        workoutCoordinator.completeWorkout()
        
        // When: Saving workout session
        guard let workoutSession = workoutCoordinator.workoutSession else {
            XCTFail("Workout session should exist after completion")
            return
        }
        
        await dataController.saveWorkoutSession(workoutSession)
        
        // Then: Workout should be persisted
        let savedSessions = try await dataController.fetchWorkoutSessions()
        XCTAssertTrue(savedSessions.contains { $0.id == workoutSession.id })
    }
    
    func testCloudKitSyncIntegration() async throws {
        // Given: A workout session
        let exercises = createTestExercises()
        let settings = createTestWorkoutSettings()
        let session = WorkoutSession(
            id: UUID(),
            exercises: exercises,
            settings: settings,
            startTime: Date(),
            endTime: Date(),
            exercisesCompleted: exercises.count,
            totalDuration: 1800 // 30 minutes
        )
        
        // When: Syncing with CloudKit
        await dataController.saveWorkoutSession(session)
        
        // Note: In a real test environment, you would verify CloudKit sync
        // For now, we verify local storage works
        let savedSessions = try await dataController.fetchWorkoutSessions()
        XCTAssertTrue(savedSessions.contains { $0.id == session.id })
    }
    
    // MARK: - Audio and Haptic Integration Tests
    
    func testAudioHapticIntegration() async throws {
        // Given: Audio and haptic feedback enabled
        var settings = createTestWorkoutSettings()
        settings.audioFeedback = true
        settings.hapticFeedback = true
        settings.voiceCoachingEnabled = true
        
        let exercises = createTestExercises()
        workoutCoordinator.startWorkout(exercises: exercises, settings: settings)
        
        // When: Completing a rep
        workoutCoordinator.completeRep()
        
        // Then: Audio and haptic feedback should be triggered
        // Note: In a real test, you would verify audio/haptic calls
        // For now, we verify the workout state changes correctly
        XCTAssertEqual(workoutCoordinator.currentRep, 1)
    }
    
    // MARK: - Error Handling Integration Tests
    
    func testWorkoutErrorRecovery() async throws {
        // Given: A workout in progress
        let exercises = createTestExercises()
        let settings = createTestWorkoutSettings()
        
        workoutCoordinator.startWorkout(exercises: exercises, settings: settings)
        workoutCoordinator.startExercise()
        
        // When: An error occurs (simulated by stopping the workout)
        workoutCoordinator.stopWorkout()
        
        // Then: Workout should handle the error gracefully
        XCTAssertEqual(workoutCoordinator.currentState, .workoutComplete)
        XCTAssertFalse(workoutCoordinator.isWorkoutActive)
        
        // When: Starting a new workout after error
        workoutCoordinator.startWorkout(exercises: exercises, settings: settings)
        
        // Then: New workout should start successfully
        XCTAssertTrue(workoutCoordinator.isWorkoutActive)
        XCTAssertEqual(workoutCoordinator.currentState, .exerciseIntro)
    }
    
    // MARK: - Performance Integration Tests
    
    func testWorkoutPerformanceUnderLoad() async throws {
        // Given: Multiple concurrent operations
        let exercises = createTestExercises(count: 10)
        let settings = createTestWorkoutSettings()
        
        // Measure performance
        measure {
            workoutCoordinator.startWorkout(exercises: exercises, settings: settings)
            
            // Simulate rapid rep completion
            for _ in 1...100 {
                workoutCoordinator.completeRep()
            }
            
            workoutCoordinator.stopWorkout()
        }
    }
    
    // MARK: - Helper Methods
    
    private func createTestExercises(count: Int = 2) -> [ExerciseData] {
        return (0..<count).map { index in
            ExerciseData(
                id: UUID(),
                name: "Test Exercise \(index + 1)",
                type: index == 0 ? .squat : .pushUp,
                category: .strength,
                difficulty: .beginner,
                targetSets: 3,
                targetReps: 10,
                targetDuration: nil,
                restBetweenSets: 60,
                equipment: [.bodyweight],
                primaryMuscles: [.quadriceps],
                secondaryMuscles: [.glutes],
                instructions: ["Test instruction"],
                tips: ["Test tip"],
                modifications: [],
                biomechanicalPoints: [],
                safetyNotes: []
            )
        }
    }
    
    private func createTestWorkoutSettings() -> WorkoutSettings {
        return WorkoutSettings(
            duration: .medium,
            difficulty: .beginner,
            restBetweenSets: 60,
            restBetweenExercises: 120,
            coachingPersonality: .encouraging,
            audioFeedback: true,
            hapticFeedback: true,
            formStrictness: .moderate,
            autoProgressSets: false,
            countdownEnabled: true,
            musicEnabled: false,
            voiceCoachingEnabled: true,
            realTimeFeedback: true,
            pauseOnFormError: false
        )
    }
    
    private func createPoorFormPoseData() -> BodyPoseData {
        let joints: [JointName: Joint3D] = [
            .root: Joint3D(position: simd_float3(0, 0, 0), confidence: 0.9, isTracked: true),
            .leftKnee: Joint3D(position: simd_float3(-0.3, -0.5, 0), confidence: 0.8, isTracked: true), // Poor alignment
            .rightKnee: Joint3D(position: simd_float3(0.1, -0.5, 0), confidence: 0.8, isTracked: true)  // Poor alignment
        ]
        
        return BodyPoseData(
            joints: joints,
            trackingQuality: .good,
            confidence: 0.8,
            boundingBox: CGRect(x: 0, y: 0, width: 100, height: 200),
            isFullBodyVisible: true
        )
    }
    
    private func createGoodFormPoseData() -> BodyPoseData {
        let joints: [JointName: Joint3D] = [
            .root: Joint3D(position: simd_float3(0, 0, 0), confidence: 0.95, isTracked: true),
            .leftKnee: Joint3D(position: simd_float3(-0.15, -0.5, 0), confidence: 0.9, isTracked: true), // Good alignment
            .rightKnee: Joint3D(position: simd_float3(0.15, -0.5, 0), confidence: 0.9, isTracked: true)  // Good alignment
        ]
        
        return BodyPoseData(
            joints: joints,
            trackingQuality: .excellent,
            confidence: 0.95,
            boundingBox: CGRect(x: 0, y: 0, width: 100, height: 200),
            isFullBodyVisible: true
        )
    }
}
