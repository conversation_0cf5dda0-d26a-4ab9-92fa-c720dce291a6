import XCTest
import Combine
@testable import MotionFitPro

// MARK: - Workout Session Integration Tests

final class WorkoutSessionIntegrationTests: TestBase {
    
    // MARK: - Properties
    
    private var workoutManager: WorkoutSessionManager!
    private var mockARService: MockARTrackingService!
    private var mockMLService: MockMLProcessingService!
    private var mockFeedbackService: MockFeedbackService!
    
    // MARK: - Setup
    
    override func setUp() {
        super.setUp()
        
        // Configure mock services
        mockDIContainer.configureMockARService(shouldTrack: true, trackingQuality: .good)
        mockDIContainer.configureMockMLService(shouldSucceed: true, processingTime: 0.05)
        
        // Get references to mock services
        mockARService = mockDIContainer.mockARTrackingService
        mockMLService = mockDIContainer.mockMLProcessingService
        mockFeedbackService = mockDIContainer.mockFeedbackService
        
        // Create workout manager with mocked dependencies
        workoutManager = WorkoutSessionManager()
    }
    
    override func tearDown() {
        workoutManager = nil
        mockARService = nil
        mockMLService = nil
        mockFeedbackService = nil
        
        super.tearDown()
    }
    
    // MARK: - Full Workout Session Tests
    
    func testCompleteWorkoutSession_WithValidData_CompletesSuccessfully() async throws {
        // Given
        let exercises: [ExerciseType] = [.squat, .pushUp]
        
        // When
        try await workoutManager.startSession(exercises: exercises)
        
        // Simulate workout data
        await simulateWorkoutSession()
        
        await workoutManager.stopSession()
        
        // Then
        XCTAssertEqual(workoutManager.sessionState, .completed)
        XCTAssertGreaterThan(workoutManager.repCount, 0)
        XCTAssertTrue(mockARService.startTrackingCalled)
        XCTAssertTrue(mockARService.stopTrackingCalled)
        XCTAssertGreaterThan(mockMLService.processedPoses.count, 0)
    }
    
    func testWorkoutSession_WithARTrackingFailure_HandlesGracefully() async throws {
        // Given
        mockDIContainer.configureMockARService(shouldTrack: false)
        
        // When & Then
        await XCTAssertThrowsError(
            try await workoutManager.startSession()
        ) { error in
            XCTAssertTrue(error is WorkoutError)
        }
    }
    
    func testWorkoutSession_WithMLProcessingFailure_ContinuesWithFallback() async throws {
        // Given
        mockDIContainer.configureMockMLService(shouldSucceed: false)
        try await workoutManager.startSession()
        
        // When
        let poseData = createMockBodyPoseData()
        mockARService.simulatePoseData(poseData)
        
        // Wait for processing
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        // Then
        XCTAssertEqual(workoutManager.sessionState, .active)
        // Should continue working even with ML failures
    }
    
    // MARK: - Real-time Feedback Integration Tests
    
    func testRealtimeFeedback_WithGoodForm_ProvidesPositiveFeedback() async throws {
        // Given
        try await workoutManager.startSession()
        let goodFormPoseData = createMockGoodFormPoseData()
        
        // When
        mockARService.simulatePoseData(goodFormPoseData)
        
        // Wait for processing
        try await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // Then
        XCTAssertGreaterThan(workoutManager.currentFormScore, 0.7)
        XCTAssertTrue(workoutManager.realtimeFeedback.contains { feedback in
            feedback.type == .encouragement || feedback.type == .success
        })
    }
    
    func testRealtimeFeedback_WithPoorForm_ProvidesCorrectionFeedback() async throws {
        // Given
        try await workoutManager.startSession()
        let poorFormPoseData = createMockPoorFormPoseData()
        
        // When
        mockARService.simulatePoseData(poorFormPoseData)
        
        // Wait for processing
        try await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // Then
        XCTAssertLessThan(workoutManager.currentFormScore, 0.6)
        XCTAssertTrue(workoutManager.realtimeFeedback.contains { feedback in
            feedback.type == .correction || feedback.type == .warning
        })
    }
    
    // MARK: - Exercise Detection Integration Tests
    
    func testExerciseDetection_WithSquatPose_DetectsSquat() async throws {
        // Given
        try await workoutManager.startSession()
        let squatPoseData = createMockSquatPoseData()
        
        // When
        for _ in 0..<5 { // Send multiple frames for confidence
            mockARService.simulatePoseData(squatPoseData)
            try await Task.sleep(nanoseconds: 50_000_000) // 0.05 seconds
        }
        
        // Wait for processing
        try await Task.sleep(nanoseconds: 300_000_000) // 0.3 seconds
        
        // Then
        XCTAssertEqual(workoutManager.currentExercise, .squat)
    }
    
    func testExerciseTransition_FromSquatToPushUp_TransitionsCorrectly() async throws {
        // Given
        try await workoutManager.startSession()
        
        // Start with squat
        let squatPoseData = createMockSquatPoseData()
        for _ in 0..<3 {
            mockARService.simulatePoseData(squatPoseData)
            try await Task.sleep(nanoseconds: 50_000_000)
        }
        
        // Wait for squat detection
        try await Task.sleep(nanoseconds: 200_000_000)
        XCTAssertEqual(workoutManager.currentExercise, .squat)
        
        // When - transition to push-up
        let pushUpPoseData = createMockPushUpPoseData()
        for _ in 0..<3 {
            mockARService.simulatePoseData(pushUpPoseData)
            try await Task.sleep(nanoseconds: 50_000_000)
        }
        
        // Wait for transition
        try await Task.sleep(nanoseconds: 200_000_000)
        
        // Then
        XCTAssertEqual(workoutManager.currentExercise, .pushUp)
    }
    
    // MARK: - Repetition Counting Integration Tests
    
    func testRepetitionCounting_WithCompleteSquat_CountsRep() async throws {
        // Given
        try await workoutManager.startSession()
        let initialRepCount = workoutManager.repCount
        
        // When - simulate complete squat repetition
        await simulateCompleteSquatRepetition()
        
        // Then
        XCTAssertGreaterThan(workoutManager.repCount, initialRepCount)
    }
    
    func testRepetitionCounting_WithIncompleteMovement_DoesNotCount() async throws {
        // Given
        try await workoutManager.startSession()
        let initialRepCount = workoutManager.repCount
        
        // When - simulate incomplete movement
        let startPose = createMockSquatStartPoseData()
        let midPose = createMockSquatMidPoseData()
        
        mockARService.simulatePoseData(startPose)
        try await Task.sleep(nanoseconds: 100_000_000)
        mockARService.simulatePoseData(midPose)
        try await Task.sleep(nanoseconds: 100_000_000)
        // Don't complete the movement
        
        // Then
        XCTAssertEqual(workoutManager.repCount, initialRepCount)
    }
    
    // MARK: - Performance Integration Tests
    
    func testWorkoutSession_PerformanceUnderLoad() async throws {
        // Given
        try await workoutManager.startSession()
        let poseDataArray = (0..<100).map { _ in createMockBodyPoseData() }
        
        // When
        let startTime = Date()
        
        for poseData in poseDataArray {
            mockARService.simulatePoseData(poseData)
            try await Task.sleep(nanoseconds: 16_666_667) // ~60 FPS
        }
        
        let endTime = Date()
        let duration = endTime.timeIntervalSince(startTime)
        
        // Then
        XCTAssertLessThan(duration, 5.0, "Processing should complete within reasonable time")
        XCTAssertEqual(workoutManager.sessionState, .active)
        XCTAssertGreaterThan(mockMLService.processedPoses.count, 50) // Should process most frames
    }
    
    // MARK: - Memory Management Integration Tests
    
    func testWorkoutSession_MemoryManagement_DoesNotLeak() async throws {
        // Given
        weak var weakManager: WorkoutSessionManager?
        
        // When
        await autoreleasepool {
            let manager = WorkoutSessionManager()
            weakManager = manager
            
            try await manager.startSession()
            await manager.stopSession()
        }
        
        // Then
        XCTAssertNil(weakManager, "WorkoutSessionManager should be deallocated")
    }
    
    // MARK: - Error Recovery Integration Tests
    
    func testWorkoutSession_RecoveryFromARInterruption() async throws {
        // Given
        try await workoutManager.startSession()
        XCTAssertEqual(workoutManager.sessionState, .active)
        
        // When - simulate AR interruption
        mockARService.shouldTrack = false
        workoutManager.pauseSession()
        
        // Recovery
        mockARService.shouldTrack = true
        workoutManager.resumeSession()
        
        // Then
        XCTAssertEqual(workoutManager.sessionState, .active)
    }
    
    // MARK: - Helper Methods
    
    private func simulateWorkoutSession() async {
        // Simulate a short workout with multiple exercises
        await simulateCompleteSquatRepetition()
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        await simulateCompletePushUpRepetition()
    }
    
    private func simulateCompleteSquatRepetition() async {
        let phases = [
            createMockSquatStartPoseData(),
            createMockSquatEccentricPoseData(),
            createMockSquatBottomPoseData(),
            createMockSquatConcentricPoseData(),
            createMockSquatStartPoseData()
        ]
        
        for phase in phases {
            mockARService.simulatePoseData(phase)
            try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds per phase
        }
    }
    
    private func simulateCompletePushUpRepetition() async {
        let phases = [
            createMockPushUpTopPoseData(),
            createMockPushUpEccentricPoseData(),
            createMockPushUpBottomPoseData(),
            createMockPushUpConcentricPoseData(),
            createMockPushUpTopPoseData()
        ]
        
        for phase in phases {
            mockARService.simulatePoseData(phase)
            try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds per phase
        }
    }
    
    // MARK: - Mock Data Helpers
    
    private func createMockGoodFormPoseData() -> BodyPoseData {
        var poseData = createMockBodyPoseData()
        poseData.confidence = 0.95
        return poseData
    }
    
    private func createMockPoorFormPoseData() -> BodyPoseData {
        var poseData = createMockBodyPoseData()
        poseData.confidence = 0.4
        return poseData
    }
    
    private func createMockSquatPoseData() -> BodyPoseData {
        return createMockBodyPoseData() // Simplified for testing
    }
    
    private func createMockPushUpPoseData() -> BodyPoseData {
        return createMockBodyPoseData() // Simplified for testing
    }
    
    private func createMockSquatStartPoseData() -> BodyPoseData {
        return createMockBodyPoseData()
    }
    
    private func createMockSquatMidPoseData() -> BodyPoseData {
        return createMockBodyPoseData()
    }
    
    private func createMockSquatEccentricPoseData() -> BodyPoseData {
        return createMockBodyPoseData()
    }
    
    private func createMockSquatBottomPoseData() -> BodyPoseData {
        return createMockBodyPoseData()
    }
    
    private func createMockSquatConcentricPoseData() -> BodyPoseData {
        return createMockBodyPoseData()
    }
    
    private func createMockPushUpTopPoseData() -> BodyPoseData {
        return createMockBodyPoseData()
    }
    
    private func createMockPushUpEccentricPoseData() -> BodyPoseData {
        return createMockBodyPoseData()
    }
    
    private func createMockPushUpBottomPoseData() -> BodyPoseData {
        return createMockBodyPoseData()
    }
    
    private func createMockPushUpConcentricPoseData() -> BodyPoseData {
        return createMockBodyPoseData()
    }
}
