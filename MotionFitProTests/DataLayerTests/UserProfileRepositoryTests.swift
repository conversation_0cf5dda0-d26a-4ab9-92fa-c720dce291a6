import XCTest
import SwiftData
@testable import MotionFitPro

@MainActor
final class UserProfileRepositoryTests: XCTestCase {
    private var dataController: DataController!
    private var repository: UserProfileRepository!
    private var testContainer: ModelContainer!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Create in-memory test container
        let schema = Schema([
            UserProfile.self,
            WorkoutSession.self,
            ExercisePerformance.self,
            SetPerformance.self,
            RepPerformance.self
        ])
        
        let configuration = ModelConfiguration(
            schema: schema,
            isStoredInMemoryOnly: true,
            allowsSave: true
        )
        
        testContainer = try ModelContainer(for: schema, configurations: [configuration])
        
        // Create test data controller
        dataController = DataController()
        // Override the container for testing
        dataController.container = testContainer
        dataController.context = ModelContext(testContainer)
        
        repository = UserProfileRepository(dataController: dataController)
    }
    
    override func tearDown() async throws {
        dataController = nil
        repository = nil
        testContainer = nil
        try await super.tearDown()
    }
    
    // MARK: - Create Tests
    
    func testCreateUserProfile() throws {
        let preferences = UserPreferences()
        
        let profile = try repository.createUserProfile(
            name: "Test User",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .beginner,
            fitnessGoals: [.buildMuscle, .loseWeight],
            preferences: preferences
        )
        
        XCTAssertEqual(profile.name, "Test User")
        XCTAssertEqual(profile.email, "<EMAIL>")
        XCTAssertEqual(profile.age, 25)
        XCTAssertEqual(profile.height, 180.0)
        XCTAssertEqual(profile.weight, 75.0)
        XCTAssertEqual(profile.fitnessLevel, .beginner)
        XCTAssertEqual(profile.fitnessGoals.count, 2)
        XCTAssertTrue(profile.fitnessGoals.contains(.buildMuscle))
        XCTAssertTrue(profile.fitnessGoals.contains(.loseWeight))
    }
    
    func testCreateUserProfileWithDuplicateEmail() throws {
        let preferences = UserPreferences()
        
        // Create first user
        _ = try repository.createUserProfile(
            name: "User 1",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .beginner,
            preferences: preferences
        )
        
        // Attempt to create second user with same email should succeed
        // (We're not enforcing email uniqueness at the repository level)
        let secondProfile = try repository.createUserProfile(
            name: "User 2",
            email: "<EMAIL>",
            age: 30,
            height: 175.0,
            weight: 70.0,
            fitnessLevel: .intermediate,
            preferences: preferences
        )
        
        XCTAssertEqual(secondProfile.name, "User 2")
    }
    
    // MARK: - Read Tests
    
    func testGetUserProfileById() throws {
        let preferences = UserPreferences()
        let profile = try repository.createUserProfile(
            name: "Test User",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .beginner,
            preferences: preferences
        )
        
        let retrievedProfile = try repository.getUserProfile(by: profile.id)
        
        XCTAssertNotNil(retrievedProfile)
        XCTAssertEqual(retrievedProfile?.id, profile.id)
        XCTAssertEqual(retrievedProfile?.name, "Test User")
    }
    
    func testGetUserProfileByEmail() throws {
        let preferences = UserPreferences()
        let profile = try repository.createUserProfile(
            name: "Test User",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .beginner,
            preferences: preferences
        )
        
        let retrievedProfile = try repository.getUserProfile(by: "<EMAIL>")
        
        XCTAssertNotNil(retrievedProfile)
        XCTAssertEqual(retrievedProfile?.id, profile.id)
        XCTAssertEqual(retrievedProfile?.email, "<EMAIL>")
    }
    
    func testGetUserProfileByNonexistentEmail() throws {
        let retrievedProfile = try repository.getUserProfile(by: "<EMAIL>")
        XCTAssertNil(retrievedProfile)
    }
    
    func testGetAllUserProfiles() throws {
        let preferences = UserPreferences()
        
        // Create multiple profiles
        _ = try repository.createUserProfile(
            name: "User 1",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .beginner,
            preferences: preferences
        )
        
        _ = try repository.createUserProfile(
            name: "User 2",
            email: "<EMAIL>",
            age: 30,
            height: 175.0,
            weight: 70.0,
            fitnessLevel: .intermediate,
            preferences: preferences
        )
        
        let allProfiles = try repository.getAllUserProfiles()
        
        XCTAssertEqual(allProfiles.count, 2)
    }
    
    func testGetUserProfilesWithFitnessLevel() throws {
        let preferences = UserPreferences()
        
        // Create profiles with different fitness levels
        _ = try repository.createUserProfile(
            name: "Beginner User",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .beginner,
            preferences: preferences
        )
        
        _ = try repository.createUserProfile(
            name: "Intermediate User",
            email: "<EMAIL>",
            age: 30,
            height: 175.0,
            weight: 70.0,
            fitnessLevel: .intermediate,
            preferences: preferences
        )
        
        let beginnerProfiles = try repository.getUserProfilesWithFitnessLevel(.beginner)
        let intermediateProfiles = try repository.getUserProfilesWithFitnessLevel(.intermediate)
        
        XCTAssertEqual(beginnerProfiles.count, 1)
        XCTAssertEqual(intermediateProfiles.count, 1)
        XCTAssertEqual(beginnerProfiles.first?.name, "Beginner User")
        XCTAssertEqual(intermediateProfiles.first?.name, "Intermediate User")
    }
    
    // MARK: - Update Tests
    
    func testUpdateUserName() throws {
        let preferences = UserPreferences()
        let profile = try repository.createUserProfile(
            name: "Original Name",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .beginner,
            preferences: preferences
        )
        
        try repository.updateUserName(profile, name: "Updated Name")
        
        let retrievedProfile = try repository.getUserProfile(by: profile.id)
        XCTAssertEqual(retrievedProfile?.name, "Updated Name")
    }
    
    func testUpdateUserWeight() throws {
        let preferences = UserPreferences()
        let profile = try repository.createUserProfile(
            name: "Test User",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .beginner,
            preferences: preferences
        )
        
        let originalWeightHistoryCount = profile.weightHistory.count
        
        try repository.updateUserWeight(profile, weight: 80.0)
        
        XCTAssertEqual(profile.weight, 80.0)
        XCTAssertEqual(profile.weightHistory.count, originalWeightHistoryCount + 1)
        XCTAssertEqual(profile.weightHistory.last?.weight, 80.0)
    }
    
    func testUpdateUserFitnessLevel() throws {
        let preferences = UserPreferences()
        let profile = try repository.createUserProfile(
            name: "Test User",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .beginner,
            preferences: preferences
        )
        
        try repository.updateUserFitnessLevel(profile, level: .intermediate)
        
        XCTAssertEqual(profile.fitnessLevel, .intermediate)
    }
    
    func testAddEquipment() throws {
        let preferences = UserPreferences()
        let profile = try repository.createUserProfile(
            name: "Test User",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .beginner,
            preferences: preferences
        )
        
        XCTAssertTrue(profile.availableEquipment.isEmpty)
        
        try repository.addEquipment(profile, equipment: .dumbbells)
        try repository.addEquipment(profile, equipment: .barbell)
        
        XCTAssertEqual(profile.availableEquipment.count, 2)
        XCTAssertTrue(profile.availableEquipment.contains(.dumbbells))
        XCTAssertTrue(profile.availableEquipment.contains(.barbell))
    }
    
    func testAddDuplicateEquipment() throws {
        let preferences = UserPreferences()
        let profile = try repository.createUserProfile(
            name: "Test User",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .beginner,
            preferences: preferences
        )
        
        try repository.addEquipment(profile, equipment: .dumbbells)
        try repository.addEquipment(profile, equipment: .dumbbells) // Should not duplicate
        
        XCTAssertEqual(profile.availableEquipment.count, 1)
        XCTAssertTrue(profile.availableEquipment.contains(.dumbbells))
    }
    
    func testRemoveEquipment() throws {
        let preferences = UserPreferences()
        let profile = try repository.createUserProfile(
            name: "Test User",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .beginner,
            preferences: preferences
        )
        
        try repository.addEquipment(profile, equipment: .dumbbells)
        try repository.addEquipment(profile, equipment: .barbell)
        
        XCTAssertEqual(profile.availableEquipment.count, 2)
        
        try repository.removeEquipment(profile, equipment: .dumbbells)
        
        XCTAssertEqual(profile.availableEquipment.count, 1)
        XCTAssertFalse(profile.availableEquipment.contains(.dumbbells))
        XCTAssertTrue(profile.availableEquipment.contains(.barbell))
    }
    
    // MARK: - Delete Tests
    
    func testDeleteUserProfile() throws {
        let preferences = UserPreferences()
        let profile = try repository.createUserProfile(
            name: "Test User",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .beginner,
            preferences: preferences
        )
        
        let profileId = profile.id
        
        try repository.deleteUserProfile(profile)
        
        let retrievedProfile = try repository.getUserProfile(by: profileId)
        XCTAssertNil(retrievedProfile)
    }
    
    func testDeleteUserProfileById() throws {
        let preferences = UserPreferences()
        let profile = try repository.createUserProfile(
            name: "Test User",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .beginner,
            preferences: preferences
        )
        
        let profileId = profile.id
        
        try repository.deleteUserProfile(by: profileId)
        
        let retrievedProfile = try repository.getUserProfile(by: profileId)
        XCTAssertNil(retrievedProfile)
    }
    
    func testDeleteAllUserProfiles() throws {
        let preferences = UserPreferences()
        
        // Create multiple profiles
        _ = try repository.createUserProfile(
            name: "User 1",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .beginner,
            preferences: preferences
        )
        
        _ = try repository.createUserProfile(
            name: "User 2",
            email: "<EMAIL>",
            age: 30,
            height: 175.0,
            weight: 70.0,
            fitnessLevel: .intermediate,
            preferences: preferences
        )
        
        let profilesBeforeDelete = try repository.getAllUserProfiles()
        XCTAssertEqual(profilesBeforeDelete.count, 2)
        
        try repository.deleteAllUserProfiles()
        
        let profilesAfterDelete = try repository.getAllUserProfiles()
        XCTAssertEqual(profilesAfterDelete.count, 0)
    }
    
    // MARK: - Statistics Tests
    
    func testGetUserProfileCount() throws {
        let preferences = UserPreferences()
        
        let initialCount = try repository.getUserProfileCount()
        XCTAssertEqual(initialCount, 0)
        
        _ = try repository.createUserProfile(
            name: "User 1",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .beginner,
            preferences: preferences
        )
        
        let countAfterOne = try repository.getUserProfileCount()
        XCTAssertEqual(countAfterOne, 1)
        
        _ = try repository.createUserProfile(
            name: "User 2",
            email: "<EMAIL>",
            age: 30,
            height: 175.0,
            weight: 70.0,
            fitnessLevel: .intermediate,
            preferences: preferences
        )
        
        let countAfterTwo = try repository.getUserProfileCount()
        XCTAssertEqual(countAfterTwo, 2)
    }
    
    func testGetAverageAge() throws {
        let preferences = UserPreferences()
        
        // Test with no profiles
        let averageAgeEmpty = try repository.getAverageAge()
        XCTAssertEqual(averageAgeEmpty, 0)
        
        // Create profiles with different ages
        _ = try repository.createUserProfile(
            name: "User 1",
            email: "<EMAIL>",
            age: 20,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .beginner,
            preferences: preferences
        )
        
        _ = try repository.createUserProfile(
            name: "User 2",
            email: "<EMAIL>",
            age: 30,
            height: 175.0,
            weight: 70.0,
            fitnessLevel: .intermediate,
            preferences: preferences
        )
        
        let averageAge = try repository.getAverageAge()
        XCTAssertEqual(averageAge, 25.0) // (20 + 30) / 2
    }
    
    func testGetAverageWeight() throws {
        let preferences = UserPreferences()
        
        // Test with no profiles
        let averageWeightEmpty = try repository.getAverageWeight()
        XCTAssertEqual(averageWeightEmpty, 0)
        
        // Create profiles with different weights
        _ = try repository.createUserProfile(
            name: "User 1",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 70.0,
            fitnessLevel: .beginner,
            preferences: preferences
        )
        
        _ = try repository.createUserProfile(
            name: "User 2",
            email: "<EMAIL>",
            age: 30,
            height: 175.0,
            weight: 80.0,
            fitnessLevel: .intermediate,
            preferences: preferences
        )
        
        let averageWeight = try repository.getAverageWeight()
        XCTAssertEqual(averageWeight, 75.0) // (70 + 80) / 2
    }
    
    func testGetFitnessLevelDistribution() throws {
        let preferences = UserPreferences()
        
        // Create profiles with different fitness levels
        _ = try repository.createUserProfile(
            name: "Beginner 1",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .beginner,
            preferences: preferences
        )
        
        _ = try repository.createUserProfile(
            name: "Beginner 2",
            email: "<EMAIL>",
            age: 30,
            height: 175.0,
            weight: 70.0,
            fitnessLevel: .beginner,
            preferences: preferences
        )
        
        _ = try repository.createUserProfile(
            name: "Intermediate 1",
            email: "<EMAIL>",
            age: 28,
            height: 178.0,
            weight: 73.0,
            fitnessLevel: .intermediate,
            preferences: preferences
        )
        
        let distribution = try repository.getFitnessLevelDistribution()
        
        XCTAssertEqual(distribution[.beginner], 2)
        XCTAssertEqual(distribution[.intermediate], 1)
        XCTAssertEqual(distribution[.advanced], 0)
        XCTAssertEqual(distribution[.expert], 0)
    }
}