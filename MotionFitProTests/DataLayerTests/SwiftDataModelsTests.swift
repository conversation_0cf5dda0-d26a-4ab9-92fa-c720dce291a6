import XCTest
import SwiftData
@testable import MotionFitPro

@MainActor
final class SwiftDataModelsTests: XCTestCase {
    private var dataController: DataController!
    private var testContainer: ModelContainer!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Create in-memory test container
        let schema = Schema([
            UserProfile.self,
            WorkoutSession.self,
            ExercisePerformance.self,
            SetPerformance.self,
            RepPerformance.self
        ])
        
        let configuration = ModelConfiguration(
            schema: schema,
            isStoredInMemoryOnly: true,
            allowsSave: true
        )
        
        testContainer = try ModelContainer(for: schema, configurations: [configuration])
        
        // Create test data controller
        dataController = DataController()
        dataController.container = testContainer
        dataController.context = ModelContext(testContainer)
    }
    
    override func tearDown() async throws {
        dataController = nil
        testContainer = nil
        try await super.tearDown()
    }
    
    // MARK: - UserProfile Model Tests
    
    func testUserProfileCreation() throws {
        let preferences = UserPreferences()
        let profile = UserProfile(
            name: "Test User",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .intermediate,
            fitnessGoals: [.buildMuscle, .loseWeight],
            preferences: preferences
        )
        
        XCTAssertEqual(profile.name, "Test User")
        XCTAssertEqual(profile.email, "<EMAIL>")
        XCTAssertEqual(profile.age, 25)
        XCTAssertEqual(profile.height, 180.0)
        XCTAssertEqual(profile.weight, 75.0)
        XCTAssertEqual(profile.fitnessLevel, .intermediate)
        XCTAssertEqual(profile.fitnessGoals.count, 2)
        XCTAssertNotNil(profile.id)
        XCTAssertNotNil(profile.createdAt)
        XCTAssertNotNil(profile.updatedAt)
    }
    
    func testUserProfileComputedProperties() throws {
        let profile = UserProfile(
            name: "Test User",
            email: "<EMAIL>",
            age: 25,
            height: 180.0, // 1.8m
            weight: 75.0,  // 75kg
            fitnessLevel: .intermediate
        )
        
        // Test BMI calculation: weight(kg) / height(m)^2
        let expectedBMI = 75.0 / (1.8 * 1.8) // ≈ 23.15
        XCTAssertEqual(profile.bmi, expectedBMI, accuracy: 0.01)
        
        // Test BMI category
        XCTAssertEqual(profile.bmiCategory, .normal)
        
        // Test height in feet
        let expectedHeightInFeet = 180.0 / 30.48 // ≈ 5.91 feet
        XCTAssertEqual(profile.heightInFeet, expectedHeightInFeet, accuracy: 0.01)
        
        // Test weight in pounds
        let expectedWeightInPounds = 75.0 * 2.20462 // ≈ 165.35 lbs
        XCTAssertEqual(profile.weightInPounds, expectedWeightInPounds, accuracy: 0.01)
    }
    
    func testUserProfileBMICategories() throws {
        // Test underweight
        let underweightProfile = UserProfile(
            name: "Underweight",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 50.0, // BMI ≈ 15.4
            fitnessLevel: .beginner
        )
        XCTAssertEqual(underweightProfile.bmiCategory, .underweight)
        
        // Test normal
        let normalProfile = UserProfile(
            name: "Normal",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 70.0, // BMI ≈ 21.6
            fitnessLevel: .intermediate
        )
        XCTAssertEqual(normalProfile.bmiCategory, .normal)
        
        // Test overweight
        let overweightProfile = UserProfile(
            name: "Overweight",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 85.0, // BMI ≈ 26.2
            fitnessLevel: .intermediate
        )
        XCTAssertEqual(overweightProfile.bmiCategory, .overweight)
        
        // Test obese
        let obeseProfile = UserProfile(
            name: "Obese",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 110.0, // BMI ≈ 34.0
            fitnessLevel: .beginner
        )
        XCTAssertEqual(obeseProfile.bmiCategory, .obese)
    }
    
    func testUserProfileHelperMethods() throws {
        let profile = UserProfile(
            name: "Test User",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .intermediate
        )
        
        // Test addWeightEntry
        profile.addWeightEntry(weight: 76.0)
        XCTAssertEqual(profile.weightHistory.count, 1)
        XCTAssertEqual(profile.weightHistory.last?.weight, 76.0)
        XCTAssertEqual(profile.weight, 76.0) // Current weight should update
        
        // Test updateFitnessGoals
        profile.updateFitnessGoals([.buildMuscle, .improveStrength])
        XCTAssertEqual(profile.fitnessGoals.count, 2)
        XCTAssertTrue(profile.fitnessGoals.contains(.buildMuscle))
        XCTAssertTrue(profile.fitnessGoals.contains(.improveStrength))
    }
    
    // MARK: - WorkoutSession Model Tests
    
    func testWorkoutSessionCreation() throws {
        let workout = WorkoutSession(
            workoutName: "Test Workout",
            category: .strength,
            difficulty: .intermediate,
            targetCalories: 400.0,
            targetDuration: 60 * 60 // 1 hour
        )
        
        XCTAssertEqual(workout.workoutName, "Test Workout")
        XCTAssertEqual(workout.category, .strength)
        XCTAssertEqual(workout.difficulty, .intermediate)
        XCTAssertEqual(workout.targetCalories, 400.0)
        XCTAssertEqual(workout.targetDuration, 60 * 60)
        XCTAssertFalse(workout.isCompleted)
        XCTAssertFalse(workout.isPaused)
        XCTAssertFalse(workout.wasInterrupted)
        XCTAssertEqual(workout.completionPercentage, 0.0)
        XCTAssertNotNil(workout.id)
        XCTAssertNotNil(workout.startTime)
    }
    
    func testWorkoutSessionComputedProperties() throws {
        let workout = WorkoutSession(
            workoutName: "Test Workout",
            category: .strength,
            difficulty: .intermediate
        )
        
        let startTime = Date()
        workout.startTime = startTime
        workout.endTime = startTime.addingTimeInterval(3600) // 1 hour later
        workout.pausedDuration = 300 // 5 minutes paused
        
        // Test duration calculation
        XCTAssertEqual(workout.duration, 3300) // 1 hour - 5 minutes = 55 minutes
        
        // Test active duration
        XCTAssertEqual(workout.activeDuration, 3000) // 55 minutes - 5 minutes = 50 minutes
        
        // Test calories per minute
        workout.totalCaloriesBurned = 300
        let expectedCaloriesPerMinute = 300 / (3300 / 60) // 300 calories / 55 minutes
        XCTAssertEqual(workout.caloriesPerMinute, expectedCaloriesPerMinute, accuracy: 0.01)
        
        // Test isActivelyRunning
        workout.isCompleted = false
        workout.isPaused = false
        workout.endTime = nil
        XCTAssertTrue(workout.isActivelyRunning)
        
        workout.isPaused = true
        XCTAssertFalse(workout.isActivelyRunning)
    }
    
    func testWorkoutSessionHelperMethods() throws {
        let workout = WorkoutSession(
            workoutName: "Test Workout",
            category: .strength,
            difficulty: .intermediate
        )
        
        // Test startWorkout
        workout.startWorkout()
        XCTAssertFalse(workout.isPaused)
        XCTAssertFalse(workout.isCompleted)
        
        // Test pauseWorkout
        workout.pauseWorkout()
        XCTAssertTrue(workout.isPaused)
        
        // Test resumeWorkout
        workout.resumeWorkout()
        XCTAssertFalse(workout.isPaused)
        
        // Test completeWorkout
        workout.completeWorkout()
        XCTAssertTrue(workout.isCompleted)
        XCTAssertFalse(workout.isPaused)
        XCTAssertEqual(workout.completionPercentage, 100.0)
        XCTAssertNotNil(workout.endTime)
        
        // Test updateHeartRate
        workout.updateHeartRate(current: 140.0)
        XCTAssertEqual(workout.averageHeartRate, 140.0)
        XCTAssertEqual(workout.maxHeartRate, 140.0)
        
        workout.updateHeartRate(current: 160.0)
        XCTAssertEqual(workout.averageHeartRate, 150.0) // Moving average
        XCTAssertEqual(workout.maxHeartRate, 160.0)
    }
    
    // MARK: - ExercisePerformance Model Tests
    
    func testExercisePerformanceCreation() throws {
        let exercise = ExercisePerformance(
            exerciseName: "Barbell Squat",
            exerciseType: .compound,
            targetMuscleGroups: [.quadriceps, .glutes],
            targetSets: 4,
            targetReps: 8,
            targetWeight: 100.0,
            equipment: [.barbell]
        )
        
        XCTAssertEqual(exercise.exerciseName, "Barbell Squat")
        XCTAssertEqual(exercise.exerciseType, .compound)
        XCTAssertEqual(exercise.targetMuscleGroups.count, 2)
        XCTAssertTrue(exercise.targetMuscleGroups.contains(.quadriceps))
        XCTAssertTrue(exercise.targetMuscleGroups.contains(.glutes))
        XCTAssertEqual(exercise.targetSets, 4)
        XCTAssertEqual(exercise.targetReps, 8)
        XCTAssertEqual(exercise.targetWeight, 100.0)
        XCTAssertEqual(exercise.equipment.count, 1)
        XCTAssertTrue(exercise.equipment.contains(.barbell))
    }
    
    func testExercisePerformanceComputedProperties() throws {
        let exercise = ExercisePerformance(
            exerciseName: "Test Exercise",
            exerciseType: .strength,
            targetMuscleGroups: [.chest],
            targetSets: 3
        )
        
        // Test with target duration
        exercise.targetDuration = 30 * 60 // 30 minutes
        XCTAssertTrue(exercise.isTimeBased)
        XCTAssertFalse(exercise.isRepBased)
        
        // Test with target reps
        exercise.targetDuration = nil
        exercise.targetReps = 10
        XCTAssertFalse(exercise.isTimeBased)
        XCTAssertTrue(exercise.isRepBased)
        
        // Test completion percentage with sets
        exercise.targetSets = 3
        // Would need to add actual sets to test this properly
        XCTAssertEqual(exercise.completionPercentage, 0) // No completed sets
    }
    
    // MARK: - SetPerformance Model Tests
    
    func testSetPerformanceCreation() throws {
        let set = SetPerformance(
            setNumber: 1,
            targetReps: 10,
            weight: 50.0,
            targetRestTime: 90
        )
        
        XCTAssertEqual(set.setNumber, 1)
        XCTAssertEqual(set.targetReps, 10)
        XCTAssertEqual(set.weight, 50.0)
        XCTAssertEqual(set.targetRestTime, 90)
        XCTAssertEqual(set.actualReps, 0)
        XCTAssertFalse(set.isCompleted)
        XCTAssertFalse(set.isSkipped)
    }
    
    func testSetPerformanceComputedProperties() throws {
        let set = SetPerformance(
            setNumber: 1,
            targetReps: 10,
            weight: 50.0,
            targetRestTime: 90
        )
        
        set.actualReps = 8
        
        // Test volume calculation
        XCTAssertEqual(set.volume, 400.0) // 50kg * 8 reps
        
        // Test intensity (completion percentage)
        XCTAssertEqual(set.intensity, 80.0) // 8/10 * 100%
        
        // Test with time-based set
        set.targetReps = nil
        set.targetDuration = 60 // 1 minute
        set.actualDuration = 45 // 45 seconds
        XCTAssertEqual(set.intensity, 75.0) // 45/60 * 100%
        
        // Test isTimeBased and isRepBased
        XCTAssertTrue(set.isTimeBased)
        XCTAssertFalse(set.isRepBased)
        
        set.targetDuration = nil
        set.targetReps = 10
        XCTAssertFalse(set.isTimeBased)
        XCTAssertTrue(set.isRepBased)
        
        // Test rest efficiency
        set.actualRestTime = 90 // Exactly on target
        XCTAssertEqual(set.restEfficiency, 100.0)
        
        set.actualRestTime = 120 // 30 seconds over
        XCTAssertLessThan(set.restEfficiency, 100.0)
    }
    
    func testSetPerformanceHelperMethods() throws {
        let set = SetPerformance(
            setNumber: 1,
            targetReps: 10,
            weight: 50.0
        )
        
        // Test startSet
        set.startSet()
        XCTAssertNotNil(set.startTime)
        
        // Test completeSet
        set.actualReps = 10
        set.completeSet()
        XCTAssertTrue(set.isCompleted)
        XCTAssertNotNil(set.endTime)
        XCTAssertGreaterThan(set.actualDuration, 0)
        
        // Test skipSet
        let skipSet = SetPerformance(setNumber: 2, targetReps: 10)
        skipSet.skipSet(reason: "Fatigue")
        XCTAssertTrue(skipSet.isSkipped)
        XCTAssertEqual(skipSet.skipReason, "Fatigue")
        XCTAssertNotNil(skipSet.endTime)
    }
    
    // MARK: - RepPerformance Model Tests
    
    func testRepPerformanceCreation() throws {
        let rep = RepPerformance(
            repNumber: 1,
            duration: 3.5,
            formScore: 85.0
        )
        
        XCTAssertEqual(rep.repNumber, 1)
        XCTAssertEqual(rep.duration, 3.5)
        XCTAssertEqual(rep.formScore, 85.0)
        XCTAssertEqual(rep.bodyPosture, .neutral)
        XCTAssertNotNil(rep.id)
        XCTAssertNotNil(rep.timestamp)
    }
    
    func testRepPerformanceComputedProperties() throws {
        let rep = RepPerformance(
            repNumber: 1,
            formScore: 85.0
        )
        
        // Test form grade
        XCTAssertEqual(rep.formGrade, .b) // 85% should be B
        
        rep.formScore = 95.0
        XCTAssertEqual(rep.formGrade, .aPlus) // 95% should be A+
        
        rep.formScore = 55.0
        XCTAssertEqual(rep.formGrade, .d) // 55% should be D
        
        // Test isFormAcceptable
        rep.formScore = 75.0
        XCTAssertTrue(rep.isFormAcceptable) // >= 70%
        
        rep.formScore = 65.0
        XCTAssertFalse(rep.isFormAcceptable) // < 70%
        
        // Test isFormExcellent
        rep.formScore = 92.0
        XCTAssertTrue(rep.isFormExcellent) // >= 90%
        
        rep.formScore = 88.0
        XCTAssertFalse(rep.isFormExcellent) // < 90%
        
        // Test hasFormIssues
        rep.formScore = 65.0
        XCTAssertTrue(rep.hasFormIssues) // < 60%
        
        rep.formScore = 75.0
        rep.technicalErrors = [
            TechnicalError(
                type: .posture,
                description: "Poor posture",
                severity: .moderate
            )
        ]
        XCTAssertTrue(rep.hasFormIssues) // Has technical errors
        
        // Test symmetry status
        rep.leftRightSymmetry = 95.0
        XCTAssertEqual(rep.symmetryStatus, .excellent)
        
        rep.leftRightSymmetry = 85.0
        XCTAssertEqual(rep.symmetryStatus, .good)
        
        rep.leftRightSymmetry = 75.0
        XCTAssertEqual(rep.symmetryStatus, .fair)
        
        rep.leftRightSymmetry = 65.0
        XCTAssertEqual(rep.symmetryStatus, .poor)
        
        rep.leftRightSymmetry = nil
        XCTAssertEqual(rep.symmetryStatus, .unknown)
        
        // Test tempo calculation
        rep.eccentricDuration = 3.0
        rep.pauseDuration = 1.0
        rep.concentricDuration = 2.0
        XCTAssertEqual(rep.tempo, "3-1-2-1")
        
        // Test velocity ratio
        rep.velocity = 2.0
        rep.averageVelocity = 1.5
        XCTAssertEqual(rep.velocityRatio, 2.0 / 1.5, accuracy: 0.01)
    }
    
    func testRepPerformanceHelperMethods() throws {
        let rep = RepPerformance(
            repNumber: 1,
            formScore: 75.0
        )
        
        // Test updateFormScore
        rep.updateFormScore(newScore: 85.0)
        XCTAssertEqual(rep.formScore, 85.0)
        
        // Test updateFormScore with bounds
        rep.updateFormScore(newScore: 105.0) // Should clamp to 100
        XCTAssertEqual(rep.formScore, 100.0)
        
        rep.updateFormScore(newScore: -5.0) // Should clamp to 0
        XCTAssertEqual(rep.formScore, 0.0)
        
        // Test addFormFeedback
        let feedback = FormFeedback(
            type: .positive,
            message: "Great form!",
            priority: .low
        )
        rep.addFormFeedback(feedback)
        XCTAssertEqual(rep.formFeedback.count, 1)
        XCTAssertEqual(rep.formFeedback.first?.message, "Great form!")
        
        // Test addTechnicalError
        let error = TechnicalError(
            type: .posture,
            description: "Rounded shoulders",
            severity: .moderate
        )
        let originalScore = rep.formScore
        rep.addTechnicalError(error)
        XCTAssertEqual(rep.technicalErrors.count, 1)
        XCTAssertLessThan(rep.formScore, originalScore) // Score should decrease
        
        // Test updateMovementMetrics
        rep.updateMovementMetrics(velocity: 1.5, acceleration: 2.0, power: 300.0)
        XCTAssertEqual(rep.velocity, 1.5)
        XCTAssertEqual(rep.acceleration, 2.0)
        XCTAssertEqual(rep.powerOutput, 300.0)
        
        // Test updatePhaseTimings
        rep.updatePhaseTimings(eccentric: 3.0, concentric: 1.5, pause: 1.0)
        XCTAssertEqual(rep.eccentricDuration, 3.0)
        XCTAssertEqual(rep.concentricDuration, 1.5)
        XCTAssertEqual(rep.pauseDuration, 1.0)
        
        // Test updateJointAngle
        rep.updateJointAngle(joint: "knee", angle: 90.0)
        XCTAssertEqual(rep.jointAngles["knee"], 90.0)
        
        // Test generateFormSummary
        rep.formScore = 85.0
        rep.rangeOfMotion = 95.0
        rep.leftRightSymmetry = 88.0
        let summary = rep.generateFormSummary()
        XCTAssertTrue(summary.contains("Rep 1"))
        XCTAssertTrue(summary.contains("85"))
        XCTAssertTrue(summary.contains("ROM: 95"))
    }
    
    // MARK: - Supporting Types Tests
    
    func testFitnessLevelMultiplier() {
        XCTAssertEqual(FitnessLevel.beginner.multiplier, 0.8)
        XCTAssertEqual(FitnessLevel.intermediate.multiplier, 1.0)
        XCTAssertEqual(FitnessLevel.advanced.multiplier, 1.2)
        XCTAssertEqual(FitnessLevel.expert.multiplier, 1.4)
    }
    
    func testDifficultyLevelMultiplier() {
        XCTAssertEqual(DifficultyLevel.beginner.multiplier, 0.8)
        XCTAssertEqual(DifficultyLevel.intermediate.multiplier, 1.0)
        XCTAssertEqual(DifficultyLevel.advanced.multiplier, 1.3)
        XCTAssertEqual(DifficultyLevel.expert.multiplier, 1.6)
    }
    
    func testExerciseIntensityMultiplier() {
        XCTAssertEqual(ExerciseIntensity.low.calorieMultiplier, 0.7)
        XCTAssertEqual(ExerciseIntensity.moderate.calorieMultiplier, 1.0)
        XCTAssertEqual(ExerciseIntensity.high.calorieMultiplier, 1.3)
        XCTAssertEqual(ExerciseIntensity.extreme.calorieMultiplier, 1.6)
    }
    
    func testErrorSeverityScorePenalty() {
        XCTAssertEqual(ErrorSeverity.minor.scorePenalty, 2.0)
        XCTAssertEqual(ErrorSeverity.moderate.scorePenalty, 5.0)
        XCTAssertEqual(ErrorSeverity.major.scorePenalty, 10.0)
        XCTAssertEqual(ErrorSeverity.critical.scorePenalty, 20.0)
    }
    
    func testBodyPostureScoreMultiplier() {
        XCTAssertEqual(BodyPosture.excellent.scoreMultiplier, 1.0)
        XCTAssertEqual(BodyPosture.good.scoreMultiplier, 0.95)
        XCTAssertEqual(BodyPosture.neutral.scoreMultiplier, 0.9)
        XCTAssertEqual(BodyPosture.fair.scoreMultiplier, 0.8)
        XCTAssertEqual(BodyPosture.poor.scoreMultiplier, 0.6)
    }
}