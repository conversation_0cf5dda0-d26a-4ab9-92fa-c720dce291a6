import XCTest
import SwiftData
@testable import MotionFitPro

@MainActor
final class WorkoutSessionRepositoryTests: XCTestCase {
    private var dataController: DataController!
    private var workoutRepository: WorkoutSessionRepository!
    private var userRepository: UserProfileRepository!
    private var testContainer: ModelContainer!
    private var testUser: UserProfile!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Create in-memory test container
        let schema = Schema([
            UserProfile.self,
            WorkoutSession.self,
            ExercisePerformance.self,
            SetPerformance.self,
            RepPerformance.self
        ])
        
        let configuration = ModelConfiguration(
            schema: schema,
            isStoredInMemoryOnly: true,
            allowsSave: true
        )
        
        testContainer = try ModelContainer(for: schema, configurations: [configuration])
        
        // Create test data controller
        dataController = DataController()
        dataController.container = testContainer
        dataController.context = ModelContext(testContainer)
        
        workoutRepository = WorkoutSessionRepository(dataController: dataController)
        userRepository = UserProfileRepository(dataController: dataController)
        
        // Create test user
        testUser = try userRepository.createUserProfile(
            name: "Test User",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .intermediate
        )
    }
    
    override func tearDown() async throws {
        dataController = nil
        workoutRepository = nil
        userRepository = nil
        testUser = nil
        testContainer = nil
        try await super.tearDown()
    }
    
    // MARK: - Create Tests
    
    func testCreateWorkoutSession() throws {
        let workout = try workoutRepository.createWorkoutSession(
            workoutName: "Test Workout",
            category: .strength,
            difficulty: .intermediate,
            user: testUser,
            targetCalories: 400.0,
            targetDuration: 60 * 60 // 1 hour
        )
        
        XCTAssertEqual(workout.workoutName, "Test Workout")
        XCTAssertEqual(workout.category, .strength)
        XCTAssertEqual(workout.difficulty, .intermediate)
        XCTAssertEqual(workout.user?.id, testUser.id)
        XCTAssertEqual(workout.targetCalories, 400.0)
        XCTAssertEqual(workout.targetDuration, 60 * 60)
        XCTAssertFalse(workout.isCompleted)
        XCTAssertFalse(workout.isPaused)
    }
    
    func testCreateWorkoutSessionWithOptionalParameters() throws {
        let workout = try workoutRepository.createWorkoutSession(
            workoutName: "Simple Workout",
            category: .cardio,
            difficulty: .beginner,
            user: testUser
        )
        
        XCTAssertEqual(workout.workoutName, "Simple Workout")
        XCTAssertEqual(workout.category, .cardio)
        XCTAssertEqual(workout.difficulty, .beginner)
        XCTAssertNil(workout.targetCalories)
        XCTAssertNil(workout.targetDuration)
    }
    
    // MARK: - Read Tests
    
    func testGetWorkoutSessionById() throws {
        let workout = try workoutRepository.createWorkoutSession(
            workoutName: "Test Workout",
            category: .strength,
            difficulty: .intermediate,
            user: testUser
        )
        
        let retrievedWorkout = try workoutRepository.getWorkoutSession(by: workout.id)
        
        XCTAssertNotNil(retrievedWorkout)
        XCTAssertEqual(retrievedWorkout?.id, workout.id)
        XCTAssertEqual(retrievedWorkout?.workoutName, "Test Workout")
    }
    
    func testGetWorkoutSessionsByUser() throws {
        // Create multiple workouts for the user
        _ = try workoutRepository.createWorkoutSession(
            workoutName: "Workout 1",
            category: .strength,
            difficulty: .intermediate,
            user: testUser
        )
        
        _ = try workoutRepository.createWorkoutSession(
            workoutName: "Workout 2",
            category: .cardio,
            difficulty: .beginner,
            user: testUser
        )
        
        // Create another user and workout
        let otherUser = try userRepository.createUserProfile(
            name: "Other User",
            email: "<EMAIL>",
            age: 30,
            height: 175.0,
            weight: 70.0,
            fitnessLevel: .beginner
        )
        
        _ = try workoutRepository.createWorkoutSession(
            workoutName: "Other Workout",
            category: .hiit,
            difficulty: .advanced,
            user: otherUser
        )
        
        let userWorkouts = try workoutRepository.getWorkoutSessions(for: testUser)
        
        XCTAssertEqual(userWorkouts.count, 2)
        XCTAssertTrue(userWorkouts.allSatisfy { $0.user?.id == testUser.id })
    }
    
    func testGetCompletedWorkoutSessions() throws {
        let workout1 = try workoutRepository.createWorkoutSession(
            workoutName: "Completed Workout",
            category: .strength,
            difficulty: .intermediate,
            user: testUser
        )
        
        let workout2 = try workoutRepository.createWorkoutSession(
            workoutName: "Incomplete Workout",
            category: .cardio,
            difficulty: .beginner,
            user: testUser
        )
        
        // Complete one workout
        try workoutRepository.completeWorkout(workout1)
        
        let completedWorkouts = try workoutRepository.getCompletedWorkoutSessions(for: testUser)
        
        XCTAssertEqual(completedWorkouts.count, 1)
        XCTAssertEqual(completedWorkouts.first?.id, workout1.id)
        XCTAssertTrue(completedWorkouts.first?.isCompleted == true)
    }
    
    func testGetWorkoutSessionsByCategory() throws {
        _ = try workoutRepository.createWorkoutSession(
            workoutName: "Strength Workout",
            category: .strength,
            difficulty: .intermediate,
            user: testUser
        )
        
        _ = try workoutRepository.createWorkoutSession(
            workoutName: "Cardio Workout",
            category: .cardio,
            difficulty: .beginner,
            user: testUser
        )
        
        _ = try workoutRepository.createWorkoutSession(
            workoutName: "Another Strength",
            category: .strength,
            difficulty: .advanced,
            user: testUser
        )
        
        let strengthWorkouts = try workoutRepository.getWorkoutSessions(category: .strength, for: testUser)
        let cardioWorkouts = try workoutRepository.getWorkoutSessions(category: .cardio, for: testUser)
        
        XCTAssertEqual(strengthWorkouts.count, 2)
        XCTAssertEqual(cardioWorkouts.count, 1)
        XCTAssertTrue(strengthWorkouts.allSatisfy { $0.category == .strength })
        XCTAssertTrue(cardioWorkouts.allSatisfy { $0.category == .cardio })
    }
    
    func testGetWorkoutSessionsInDateRange() throws {
        let calendar = Calendar.current
        let now = Date()
        let yesterday = calendar.date(byAdding: .day, value: -1, to: now)!
        let lastWeek = calendar.date(byAdding: .day, value: -7, to: now)!
        
        let oldWorkout = try workoutRepository.createWorkoutSession(
            workoutName: "Old Workout",
            category: .strength,
            difficulty: .intermediate,
            user: testUser
        )
        oldWorkout.startTime = lastWeek
        
        let recentWorkout = try workoutRepository.createWorkoutSession(
            workoutName: "Recent Workout",
            category: .cardio,
            difficulty: .beginner,
            user: testUser
        )
        recentWorkout.startTime = yesterday
        
        try workoutRepository.updateWorkoutSession(oldWorkout)
        try workoutRepository.updateWorkoutSession(recentWorkout)
        
        let twoDaysAgo = calendar.date(byAdding: .day, value: -2, to: now)!
        let tomorrow = calendar.date(byAdding: .day, value: 1, to: now)!
        
        let workoutsInRange = try workoutRepository.getWorkoutSessionsInDateRange(
            from: twoDaysAgo,
            to: tomorrow,
            for: testUser
        )
        
        XCTAssertEqual(workoutsInRange.count, 1)
        XCTAssertEqual(workoutsInRange.first?.workoutName, "Recent Workout")
    }
    
    func testGetCurrentActiveWorkout() throws {
        // Create completed workout
        let completedWorkout = try workoutRepository.createWorkoutSession(
            workoutName: "Completed Workout",
            category: .strength,
            difficulty: .intermediate,
            user: testUser
        )
        try workoutRepository.completeWorkout(completedWorkout)
        
        // Create active workout
        let activeWorkout = try workoutRepository.createWorkoutSession(
            workoutName: "Active Workout",
            category: .cardio,
            difficulty: .beginner,
            user: testUser
        )
        try workoutRepository.startWorkout(activeWorkout)
        
        let currentActive = try workoutRepository.getCurrentActiveWorkout(for: testUser)
        
        XCTAssertNotNil(currentActive)
        XCTAssertEqual(currentActive?.id, activeWorkout.id)
        XCTAssertEqual(currentActive?.workoutName, "Active Workout")
    }
    
    // MARK: - Update Tests
    
    func testStartWorkout() throws {
        let workout = try workoutRepository.createWorkoutSession(
            workoutName: "Test Workout",
            category: .strength,
            difficulty: .intermediate,
            user: testUser
        )
        
        XCTAssertFalse(workout.isActivelyRunning)
        
        try workoutRepository.startWorkout(workout)
        
        XCTAssertTrue(workout.isActivelyRunning)
        XCTAssertFalse(workout.isPaused)
        XCTAssertFalse(workout.isCompleted)
    }
    
    func testPauseAndResumeWorkout() throws {
        let workout = try workoutRepository.createWorkoutSession(
            workoutName: "Test Workout",
            category: .strength,
            difficulty: .intermediate,
            user: testUser
        )
        
        try workoutRepository.startWorkout(workout)
        XCTAssertFalse(workout.isPaused)
        
        try workoutRepository.pauseWorkout(workout)
        XCTAssertTrue(workout.isPaused)
        
        try workoutRepository.resumeWorkout(workout)
        XCTAssertFalse(workout.isPaused)
    }
    
    func testCompleteWorkout() throws {
        let workout = try workoutRepository.createWorkoutSession(
            workoutName: "Test Workout",
            category: .strength,
            difficulty: .intermediate,
            user: testUser,
            targetCalories: 400.0
        )
        
        try workoutRepository.startWorkout(workout)
        XCTAssertFalse(workout.isCompleted)
        XCTAssertEqual(workout.completionPercentage, 0.0)
        
        try workoutRepository.completeWorkout(workout)
        
        XCTAssertTrue(workout.isCompleted)
        XCTAssertFalse(workout.isPaused)
        XCTAssertEqual(workout.completionPercentage, 100.0)
        XCTAssertNotNil(workout.endTime)
    }
    
    func testInterruptWorkout() throws {
        let workout = try workoutRepository.createWorkoutSession(
            workoutName: "Test Workout",
            category: .strength,
            difficulty: .intermediate,
            user: testUser
        )
        
        try workoutRepository.startWorkout(workout)
        XCTAssertFalse(workout.wasInterrupted)
        
        try workoutRepository.interruptWorkout(workout)
        
        XCTAssertTrue(workout.wasInterrupted)
        XCTAssertFalse(workout.isPaused)
        XCTAssertNotNil(workout.endTime)
    }
    
    func testUpdateWorkoutRating() throws {
        let workout = try workoutRepository.createWorkoutSession(
            workoutName: "Test Workout",
            category: .strength,
            difficulty: .intermediate,
            user: testUser
        )
        
        XCTAssertNil(workout.rating)
        
        try workoutRepository.updateWorkoutRating(workout, rating: 4)
        XCTAssertEqual(workout.rating, 4)
        
        // Test rating bounds
        try workoutRepository.updateWorkoutRating(workout, rating: 0) // Should clamp to 1
        XCTAssertEqual(workout.rating, 1)
        
        try workoutRepository.updateWorkoutRating(workout, rating: 6) // Should clamp to 5
        XCTAssertEqual(workout.rating, 5)
    }
    
    func testUpdateHeartRate() throws {
        let workout = try workoutRepository.createWorkoutSession(
            workoutName: "Test Workout",
            category: .cardio,
            difficulty: .intermediate,
            user: testUser
        )
        
        XCTAssertNil(workout.averageHeartRate)
        XCTAssertNil(workout.maxHeartRate)
        
        try workoutRepository.updateHeartRate(workout, current: 140.0)
        
        XCTAssertEqual(workout.averageHeartRate, 140.0)
        XCTAssertEqual(workout.maxHeartRate, 140.0)
        
        try workoutRepository.updateHeartRate(workout, current: 160.0)
        
        XCTAssertEqual(workout.averageHeartRate, 150.0) // Simple moving average
        XCTAssertEqual(workout.maxHeartRate, 160.0) // Updated max
    }
    
    // MARK: - Delete Tests
    
    func testDeleteWorkoutSession() throws {
        let workout = try workoutRepository.createWorkoutSession(
            workoutName: "Test Workout",
            category: .strength,
            difficulty: .intermediate,
            user: testUser
        )
        
        let workoutId = workout.id
        
        try workoutRepository.deleteWorkoutSession(workout)
        
        let retrievedWorkout = try workoutRepository.getWorkoutSession(by: workoutId)
        XCTAssertNil(retrievedWorkout)
    }
    
    func testDeleteWorkoutSessionById() throws {
        let workout = try workoutRepository.createWorkoutSession(
            workoutName: "Test Workout",
            category: .strength,
            difficulty: .intermediate,
            user: testUser
        )
        
        let workoutId = workout.id
        
        try workoutRepository.deleteWorkoutSession(by: workoutId)
        
        let retrievedWorkout = try workoutRepository.getWorkoutSession(by: workoutId)
        XCTAssertNil(retrievedWorkout)
    }
    
    func testDeleteOldWorkoutSessions() throws {
        let calendar = Calendar.current
        let now = Date()
        let lastWeek = calendar.date(byAdding: .day, value: -7, to: now)!
        let yesterday = calendar.date(byAdding: .day, value: -1, to: now)!
        
        let oldWorkout = try workoutRepository.createWorkoutSession(
            workoutName: "Old Workout",
            category: .strength,
            difficulty: .intermediate,
            user: testUser
        )
        oldWorkout.startTime = lastWeek
        
        let recentWorkout = try workoutRepository.createWorkoutSession(
            workoutName: "Recent Workout",
            category: .cardio,
            difficulty: .beginner,
            user: testUser
        )
        recentWorkout.startTime = yesterday
        
        try workoutRepository.updateWorkoutSession(oldWorkout)
        try workoutRepository.updateWorkoutSession(recentWorkout)
        
        let workoutsBeforeDelete = try workoutRepository.getWorkoutSessions(for: testUser)
        XCTAssertEqual(workoutsBeforeDelete.count, 2)
        
        let threeDaysAgo = calendar.date(byAdding: .day, value: -3, to: now)!
        try workoutRepository.deleteWorkoutSessionsOlderThan(threeDaysAgo)
        
        let workoutsAfterDelete = try workoutRepository.getWorkoutSessions(for: testUser)
        XCTAssertEqual(workoutsAfterDelete.count, 1)
        XCTAssertEqual(workoutsAfterDelete.first?.workoutName, "Recent Workout")
    }
    
    // MARK: - Statistics Tests
    
    func testGetWorkoutSessionCount() throws {
        let initialCount = try workoutRepository.getWorkoutSessionCount(for: testUser)
        XCTAssertEqual(initialCount, 0)
        
        _ = try workoutRepository.createWorkoutSession(
            workoutName: "Workout 1",
            category: .strength,
            difficulty: .intermediate,
            user: testUser
        )
        
        let countAfterOne = try workoutRepository.getWorkoutSessionCount(for: testUser)
        XCTAssertEqual(countAfterOne, 1)
        
        _ = try workoutRepository.createWorkoutSession(
            workoutName: "Workout 2",
            category: .cardio,
            difficulty: .beginner,
            user: testUser
        )
        
        let countAfterTwo = try workoutRepository.getWorkoutSessionCount(for: testUser)
        XCTAssertEqual(countAfterTwo, 2)
    }
    
    func testGetCompletedWorkoutCount() throws {
        let workout1 = try workoutRepository.createWorkoutSession(
            workoutName: "Completed Workout",
            category: .strength,
            difficulty: .intermediate,
            user: testUser
        )
        
        let workout2 = try workoutRepository.createWorkoutSession(
            workoutName: "Incomplete Workout",
            category: .cardio,
            difficulty: .beginner,
            user: testUser
        )
        
        let initialCompletedCount = try workoutRepository.getCompletedWorkoutCount(for: testUser)
        XCTAssertEqual(initialCompletedCount, 0)
        
        try workoutRepository.completeWorkout(workout1)
        
        let completedCountAfterOne = try workoutRepository.getCompletedWorkoutCount(for: testUser)
        XCTAssertEqual(completedCountAfterOne, 1)
    }
    
    func testGetCategoryDistribution() throws {
        _ = try workoutRepository.createWorkoutSession(
            workoutName: "Strength 1",
            category: .strength,
            difficulty: .intermediate,
            user: testUser
        )
        
        _ = try workoutRepository.createWorkoutSession(
            workoutName: "Strength 2",
            category: .strength,
            difficulty: .advanced,
            user: testUser
        )
        
        _ = try workoutRepository.createWorkoutSession(
            workoutName: "Cardio 1",
            category: .cardio,
            difficulty: .beginner,
            user: testUser
        )
        
        let distribution = try workoutRepository.getCategoryDistribution(for: testUser)
        
        XCTAssertEqual(distribution[.strength], 2)
        XCTAssertEqual(distribution[.cardio], 1)
        XCTAssertEqual(distribution[.hiit] ?? 0, 0)
    }
    
    func testGetDifficultyDistribution() throws {
        _ = try workoutRepository.createWorkoutSession(
            workoutName: "Beginner Workout",
            category: .strength,
            difficulty: .beginner,
            user: testUser
        )
        
        _ = try workoutRepository.createWorkoutSession(
            workoutName: "Intermediate 1",
            category: .cardio,
            difficulty: .intermediate,
            user: testUser
        )
        
        _ = try workoutRepository.createWorkoutSession(
            workoutName: "Intermediate 2",
            category: .hiit,
            difficulty: .intermediate,
            user: testUser
        )
        
        let distribution = try workoutRepository.getDifficultyDistribution(for: testUser)
        
        XCTAssertEqual(distribution[.beginner], 1)
        XCTAssertEqual(distribution[.intermediate], 2)
        XCTAssertEqual(distribution[.advanced] ?? 0, 0)
        XCTAssertEqual(distribution[.expert] ?? 0, 0)
    }
}