import XCTest
import SwiftData
@testable import MotionFitPro

@MainActor
final class DataControllerTests: XCTestCase {
    private var dataController: DataController!
    private var testContainer: ModelContainer!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Create in-memory test container
        let schema = Schema([
            UserProfile.self,
            WorkoutSession.self,
            ExercisePerformance.self,
            SetPerformance.self,
            RepPerformance.self
        ])
        
        let configuration = ModelConfiguration(
            schema: schema,
            isStoredInMemoryOnly: true,
            allowsSave: true
        )
        
        testContainer = try ModelContainer(for: schema, configurations: [configuration])
        
        // Create test data controller
        dataController = DataController()
        dataController.container = testContainer
        dataController.context = ModelContext(testContainer)
    }
    
    override func tearDown() async throws {
        dataController = nil
        testContainer = nil
        try await super.tearDown()
    }
    
    // MARK: - Basic CRUD Tests
    
    func testInsertModel() throws {
        let profile = UserProfile(
            name: "Test User",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .intermediate
        )
        
        try dataController.insert(profile)
        
        // Verify the model was inserted
        let profiles = try dataController.fetch(UserProfile.self)
        XCTAssertEqual(profiles.count, 1)
        XCTAssertEqual(profiles.first?.name, "Test User")
    }
    
    func testDeleteModel() throws {
        let profile = UserProfile(
            name: "Test User",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .intermediate
        )
        
        try dataController.insert(profile)
        
        let profilesBeforeDelete = try dataController.fetch(UserProfile.self)
        XCTAssertEqual(profilesBeforeDelete.count, 1)
        
        try dataController.delete(profile)
        
        let profilesAfterDelete = try dataController.fetch(UserProfile.self)
        XCTAssertEqual(profilesAfterDelete.count, 0)
    }
    
    func testFetchModels() throws {
        // Create multiple profiles
        let profile1 = UserProfile(
            name: "User 1",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .beginner
        )
        
        let profile2 = UserProfile(
            name: "User 2",
            email: "<EMAIL>",
            age: 30,
            height: 175.0,
            weight: 70.0,
            fitnessLevel: .intermediate
        )
        
        try dataController.insert(profile1)
        try dataController.insert(profile2)
        
        let allProfiles = try dataController.fetch(UserProfile.self)
        XCTAssertEqual(allProfiles.count, 2)
    }
    
    func testFetchWithPredicate() throws {
        // Create profiles with different fitness levels
        let beginnerProfile = UserProfile(
            name: "Beginner User",
            email: "<EMAIL>",
            age: 20,
            height: 170.0,
            weight: 65.0,
            fitnessLevel: .beginner
        )
        
        let intermediateProfile = UserProfile(
            name: "Intermediate User",
            email: "<EMAIL>",
            age: 30,
            height: 175.0,
            weight: 70.0,
            fitnessLevel: .intermediate
        )
        
        try dataController.insert(beginnerProfile)
        try dataController.insert(intermediateProfile)
        
        // Fetch only beginner profiles
        let beginnerPredicate = #Predicate<UserProfile> { profile in
            profile.fitnessLevel == .beginner
        }
        
        let beginnerProfiles = try dataController.fetch(
            UserProfile.self,
            predicate: beginnerPredicate
        )
        
        XCTAssertEqual(beginnerProfiles.count, 1)
        XCTAssertEqual(beginnerProfiles.first?.name, "Beginner User")
    }
    
    func testFetchWithSorting() throws {
        // Create profiles with different ages
        let youngerProfile = UserProfile(
            name: "Younger User",
            email: "<EMAIL>",
            age: 20,
            height: 170.0,
            weight: 65.0,
            fitnessLevel: .beginner
        )
        
        let olderProfile = UserProfile(
            name: "Older User",
            email: "<EMAIL>",
            age: 40,
            height: 175.0,
            weight: 70.0,
            fitnessLevel: .intermediate
        )
        
        try dataController.insert(olderProfile) // Insert older first
        try dataController.insert(youngerProfile)
        
        // Fetch sorted by age ascending
        let sortDescriptor = SortDescriptor(\UserProfile.age, order: .forward)
        let sortedProfiles = try dataController.fetch(
            UserProfile.self,
            sortBy: [sortDescriptor]
        )
        
        XCTAssertEqual(sortedProfiles.count, 2)
        XCTAssertEqual(sortedProfiles.first?.name, "Younger User")
        XCTAssertEqual(sortedProfiles.last?.name, "Older User")
    }
    
    func testFetchFirst() throws {
        let profile1 = UserProfile(
            name: "User 1",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .beginner
        )
        
        let profile2 = UserProfile(
            name: "User 2",
            email: "<EMAIL>",
            age: 30,
            height: 175.0,
            weight: 70.0,
            fitnessLevel: .intermediate
        )
        
        try dataController.insert(profile1)
        try dataController.insert(profile2)
        
        let firstProfile = try dataController.fetchFirst(UserProfile.self)
        XCTAssertNotNil(firstProfile)
        
        // Test fetchFirst with predicate
        let beginnerPredicate = #Predicate<UserProfile> { profile in
            profile.fitnessLevel == .beginner
        }
        
        let firstBeginner = try dataController.fetchFirst(
            UserProfile.self,
            predicate: beginnerPredicate
        )
        
        XCTAssertNotNil(firstBeginner)
        XCTAssertEqual(firstBeginner?.fitnessLevel, .beginner)
    }
    
    func testCount() throws {
        let initialCount = try dataController.count(UserProfile.self)
        XCTAssertEqual(initialCount, 0)
        
        let profile = UserProfile(
            name: "Test User",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .intermediate
        )
        
        try dataController.insert(profile)
        
        let countAfterInsert = try dataController.count(UserProfile.self)
        XCTAssertEqual(countAfterInsert, 1)
        
        // Test count with predicate
        let beginnerPredicate = #Predicate<UserProfile> { profile in
            profile.fitnessLevel == .beginner
        }
        
        let beginnerCount = try dataController.count(UserProfile.self, predicate: beginnerPredicate)
        XCTAssertEqual(beginnerCount, 0) // No beginner profiles
    }
    
    // MARK: - Batch Operations Tests
    
    func testBatchInsert() throws {
        let profiles = [
            UserProfile(
                name: "User 1",
                email: "<EMAIL>",
                age: 25,
                height: 180.0,
                weight: 75.0,
                fitnessLevel: .beginner
            ),
            UserProfile(
                name: "User 2",
                email: "<EMAIL>",
                age: 30,
                height: 175.0,
                weight: 70.0,
                fitnessLevel: .intermediate
            ),
            UserProfile(
                name: "User 3",
                email: "<EMAIL>",
                age: 35,
                height: 178.0,
                weight: 80.0,
                fitnessLevel: .advanced
            )
        ]
        
        try dataController.batchInsert(profiles)
        
        let allProfiles = try dataController.fetch(UserProfile.self)
        XCTAssertEqual(allProfiles.count, 3)
    }
    
    func testBatchDelete() throws {
        // Create multiple profiles
        let beginnerProfile1 = UserProfile(
            name: "Beginner 1",
            email: "<EMAIL>",
            age: 20,
            height: 170.0,
            weight: 65.0,
            fitnessLevel: .beginner
        )
        
        let beginnerProfile2 = UserProfile(
            name: "Beginner 2",
            email: "<EMAIL>",
            age: 22,
            height: 172.0,
            weight: 67.0,
            fitnessLevel: .beginner
        )
        
        let intermediateProfile = UserProfile(
            name: "Intermediate User",
            email: "<EMAIL>",
            age: 30,
            height: 175.0,
            weight: 70.0,
            fitnessLevel: .intermediate
        )
        
        try dataController.insert(beginnerProfile1)
        try dataController.insert(beginnerProfile2)
        try dataController.insert(intermediateProfile)
        
        let profilesBeforeDelete = try dataController.fetch(UserProfile.self)
        XCTAssertEqual(profilesBeforeDelete.count, 3)
        
        // Batch delete all beginner profiles
        let beginnerPredicate = #Predicate<UserProfile> { profile in
            profile.fitnessLevel == .beginner
        }
        
        try dataController.batchDelete(UserProfile.self, predicate: beginnerPredicate)
        
        let profilesAfterDelete = try dataController.fetch(UserProfile.self)
        XCTAssertEqual(profilesAfterDelete.count, 1)
        XCTAssertEqual(profilesAfterDelete.first?.fitnessLevel, .intermediate)
    }
    
    // MARK: - Data Cleanup Tests
    
    func testDeleteAllData() throws {
        // Create test data
        let profile = UserProfile(
            name: "Test User",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .intermediate
        )
        
        let workout = WorkoutSession(
            workoutName: "Test Workout",
            category: .strength,
            difficulty: .intermediate
        )
        workout.user = profile
        
        try dataController.insert(profile)
        try dataController.insert(workout)
        
        // Verify data exists
        let profilesBefore = try dataController.fetch(UserProfile.self)
        let workoutsBefore = try dataController.fetch(WorkoutSession.self)
        XCTAssertEqual(profilesBefore.count, 1)
        XCTAssertEqual(workoutsBefore.count, 1)
        
        // Delete all data
        try dataController.deleteAllData()
        
        // Verify all data is gone
        let profilesAfter = try dataController.fetch(UserProfile.self)
        let workoutsAfter = try dataController.fetch(WorkoutSession.self)
        XCTAssertEqual(profilesAfter.count, 0)
        XCTAssertEqual(workoutsAfter.count, 0)
    }
    
    func testDeleteOldData() throws {
        let calendar = Calendar.current
        let now = Date()
        let oldDate = calendar.date(byAdding: .day, value: -31, to: now)! // 31 days ago
        let recentDate = calendar.date(byAdding: .day, value: -15, to: now)! // 15 days ago
        
        let profile = UserProfile(
            name: "Test User",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .intermediate
        )
        
        let oldWorkout = WorkoutSession(
            workoutName: "Old Workout",
            category: .strength,
            difficulty: .intermediate
        )
        oldWorkout.user = profile
        oldWorkout.createdAt = oldDate
        
        let recentWorkout = WorkoutSession(
            workoutName: "Recent Workout",
            category: .cardio,
            difficulty: .beginner
        )
        recentWorkout.user = profile
        recentWorkout.createdAt = recentDate
        
        try dataController.insert(profile)
        try dataController.insert(oldWorkout)
        try dataController.insert(recentWorkout)
        
        let workoutsBefore = try dataController.fetch(WorkoutSession.self)
        XCTAssertEqual(workoutsBefore.count, 2)
        
        // Delete data older than 30 days
        let thirtyDaysAgo = calendar.date(byAdding: .day, value: -30, to: now)!
        try dataController.deleteOldData(olderThan: thirtyDaysAgo)
        
        let workoutsAfter = try dataController.fetch(WorkoutSession.self)
        XCTAssertEqual(workoutsAfter.count, 1)
        XCTAssertEqual(workoutsAfter.first?.workoutName, "Recent Workout")
    }
    
    // MARK: - Entity Count Tests
    
    func testGetEntityCounts() throws {
        // Create test data
        let profile = UserProfile(
            name: "Test User",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .intermediate
        )
        
        let workout = WorkoutSession(
            workoutName: "Test Workout",
            category: .strength,
            difficulty: .intermediate
        )
        workout.user = profile
        
        let exercise = ExercisePerformance(
            exerciseName: "Squat",
            exerciseType: .compound,
            targetMuscleGroups: [.quadriceps, .glutes]
        )
        exercise.workoutSession = workout
        
        try dataController.insert(profile)
        try dataController.insert(workout)
        try dataController.insert(exercise)
        
        let entityCounts = try dataController.getEntityCounts()
        
        XCTAssertEqual(entityCounts["UserProfile"], 1)
        XCTAssertEqual(entityCounts["WorkoutSession"], 1)
        XCTAssertEqual(entityCounts["ExercisePerformance"], 1)
        XCTAssertEqual(entityCounts["SetPerformance"], 0)
        XCTAssertEqual(entityCounts["RepPerformance"], 0)
    }
    
    // MARK: - Error Handling Tests
    
    func testSaveWithValidData() throws {
        let profile = UserProfile(
            name: "Test User",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .intermediate
        )
        
        dataController.context?.insert(profile)
        
        // Should not throw an error
        XCTAssertNoThrow(try dataController.save())
    }
    
    func testFetchNonexistentModel() throws {
        // Fetch when no data exists
        let profiles = try dataController.fetch(UserProfile.self)
        XCTAssertEqual(profiles.count, 0)
        
        let firstProfile = try dataController.fetchFirst(UserProfile.self)
        XCTAssertNil(firstProfile)
        
        let count = try dataController.count(UserProfile.self)
        XCTAssertEqual(count, 0)
    }
    
    // MARK: - Relationship Tests
    
    func testCascadeDelete() throws {
        // Create user, workout, and exercise with relationships
        let profile = UserProfile(
            name: "Test User",
            email: "<EMAIL>",
            age: 25,
            height: 180.0,
            weight: 75.0,
            fitnessLevel: .intermediate
        )
        
        let workout = WorkoutSession(
            workoutName: "Test Workout",
            category: .strength,
            difficulty: .intermediate
        )
        workout.user = profile
        
        let exercise = ExercisePerformance(
            exerciseName: "Squat",
            exerciseType: .compound,
            targetMuscleGroups: [.quadriceps, .glutes]
        )
        exercise.workoutSession = workout
        
        try dataController.insert(profile)
        try dataController.insert(workout)
        try dataController.insert(exercise)
        
        // Verify data exists
        let workoutsBefore = try dataController.fetch(WorkoutSession.self)
        let exercisesBefore = try dataController.fetch(ExercisePerformance.self)
        XCTAssertEqual(workoutsBefore.count, 1)
        XCTAssertEqual(exercisesBefore.count, 1)
        
        // Delete user (should cascade to workouts and exercises)
        try dataController.delete(profile)
        
        // Verify cascade delete worked
        let workoutsAfter = try dataController.fetch(WorkoutSession.self)
        let exercisesAfter = try dataController.fetch(ExercisePerformance.self)
        XCTAssertEqual(workoutsAfter.count, 0)
        XCTAssertEqual(exercisesAfter.count, 0)
    }
}