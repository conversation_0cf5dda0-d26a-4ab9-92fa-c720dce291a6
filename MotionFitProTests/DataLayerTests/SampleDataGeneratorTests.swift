import XCTest
import SwiftData
@testable import MotionFitPro

@MainActor
final class SampleDataGeneratorTests: XCTestCase {
    private var dataController: DataController!
    private var sampleDataGenerator: SampleDataGenerator!
    private var testContainer: ModelContainer!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Create in-memory test container
        let schema = Schema([
            UserProfile.self,
            WorkoutSession.self,
            ExercisePerformance.self,
            SetPerformance.self,
            RepPerformance.self
        ])
        
        let configuration = ModelConfiguration(
            schema: schema,
            isStoredInMemoryOnly: true,
            allowsSave: true
        )
        
        testContainer = try ModelContainer(for: schema, configurations: [configuration])
        
        // Create test data controller
        dataController = DataController()
        dataController.container = testContainer
        dataController.context = ModelContext(testContainer)
        
        sampleDataGenerator = SampleDataGenerator(dataController: dataController)
    }
    
    override func tearDown() async throws {
        dataController = nil
        sampleDataGenerator = nil
        testContainer = nil
        try await super.tearDown()
    }
    
    // MARK: - Minimal Sample Data Tests
    
    func testGenerateMinimalSampleData() async throws {
        // Verify no data exists initially
        let initialUserCount = try dataController.count(UserProfile.self)
        let initialWorkoutCount = try dataController.count(WorkoutSession.self)
        XCTAssertEqual(initialUserCount, 0)
        XCTAssertEqual(initialWorkoutCount, 0)
        
        // Generate minimal sample data
        try await sampleDataGenerator.generateMinimalSampleData()
        
        // Verify user was created
        let users = try dataController.fetch(UserProfile.self)
        XCTAssertEqual(users.count, 1)
        
        let user = users.first!
        XCTAssertEqual(user.name, "Alex Johnson")
        XCTAssertEqual(user.email, "<EMAIL>")
        XCTAssertEqual(user.age, 28)
        XCTAssertEqual(user.fitnessLevel, .intermediate)
        XCTAssertGreaterThan(user.availableEquipment.count, 0)
        
        // Verify workout was created
        let workouts = try dataController.fetch(WorkoutSession.self)
        XCTAssertEqual(workouts.count, 1)
        
        let workout = workouts.first!
        XCTAssertEqual(workout.workoutName, "Quick Strength Training")
        XCTAssertEqual(workout.user?.id, user.id)
        XCTAssertTrue(workout.isCompleted)
        
        // Verify exercises were created
        let exercises = try dataController.fetch(ExercisePerformance.self)
        XCTAssertEqual(exercises.count, 3) // Should have 3 exercises
        
        // Verify sets were created
        let sets = try dataController.fetch(SetPerformance.self)
        XCTAssertGreaterThan(sets.count, 0)
        
        // Verify reps were created
        let reps = try dataController.fetch(RepPerformance.self)
        XCTAssertGreaterThan(reps.count, 0)
    }
    
    // MARK: - Full Sample Data Tests
    
    func testGenerateSampleData() async throws {
        // Verify no data exists initially
        let initialUserCount = try dataController.count(UserProfile.self)
        XCTAssertEqual(initialUserCount, 0)
        
        // Generate full sample data
        try await sampleDataGenerator.generateSampleData()
        
        // Verify user was created
        let users = try dataController.fetch(UserProfile.self)
        XCTAssertEqual(users.count, 1)
        
        let user = users.first!
        XCTAssertEqual(user.name, "Alex Johnson")
        XCTAssertEqual(user.fitnessLevel, .intermediate)
        
        // Verify multiple workouts were created
        let workouts = try dataController.fetch(WorkoutSession.self)
        XCTAssertEqual(workouts.count, 10) // Should have 10 workouts
        
        // Verify workouts have different categories
        let categories = Set(workouts.map { $0.category })
        XCTAssertGreaterThan(categories.count, 1)
        
        // Verify some workouts are completed
        let completedWorkouts = workouts.filter { $0.isCompleted }
        XCTAssertGreaterThan(completedWorkouts.count, 5) // At least 80% should be completed
        
        // Verify exercises were created for workouts
        let exercises = try dataController.fetch(ExercisePerformance.self)
        XCTAssertGreaterThan(exercises.count, 20) // Should have many exercises
        
        // Verify sets and reps were created
        let sets = try dataController.fetch(SetPerformance.self)
        let reps = try dataController.fetch(RepPerformance.self)
        XCTAssertGreaterThan(sets.count, 50)
        XCTAssertGreaterThan(reps.count, 200)
        
        // Verify user has weight history
        XCTAssertGreaterThan(user.weightHistory.count, 0)
        
        // Verify user has equipment
        XCTAssertGreaterThan(user.availableEquipment.count, 0)
    }
    
    // MARK: - Data Quality Tests
    
    func testGeneratedUserDataQuality() async throws {
        try await sampleDataGenerator.generateMinimalSampleData()
        
        let users = try dataController.fetch(UserProfile.self)
        let user = users.first!
        
        // Check user data validity
        XCTAssertFalse(user.name.isEmpty)
        XCTAssertTrue(user.email.contains("@"))
        XCTAssertGreaterThan(user.age, 0)
        XCTAssertGreaterThan(user.height, 0)
        XCTAssertGreaterThan(user.weight, 0)
        XCTAssertGreaterThan(user.fitnessGoals.count, 0)
        
        // Check preferences are set
        XCTAssertNotNil(user.preferences.preferredWorkoutTime)
        XCTAssertGreaterThan(user.preferences.workoutDaysPerWeek, 0)
        XCTAssertGreaterThan(user.preferences.sessionDurationMinutes, 0)
    }
    
    func testGeneratedWorkoutDataQuality() async throws {
        try await sampleDataGenerator.generateMinimalSampleData()
        
        let workouts = try dataController.fetch(WorkoutSession.self)
        
        for workout in workouts {
            // Check basic workout data
            XCTAssertFalse(workout.workoutName.isEmpty)
            XCTAssertNotNil(workout.user)
            XCTAssertNotNil(workout.startTime)
            
            if workout.isCompleted {
                XCTAssertNotNil(workout.endTime)
                XCTAssertEqual(workout.completionPercentage, 100.0)
                XCTAssertGreaterThan(workout.duration, 0)
            }
            
            // Check heart rate data for completed workouts
            if workout.isCompleted {
                if let avgHR = workout.averageHeartRate {
                    XCTAssertGreaterThan(avgHR, 60) // Reasonable heart rate
                    XCTAssertLessThan(avgHR, 220)
                }
                
                if let maxHR = workout.maxHeartRate {
                    XCTAssertGreaterThan(maxHR, 60)
                    XCTAssertLessThan(maxHR, 220)
                    
                    if let avgHR = workout.averageHeartRate {
                        XCTAssertGreaterThanOrEqual(maxHR, avgHR)
                    }
                }
            }
            
            // Check rating if present
            if let rating = workout.rating {
                XCTAssertGreaterThanOrEqual(rating, 1)
                XCTAssertLessThanOrEqual(rating, 5)
            }
        }
    }
    
    func testGeneratedExerciseDataQuality() async throws {
        try await sampleDataGenerator.generateMinimalSampleData()
        
        let exercises = try dataController.fetch(ExercisePerformance.self)
        
        for exercise in exercises {
            // Check basic exercise data
            XCTAssertFalse(exercise.exerciseName.isEmpty)
            XCTAssertNotNil(exercise.workoutSession)
            XCTAssertGreaterThan(exercise.targetMuscleGroups.count, 0)
            XCTAssertGreaterThan(exercise.targetSets, 0)
            
            // Check exercise type is valid
            XCTAssertTrue(ExerciseType.allCases.contains(exercise.exerciseType))
            
            // Check order index
            XCTAssertGreaterThanOrEqual(exercise.orderIndex, 0)
            
            if exercise.isCompleted {
                XCTAssertNotNil(exercise.startTime)
                XCTAssertNotNil(exercise.endTime)
                XCTAssertGreaterThan(exercise.duration, 0)
            }
        }
    }
    
    func testGeneratedSetDataQuality() async throws {
        try await sampleDataGenerator.generateMinimalSampleData()
        
        let sets = try dataController.fetch(SetPerformance.self)
        
        for set in sets {
            // Check basic set data
            XCTAssertNotNil(set.exercisePerformance)
            XCTAssertGreaterThan(set.setNumber, 0)
            XCTAssertGreaterThanOrEqual(set.actualReps, 0)
            XCTAssertGreaterThanOrEqual(set.targetRestTime, 0)
            
            if set.isCompleted {
                XCTAssertGreaterThan(set.actualReps, 0)
                XCTAssertGreaterThan(set.duration, 0)
                
                // Check perceived exertion if present
                if let rpe = set.perceivedExertion {
                    XCTAssertGreaterThanOrEqual(rpe, 1)
                    XCTAssertLessThanOrEqual(rpe, 10)
                }
                
                // Check form score
                if set.averageFormScore > 0 {
                    XCTAssertGreaterThanOrEqual(set.averageFormScore, 0)
                    XCTAssertLessThanOrEqual(set.averageFormScore, 100)
                }
                
                // Check heart rate data if present
                if let startHR = set.heartRateAtStart {
                    XCTAssertGreaterThan(startHR, 60)
                    XCTAssertLessThan(startHR, 220)
                }
                
                if let endHR = set.heartRateAtEnd {
                    XCTAssertGreaterThan(endHR, 60)
                    XCTAssertLessThan(endHR, 220)
                }
            }
        }
    }
    
    func testGeneratedRepDataQuality() async throws {
        try await sampleDataGenerator.generateMinimalSampleData()
        
        let reps = try dataController.fetch(RepPerformance.self)
        
        for rep in reps {
            // Check basic rep data
            XCTAssertNotNil(rep.setPerformance)
            XCTAssertGreaterThan(rep.repNumber, 0)
            XCTAssertGreaterThan(rep.duration, 0)
            
            // Check form score
            XCTAssertGreaterThanOrEqual(rep.formScore, 0)
            XCTAssertLessThanOrEqual(rep.formScore, 100)
            
            // Check movement metrics if present
            if let velocity = rep.velocity {
                XCTAssertGreaterThan(velocity, 0)
                XCTAssertLessThan(velocity, 10) // Reasonable velocity range
            }
            
            if let powerOutput = rep.powerOutput {
                XCTAssertGreaterThan(powerOutput, 0)
                XCTAssertLessThan(powerOutput, 5000) // Reasonable power range
            }
            
            // Check range of motion if present
            if let rom = rep.rangeOfMotion {
                XCTAssertGreaterThanOrEqual(rom, 0)
                XCTAssertLessThanOrEqual(rom, 100)
            }
            
            // Check symmetry if present
            if let symmetry = rep.leftRightSymmetry {
                XCTAssertGreaterThanOrEqual(symmetry, 0)
                XCTAssertLessThanOrEqual(symmetry, 100)
            }
            
            // Check phase timings if present
            if let eccentric = rep.eccentricDuration {
                XCTAssertGreaterThan(eccentric, 0)
                XCTAssertLessThan(eccentric, 10)
            }
            
            if let concentric = rep.concentricDuration {
                XCTAssertGreaterThan(concentric, 0)
                XCTAssertLessThan(concentric, 10)
            }
            
            // Check body posture and breathing pattern
            XCTAssertTrue(BodyPosture.allCases.contains(rep.bodyPosture))
            
            if let breathing = rep.breathingPattern {
                XCTAssertTrue(BreathingPattern.allCases.contains(breathing))
            }
            
            // Check heart rate if present
            if let hr = rep.heartRate {
                XCTAssertGreaterThan(hr, 60)
                XCTAssertLessThan(hr, 220)
            }
        }
    }
    
    // MARK: - Form Analysis Tests
    
    func testFormFeedbackGeneration() async throws {
        try await sampleDataGenerator.generateMinimalSampleData()
        
        let reps = try dataController.fetch(RepPerformance.self)
        var feedbackCount = 0
        var errorCount = 0
        
        for rep in reps {
            feedbackCount += rep.formFeedback.count
            errorCount += rep.technicalErrors.count
            
            // Check form feedback quality
            for feedback in rep.formFeedback {
                XCTAssertFalse(feedback.message.isEmpty)
                XCTAssertTrue(FeedbackType.allCases.contains(feedback.type))
                XCTAssertTrue(FeedbackPriority.allCases.contains(feedback.priority))
            }
            
            // Check technical errors
            for error in rep.technicalErrors {
                XCTAssertFalse(error.description.isEmpty)
                XCTAssertTrue(ErrorType.allCases.contains(error.type))
                XCTAssertTrue(ErrorSeverity.allCases.contains(error.severity))
                
                if let correction = error.correction {
                    XCTAssertFalse(correction.isEmpty)
                }
            }
        }
        
        // Should have some feedback and errors generated
        XCTAssertGreaterThan(feedbackCount, 0)
    }
    
    func testFormScoreProgression() async throws {
        try await sampleDataGenerator.generateMinimalSampleData()
        
        let sets = try dataController.fetch(SetPerformance.self)
        
        for set in sets {
            let reps = set.reps.sorted { $0.repNumber < $1.repNumber }
            
            if reps.count > 1 {
                // Form scores should generally decrease due to fatigue
                let firstRepScore = reps.first!.formScore
                let lastRepScore = reps.last!.formScore
                
                // Allow for some variation, but last rep shouldn't be significantly better than first
                XCTAssertLessThanOrEqual(lastRepScore, firstRepScore + 10)
            }
        }
    }
    
    // MARK: - Relationship Tests
    
    func testDataRelationships() async throws {
        try await sampleDataGenerator.generateMinimalSampleData()
        
        let users = try dataController.fetch(UserProfile.self)
        let user = users.first!
        
        // Test user -> workouts relationship
        XCTAssertGreaterThan(user.workoutSessions.count, 0)
        
        for workout in user.workoutSessions {
            XCTAssertEqual(workout.user?.id, user.id)
            
            // Test workout -> exercises relationship
            XCTAssertGreaterThan(workout.exercises.count, 0)
            
            for exercise in workout.exercises {
                XCTAssertEqual(exercise.workoutSession?.id, workout.id)
                
                // Test exercise -> sets relationship
                XCTAssertGreaterThan(exercise.sets.count, 0)
                
                for set in exercise.sets {
                    XCTAssertEqual(set.exercisePerformance?.id, exercise.id)
                    
                    // Test set -> reps relationship
                    XCTAssertGreaterThan(set.reps.count, 0)
                    
                    for rep in set.reps {
                        XCTAssertEqual(rep.setPerformance?.id, set.id)
                    }
                }
            }
        }
    }
    
    // MARK: - Performance Tests
    
    func testGenerationPerformance() async throws {
        let startTime = Date()
        
        try await sampleDataGenerator.generateMinimalSampleData()
        
        let endTime = Date()
        let duration = endTime.timeIntervalSince(startTime)
        
        // Generation should complete within reasonable time (5 seconds)
        XCTAssertLessThan(duration, 5.0)
    }
    
    func testClearExistingData() async throws {
        // Generate some data first
        try await sampleDataGenerator.generateMinimalSampleData()
        
        let usersAfterGeneration = try dataController.fetch(UserProfile.self)
        let workoutsAfterGeneration = try dataController.fetch(WorkoutSession.self)
        XCTAssertGreaterThan(usersAfterGeneration.count, 0)
        XCTAssertGreaterThan(workoutsAfterGeneration.count, 0)
        
        // Generate again (should clear existing data)
        try await sampleDataGenerator.generateMinimalSampleData()
        
        let usersAfterSecondGeneration = try dataController.fetch(UserProfile.self)
        let workoutsAfterSecondGeneration = try dataController.fetch(WorkoutSession.self)
        
        // Should still have the same amount of data (not doubled)
        XCTAssertEqual(usersAfterSecondGeneration.count, 1)
        XCTAssertEqual(workoutsAfterSecondGeneration.count, 1)
    }
}