import XCTest
import Foundation

class AccessibilityTests: XCTestCase {
    
    var app: XCUIApplication!
    
    override func setUpWithError() throws {
        super.setUp()
        
        continueAfterFailure = false
        
        app = XCUIApplication()
        app.launchArguments = ["--uitesting", "--accessibility-testing"]
        app.launchEnvironment = ["UITEST_MODE": "1"]
        
        // Enable accessibility features for testing
        setupAccessibilityTestEnvironment()
    }
    
    override func tearDownWithError() throws {
        app.terminate()
        app = nil
        super.tearDown()
    }
    
    // MARK: - VoiceOver Navigation Tests
    
    func testVoiceOverNavigationThroughOnboarding() throws {
        // Given: VoiceOver is enabled
        enableVoiceOver()
        app.launch()
        
        // Then: Welcome screen should be accessible
        let welcomeScreen = app.otherElements["OnboardingWelcomeScreen"]
        XCTAssertTrue(welcomeScreen.waitForExistence(timeout: 5.0), "Welcome screen should exist")
        
        // Verify accessibility label
        XCTAssertFalse(welcomeScreen.label.isEmpty, "Welcome screen should have accessibility label")
        
        // When: Navigate using VoiceOver gestures
        let getStartedButton = app.buttons["GetStartedButton"]
        XCTAssertTrue(getStartedButton.exists, "Get started button should exist")
        
        // Verify button accessibility
        XCTAssertFalse(getStartedButton.label.isEmpty, "Button should have accessibility label")
        XCTAssertTrue(getStartedButton.isEnabled, "Button should be enabled")
        
        // Test VoiceOver announcement
        let accessibilityHint = getStartedButton.value as? String ?? ""
        XCTAssertFalse(accessibilityHint.isEmpty, "Button should have accessibility hint")
        
        getStartedButton.tap()
        
        // Then: Should navigate to next screen with proper focus
        let permissionsScreen = app.otherElements["OnboardingPermissionsScreen"]
        XCTAssertTrue(permissionsScreen.waitForExistence(timeout: 3.0), "Permissions screen should appear")
        
        // Verify focus management
        let focusedElement = app.otherElements.firstMatch.firstMatch
        XCTAssertTrue(focusedElement.exists, "An element should have VoiceOver focus")
    }
    
    func testVoiceOverWorkoutInterface() throws {
        // Given: App with completed onboarding and VoiceOver enabled
        enableVoiceOver()
        launchAppWithCompletedOnboarding()
        
        // When: Start workout
        let startWorkoutButton = app.buttons["StartWorkoutButton"]
        XCTAssertTrue(startWorkoutButton.waitForExistence(timeout: 3.0), "Start workout button should exist")
        
        // Verify button accessibility
        XCTAssertFalse(startWorkoutButton.label.isEmpty, "Start workout button should have label")
        XCTAssertNotNil(startWorkoutButton.value, "Button should have accessibility value")
        
        startWorkoutButton.tap()
        
        // Select exercise
        let squatButton = app.buttons["SquatExerciseButton"]
        if squatButton.waitForExistence(timeout: 3.0) {
            // Verify exercise button accessibility
            XCTAssertTrue(squatButton.label.contains("Squat"), "Exercise button should contain exercise name")
            
            squatButton.tap()
            
            let startButton = app.buttons["StartWorkoutButton"]
            startButton.tap()
        }
        
        // Then: Workout screen should be accessible
        let arWorkoutScreen = app.otherElements["ARWorkoutScreen"]
        XCTAssertTrue(arWorkoutScreen.waitForExistence(timeout: 5.0), "AR workout screen should appear")
        
        // Verify workout UI accessibility
        let repCountLabel = app.staticTexts["RepCountLabel"]
        XCTAssertTrue(repCountLabel.exists, "Rep count should exist")
        XCTAssertFalse(repCountLabel.label.isEmpty, "Rep count should have accessible label")
        
        let setCountLabel = app.staticTexts["SetCountLabel"]
        XCTAssertTrue(setCountLabel.exists, "Set count should exist")
        XCTAssertFalse(setCountLabel.label.isEmpty, "Set count should have accessible label")
        
        // Test workout control accessibility
        let pauseButton = app.buttons["PauseWorkoutButton"]
        if pauseButton.exists {
            XCTAssertFalse(pauseButton.label.isEmpty, "Pause button should have label")
            XCTAssertTrue(pauseButton.isEnabled, "Pause button should be enabled")
        }
    }
    
    func testVoiceOverFormFeedbackAnnouncements() throws {
        // Given: Workout with VoiceOver enabled
        enableVoiceOver()
        startAccessibleWorkout()
        
        // When: Form feedback is provided
        simulateFormFeedback(message: "Keep your back straight")
        
        // Then: Should announce feedback through VoiceOver
        let formFeedbackView = app.otherElements["FormFeedbackView"]
        if formFeedbackView.waitForExistence(timeout: 5.0) {
            // Verify accessibility announcement
            let feedbackText = formFeedbackView.label
            XCTAssertTrue(feedbackText.contains("back straight"), "Form feedback should be announced")
            
            // Verify interruption behavior for important feedback
            let feedbackTraits = formFeedbackView.accessibilityTraits
            XCTAssertTrue(feedbackTraits.contains(.updatesFrequently), "Form feedback should update frequently")
        }
        
        // Test safety warning announcements
        simulateSafetyWarning(message: "Stop! Check your form")
        
        let safetyAlert = app.alerts["SafetyWarningAlert"]
        if safetyAlert.waitForExistence(timeout: 3.0) {
            // Safety warnings should interrupt other announcements
            let alertTraits = safetyAlert.accessibilityTraits
            XCTAssertTrue(alertTraits.contains(.playsSound), "Safety warnings should play sound")
        }
    }
    
    // MARK: - Dynamic Type Support Tests
    
    func testDynamicTypeSupport_LargeText() throws {
        // Given: Large text size is enabled
        enableLargeText()
        app.launch()
        
        // Then: Text should scale appropriately
        let welcomeTitle = app.staticTexts["WelcomeTitle"]
        if welcomeTitle.waitForExistence(timeout: 5.0) {
            let titleFrame = welcomeTitle.frame
            
            // Verify text is larger and still readable
            XCTAssertGreaterThan(titleFrame.height, 30, "Large text should have increased height")
            XCTAssertTrue(titleFrame.width > 0, "Text should still be visible")
        }
        
        // Navigate through app to test text scaling
        completeOnboardingWithLargeText()
        
        // Test workout interface with large text
        let startWorkoutButton = app.buttons["StartWorkoutButton"]
        if startWorkoutButton.exists {
            let buttonFrame = startWorkoutButton.frame
            XCTAssertGreaterThan(buttonFrame.height, 44, "Button should be taller for large text")
            
            // Verify button text is still readable
            XCTAssertFalse(startWorkoutButton.label.isEmpty, "Button text should still be visible")
        }
    }
    
    func testDynamicTypeSupport_ExtraLargeText() throws {
        // Given: Extra large text size
        enableExtraLargeText()
        app.launch()
        
        completeOnboardingWithLargeText()
        
        // When: In workout interface
        startAccessibleWorkout()
        
        // Then: All text should remain readable
        let repCountLabel = app.staticTexts["RepCountLabel"]
        if repCountLabel.exists {
            let labelFrame = repCountLabel.frame
            
            // Text should be significantly larger
            XCTAssertGreaterThan(labelFrame.height, 40, "Rep count should be much larger")
            
            // Should not be truncated
            let labelText = repCountLabel.label
            XCTAssertFalse(labelText.contains("..."), "Text should not be truncated")
        }
        
        // Test scrollable content with large text
        let instructionsText = app.scrollViews["InstructionsScrollView"]
        if instructionsText.exists {
            // Should be scrollable when text is too large
            XCTAssertTrue(instructionsText.isScrollEnabled, "Instructions should be scrollable with large text")
        }
    }
    
    // MARK: - High Contrast Mode Tests
    
    func testHighContrastModeSupport() throws {
        // Given: High contrast mode is enabled
        enableHighContrastMode()
        app.launch()
        
        // Then: UI should adapt to high contrast
        let welcomeScreen = app.otherElements["OnboardingWelcomeScreen"]
        XCTAssertTrue(welcomeScreen.waitForExistence(timeout: 5.0), "Welcome screen should appear")
        
        // Verify button contrast
        let getStartedButton = app.buttons["GetStartedButton"]
        if getStartedButton.exists {
            // Button should have high contrast appearance
            // Note: Actual color testing requires additional framework, 
            // but we can test for high contrast traits
            let traits = getStartedButton.accessibilityTraits
            
            // Verify accessibility improvements
            XCTAssertTrue(getStartedButton.isEnabled, "Button should be clearly interactive")
            XCTAssertFalse(getStartedButton.label.isEmpty, "Button text should be clearly visible")
        }
        
        // Test workout interface contrast
        completeOnboardingWithAccessibility()
        startAccessibleWorkout()
        
        // Verify AR overlay elements have good contrast
        let repCountLabel = app.staticTexts["RepCountLabel"]
        if repCountLabel.exists {
            // Text should have sufficient contrast background
            let labelFrame = repCountLabel.frame
            XCTAssertGreaterThan(labelFrame.width, 0, "Label should be visible")
            XCTAssertGreaterThan(labelFrame.height, 0, "Label should be visible")
        }
    }
    
    func testReduceTransparencySupport() throws {
        // Given: Reduce transparency is enabled
        enableReduceTransparency()
        app.launch()
        
        completeOnboardingWithAccessibility()
        
        // When: Using AR workout interface
        startAccessibleWorkout()
        
        // Then: Overlay elements should have solid backgrounds
        let workoutOverlay = app.otherElements["WorkoutOverlay"]
        if workoutOverlay.exists {
            // Overlay should be more opaque for better readability
            // This would typically be tested through visual inspection
            // or by checking accessibility contrast ratios
            XCTAssertTrue(workoutOverlay.exists, "Workout overlay should be visible")
        }
        
        // Test form feedback visibility
        simulateFormFeedback(message: "Good form!")
        
        let formFeedbackView = app.otherElements["FormFeedbackView"]
        if formFeedbackView.waitForExistence(timeout: 3.0) {
            // Feedback should have solid background for better visibility
            XCTAssertTrue(formFeedbackView.exists, "Form feedback should be clearly visible")
        }
    }
    
    // MARK: - Motor Accessibility Tests
    
    func testSwitchControlSupport() throws {
        // Given: Switch control is enabled
        enableSwitchControl()
        app.launch()
        
        // Then: All interactive elements should be accessible via switch control
        let welcomeScreen = app.otherElements["OnboardingWelcomeScreen"]
        XCTAssertTrue(welcomeScreen.waitForExistence(timeout: 5.0), "Welcome screen should appear")
        
        // Test button accessibility for switch control
        let getStartedButton = app.buttons["GetStartedButton"]
        if getStartedButton.exists {
            // Button should be focusable by switch control
            XCTAssertTrue(getStartedButton.isEnabled, "Button should be switch control accessible")
            
            // Should have clear action
            let buttonHint = getStartedButton.value as? String ?? ""
            XCTAssertFalse(buttonHint.isEmpty, "Button should have clear action hint")
        }
        
        // Test workout controls
        completeOnboardingWithAccessibility()
        startAccessibleWorkout()
        
        // Verify all workout controls are switch accessible
        let pauseButton = app.buttons["PauseWorkoutButton"]
        if pauseButton.exists {
            XCTAssertTrue(pauseButton.isEnabled, "Pause button should be switch accessible")
        }
        
        let stopButton = app.buttons["StopWorkoutButton"]
        if stopButton.exists {
            XCTAssertTrue(stopButton.isEnabled, "Stop button should be switch accessible")
        }
    }
    
    func testReducedMotionSupport() throws {
        // Given: Reduced motion is enabled
        enableReducedMotion()
        app.launch()
        
        // Then: Animations should be reduced or eliminated
        completeOnboardingWithAccessibility()
        
        // Test workout transitions with reduced motion
        let startWorkoutButton = app.buttons["StartWorkoutButton"]
        if startWorkoutButton.exists {
            startWorkoutButton.tap()
            
            // Exercise selection should appear without excessive animation
            let exerciseSelectionScreen = app.otherElements["ExerciseSelectionScreen"]
            XCTAssertTrue(exerciseSelectionScreen.waitForExistence(timeout: 2.0), "Screen should appear quickly")
        }
        
        // Test rep counter animations
        if app.buttons["SquatExerciseButton"].exists {
            app.buttons["SquatExerciseButton"].tap()
            app.buttons["StartWorkoutButton"].tap()
            
            // Rep counter should update without distracting animations
            simulateRepDetection()
            
            let repCountLabel = app.staticTexts["RepCountLabel"]
            if repCountLabel.exists {
                // Counter should update smoothly without jarring motion
                XCTAssertTrue(repCountLabel.exists, "Rep counter should update accessibly")
            }
        }
    }
    
    // MARK: - Accessibility in Different Contexts Tests
    
    func testAccessibilityDuringWorkout() throws {
        // Given: Accessible workout session
        enableVoiceOver()
        startAccessibleWorkout()
        
        // When: Workout is in progress
        simulateRepDetection()
        
        // Then: Real-time updates should be accessible
        let repCountLabel = app.staticTexts["RepCountLabel"]
        if repCountLabel.exists {
            // Rep count changes should be announced
            let repCountText = repCountLabel.label
            XCTAssertTrue(repCountText.contains("Rep"), "Rep count should be clearly labeled")
        }
        
        // Test form feedback accessibility during movement
        simulateFormFeedback(message: "Lower your squat deeper")
        
        let formFeedbackView = app.otherElements["FormFeedbackView"]
        if formFeedbackView.waitForExistence(timeout: 3.0) {
            // Feedback should be immediately accessible
            XCTAssertFalse(formFeedbackView.label.isEmpty, "Form feedback should have accessible text")
        }
        
        // Test workout completion accessibility
        simulateWorkoutCompletion()
        
        let summaryScreen = app.otherElements["WorkoutSummaryScreen"]
        if summaryScreen.waitForExistence(timeout: 3.0) {
            // Summary should be fully accessible
            XCTAssertFalse(summaryScreen.label.isEmpty, "Summary should have accessibility label")
            
            let summaryText = app.staticTexts["WorkoutSummaryText"]
            if summaryText.exists {
                XCTAssertFalse(summaryText.label.isEmpty, "Summary text should be accessible")
            }
        }
    }
    
    func testAccessibilityErrorHandling() throws {
        // Given: VoiceOver enabled with potential errors
        enableVoiceOver()
        app.launch()
        
        completeOnboardingWithAccessibility()
        
        // When: Camera permission is denied
        app.launchEnvironment["SIMULATE_CAMERA_DENIED"] = "1"
        
        let startWorkoutButton = app.buttons["StartWorkoutButton"]
        startWorkoutButton.tap()
        
        // Then: Error should be accessibly announced
        let errorAlert = app.alerts["CameraPermissionAlert"]
        if errorAlert.waitForExistence(timeout: 3.0) {
            XCTAssertFalse(errorAlert.label.isEmpty, "Error alert should have accessible label")
            
            let errorMessage = errorAlert.staticTexts.firstMatch
            XCTAssertFalse(errorMessage.label.isEmpty, "Error message should be accessible")
            
            // Error actions should be accessible
            let settingsButton = errorAlert.buttons["GoToSettings"]
            if settingsButton.exists {
                XCTAssertFalse(settingsButton.label.isEmpty, "Settings button should be labeled")
            }
        }
        
        // Test network error accessibility
        app.launchEnvironment["SIMULATE_NETWORK_ERROR"] = "1"
        
        let networkAlert = app.alerts["NetworkErrorAlert"]
        if networkAlert.waitForExistence(timeout: 3.0) {
            XCTAssertFalse(networkAlert.label.isEmpty, "Network error should be accessible")
        }
    }
    
    // MARK: - Helper Methods
    
    private func setupAccessibilityTestEnvironment() {
        app.launchEnvironment["ACCESSIBILITY_TESTING"] = "1"
        app.launchEnvironment["SKIP_ANIMATIONS"] = "1"
    }
    
    private func enableVoiceOver() {
        app.launchEnvironment["VOICEOVER_ENABLED"] = "1"
    }
    
    private func enableLargeText() {
        app.launchEnvironment["LARGE_TEXT"] = "1"
    }
    
    private func enableExtraLargeText() {
        app.launchEnvironment["EXTRA_LARGE_TEXT"] = "1"
    }
    
    private func enableHighContrastMode() {
        app.launchEnvironment["HIGH_CONTRAST"] = "1"
    }
    
    private func enableReduceTransparency() {
        app.launchEnvironment["REDUCE_TRANSPARENCY"] = "1"
    }
    
    private func enableSwitchControl() {
        app.launchEnvironment["SWITCH_CONTROL"] = "1"
    }
    
    private func enableReducedMotion() {
        app.launchEnvironment["REDUCE_MOTION"] = "1"
    }
    
    private func launchAppWithCompletedOnboarding() {
        app.launchArguments.append("--completed-onboarding")
        app.launch()
        
        let homeScreen = app.otherElements["HomeScreen"]
        XCTAssertTrue(homeScreen.waitForExistence(timeout: 5.0), "Home screen should appear")
    }
    
    private func completeOnboardingWithLargeText() {
        // Navigate through onboarding with large text considerations
        let getStartedButton = app.buttons["GetStartedButton"]
        if getStartedButton.waitForExistence(timeout: 5.0) {
            getStartedButton.tap()
        }
        
        // Handle permission screen
        let enableCameraButton = app.buttons["EnableCameraButton"]
        if enableCameraButton.waitForExistence(timeout: 3.0) {
            enableCameraButton.tap()
            
            // Handle system dialog
            let springboard = XCUIApplication(bundleIdentifier: "com.apple.springboard")
            let allowButton = springboard.buttons["Allow"]
            if allowButton.waitForExistence(timeout: 3.0) {
                allowButton.tap()
            }
        }
        
        // Continue through onboarding
        let continueButton = app.buttons["ContinueButton"]
        if continueButton.waitForExistence(timeout: 3.0) {
            continueButton.tap()
        }
        
        // Complete quickly for testing
        let completeButton = app.buttons["CompleteOnboardingButton"]
        if completeButton.waitForExistence(timeout: 3.0) {
            completeButton.tap()
        }
    }
    
    private func completeOnboardingWithAccessibility() {
        // Complete onboarding with accessibility considerations
        completeOnboardingWithLargeText()
    }
    
    private func startAccessibleWorkout() {
        let startWorkoutButton = app.buttons["StartWorkoutButton"]
        if startWorkoutButton.waitForExistence(timeout: 3.0) {
            startWorkoutButton.tap()
        }
        
        let squatButton = app.buttons["SquatExerciseButton"]
        if squatButton.waitForExistence(timeout: 3.0) {
            squatButton.tap()
        }
        
        let beginWorkoutButton = app.buttons["StartWorkoutButton"]
        if beginWorkoutButton.exists {
            beginWorkoutButton.tap()
        }
        
        // Wait for AR workout screen
        let arWorkoutScreen = app.otherElements["ARWorkoutScreen"]
        XCTAssertTrue(arWorkoutScreen.waitForExistence(timeout: 5.0), "Workout should start")
    }
    
    private func simulateFormFeedback(message: String) {
        // Simulate form feedback for accessibility testing
        app.buttons["SimulateFormFeedback"].tap()
    }
    
    private func simulateSafetyWarning(message: String) {
        // Simulate safety warning for accessibility testing
        app.buttons["SimulateSafetyWarning"].tap()
    }
    
    private func simulateRepDetection() {
        // Simulate rep detection for accessibility testing
        app.buttons["SimulateRepDetection"].tap()
    }
    
    private func simulateWorkoutCompletion() {
        // Simulate workout completion for accessibility testing
        app.buttons["SimulateWorkoutComplete"].tap()
    }
}

// MARK: - Accessibility Test Extensions

extension XCUIElement {
    var accessibilityTraits: UIAccessibilityTraits {
        // Extract accessibility traits for testing
        // This would be implemented based on the specific accessibility framework used
        return UIAccessibilityTraits()
    }
    
    var isScrollEnabled: Bool {
        // Check if element is scrollable
        return elementType == .scrollView
    }
}