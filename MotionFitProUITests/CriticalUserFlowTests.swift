import XCTest
import Foundation

class CriticalUserFlowTests: XCTestCase {
    
    var app: XCUIApplication!
    
    override func setUpWithError() throws {
        super.setUp()
        
        continueAfterFailure = false
        
        app = XCUIApplication()
        app.launchArguments = ["--uitesting", "--reset-data"]
        app.launchEnvironment = ["UITEST_MODE": "1"]
        
        // Configure test environment
        setupTestEnvironment()
    }
    
    override func tearDownWithError() throws {
        app.terminate()
        app = nil
        super.tearDown()
    }
    
    // MARK: - Onboarding Flow Tests
    
    func testCompleteOnboardingFlow() throws {
        // Given: Fresh app installation
        app.launch()
        
        // Then: Should show onboarding welcome screen
        let welcomeScreen = app.otherElements["OnboardingWelcomeScreen"]
        XCTAssertTrue(welcomeScreen.waitForExistence(timeout: 5.0), "Welcome screen should appear")
        
        // When: Tap get started
        let getStartedButton = app.buttons["GetStartedButton"]
        XCTAssertTrue(getStartedButton.exists, "Get started button should exist")
        getStartedButton.tap()
        
        // Then: Should show permissions screen
        let permissionsScreen = app.otherElements["OnboardingPermissionsScreen"]
        XCTAssertTrue(permissionsScreen.waitForExistence(timeout: 2.0), "Permissions screen should appear")
        
        // When: Grant camera permission
        let cameraPermissionButton = app.buttons["EnableCameraButton"]
        XCTAssertTrue(cameraPermissionButton.exists, "Camera permission button should exist")
        cameraPermissionButton.tap()
        
        // Handle system permission dialog
        handleCameraPermissionDialog()
        
        // When: Continue to fitness level selection
        let continueButton = app.buttons["ContinueButton"]
        XCTAssertTrue(continueButton.waitForExistence(timeout: 3.0), "Continue button should appear after permission")
        continueButton.tap()
        
        // Then: Should show fitness level screen
        let fitnessLevelScreen = app.otherElements["OnboardingFitnessLevelScreen"]
        XCTAssertTrue(fitnessLevelScreen.waitForExistence(timeout: 2.0), "Fitness level screen should appear")
        
        // When: Select intermediate fitness level
        let intermediateButton = app.buttons["IntermediateFitnessButton"]
        XCTAssertTrue(intermediateButton.exists, "Intermediate fitness button should exist")
        intermediateButton.tap()
        
        // When: Continue to coaching preferences
        let nextButton = app.buttons["NextButton"]
        XCTAssertTrue(nextButton.exists, "Next button should exist")
        nextButton.tap()
        
        // Then: Should show coaching preferences screen
        let coachingScreen = app.otherElements["OnboardingCoachingScreen"]
        XCTAssertTrue(coachingScreen.waitForExistence(timeout: 2.0), "Coaching preferences screen should appear")
        
        // When: Select supportive coaching style
        let supportiveButton = app.buttons["SupportiveCoachingButton"]
        XCTAssertTrue(supportiveButton.exists, "Supportive coaching button should exist")
        supportiveButton.tap()
        
        // When: Complete onboarding
        let completeButton = app.buttons["CompleteOnboardingButton"]
        XCTAssertTrue(completeButton.exists, "Complete onboarding button should exist")
        completeButton.tap()
        
        // Then: Should navigate to home screen
        let homeScreen = app.otherElements["HomeScreen"]
        XCTAssertTrue(homeScreen.waitForExistence(timeout: 3.0), "Should navigate to home screen after onboarding")
        
        // Verify onboarding completion state
        let startWorkoutButton = app.buttons["StartWorkoutButton"]
        XCTAssertTrue(startWorkoutButton.exists, "Start workout button should be available after onboarding")
    }
    
    func testOnboardingSkipAndReturnFlow() throws {
        // Given: User starts onboarding but skips
        app.launch()
        
        let welcomeScreen = app.otherElements["OnboardingWelcomeScreen"]
        XCTAssertTrue(welcomeScreen.waitForExistence(timeout: 5.0), "Welcome screen should appear")
        
        // When: Skip onboarding
        let skipButton = app.buttons["SkipOnboardingButton"]
        if skipButton.exists {
            skipButton.tap()
            
            // Then: Should go to home but show limited functionality
            let homeScreen = app.otherElements["HomeScreen"]
            XCTAssertTrue(homeScreen.waitForExistence(timeout: 3.0), "Should navigate to home screen")
            
            // When: Try to start workout without completing onboarding
            let startWorkoutButton = app.buttons["StartWorkoutButton"]
            if startWorkoutButton.exists {
                startWorkoutButton.tap()
                
                // Then: Should show onboarding reminder
                let onboardingAlert = app.alerts["OnboardingRequiredAlert"]
                XCTAssertTrue(onboardingAlert.waitForExistence(timeout: 2.0), "Should show onboarding required alert")
                
                let completeOnboardingButton = onboardingAlert.buttons["CompleteOnboarding"]
                completeOnboardingButton.tap()
                
                // Then: Should return to onboarding flow
                let permissionsScreen = app.otherElements["OnboardingPermissionsScreen"]
                XCTAssertTrue(permissionsScreen.waitForExistence(timeout: 2.0), "Should return to onboarding")
            }
        }
    }
    
    // MARK: - Exercise Selection and Workout Start Tests
    
    func testExerciseSelectionAndWorkoutStart() throws {
        // Given: App is set up with completed onboarding
        launchAppWithCompletedOnboarding()
        
        // When: Navigate to exercise selection
        let startWorkoutButton = app.buttons["StartWorkoutButton"]
        XCTAssertTrue(startWorkoutButton.waitForExistence(timeout: 3.0), "Start workout button should exist")
        startWorkoutButton.tap()
        
        // Then: Should show exercise selection screen
        let exerciseSelectionScreen = app.otherElements["ExerciseSelectionScreen"]
        XCTAssertTrue(exerciseSelectionScreen.waitForExistence(timeout: 3.0), "Exercise selection screen should appear")
        
        // When: Select squat exercise
        let squatButton = app.buttons["SquatExerciseButton"]
        XCTAssertTrue(squatButton.exists, "Squat exercise button should exist")
        squatButton.tap()
        
        // Verify exercise is selected
        XCTAssertTrue(squatButton.isSelected, "Squat button should show selected state")
        
        // When: Configure workout settings
        let setsTextField = app.textFields["SetsTextField"]
        if setsTextField.exists {
            setsTextField.tap()
            setsTextField.typeText("3")
        }
        
        let repsTextField = app.textFields["RepsTextField"]
        if repsTextField.exists {
            repsTextField.tap()
            repsTextField.typeText("15")
        }
        
        // When: Start the workout
        let startButton = app.buttons["StartWorkoutButton"]
        XCTAssertTrue(startButton.exists, "Start workout button should exist")
        startButton.tap()
        
        // Then: Should navigate to AR workout screen
        let arWorkoutScreen = app.otherElements["ARWorkoutScreen"]
        XCTAssertTrue(arWorkoutScreen.waitForExistence(timeout: 5.0), "AR workout screen should appear")
        
        // Verify AR session setup
        let arCameraView = app.otherElements["ARCameraView"]
        XCTAssertTrue(arCameraView.waitForExistence(timeout: 3.0), "AR camera view should be active")
        
        // Verify workout UI elements
        let repCountLabel = app.staticTexts["RepCountLabel"]
        XCTAssertTrue(repCountLabel.exists, "Rep count label should exist")
        XCTAssertEqual(repCountLabel.label, "0", "Rep count should start at 0")
        
        let setCountLabel = app.staticTexts["SetCountLabel"]
        XCTAssertTrue(setCountLabel.exists, "Set count label should exist")
        XCTAssertEqual(setCountLabel.label, "1 / 3", "Set count should show current set")
    }
    
    func testQuickWorkoutStart() throws {
        // Given: App with completed onboarding
        launchAppWithCompletedOnboarding()
        
        // When: Use quick workout option
        let quickWorkoutButton = app.buttons["QuickWorkoutButton"]
        if quickWorkoutButton.exists {
            quickWorkoutButton.tap()
            
            // Then: Should start a predefined workout immediately
            let arWorkoutScreen = app.otherElements["ARWorkoutScreen"]
            XCTAssertTrue(arWorkoutScreen.waitForExistence(timeout: 5.0), "Quick workout should start immediately")
            
            // Verify default exercise is loaded
            let exerciseLabel = app.staticTexts["CurrentExerciseLabel"]
            XCTAssertTrue(exerciseLabel.exists, "Current exercise label should exist")
            XCTAssertFalse(exerciseLabel.label.isEmpty, "Exercise name should be displayed")
        }
    }
    
    // MARK: - Complete Workout Session Tests
    
    func testCompleteWorkoutSessionWithRepCounting() throws {
        // Given: Workout is started
        startWorkoutSession(exercise: .squat, sets: 2, reps: 5)
        
        // When: Perform simulated reps
        performSimulatedSquatReps(count: 5)
        
        // Then: Rep counter should update
        let repCountLabel = app.staticTexts["RepCountLabel"]
        XCTAssertTrue(repCountLabel.waitForExistence(timeout: 2.0), "Rep count label should exist")
        
        // Wait for rep detection and count update
        let repCountUpdated = expectation(for: NSPredicate(format: "label != '0'"), evaluatedWith: repCountLabel, handler: nil)
        wait(for: [repCountUpdated], timeout: 10.0)
        
        // When: Complete first set
        let nextSetButton = app.buttons["NextSetButton"]
        if nextSetButton.waitForExistence(timeout: 5.0) {
            nextSetButton.tap()
            
            // Then: Should advance to second set
            let setCountLabel = app.staticTexts["SetCountLabel"]
            XCTAssertTrue(setCountLabel.waitForExistence(timeout: 2.0), "Set count should update")
        }
        
        // When: Perform second set
        performSimulatedSquatReps(count: 5)
        
        // When: Complete workout
        let finishWorkoutButton = app.buttons["FinishWorkoutButton"]
        if finishWorkoutButton.waitForExistence(timeout: 5.0) {
            finishWorkoutButton.tap()
            
            // Then: Should show workout summary
            let summaryScreen = app.otherElements["WorkoutSummaryScreen"]
            XCTAssertTrue(summaryScreen.waitForExistence(timeout: 3.0), "Workout summary should appear")
            
            // Verify summary data
            let totalRepsLabel = app.staticTexts["TotalRepsLabel"]
            XCTAssertTrue(totalRepsLabel.exists, "Total reps should be displayed")
            
            let workoutDurationLabel = app.staticTexts["WorkoutDurationLabel"]
            XCTAssertTrue(workoutDurationLabel.exists, "Workout duration should be displayed")
            
            let averageFormScoreLabel = app.staticTexts["AverageFormScoreLabel"]
            XCTAssertTrue(averageFormScoreLabel.exists, "Average form score should be displayed")
        }
    }
    
    func testWorkoutSessionWithFormFeedback() throws {
        // Given: Workout with form analysis enabled
        startWorkoutSession(exercise: .squat, sets: 1, reps: 3)
        
        // When: Perform movements with varying form quality
        simulateMovementWithFormIssues()
        
        // Then: Should show form feedback
        let formFeedbackView = app.otherElements["FormFeedbackView"]
        XCTAssertTrue(formFeedbackView.waitForExistence(timeout: 5.0), "Form feedback should appear")
        
        // Check for specific feedback messages
        let formCorrectionMessage = app.staticTexts.matching(NSPredicate(format: "label CONTAINS 'form'")).firstMatch
        if formCorrectionMessage.exists {
            XCTAssertTrue(formCorrectionMessage.exists, "Form correction message should appear")
        }
        
        // Verify haptic feedback indicator (if available)
        let hapticIndicator = app.otherElements["HapticFeedbackIndicator"]
        if hapticIndicator.exists {
            XCTAssertTrue(hapticIndicator.exists, "Haptic feedback should be triggered")
        }
    }
    
    // MARK: - App Backgrounding and Foregrounding Tests
    
    func testWorkoutContinuationAfterBackgrounding() throws {
        // Given: Active workout session
        startWorkoutSession(exercise: .squat, sets: 2, reps: 5)
        
        // Verify workout is active
        let repCountLabel = app.staticTexts["RepCountLabel"]
        XCTAssertTrue(repCountLabel.exists, "Workout should be active")
        
        // When: App goes to background
        XCUIDevice.shared.press(.home)
        
        // Wait briefly
        sleep(2)
        
        // When: Return to foreground
        app.activate()
        
        // Then: Workout should still be active
        let arWorkoutScreen = app.otherElements["ARWorkoutScreen"]
        XCTAssertTrue(arWorkoutScreen.waitForExistence(timeout: 3.0), "Workout screen should still be active")
        
        // Verify session continuity
        XCTAssertTrue(repCountLabel.exists, "Rep counter should still be visible")
        
        // Verify AR session state
        let arStatusIndicator = app.otherElements["ARStatusIndicator"]
        if arStatusIndicator.exists {
            // AR session might need reinitialization
            let resumeButton = app.buttons["ResumeARButton"]
            if resumeButton.exists {
                resumeButton.tap()
                
                // Wait for AR session to resume
                XCTAssertTrue(arStatusIndicator.waitForExistence(timeout: 5.0), "AR session should resume")
            }
        }
        
        // When: Continue workout
        performSimulatedSquatReps(count: 2)
        
        // Then: Rep counting should continue working
        let repCountUpdated = expectation(for: NSPredicate(format: "label != '0'"), evaluatedWith: repCountLabel, handler: nil)
        wait(for: [repCountUpdated], timeout: 10.0)
    }
    
    func testWorkoutPauseAndResumeFlow() throws {
        // Given: Active workout session
        startWorkoutSession(exercise: .pushUp, sets: 2, reps: 10)
        
        // When: Pause workout
        let pauseButton = app.buttons["PauseWorkoutButton"]
        XCTAssertTrue(pauseButton.exists, "Pause button should exist")
        pauseButton.tap()
        
        // Then: Should show paused state
        let pausedIndicator = app.otherElements["WorkoutPausedIndicator"]
        XCTAssertTrue(pausedIndicator.waitForExistence(timeout: 2.0), "Workout should show paused state")
        
        let resumeButton = app.buttons["ResumeWorkoutButton"]
        XCTAssertTrue(resumeButton.exists, "Resume button should appear")
        
        // When: Resume workout
        resumeButton.tap()
        
        // Then: Should return to active state
        let arWorkoutScreen = app.otherElements["ARWorkoutScreen"]
        XCTAssertTrue(arWorkoutScreen.exists, "Should return to active workout screen")
        
        // Verify rep counting continues
        performSimulatedPushUpReps(count: 3)
        
        let repCountLabel = app.staticTexts["RepCountLabel"]
        let repCountUpdated = expectation(for: NSPredicate(format: "label != '0'"), evaluatedWith: repCountLabel, handler: nil)
        wait(for: [repCountUpdated], timeout: 10.0)
    }
    
    // MARK: - Error Recovery Tests
    
    func testCameraPermissionDeniedRecovery() throws {
        // Given: App without camera permission
        app.launchArguments.append("--no-camera-permission")
        app.launch()
        
        // When: Try to start workout
        if app.otherElements["OnboardingWelcomeScreen"].waitForExistence(timeout: 3.0) {
            // Complete onboarding first
            completeMinimalOnboarding()
        }
        
        let startWorkoutButton = app.buttons["StartWorkoutButton"]
        startWorkoutButton.tap()
        
        // Then: Should show permission error
        let permissionAlert = app.alerts["CameraPermissionAlert"]
        XCTAssertTrue(permissionAlert.waitForExistence(timeout: 3.0), "Camera permission alert should appear")
        
        // When: Go to settings
        let settingsButton = permissionAlert.buttons["GoToSettings"]
        if settingsButton.exists {
            settingsButton.tap()
            
            // Should navigate to Settings app (can't test actual permission change in UI tests)
            // For testing purposes, simulate permission granted
            app.activate()
            
            // Try again
            let retryButton = app.buttons["RetryWorkoutButton"]
            if retryButton.waitForExistence(timeout: 3.0) {
                retryButton.tap()
            }
        }
    }
    
    func testARSessionFailureRecovery() throws {
        // Given: Workout started
        startWorkoutSession(exercise: .squat, sets: 1, reps: 5)
        
        // Simulate AR session failure
        app.launchEnvironment["SIMULATE_AR_FAILURE"] = "1"
        
        // When: AR session fails
        let arErrorAlert = app.alerts["ARSessionErrorAlert"]
        if arErrorAlert.waitForExistence(timeout: 10.0) {
            // Then: Should show error recovery options
            let retryButton = arErrorAlert.buttons["RetryARSession"]
            XCTAssertTrue(retryButton.exists, "Retry button should exist")
            
            // When: Retry AR session
            retryButton.tap()
            
            // Then: Should attempt to restart AR
            let arWorkoutScreen = app.otherElements["ARWorkoutScreen"]
            XCTAssertTrue(arWorkoutScreen.waitForExistence(timeout: 5.0), "Should return to workout screen")
        }
    }
    
    func testNetworkErrorDuringSync() throws {
        // Given: Completed workout
        completeTestWorkout()
        
        // Simulate network error
        app.launchEnvironment["SIMULATE_NETWORK_ERROR"] = "1"
        
        // When: Workout tries to sync
        let syncErrorAlert = app.alerts["SyncErrorAlert"]
        if syncErrorAlert.waitForExistence(timeout: 5.0) {
            // Then: Should show sync error with retry option
            let retryLaterButton = syncErrorAlert.buttons["RetryLater"]
            XCTAssertTrue(retryLaterButton.exists, "Retry later option should exist")
            
            retryLaterButton.tap()
            
            // Verify workout is saved locally
            navigateToWorkoutHistory()
            
            let workoutHistoryList = app.tables["WorkoutHistoryList"]
            XCTAssertTrue(workoutHistoryList.exists, "Workout history should exist")
            
            let firstWorkout = workoutHistoryList.cells.firstMatch
            XCTAssertTrue(firstWorkout.exists, "Completed workout should be saved locally")
            
            // Check for sync pending indicator
            let syncPendingIcon = firstWorkout.images["SyncPendingIcon"]
            if syncPendingIcon.exists {
                XCTAssertTrue(syncPendingIcon.exists, "Should show sync pending indicator")
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func setupTestEnvironment() {
        // Configure app for UI testing
        app.launchEnvironment["ANIMATIONS_DISABLED"] = "1"
        app.launchEnvironment["SKIP_ANALYTICS"] = "1"
    }
    
    private func handleCameraPermissionDialog() {
        // Handle system camera permission dialog
        let springboard = XCUIApplication(bundleIdentifier: "com.apple.springboard")
        let allowButton = springboard.buttons["Allow"]
        
        if allowButton.waitForExistence(timeout: 5.0) {
            allowButton.tap()
        }
    }
    
    private func launchAppWithCompletedOnboarding() {
        app.launchArguments.append("--completed-onboarding")
        app.launch()
        
        let homeScreen = app.otherElements["HomeScreen"]
        XCTAssertTrue(homeScreen.waitForExistence(timeout: 5.0), "Home screen should appear with completed onboarding")
    }
    
    private func completeMinimalOnboarding() {
        let getStartedButton = app.buttons["GetStartedButton"]
        if getStartedButton.exists {
            getStartedButton.tap()
        }
        
        // Skip through onboarding screens quickly
        let skipButton = app.buttons["SkipButton"]
        while skipButton.exists {
            skipButton.tap()
        }
        
        let completeButton = app.buttons["CompleteOnboardingButton"]
        if completeButton.exists {
            completeButton.tap()
        }
    }
    
    private func startWorkoutSession(exercise: ExerciseType, sets: Int, reps: Int) {
        launchAppWithCompletedOnboarding()
        
        let startWorkoutButton = app.buttons["StartWorkoutButton"]
        startWorkoutButton.tap()
        
        // Select exercise
        let exerciseButton = app.buttons["\(exercise.rawValue)ExerciseButton"]
        if exerciseButton.waitForExistence(timeout: 3.0) {
            exerciseButton.tap()
        }
        
        // Configure workout if options exist
        configureWorkoutSettings(sets: sets, reps: reps)
        
        let beginWorkoutButton = app.buttons["StartWorkoutButton"]
        beginWorkoutButton.tap()
        
        // Wait for workout to start
        let arWorkoutScreen = app.otherElements["ARWorkoutScreen"]
        XCTAssertTrue(arWorkoutScreen.waitForExistence(timeout: 5.0), "Workout should start")
    }
    
    private func configureWorkoutSettings(sets: Int, reps: Int) {
        let setsTextField = app.textFields["SetsTextField"]
        if setsTextField.exists {
            setsTextField.tap()
            setsTextField.typeText("\(sets)")
        }
        
        let repsTextField = app.textFields["RepsTextField"]
        if repsTextField.exists {
            repsTextField.tap()
            repsTextField.typeText("\(reps)")
        }
    }
    
    private func performSimulatedSquatReps(count: Int) {
        // Simulate squat movements by triggering test mode rep detection
        for i in 1...count {
            app.buttons["SimulateSquatRep"].tap()
            
            // Wait between reps
            sleep(1)
        }
    }
    
    private func performSimulatedPushUpReps(count: Int) {
        // Simulate push-up movements
        for i in 1...count {
            app.buttons["SimulatePushUpRep"].tap()
            sleep(1)
        }
    }
    
    private func simulateMovementWithFormIssues() {
        // Simulate movement with poor form to trigger feedback
        app.buttons["SimulatePoorForm"].tap()
        sleep(2)
        
        // Then good form
        app.buttons["SimulateGoodForm"].tap()
        sleep(2)
    }
    
    private func completeTestWorkout() {
        startWorkoutSession(exercise: .squat, sets: 1, reps: 3)
        performSimulatedSquatReps(count: 3)
        
        let finishButton = app.buttons["FinishWorkoutButton"]
        if finishButton.waitForExistence(timeout: 5.0) {
            finishButton.tap()
        }
        
        // Skip through summary screen
        let doneButton = app.buttons["DoneButton"]
        if doneButton.waitForExistence(timeout: 3.0) {
            doneButton.tap()
        }
    }
    
    private func navigateToWorkoutHistory() {
        let tabBar = app.tabBars["MainTabBar"]
        let progressTab = tabBar.buttons["Progress"]
        if progressTab.exists {
            progressTab.tap()
        } else {
            // Use menu navigation
            let menuButton = app.buttons["MenuButton"]
            if menuButton.exists {
                menuButton.tap()
                
                let historyButton = app.buttons["WorkoutHistoryButton"]
                historyButton.tap()
            }
        }
    }
}

// MARK: - Supporting Types

extension ExerciseType {
    var rawValue: String {
        switch self {
        case .squat: return "Squat"
        case .pushUp: return "PushUp"
        case .plank: return "Plank"
        case .burpee: return "Burpee"
        case .mountainClimber: return "MountainClimber"
        }
    }
}