import XCTest

/// UI tests for the complete workout flow and user interactions
final class WorkoutFlowUITests: XCTestCase {
    
    var app: XCUIApplication!
    
    override func setUpWithError() throws {
        try super.setUpWithError()
        
        continueAfterFailure = false
        
        app = XCUIApplication()
        app.launchArguments = ["--uitesting"]
        app.launch()
    }
    
    override func tearDownWithError() throws {
        app = nil
        try super.tearDownWithError()
    }
    
    // MARK: - App Launch and Navigation Tests
    
    func testAppLaunch() throws {
        // Test that the app launches successfully
        XCTAssertTrue(app.state == .runningForeground)
        
        // Verify launch screen elements
        let launchTitle = app.staticTexts["MotionFitPro"]
        XCTAssertTrue(launchTitle.waitForExistence(timeout: 5))
        
        // Wait for main interface to load
        let workoutButton = app.buttons["Start Workout"]
        XCTAssertTrue(workoutButton.waitForExistence(timeout: 10))
    }
    
    func testMainNavigationTabs() throws {
        // Test navigation between main tabs
        let tabBar = app.tabBars.firstMatch
        XCTAssertTrue(tabBar.exists)
        
        // Test Workouts tab
        let workoutsTab = tabBar.buttons["Workouts"]
        workoutsTab.tap()
        XCTAssertTrue(app.navigationBars["Workouts"].exists)
        
        // Test Progress tab
        let progressTab = tabBar.buttons["Progress"]
        progressTab.tap()
        XCTAssertTrue(app.navigationBars["Progress"].exists)
        
        // Test Profile tab
        let profileTab = tabBar.buttons["Profile"]
        profileTab.tap()
        XCTAssertTrue(app.navigationBars["Profile"].exists)
    }
    
    // MARK: - Workout Selection Tests
    
    func testWorkoutSelection() throws {
        // Navigate to workout selection
        let startWorkoutButton = app.buttons["Start Workout"]
        startWorkoutButton.tap()
        
        // Verify workout selection screen
        let workoutSelectionTitle = app.navigationBars["Select Workout"]
        XCTAssertTrue(workoutSelectionTitle.waitForExistence(timeout: 5))
        
        // Test exercise category filtering
        let strengthButton = app.buttons["Strength"]
        strengthButton.tap()
        
        // Verify filtered exercises appear
        let exerciseList = app.collectionViews.firstMatch
        XCTAssertTrue(exerciseList.exists)
        XCTAssertGreaterThan(exerciseList.cells.count, 0)
        
        // Select an exercise
        let firstExercise = exerciseList.cells.firstMatch
        firstExercise.tap()
        
        // Verify exercise detail view
        let exerciseDetailView = app.scrollViews["ExerciseDetailView"]
        XCTAssertTrue(exerciseDetailView.waitForExistence(timeout: 3))
        
        // Add exercise to workout
        let addToWorkoutButton = app.buttons["Add to Workout"]
        addToWorkoutButton.tap()
        
        // Verify exercise was added
        let workoutCounter = app.staticTexts["1 exercise selected"]
        XCTAssertTrue(workoutCounter.exists)
    }
    
    func testCustomWorkoutCreation() throws {
        // Navigate to custom workout creation
        app.buttons["Create Custom Workout"].tap()
        
        // Verify custom workout screen
        let customWorkoutTitle = app.navigationBars["Custom Workout"]
        XCTAssertTrue(customWorkoutTitle.waitForExistence(timeout: 5))
        
        // Add multiple exercises
        let exerciseLibraryButton = app.buttons["Browse Exercises"]
        exerciseLibraryButton.tap()
        
        // Select exercises from different categories
        let cardioTab = app.buttons["Cardio"]
        cardioTab.tap()
        
        let firstCardioExercise = app.collectionViews.firstMatch.cells.firstMatch
        firstCardioExercise.tap()
        app.buttons["Add to Workout"].tap()
        
        // Navigate back and add strength exercise
        app.navigationBars.buttons.firstMatch.tap() // Back button
        
        let strengthTab = app.buttons["Strength"]
        strengthTab.tap()
        
        let firstStrengthExercise = app.collectionViews.firstMatch.cells.firstMatch
        firstStrengthExercise.tap()
        app.buttons["Add to Workout"].tap()
        
        // Verify multiple exercises selected
        let workoutCounter = app.staticTexts["2 exercises selected"]
        XCTAssertTrue(workoutCounter.exists)
        
        // Start the custom workout
        app.buttons["Start Workout"].tap()
        
        // Verify workout begins
        let workoutView = app.otherElements["WorkoutView"]
        XCTAssertTrue(workoutView.waitForExistence(timeout: 5))
    }
    
    // MARK: - Workout Execution Tests
    
    func testWorkoutExecution() throws {
        // Start a quick workout
        startQuickWorkout()
        
        // Verify workout interface elements
        let exerciseNameLabel = app.staticTexts.matching(identifier: "ExerciseName").firstMatch
        XCTAssertTrue(exerciseNameLabel.exists)
        
        let repCounterLabel = app.staticTexts.matching(identifier: "RepCounter").firstMatch
        XCTAssertTrue(repCounterLabel.exists)
        
        let setCounterLabel = app.staticTexts.matching(identifier: "SetCounter").firstMatch
        XCTAssertTrue(setCounterLabel.exists)
        
        // Test rep completion
        let completeRepButton = app.buttons["Complete Rep"]
        for _ in 1...5 {
            completeRepButton.tap()
            // Brief pause to allow UI updates
            Thread.sleep(forTimeInterval: 0.5)
        }
        
        // Verify rep count updated
        let repCount = app.staticTexts["5"]
        XCTAssertTrue(repCount.exists)
        
        // Complete remaining reps to finish set
        for _ in 6...10 {
            completeRepButton.tap()
            Thread.sleep(forTimeInterval: 0.3)
        }
        
        // Verify set completion
        let setCompleteMessage = app.staticTexts["Set Complete!"]
        XCTAssertTrue(setCompleteMessage.waitForExistence(timeout: 3))
        
        // Start next set
        let nextSetButton = app.buttons["Start Next Set"]
        if nextSetButton.waitForExistence(timeout: 5) {
            nextSetButton.tap()
        }
    }
    
    func testWorkoutPauseResume() throws {
        // Start a workout
        startQuickWorkout()
        
        // Pause the workout
        let pauseButton = app.buttons["Pause"]
        pauseButton.tap()
        
        // Verify pause screen
        let pauseOverlay = app.otherElements["PauseOverlay"]
        XCTAssertTrue(pauseOverlay.waitForExistence(timeout: 3))
        
        let resumeButton = app.buttons["Resume"]
        XCTAssertTrue(resumeButton.exists)
        
        let endWorkoutButton = app.buttons["End Workout"]
        XCTAssertTrue(endWorkoutButton.exists)
        
        // Resume the workout
        resumeButton.tap()
        
        // Verify workout resumed
        let workoutView = app.otherElements["WorkoutView"]
        XCTAssertTrue(workoutView.exists)
        XCTAssertFalse(pauseOverlay.exists)
    }
    
    func testWorkoutCompletion() throws {
        // Start and complete a short workout
        startQuickWorkout()
        
        // Simulate completing the workout quickly
        let completeRepButton = app.buttons["Complete Rep"]
        let skipSetButton = app.buttons["Skip Set"]
        
        // Complete first exercise quickly
        for _ in 1...3 { // 3 sets
            for _ in 1...10 { // 10 reps per set
                completeRepButton.tap()
            }
            if skipSetButton.exists {
                skipSetButton.tap()
            }
        }
        
        // Skip to workout completion
        let skipExerciseButton = app.buttons["Skip Exercise"]
        if skipExerciseButton.exists {
            skipExerciseButton.tap()
        }
        
        // Verify workout completion screen
        let completionScreen = app.otherElements["WorkoutCompletionView"]
        XCTAssertTrue(completionScreen.waitForExistence(timeout: 10))
        
        let congratulationsMessage = app.staticTexts["Workout Complete!"]
        XCTAssertTrue(congratulationsMessage.exists)
        
        let workoutSummary = app.otherElements["WorkoutSummary"]
        XCTAssertTrue(workoutSummary.exists)
        
        // Test sharing workout
        let shareButton = app.buttons["Share Workout"]
        if shareButton.exists {
            shareButton.tap()
            
            // Verify share sheet appears
            let shareSheet = app.otherElements["ActivityListView"]
            XCTAssertTrue(shareSheet.waitForExistence(timeout: 3))
            
            // Cancel sharing
            let cancelButton = app.buttons["Cancel"]
            cancelButton.tap()
        }
        
        // Return to main screen
        let doneButton = app.buttons["Done"]
        doneButton.tap()
        
        // Verify back to main screen
        let startWorkoutButton = app.buttons["Start Workout"]
        XCTAssertTrue(startWorkoutButton.waitForExistence(timeout: 5))
    }
    
    // MARK: - Settings and Preferences Tests
    
    func testWorkoutSettings() throws {
        // Navigate to settings
        app.tabBars.buttons["Profile"].tap()
        app.buttons["Settings"].tap()
        
        // Test workout settings
        app.buttons["Workout Settings"].tap()
        
        // Test audio feedback toggle
        let audioFeedbackToggle = app.switches["Audio Feedback"]
        let initialAudioState = audioFeedbackToggle.value as? String == "1"
        audioFeedbackToggle.tap()
        
        let newAudioState = audioFeedbackToggle.value as? String == "1"
        XCTAssertNotEqual(initialAudioState, newAudioState)
        
        // Test haptic feedback toggle
        let hapticFeedbackToggle = app.switches["Haptic Feedback"]
        let initialHapticState = hapticFeedbackToggle.value as? String == "1"
        hapticFeedbackToggle.tap()
        
        let newHapticState = hapticFeedbackToggle.value as? String == "1"
        XCTAssertNotEqual(initialHapticState, newHapticState)
        
        // Test form strictness setting
        let formStrictnessButton = app.buttons["Form Strictness"]
        formStrictnessButton.tap()
        
        let strictOption = app.buttons["Strict"]
        strictOption.tap()
        
        // Verify setting was saved
        XCTAssertTrue(app.staticTexts["Strict"].exists)
    }
    
    func testAccessibilityFeatures() throws {
        // Navigate to accessibility settings
        app.tabBars.buttons["Profile"].tap()
        app.buttons["Accessibility"].tap()
        
        // Test VoiceOver support
        let voiceOverButton = app.buttons["VoiceOver Settings"]
        voiceOverButton.tap()
        
        let detailedAnnouncementsToggle = app.switches["Detailed Announcements"]
        detailedAnnouncementsToggle.tap()
        
        // Test large text support
        app.navigationBars.buttons.firstMatch.tap() // Back
        
        let largeTextButton = app.buttons["Large Text"]
        largeTextButton.tap()
        
        let extraLargeOption = app.buttons["Extra Large"]
        extraLargeOption.tap()
        
        // Verify text size changed
        let sampleText = app.staticTexts.firstMatch
        XCTAssertTrue(sampleText.exists)
    }
    
    // MARK: - Performance Tests
    
    func testAppPerformance() throws {
        // Measure app launch time
        measure(metrics: [XCTApplicationLaunchMetric()]) {
            app.launch()
        }
    }
    
    func testWorkoutFlowPerformance() throws {
        // Measure workout flow performance
        measure {
            startQuickWorkout()
            
            // Simulate rapid interactions
            let completeRepButton = app.buttons["Complete Rep"]
            for _ in 1...20 {
                completeRepButton.tap()
            }
            
            // End workout
            app.buttons["Pause"].tap()
            app.buttons["End Workout"].tap()
        }
    }
    
    // MARK: - Error Handling Tests
    
    func testNetworkErrorHandling() throws {
        // Simulate network error conditions
        app.launchArguments.append("--simulate-network-error")
        app.launch()
        
        // Try to sync data
        app.tabBars.buttons["Profile"].tap()
        app.buttons["Sync Data"].tap()
        
        // Verify error message appears
        let errorAlert = app.alerts.firstMatch
        XCTAssertTrue(errorAlert.waitForExistence(timeout: 5))
        
        let errorMessage = app.staticTexts["Network connection failed"]
        XCTAssertTrue(errorMessage.exists)
        
        // Dismiss error
        app.buttons["OK"].tap()
    }
    
    // MARK: - Helper Methods
    
    private func startQuickWorkout() {
        let startWorkoutButton = app.buttons["Start Workout"]
        startWorkoutButton.tap()
        
        // Select quick workout option
        let quickWorkoutButton = app.buttons["Quick Workout"]
        if quickWorkoutButton.waitForExistence(timeout: 5) {
            quickWorkoutButton.tap()
        } else {
            // Fallback: select first available workout
            let firstWorkout = app.collectionViews.firstMatch.cells.firstMatch
            firstWorkout.tap()
            app.buttons["Start"].tap()
        }
        
        // Wait for workout to begin
        let workoutView = app.otherElements["WorkoutView"]
        XCTAssertTrue(workoutView.waitForExistence(timeout: 10))
    }
    
    private func waitForElementToDisappear(_ element: XCUIElement, timeout: TimeInterval = 5) {
        let predicate = NSPredicate(format: "exists == false")
        let expectation = XCTNSPredicateExpectation(predicate: predicate, object: element)
        let result = XCTWaiter().wait(for: [expectation], timeout: timeout)
        XCTAssertEqual(result, .completed)
    }
}
